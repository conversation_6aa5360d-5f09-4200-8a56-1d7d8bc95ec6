package com.iqiyi.vip.service;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/13 14:20
 */
public class CollectionTest {

    @Test
    public void test() {

        ArrayList<String> strings = Lists.newArrayList("1", "2", "3");
        ArrayList<String> strings2 = Lists.newArrayList("7", "5", "8", "6");
        System.out.println(strings.retainAll(strings2));
        System.out.println(strings);

    }

}
