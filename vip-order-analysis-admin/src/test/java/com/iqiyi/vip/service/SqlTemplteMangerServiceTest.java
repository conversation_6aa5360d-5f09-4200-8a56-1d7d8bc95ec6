package com.iqiyi.vip.service;

import com.iqiyi.vip.BaseTest;
import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.dto.base.DataResult;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertTrue;

class SqlTemplteMangerServiceTest extends BaseTest {

    @Resource
    private SqlTemplteMangerService sqlTemplteMangerService;


    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void save() {

    }

    @Test
    void update() {
    }

    @Test
    void delete() {
    }

    @Test
    void pageList() {

    }

    @Test
    void detail() {
        DataResult<AnalysisSqlTemplate> result = sqlTemplteMangerService.detail(20L);
        assertTrue(result.getSuccess());
        assertTrue(StringUtils.isNotEmpty(result.getData().getDescription()));
    }

    @Test
    void list() {
    }
}