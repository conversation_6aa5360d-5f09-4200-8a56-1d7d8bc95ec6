package com.iqiyi.vip.service;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.iqiyi.vip.BaseTest;
import com.iqiyi.vip.dto.permission.DataPermissionApprovalCallbackDTO;
import com.iqiyi.vip.enums.DataPermissionOaStatusEnum;

/**
 * <AUTHOR>
 * @className DataPermissionServiceTest
 * @description
 * @date 2024/1/5
 **/
public class DataPermissionServiceTest extends BaseTest {

    @Autowired
    private DataPermissionService dataPermissionService;

    @Test
    public void callback() {
        DataPermissionApprovalCallbackDTO callbackDTO = DataPermissionApprovalCallbackDTO.builder()
            .acId("7543673")
            .oaStatus(0)
            .build();
        dataPermissionService.updateDataPermissionOaApprovalStatus(callbackDTO, DataPermissionOaStatusEnum.transfor(callbackDTO.getOaStatus()));
    }

}
