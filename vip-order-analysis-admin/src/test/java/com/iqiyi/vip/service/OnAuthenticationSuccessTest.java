//package com.iqiyi.vip.service;
//
//import com.iqiyi.vip.config.CustomAuthenticationSuccessHandler;
//import org.junit.Test;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.Map;
//
///**
// * <AUTHOR> <PERSON>
// * @date 2023/8/23 17:25
// */
//public class OnAuthenticationSuccessTest {
//
//    private static final String DEVOPS_SYSTEM_CODE = "vip-order-analysis"; // 对接devops权限系统的系统名
//    private static final String DEVOPS_TOKEN = "kouri43uriuj4k3o5uifyfioq3iui"; // 对接devops权限系统的token
//
//    @Test
//    public void testCheckAndSignUpUserToDevOps() {
//        // checkAndSignUpUserToDevOps("libolin01");
//        CustomAuthenticationSuccessHandler handler = new CustomAuthenticationSuccessHandler();
//        handler.checkAndSignUpUserToDevOps("libolin01");
//    }
//
//    @Test
//    public void testQueryAllMenuAndResource() {
//        RestTemplate restTemplate = new RestTemplate();
//        String permissionUrl = "http://devops.vip.online.qiyi.qae/vip-devops/api/v1/menu/queryAllMenuAndResource";
//        try {
//            ResponseEntity<Object> permissionResponse = restTemplate.exchange(
//                    permissionUrl + "?sysCode=" + DEVOPS_SYSTEM_CODE + "&token=" + DEVOPS_TOKEN,
//                    HttpMethod.GET, null, Object.class);
//
//            Object permissionRes = permissionResponse.getBody();
//            System.out.println(permissionRes);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//}
