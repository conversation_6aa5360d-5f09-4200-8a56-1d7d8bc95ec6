package com.iqiyi.vip.service;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.merge.LoopMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/24 14:02
 */
public class EasyExcelTest {

    @Test
    public void test() {
//        EasyExcel.write("demo.xlsx")
//                // 这里放入动态头
//                .head(head())
//                .sheet("模板")
//                // 当然这里数据也可以用 List<List<String>> 去传入
//                .doWrite(data());

        WriteWorkbook writeWorkbook = new WriteWorkbook();
        writeWorkbook.setHead(head());
        writeWorkbook.setFile(new File("demo.xlsx"));
        writeWorkbook.getCustomWriteHandlerList()
                .add(new LoopMergeStrategy(1, 8, 0) { // 合并单元格数量 eachRow 合并行，columnExtend 合并列，columnIndex 在哪列开始合并（向后）
                    @Override
                    public void afterRowDispose(RowWriteHandlerContext context) {
                        if (context.getRowIndex() == 0) {
                            super.afterRowDispose(context);
                        }
                    }
                });
        writeWorkbook.getCustomWriteHandlerList()
                .add(new LoopMergeStrategy(1, 2, 0) {
                    @Override
                    public void afterRowDispose(RowWriteHandlerContext context) {
                        if (context.getRowIndex() == 1) {
                            super.afterRowDispose(context);
                        }
                    }
                });
        writeWorkbook.getCustomWriteHandlerList()
                .add(new LongestMatchColumnWidthStyleStrategy() {
                    @Override
                    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
                        writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 45 * 256);
                    }

                });
        writeWorkbook.getCustomWriteHandlerList()
                .add(new SimpleRowHeightStyleStrategy((short) 10, (short) 30) {
                    @Override
                    protected void setContentColumnHeight(Row row, int relativeRowIndex) {
                        if (row.getRowNum() == 0) {
                            row.setHeightInPoints(120);
                        } else if (row.getRowNum() == 1) {
                            row.setHeightInPoints(50);
                        } else if (row.getRowNum() == 2) {
                            row.setHeightInPoints(40);
                        } else {
                            super.setContentColumnHeight(row, relativeRowIndex);
                        }
                    }
                });
        writeWorkbook.getCustomWriteHandlerList()
                .add(new CellWriteHandler() {
                    @Override
                    public void afterCellDispose(CellWriteHandlerContext context) {
                        Cell cell = context.getCell();
                        WriteCellData<?> cellData = context.getFirstCellData();
                        WriteCellStyle orCreateStyle = cellData.getOrCreateStyle();
                        if (cell.getRowIndex() == 0) {
                            WriteCellStyle writeCellStyle = orCreateStyle;
                            WriteFont writeFont = new WriteFont();
                            writeFont.setColor(Font.COLOR_RED);
//                            writeFont.setBold(true);
                            writeCellStyle.setWriteFont(writeFont);
                            writeCellStyle.setWrapped(true);
                            writeCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
                        } else {
                            WriteCellStyle writeCellStyle = orCreateStyle;
                            if (cell.getRowIndex() == 2 || cellData.getRowIndex() == 1) {
                                WriteFont writeFont = new WriteFont();
                                writeFont.setBold(true);
                                writeFont.setFontHeightInPoints(cellData.getRowIndex() == 1 ? Short.valueOf((short) 16) :  Short.valueOf((short) 13));
                                writeCellStyle.setWriteFont(writeFont);
                                writeCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                                writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                            }
                            writeCellStyle.setWrapped(true);
                            writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                        }

                        orCreateStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                        orCreateStyle.setBorderRight(BorderStyle.THIN);
                    }
                });
        ExcelWriter excelWriter = new ExcelWriter(writeWorkbook);

        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setSheetName("模板");


        excelWriter.write(this::txt, writeSheet);
        excelWriter.write(this::mapping, writeSheet);
        excelWriter.write(head(), writeSheet);
        excelWriter.write(data(), writeSheet);
        excelWriter.finish();

    }

    private List<List<String>> head() {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<String>();
        head0.add("字符串" + System.currentTimeMillis());
        head0.add("数字" + System.currentTimeMillis());
        head0.add("日期" + System.currentTimeMillis());
        list.add(head0);
        return list;
    }

    public List<List<Object>> txt() {
        String txt = "表头说明文案：\n" +
                "\n" +
                "注：\n" +
                "1、此表数据主要分析符合筛选条件的用户，以前（分析周期内最近一笔订单）是什么样的，来自哪里。圈选用户为待分析的用户订单，这些用户之前是什么渠道购买，购买的什么卡种类型等\n" +
                "2、在查询页面勾选的维度，渠道迁移和卡种变迁，会相应地分别展示在圈选用户列和溯源分析列\n" +
                "3、圈选用户：符合分析条件的订单用户\n" +
                "4、溯源分析：圈选的用户来自哪儿，什么渠道，什么卡种类型等，溯源月份即之前（分析周期内）的订单按月聚合，溯源订购数即分析周期内最近一笔订单数统计";
        List<List<Object>> head0 = new ArrayList<>();
        head0.add(Lists.newArrayList(txt, "-", "-"));
        return head0;
    }

    public List<List<Object>> mapping() {
        List<List<Object>> head0 = new ArrayList<>();
        head0.add(Lists.newArrayList("区间1", "-", "区间2"));
        return head0;
    }

    private List<List<Object>> data() {

        List<List<Object>> list = ListUtils.newArrayList();
        for (int i = 0; i < 10; i++) {
            List<Object> list1 = new ArrayList<>();
            list1.add("字符串" + i);
            list1.add("2023-03-34");
            list1.add(0.56);
            list.add(list1);
        }
        return list;
    }

    @Test
    public void test2() {
        StringBuilder stringBuilder = new StringBuilder("");
        System.out.println(stringBuilder.length());
    }

}
