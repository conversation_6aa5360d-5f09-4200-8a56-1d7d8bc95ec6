package com.iqiyi.vip.utils;

import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.BaseTest;

/**
 * @author: linpe<PERSON><PERSON>
 * @createTime: 2023/09/13
 */
public class ConditionUtilsTest  extends BaseTest {

    @Test
    public void testGetValue() {
        Map<String, List<Object>> conditionParamMap = new HashMap<>();
        conditionParamMap.put("a", Lists.newArrayList(1));
        conditionParamMap.put("b", Lists.newArrayList("2"));
        conditionParamMap.put("c", Lists.newArrayList(3));
        conditionParamMap.put("d", Lists.newArrayList("4.3"));
        conditionParamMap.put("e", Lists.newArrayList(1,2,3));
        Integer a = ConditionParamUtils.getSingleValue(conditionParamMap, "a", Integer.class);
        System.out.println(a);
        String b = ConditionParamUtils.getSingleValue(conditionParamMap, "b", String.class);
        System.out.println(b);
        Double c = ConditionParamUtils.getSingleValue(conditionParamMap, "c", Double.class);
        System.out.println(c);
        Double d = ConditionParamUtils.getSingleValue(conditionParamMap, "d", Double.class);
        System.out.println(d);
        List<Integer> e = ConditionParamUtils.getListValue(conditionParamMap, "e", Integer.class);
        System.out.println(e);

    }
}
