package com.iqiyi.vip;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = AdminApplication.class)
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@AutoConfigureMetrics
@Transactional
@Rollback
public class BaseTest {
    static{
        System.setProperty("eureka.client.register-with-eureka", "false");
        System.setProperty("env", "dev");
        System.setProperty("app.id", "vip-order-analysis");
        System.setProperty("hystrix.command.default.execution.timeout.enabled", "false");
        System.setProperty("HOST","127.0.0.1");
        System.setProperty("PORT_8080","8080");
    }
}
