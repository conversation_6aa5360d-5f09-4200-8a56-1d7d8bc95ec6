package repository;

import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.iqiyi.vip.BaseTest;

import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionPageQryDTO;

import static org.junit.jupiter.api.Assertions.assertTrue;

class AnalysisDimensionRepositoryImplTest extends BaseTest {

    @Resource
    private AnalysisDimensionRepository analysisDimensionRepositoryImpl;

    @Test
    void selectByCode() {
        AnalysisDimension result = analysisDimensionRepositoryImpl.selectByCode("level1_business_name");
        assertTrue(StringUtils.isNotEmpty(result.getDocumentation()));
    }

    @Test
    void selectById() {
        AnalysisDimension result = analysisDimensionRepositoryImpl.selectById(20L);
        assertTrue(StringUtils.isNotEmpty(result.getDocumentation()));
    }

    @Test
    void selectPageList() {
        AnalysisDimensionPageQryDTO query = new AnalysisDimensionPageQryDTO();
        query.setPageNo(1);
        query.setPageSize(3);
        PageListResult<AnalysisDimension> result = analysisDimensionRepositoryImpl.selectPageList(query);
        assertTrue(result.getSuccess());
        List<AnalysisDimension> list = result.getDataList();
        assertTrue(CollectionUtils.isNotEmpty(list));
        Stream<AnalysisDimension> stream = list.stream().filter(o-> StringUtils.isNotEmpty(o.getDocumentation()));
        assertTrue(stream.findAny().isPresent());
    }

    @Test
    void selectByCodes() {
        List<String> list = Arrays.asList("level1_business_name", "level7_business_name");
        List<AnalysisDimension> result = analysisDimensionRepositoryImpl.selectByCodes(list);
        result.forEach(o-> assertTrue(StringUtils.isNotEmpty(o.getDocumentation())));
    }

    @Test
    void selectAll() {
        List<AnalysisDimension> result = analysisDimensionRepositoryImpl.selectAll(new BaseQry());
        List<AnalysisDimension>  filterResult = result.stream().filter(o-> StringUtils.isNotEmpty(o.getDocumentation())).collect(Collectors.toList());
        assertTrue(CollectionUtils.isNotEmpty(filterResult));
    }

}