package repository;

import com.iqiyi.vip.BaseTest;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * @author: linpeihui
 * @createTime: 2023/08/10
 */
public class AnalysisConditionRepositoryTest  extends BaseTest {
    @Resource
    private AnalysisConditionRepository analysisConditionRepository;

    @Test
    public void testInsert() {
        AnalysisCondition condition = AnalysisCondition.builder()
                .code("lphtest1").type(3).required(true).defaultPrompt("ddd1").dataLoadAddr("").selectAll(true)
                .hasTipsIcon(true).tips("ee3").name("rw45").build();
        Long id = analysisConditionRepository.save(condition);
        assertTrue(id > 0);
    }

}
