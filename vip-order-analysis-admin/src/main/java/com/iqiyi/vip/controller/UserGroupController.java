package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.dto.user.group.UpdateUserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDeleteDTO;
import com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO;
import com.iqiyi.vip.dto.user.group.UserGroupQueryDTO;
import com.iqiyi.vip.enums.GroupTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.service.UserGroupService;

/**
 * 订单分析平台-分群管理
 */
@RestController
@RequestMapping("order-analysis/userGroup")
@Api(tags = "订单分析平台-分群管理")
public class UserGroupController {

    @Resource
    private UserGroupService userGroupService;

    @WebLog(simplify = false)
    @PostMapping("query")
    @ApiOperation(value = "人群和订单包查询")
    public ListResult<ConditionPair> executeQuery(@Validated UserGroupQueryDTO userGroupQueryDTO) {
        if (!ThemeTypeEnum.ORDER_THEME.getCode().equals(userGroupQueryDTO.getThemeType())) {
            userGroupQueryDTO.setGroupType(GroupTypeEnum.USER_PACKAGE.getCode());
        }
        List<UserGroupDO> userGroups = userGroupService.query(userGroupQueryDTO);
        List<ConditionPair> conditionPairs = userGroups.stream()
            .filter(fv -> StringUtils.isNotBlank(fv.getName()))
            .map(p -> ConditionPair.builder().code(p.getId()).desc(p.getName()).build())
            .collect(Collectors.toList());
        return ListResult.createSuccess(conditionPairs);
    }

    @WebLog(simplify = false)
    @GetMapping("pageQuery")
    @ApiOperation(value = "任务分页查询", httpMethod = "GET")
    public PageListResult<UserGroupDO> executeQuery(@Validated UserGroupPageQueryDTO userGroupPageQueryDTO) {
        return userGroupService.pageList(userGroupPageQueryDTO);
    }

    @WebLog(simplify = false)
    @GetMapping("deleteById")
    @ApiOperation(value = "删除任务记录", httpMethod = "GET")
    public CommonResult delete(@Validated UserGroupDeleteDTO deleteDTO) {
        return userGroupService.deleteTaskRecord(deleteDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("addUserGroup")
    @ApiOperation(value = "新建用户分群包", httpMethod = "POST")
    public CommonResult add(@Validated UserGroupDTO userGroupDTO) {
        return userGroupService.addUserGroup(userGroupDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("update")
    @ApiOperation(value = "修改用户分群包", httpMethod = "POST")
    public CommonResult updateUserGroup(@Validated UpdateUserGroupDTO updateUserGroupDTO) {
        return userGroupService.updateUserGroup(updateUserGroupDTO);
    }

}
