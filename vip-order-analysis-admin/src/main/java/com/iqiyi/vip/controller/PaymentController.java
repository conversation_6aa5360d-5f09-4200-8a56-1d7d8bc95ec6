package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.entity.PaymentChannel;
import com.iqiyi.vip.domain.entity.PaymentType;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.service.PaymentManageService;

/**
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@RestController
@Slf4j
@RequestMapping("order-analysis/payment")
@Api(tags = "支付渠道查询相关接口")
public class PaymentController {

    @Resource
    private PaymentManageService paymentManageService;

    @GetMapping(value = "channel/payTypes")
    @ApiOperation(value = "支付渠道列表（详细到支付方式）")
    public ListResult<PaymentChannel> channelPayTypeListResult() {
        return paymentManageService.listChannelPayTypes();
    }

    @PostMapping(value = "channels")
    @ApiOperation(value = "支付渠道列表")
    public ListResult<ConditionPair> channelListResult() {
        return paymentManageService.listChannels();
    }

    @GetMapping(value = "payTypes")
    @ApiOperation(value = "根据渠道查询支付方式")
    @ApiImplicitParam(name = "payChannel", value = "支付渠道", dataType = "long", required = true)
    public ListResult<PaymentType> paymentTypeListResult(@RequestParam Long payChannel) {
        return paymentManageService.listPaymentTypes(payChannel);
    }


    @GetMapping(value = "channelDetails")
    @ApiOperation(value = "支付渠道关联支付方式列表")
    public ListResult<PaymentChannel> channelPayTypesListResult() {
        return paymentManageService.channelPayTypesList();
    }
}
