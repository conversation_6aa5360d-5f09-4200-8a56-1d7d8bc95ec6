package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.GroupDimensionDTO;
import com.iqiyi.vip.dto.base.*;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionPageQryDTO;
import com.iqiyi.vip.dto.dimension.DimensionUpdateDTO;
import com.iqiyi.vip.service.DimensionMangerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Api(tags = "订单分析平台-维度管理")
@RestController
@RequestMapping("order-analysis/dimension")
public class DimensionController {

    @Resource
    private DimensionMangerService dimensionMangerService;

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-新增")
    @PostMapping("save")
    public CommonResult save(DimensionUpdateDTO updateDTO) {
        return dimensionMangerService.save(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-更新")
    @PostMapping("update")
    public CommonResult update(DimensionUpdateDTO updateDTO) {
        return dimensionMangerService.update(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-删除")
    @ApiImplicitParam(name = "id", value = "维度code", type = "Long", required = true)
    @PostMapping("delete")
    public CommonResult delete(@RequestParam Long id) {
        return dimensionMangerService.delete(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-详情")
    @ApiImplicitParam(name = "id", value = "维度id", type = "Long", required = true)
    @GetMapping("detail")
    public DataResult<AnalysisDimension> detail(@RequestParam Long id) {
        return dimensionMangerService.detail(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-列表查询（分组）")
    @GetMapping("groupList")
    public ListResult<GroupDimensionDTO> groupList(@Validated BaseQry baseQry) {
        return dimensionMangerService.groupList(baseQry);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "维度管理-分页查询")
    @GetMapping("pageList")
    public PageListResult<AnalysisDimension> pageList(AnalysisDimensionPageQryDTO query) {
        return dimensionMangerService.pageList(query);
    }
}
