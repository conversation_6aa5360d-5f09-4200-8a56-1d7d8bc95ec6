package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.List;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.target.AvailableDateQueryDTO;
import com.iqiyi.vip.dto.target.AvailableDateResultDTO;
import com.iqiyi.vip.dto.target.BaseQueryDTO;
import com.iqiyi.vip.dto.target.CommonQueryDTO;
import com.iqiyi.vip.dto.target.FeedbackDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.SimpleQueryDTO;
import com.iqiyi.vip.dto.target.SimpleQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.BusinessTypeEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.mapper.OrderPilotComponent;
import com.iqiyi.vip.service.AnalysisResultManagerService;

/**
 * @className QueryController
 * @description
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Api(tags = "订单分析平台-指标结果查询", value = "指标结果查询接口")
@RestController
@RequestMapping("order-analysis")
@Slf4j
public class QueryController {

    @Resource
    private AnalysisResultManagerService analysisResultManagerService;

    @Resource
    private OrderPilotComponent orderPilotComponent;

    @WebLog(simplify = false)
    @PostMapping("query")
    @ApiOperation(value = "指标结果查询", httpMethod = "POST")
    public CommonResult executeQuery(@Validated @RequestBody BaseQueryDTO baseQueryDTO) {
        log.info("enter executeQuery, baseQueryDTO:{}", baseQueryDTO);
        return analysisResultManagerService.executeQuery(baseQueryDTO);
    }

    @WebLog(simplify = false)
    @GetMapping("availableDate")
    @ApiOperation(value = "可分析时间范围查询", httpMethod = "GET")
    public DataResult<AvailableDateResultDTO> availableDateQuery(@Validated AvailableDateQueryDTO dateQueryDTO) {
        log.info("enter availableDateQuery : {}", dateQueryDTO);
        return DataResult.success(analysisResultManagerService.availableDate(dateQueryDTO));
    }

    @WebLog(simplify = false)
    @PostMapping("commonQuery")
    @ApiOperation(value = "commonQuery", httpMethod = "POST")
    public DataResult<List<LinkedHashMap<String, Object>>> commonQuery(@Valid @RequestBody CommonQueryDTO commonQueryDTO) {
        log.info("enter commonQuery : {}", commonQueryDTO);
        TargetAnalysisQueryDTO queryDTO = TargetAnalysisQueryDTO.builder()
            .querySql(commonQueryDTO.getSqlStr())
            .dataBase(commonQueryDTO.getDataBase())
            .build();
        try {
            OriginalQueryResultDTO queryResultDTO = orderPilotComponent.queryTargetResult(queryDTO);
            return DataResult.success(queryResultDTO.getDataList());
        } catch (SQLException e) {
            log.error("commonQuery error! commonQueryDTO:{}", commonQueryDTO, e);
            return DataResult.create(CodeEnum.QUERY_ERROR, null);
        }
    }

    @WebLog(simplify = false)
    @GetMapping("simpleQuery")
    @ApiOperation(value = "指标结果查询", httpMethod = "GET")
    public DataResult<SimpleQueryResultDTO> simpleQuery(@Validated SimpleQueryDTO queryDTO) throws UnsupportedEncodingException {
        queryDTO.setThemeType(ThemeTypeEnum.ORDER_THEME.getCode());
        queryDTO.setBusinessTypeId(BusinessTypeEnum.BASIC_ANALYSIS.getCode());
        queryDTO.setDataPermissionType(DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode());
        log.info("enter simpleQuery, queryDTO:{}", queryDTO);
        String param = queryDTO.getParam();
        if (StringUtils.isBlank(param)) {
            return DataResult.create(CodeEnum.ERROR_PARAM, null);
        }
        return analysisResultManagerService.simpleQuery(queryDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("feedback")
    @ApiOperation(value = "结果反馈", httpMethod = "POST")
    public CommonResult feedback(@Validated @RequestBody FeedbackDTO feedbackDTO) {
        log.info("enter feedback, feedbackDTO:{}", feedbackDTO);
        return analysisResultManagerService.saveFeedback(feedbackDTO);
    }
}
