package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.GroupTargetDTO;
import com.iqiyi.vip.dto.base.*;
import com.iqiyi.vip.dto.target.AnalysisTargetPageQryDTO;
import com.iqiyi.vip.dto.target.TargetGroupQry;
import com.iqiyi.vip.dto.target.TargetUpdateDTO;
import com.iqiyi.vip.service.TargetMangerService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@RestController
@RequestMapping("order-analysis/target")
@Api(tags = "订单分析平台-指标管理")
public class TargetController {

    @Resource
    private TargetMangerService targetMangerService;

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-新增")
    @PostMapping("save")
    public CommonResult save(TargetUpdateDTO updateDTO) {
        return targetMangerService.save(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-更新")
    @PostMapping("update")
    public CommonResult update(TargetUpdateDTO updateDTO) {
        return targetMangerService.update(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-删除")
    @ApiImplicitParam(name = "id", value = "指标id", type = "Long", required = true)
    @PostMapping("delete")
    public CommonResult delete(@RequestParam Long id) {
        return targetMangerService.delete(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-详情")
    @ApiImplicitParam(name = "id", value = "指标id", type = "Long", required = true)
    @GetMapping("detail")
    public DataResult<AnalysisTarget> detail(@RequestParam Long id) {
        return targetMangerService.detail(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-分页查询")
    @GetMapping("pageList")
    public PageListResult<AnalysisTarget> pageList(AnalysisTargetPageQryDTO query) {
        return targetMangerService.pageList(query);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-列表查询")
    @GetMapping("list")
    public ListResult<AnalysisTarget> list(BaseQry baseQry) {
        return targetMangerService.list(baseQry);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-列表查询（分组）")
    @GetMapping("groupList")
    public ListResult<GroupTargetDTO> groupList(TargetGroupQry targetGroupQry) {
        return targetMangerService.groupList(targetGroupQry);
    }
}
