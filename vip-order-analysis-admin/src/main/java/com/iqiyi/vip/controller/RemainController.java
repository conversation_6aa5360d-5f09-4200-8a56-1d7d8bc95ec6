package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.remain.ConditionCombinationVO;
import com.iqiyi.vip.dto.remain.RemainConditionGroupVO;
import com.iqiyi.vip.dto.remain.RemainDataReqDTO;
import com.iqiyi.vip.dto.remain.RemainDataVO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RemainDataTypeEnum;
import com.iqiyi.vip.service.RemainService;

/**
 * <AUTHOR>
 * @className RemainController
 * @description
 * @date 2024/6/25
 **/
@RestController
@RequestMapping(value = "order-analysis/remain")
@Api(tags = "remain信息查询接口")
@Slf4j
public class RemainController {

    @Resource
    private RemainService remainService;

    @ApiOperation(value = "查询remain信息")
    @PostMapping(value = "data")
    @WebLog(simplify = false)
    public DataResult<List<RemainDataVO>> getRemainData(@RequestBody @Valid RemainDataReqDTO reqDTO) {
        try {
            return DataResult.success(remainService.getRemainData(reqDTO));
        } catch (Exception e) {
            log.error("getRemainData error", e);
            return new DataResult<>(CodeEnum.ERROR_PARAM.getCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "查询remain指标类型")
    @GetMapping(value = "remainDataType")
    public DataResult<List<CodeDescPair>> getRemainDataType() {
        return DataResult.success(RemainDataTypeEnum.getCodeDescPairList());
    }

    @ApiOperation(value = "查询remain条件层级信息")
    @GetMapping(value = "remainConditionGroup")
    public DataResult<List<RemainConditionGroupVO>> getRemainConditionGroupInfo() {
        return DataResult.success(remainService.getRemainConditionGroupInfo());
    }

    @ApiOperation(value = "查询条件间组合列表")
    @GetMapping(value = "conditionCombinationList")
    public DataResult<List<ConditionCombinationVO>> getConditionCombinationList() {
        return DataResult.success(remainService.getConditionCombinationList());
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "留存看板权限")
    @GetMapping(value = "permissionCheck")
    public DataResult<Boolean> remainBoardWhiteList(String operator) {
        return DataResult.success(remainService.ownRemainBoardPermission(operator));
    }

}
