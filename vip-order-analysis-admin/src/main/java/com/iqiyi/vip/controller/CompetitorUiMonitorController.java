package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPageListParam;
import com.iqiyi.vip.dto.competitor.CompetitorUiDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorUserStatusListParam;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.service.CompetitorUiService;

/**
 * @author: guojing
 * @date: 2024/10/25 16:43
 *
 */
@Slf4j
@RestController
@RequestMapping("order-analysis/competitor/uiMonitor")
@Api(tags = "订单分析平台-竞品页面监控接口")
public class CompetitorUiMonitorController {

    @Resource
    private CompetitorUiService competitorUiService;
    @Resource
    private AvailableDateRepository availableDateRepository;

    @ApiOperation(value = "品牌列表", httpMethod = "GET")
    @GetMapping(value = "/brandsList")
    public ListResult<String> brandsList() {
        return ListResult.createSuccess(competitorUiService.brandsList());
    }

    @ApiOperation(value = "版本列表", httpMethod = "GET")
    @GetMapping(value = "/clientVersionList")
    public ListResult<String> clientVersionList() {
        return ListResult.createSuccess(competitorUiService.clientVersionList());
    }

    @ApiOperation(value = "日期范围", httpMethod = "GET")
    @GetMapping(value = "/dateRange")
    public DataResult<CompetitorDateRangeDTO> dateRange() {
        return DataResult.success(competitorUiService.dateRange());
    }

    @ApiOperation(value = "场景列表", httpMethod = "GET")
    @GetMapping(value = "/pageList")
    public ListResult<String> pageList(@Valid CompetitorPageListParam param) {
        return ListResult.createSuccess(competitorUiService.pageList(param));
    }

    @ApiOperation(value = "用户状态(生命周期)列表", httpMethod = "GET")
    @GetMapping(value = "/userStatusList")
    public ListResult<String> userStatusList(@Valid CompetitorUserStatusListParam param) {
        return ListResult.createSuccess(competitorUiService.userStatusList(param));
    }

    @ApiOperation(value = "保存竞品页面监控信息", httpMethod = "POST")
    @PostMapping(value = "/save")
    public CommonResult save(@Valid @RequestBody CompetitorUiSaveDTO param) {
        competitorUiService.save(param);
        String dt = param.getCreateTime().substring(0, 10);
        AvailableDt dateByDt = availableDateRepository.getDateByDt(dt, ThemeTypeEnum.COMPETITOR_MONITOR.getCode());
        if (dateByDt == null) {
            AvailableDt availableDt = AvailableDt.builder()
                .tableDt(dt)
                .status(1)
                .themeType(ThemeTypeEnum.COMPETITOR_MONITOR.getCode())
                .build();
            availableDateRepository.insert(availableDt);
        }
        return CommonResult.success();
    }

    @ApiOperation(value = "搜索页面监控数据", httpMethod = "GET")
    @GetMapping(value = "/search")
    public ListResult<CompetitorUiDTO> search(@Valid CompetitorUiSearchParam param) {
        return ListResult.createSuccess(competitorUiService.search(param));
    }

}
