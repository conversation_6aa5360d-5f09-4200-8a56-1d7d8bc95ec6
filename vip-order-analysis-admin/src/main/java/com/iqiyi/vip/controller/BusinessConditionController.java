package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.condition.BusinessConditionUpdateDTO;
import com.iqiyi.vip.dto.condition.ConditionUpdateDTO;
import com.iqiyi.vip.service.BusinessConditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: linpeihui
 * @createTime: 2023/08/11
 */
@Api(tags = "订单分析平台-分析类型下的条件管理")
@RestController
@RequestMapping("order-analysis/business/condition")
public class BusinessConditionController {
    @Resource
    private BusinessConditionService businessConditionService;

    @WebLog(simplify = false)
    @ApiOperation(value = "分析类型下的条件管理-排序")
    @PostMapping("sort")
    public CommonResult sort(@RequestBody BusinessConditionUpdateDTO updateDTO) {
        return businessConditionService.sort(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "分析类型下的条件管理-查看")
    @GetMapping("list")
    public ListResult list(BusinessConditionUpdateDTO updateDTO) {
        return ListResult.createSuccess(businessConditionService.list(updateDTO));
    }
}
