package com.iqiyi.vip.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.dimension.RemainV2CodeDescPair;
import com.iqiyi.vip.dto.remain.RemainConditionGroupVO;
import com.iqiyi.vip.dto.remainV2.ExpireAnalysisReq;
import com.iqiyi.vip.dto.remainV2.ExpireAnalysisRes;
import com.iqiyi.vip.dto.remainV2.LossAndBackAnalysisReq;
import com.iqiyi.vip.dto.remainV2.LossAndBackAnalysisRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RemainV2DimensionEnum;
import com.iqiyi.vip.service.ExpireAnalysisService;
import com.iqiyi.vip.service.LossAndBackAnalysisService;
import com.iqiyi.vip.service.RemainService;

/**
 * <AUTHOR> Li
 * @date 2025/5/29 11:22
 */
@RestController
@RequestMapping(value = "order-analysis/remainV2")
@Api(tags = "流失和回归分析接口")
@Slf4j
public class RemainControllerV2 {

    @Resource
    private ExpireAnalysisService expireAnalysisService;

    @Resource
    private LossAndBackAnalysisService backAnalysisService;

    @Resource
    private RemainService remainService;

    @ApiOperation(value = "维度查询")
    @GetMapping(value = "getRemainV2DimensionCode")
    public DataResult<List<RemainV2CodeDescPair>> getRemainV2DimensionCode() {
        return DataResult.success(RemainV2DimensionEnum.getCodeDescPairList());
    }

    @ApiOperation(value = "条件查询")
    @GetMapping(value = "getRemainV2ConditionGroup")
    public DataResult<List<RemainConditionGroupVO>> getRemainV2ConditionGroup() {
        List<RemainConditionGroupVO> remainConditionGroupInfo = remainService.getRemainConditionGroupInfo();
        remainConditionGroupInfo.add(RemainConditionGroupVO.builder()
            .code("kr_album_id")
            .name("KR专辑ID")
            .groupValue(null)
            .build()
        );
        return DataResult.success(remainConditionGroupInfo);
    }

    @ApiOperation(value = "查询权益到期信息")
    @PostMapping(value = "expireData")
    @WebLog(simplify = false)
    public DataResult<ExpireAnalysisRes> getExpireAnalysisData(@RequestBody @Valid ExpireAnalysisReq reqDTO) {
        try {
            log.info("enter getExpireAnalysisData, request={}", JSON.toJSONString(reqDTO));
            return DataResult.success(expireAnalysisService.getExpireAnalysisData(reqDTO));
        } catch (Exception e) {
            log.error("getRemainData error", e);
            return new DataResult<>(CodeEnum.QUERY_ERROR.getCode(), CodeEnum.QUERY_ERROR.getMessage());
        }
    }

    @ApiOperation(value = "查询流失后回归信息")
    @PostMapping(value = "lossAndBackData")
    @WebLog(simplify = false)
    public DataResult<LossAndBackAnalysisRes> getLossAndBackAnalysisData(@RequestBody @Valid LossAndBackAnalysisReq reqDTO) {
        try {
            log.info("enter getLossAndBackAnalysisData, request={}", JSON.toJSONString(reqDTO));
            return DataResult.success(backAnalysisService.getLossAndBackData(reqDTO));
        } catch (Exception e) {
            log.error("getRemainData error", e);
            return new DataResult<>(CodeEnum.QUERY_ERROR.getCode(), CodeEnum.QUERY_ERROR.getMessage());
        }
    }

}
