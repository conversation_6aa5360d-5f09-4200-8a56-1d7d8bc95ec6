package com.iqiyi.vip.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.sku.SkuInfoQueryDTO;


/**
 * <AUTHOR>
 * @className SkuInfoController
 * @description
 * @date 2024/5/16
 **/
@RestController
@RequestMapping(value = "order-analysis/admin/skuInfo")
@Api(tags = "商品中心信息接口")
public class SkuInfoController {

    @WebLog(simplify = false)
    @GetMapping("code")
    @ApiOperation(value = "根据code查询", httpMethod = "GET")
    public CommonResult query(@Validated SkuInfoQueryDTO queryDTO) throws JsonProcessingException {
        return CommonResult.success();
    }

}
