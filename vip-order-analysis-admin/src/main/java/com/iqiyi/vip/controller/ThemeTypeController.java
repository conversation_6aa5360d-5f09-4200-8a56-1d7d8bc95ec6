package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.theme.ThemeTypeWhiteList;
import com.iqiyi.vip.service.ThemeTypeService;

/**
 * 天眼主题类型接口
 *
 * @author: linpeihui
 * @createTime: 2023/08/11
 */
@RestController
@RequestMapping(value = "order-analysis/themeType")
@Api(tags = "天眼主题类型接口")
public class ThemeTypeController {

    @Resource
    private ThemeTypeService themeTypeService;

    @WebLog(simplify = false)
    @ApiOperation(value = "天眼主题类型列表")
    @GetMapping(value = "list")
    public ListResult<AnalysisThemeType> listThemeTypes(String operator) {
        return themeTypeService.themeTypes(operator);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "天眼主题白名单")
    @GetMapping(value = "themeTypeWhiteList")
    public ListResult<ThemeTypeWhiteList> themeTypeWhiteList() {
        return ListResult.createSuccess(themeTypeService.themeTypeWhiteList());
    }
}
