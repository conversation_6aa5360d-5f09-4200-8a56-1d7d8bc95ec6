package com.iqiyi.vip.controller;

import com.iqiyi.vip.service.AnalysisResultStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 6/2/22
 * @apiNote
 */
@RestController
@Slf4j
@RequestMapping("order-analysis/storage")
@Api(tags = "订单分析平台-结果存储相关接口")
public class ResultDownloadController {

    @Resource
    private AnalysisResultStorageService analysisResultStorageService;

    @ApiOperation(value = "订单分析结果下载")
    @ApiImplicitParam(name = "filename", value = "文件名称", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "download")
    public void download(@RequestParam String fileName) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        analysisResultStorageService.downloadResult(fileName, response);
    }
}
