package com.iqiyi.vip.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;
import com.iqiyi.vip.domain.repository.AnalysisSubTaskRepository;
import com.iqiyi.vip.dto.base.BeiDouDataResult;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.beidou.*;
import com.iqiyi.vip.enums.CodeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/1/4 21:45
 */
@RestController
@Slf4j
@RequestMapping(value = "order-analysis/beidou")
@Api(tags = "天眼上传北斗接口")
public class BeiDouController {

    @Value("${beidou.token}")
    private String uploadToBeiDouToken;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private AnalysisSubTaskRepository analysisSubTaskRepository;

    private final ObjectMapper mapper = new ObjectMapper();

    public static final String SUCCESS_CODE = "A00000";

    @WebLog(simplify = false)
    @GetMapping("beidouQueryInfo")
    @ApiOperation(value = "北斗查询信息，包括用户可选的业务线和人群包名称", httpMethod = "GET")
    public DataResult<BeidouQueryInfoDTO> beidouQueryInfo(String operator, Long taskId, Integer userGroupId) {

        String url = "http://analytics.qiyi.domain/openapi/app/list?username=" + operator;
        BeiDouDataResult<List<BusinessResDTO>> result;

        try {
            ResponseEntity<BeiDouDataResult<List<BusinessResDTO>>> responseEntity =
                    restTemplate.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<BeiDouDataResult<List<BusinessResDTO>>>() {
                    });

            result = responseEntity.getBody();

            if (result == null) {
                log.error("解析用户可选业务线失败");
                return DataResult.create(CodeEnum.ERROR_SYSTEM, null);
            }

            if ("E00001".equals(result.getCode())) {
                return DataResult.create(CodeEnum.GET_BEI_DOU_BUSINESS_ERROR, null);
            }
        } catch (Exception e) {
            log.error("解析用户可选业务线失败, errMsg: {}", e.getMessage());
            return DataResult.create(CodeEnum.ERROR_SYSTEM, null);
        }
        // 获得用户在北斗上注册的业务线
        List<BusinessResDTO> businessRes = result.getData();

        BeidouQueryInfoDTO queryInfo = new BeidouQueryInfoDTO();

        // 根据 taskId 查表：analysis_sub_task，获取人群包名称，并确定哪些业务线已经上传
        AnalysisSubTaskDO subTask = analysisSubTaskRepository.getSubTaskByTaskIdOrUserGroupId(taskId, userGroupId);
        queryInfo.setUserPackageName(subTask.getUserPackageName());

        String businessIds = subTask.getBusinessIds();

        if (StringUtils.isEmpty(businessIds)) {
            queryInfo.setBusinessRes(businessRes);
            return DataResult.success(queryInfo);
        }

        String[] businessIdsArr = businessIds.split(",");
        for (BusinessResDTO res : businessRes) {
            for (String s : businessIdsArr) {
                if (res.getValue().equals(s)) {
                    res.setAvailable(0);
                }
            }
        }
        queryInfo.setBusinessRes(businessRes);

        return DataResult.success(queryInfo);
    }

    @WebLog(simplify = false)
    @PostMapping("uploadToBeiDou")
    @ApiOperation(value = "上传人群包信息至北斗", httpMethod = "POST")
    public CommonResult uploadToBeiDou(@Validated @RequestBody UploadToBeiDouDTO uploadToBeiDouDTO) {
        // 从表：analysis_sub_task中根据taskId取对应子任务的file_name
        String fileUrl = analysisSubTaskRepository.getSubTaskByTaskIdOrUserGroupId(uploadToBeiDouDTO.getTaskId(), uploadToBeiDouDTO.getUserGroupId())
            .getFileUrl();
        String businessIdsHasUpload = analysisSubTaskRepository.getSubTaskByTaskIdOrUserGroupId(uploadToBeiDouDTO.getTaskId(), uploadToBeiDouDTO.getUserGroupId())
            .getBusinessIds();

        if (StringUtils.isEmpty(fileUrl)) {
            return CommonResult.create(CodeEnum.UPLOAD_TO_BEI_DOU_ERROR);
        }

        // 将请求中的businessIds与表analysis_sub_task中的businessIds进行比对，如果有重复的，则过滤掉
        if (StringUtils.isNotEmpty(businessIdsHasUpload)) {
            String[] businessIdsHasUploadArr = businessIdsHasUpload.split(",");
            for (String s : businessIdsHasUploadArr) {
                uploadToBeiDouDTO.getBusinessIds().remove(s);
            }
        }

        try {
            for (String businessId : uploadToBeiDouDTO.getBusinessIds()) {
                UploadToBeiDouRes uploadRes = uploadToBeiDouCore(fileUrl, uploadToBeiDouDTO.getUserPackageName(), uploadToBeiDouDTO.getOperator(), businessId);
                if (!SUCCESS_CODE.equals(uploadRes.getCode())) {
                    return CommonResult.create(CodeEnum.UPLOAD_TO_BEI_DOU_ERROR);
                }
            }
        } catch (JsonProcessingException e) {
            return CommonResult.create(CodeEnum.UPLOAD_TO_BEI_DOU_ERROR);
        }

        // 更新表analysis_sub_task中的业务线和人群包名称
        updateSubTask(uploadToBeiDouDTO, businessIdsHasUpload);
        return CommonResult.success();
    }

    private UploadToBeiDouRes uploadToBeiDouCore(String fileURL, String userPackageName, String operator, String businessId) throws JsonProcessingException {
        UserPackageDto userPackageDto = new UserPackageDto();

        userPackageDto.setName(userPackageName);
        userPackageDto.setUsername(operator);
        userPackageDto.setPid(Integer.parseInt(businessId));
        userPackageDto.setPtype(2);
        userPackageDto.setUrl(fileURL);
        userPackageDto.setExternal("tianyan");
        userPackageDto.setToken(uploadToBeiDouToken);

        // 将对应txt文件上传至北斗
        String jsonStr = mapper.writeValueAsString(userPackageDto);
        log.info("[uploadToBeiDou] userPackageDto json str: {}", jsonStr);

        String beiDouURL = "http://analytics.qiyi.domain/openapi/external/cluster/create";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HttpEntity<String> entity = new HttpEntity<>(jsonStr, headers);
        ResponseEntity<String> response = restTemplate.exchange(beiDouURL, HttpMethod.POST, entity, String.class);

        log.info("uploadToBeiDouCore response: {}", response.getBody());
        return mapper.readValue(response.getBody(), UploadToBeiDouRes.class);
    }

    private void updateSubTask(UploadToBeiDouDTO uploadToBeiDouDTO, String businessIdsHasUpload) {
        AnalysisSubTaskDO subTask = new AnalysisSubTaskDO();
        subTask.setParentTaskId(uploadToBeiDouDTO.getTaskId());
        subTask.setUserGroupId(uploadToBeiDouDTO.getUserGroupId());
        subTask.setUserPackageName(uploadToBeiDouDTO.getUserPackageName());

        // 将请求中的businessIds与表analysis_sub_task中的businessIds进行比对，如果有重复的，则合并
        List<String> businessIds = uploadToBeiDouDTO.getBusinessIds();
        if (StringUtils.isNotEmpty(businessIdsHasUpload)) {
            for (String s : businessIdsHasUpload.split(",")) {
                if (!businessIds.contains(s)) {
                    businessIds.add(s);
                }
            }
        }
        businessIds.sort(Comparator.comparing(Integer::valueOf));

        subTask.setBusinessIds(String.join(",", businessIds));
        log.info("updateSubTask :{}", subTask);
        analysisSubTaskRepository.updateByTaskId(subTask);
    }
}
