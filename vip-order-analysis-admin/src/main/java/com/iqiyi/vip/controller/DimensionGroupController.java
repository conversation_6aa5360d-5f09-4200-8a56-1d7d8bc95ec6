package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisDimensionGroup;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.service.DimensionGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@RestController
@RequestMapping(value = "order-analysis/dimensionGroup")
@Api(tags = "维度分组接口")
public class DimensionGroupController {

    @Resource
    private DimensionGroupService dimensionGroupService;

    @WebLog(simplify = false)
    @ApiOperation(value = "维度分组列表")
    @GetMapping(value = "list")
    public ListResult<AnalysisDimensionGroup> groups(@Validated BaseQry baseQry) {
        return dimensionGroupService.groups(baseQry);
    }
}
