package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.config.UserInfo;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.utils.SpringSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2023/8/22 13:43
 */

@RestController
@RequestMapping(value = "order-analysis/admin")
@Api(tags = "天眼用户管理接口")
public class AdminController {
    @WebLog
    @GetMapping(value = "getUser")
    @ApiOperation(value = "getUser", httpMethod = "GET")
    public CommonResult getUserRole() {
        UserInfo user = SpringSecurityUtils.getCurrentUser();
        return new DataResult<>(user);
    }
}