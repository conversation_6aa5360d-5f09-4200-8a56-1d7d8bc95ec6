package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.memory.DataPermissionMemory;
import com.iqiyi.vip.domain.repository.ConditionGeneralMySQLRepository;
import com.iqiyi.vip.domain.repository.VipTypeRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.condition.BusinessListReqDTO;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.condition.ConditionEnumReq;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.dto.condition.ConditionSQLReq;
import com.iqiyi.vip.dto.condition.EndKeyListReqDTO;
import com.iqiyi.vip.dto.condition.FvLevelReqDTO;
import com.iqiyi.vip.dto.condition.VipTypeByGroupReqDTO;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.VipProductTypeEnum;
import com.iqiyi.vip.service.ConditionConfigService;
import com.iqiyi.vip.service.ConditionPermissionService;
import com.iqiyi.vip.service.QiyuePlatformService;

import static com.iqiyi.vip.constant.Constants.IQIYI_VIDEO_NAME;
import static com.iqiyi.vip.constant.Constants.TV_PLATFORM_END_KEY;


/**
 * <AUTHOR>
 * @date 6/13/22
 * @apiNote
 */
@RestController
@Slf4j
@Api(tags = "订单分析-条件查询")
@RequestMapping("order-analysis/condition")
@Validated
public class ConditionController {
    @Resource
    private ConditionPermissionService conditionPermissionService;

    @Resource
    private ConditionConfigService conditionConfigService;

    @Resource
    private ConditionGeneralMySQLRepository conditionGeneralMySQLRepository;

    @Resource
    private DataPermissionMemory dataPermissionMemory;
    @Resource
    private VipTypeRepository vipTypeRepository;
    @Resource
    private QiyuePlatformService qiyuePlatformService;

    @WebLog(simplify = false)
    @ApiOperation(value = "会员体系分组")
    @PostMapping(value = "vipGroup/list")
    public ListResult<CodeDescPair> vipGroupPairs(BaseQry baseQry) {
        LabelEnum firstVipTypeLabelEnum = LabelEnum.getFirstVipTypeLabel().getNextLabelEnum();
        List<DataPermissionDTO> vipTypes = conditionPermissionService.getVipType(baseQry, firstVipTypeLabelEnum, null);
        return ListResult.createSuccess(vipTypes != null ?
                vipTypes.stream()
                        .map(v -> CodeDescPair.builder()
                            .code(Integer.valueOf(v.getId()))
                            .desc(v.getName()).build())
                        .collect(Collectors.toList())
                : new ArrayList<>());
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "根据会员体系获取会员类型")
    @PostMapping(value = "vipType/getByGroup")
    public ListResult<CodeDescPair> getVipTypesByGroup(@RequestBody @Validated VipTypeByGroupReqDTO reqDTO) {
        LabelEnum labelEnum = LabelEnum.VT2;
        if (CollectionUtils.isNotEmpty(reqDTO.getVipOrderGroupIds())) {
            reqDTO.setGroupIds(reqDTO.getVipOrderGroupIds());
        }
        List<DataPermissionDTO> vipTypes = conditionPermissionService.getVipType(reqDTO, labelEnum, reqDTO.getGroupIds().stream()
                .map(v -> String.valueOf(v))
                .collect(Collectors.toList()));
        return ListResult.createSuccess(vipTypes != null ?
                vipTypes.stream()

                        .map(v -> CodeDescPair.builder()
                                .code(Integer.valueOf(v.getId()))
                                .desc(v.getName()).build())
                        .collect(Collectors.toList())
                : new ArrayList<>());
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "获取会员类型列表,黄金、白金、奇异果白金等")
    @PostMapping(value = "/vipType/list")
    public ListResult<CodeDescPair> getVipTypeList() {
        List<CodeDescPair> vipTypePairs = vipTypeRepository.selectAll();
        return ListResult.createSuccess(vipTypePairs);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "获取平台列表")
    @PostMapping(value = "/platform/list")
    public ListResult<CodeDescPair> getPlatformList() {
        List<CodeDescPair> data = qiyuePlatformService.selectAll();
        return ListResult.createSuccess(data);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "获取产品列表")
    @PostMapping(value = "business/list")
    public ListResult<ConditionPair> getBusinessList(@RequestBody @Validated BusinessListReqDTO reqDTO) {
        LabelEnum firstProLabelEnum = LabelEnum.getFirstProLabel().getNextLabelEnum();
        List<DataPermissionDTO> proPermissions = conditionPermissionService.getProPermissions(reqDTO, firstProLabelEnum, null);
        return ListResult.createSuccess(proPermissions.stream()
            .map(p -> new ConditionPair(p.getName(), p.getName()))
            .sorted(Comparator.comparing(v -> Constants.proOrderIndexOf(v.getDesc())))
            .collect(Collectors.toList()));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "根据产品列表获取端平台")
    @PostMapping(value = "endKey/getByBusiness")
    public ListResult<ConditionPair> getEndKeyByBusiness(@RequestBody @Validated EndKeyListReqDTO reqDTO) {
        LabelEnum labelEnum = LabelEnum.P2;
        List<DataPermissionDTO> proPermissions = conditionPermissionService.getProPermissions(reqDTO, labelEnum, reqDTO.getBusinessName());
        List<ConditionPair> ConditionPair = proPermissions.stream()
            .filter(p -> !(IQIYI_VIDEO_NAME.equals(p.getParentName()) && TV_PLATFORM_END_KEY.equals(p.getName())))
            .map(p -> {
                String bizEndKey = Constants.getBizEndKey(p.getParentName(), p.getName());
                return new ConditionPair(bizEndKey, bizEndKey);
            })
            .map(p -> {
                p.setDesc(StringUtils.replace(p.getDesc(), "有线网TV", "TVA-APK_有线网TV"));
                return p;
            })
            .map(p -> {
                p.setDesc(StringUtils.replace(p.getDesc(), "电信运营商TV", "TVA-APK_电信运营商TV"));
                return p;
            })
            .map(p -> {
                p.setDesc(StringUtils.replace(p.getDesc(), "厂商TV", "TVA-APK_厂商TV"));
                return p;
            })
            .collect(Collectors.toList());
        return ListResult.createSuccess(ConditionPair);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "获取渠道信息")
    @PostMapping(value = "fv/getCurrentLevelInfo")
    public ListResult getFv(@RequestBody @Validated FvLevelReqDTO reqDTO) {
        Integer currentLevel = reqDTO.getCurrentLevel();
        LabelEnum labelEnum = LabelEnum.levelOf(currentLevel);
        List<Integer> preLevelIds = reqDTO.getPreLevelIds();
        List<Integer> teamIds = reqDTO.getTeamIds();
        if (LabelEnum.L2.getLevel() == currentLevel) {
            preLevelIds.add(2084);
        }
        if (missingTeamIdOrPreLevelId(currentLevel, preLevelIds, teamIds)) {
            return new ListResult(CodeEnum.ERROR_PARAM.getCode(), CodeEnum.ERROR_PARAM.getMessage());
        }
        List<String> curDataPermissionIds = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(preLevelIds)) {
            curDataPermissionIds = preLevelIds.stream().map(String::valueOf).collect(Collectors.toList());
        }
        List<String> teamIdStr = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(teamIds)) {
            teamIdStr = teamIds.stream().map(String::valueOf).collect(Collectors.toList());
        }
        List<DataPermissionDTO> fvPermissions = conditionPermissionService.getFvPermissions(reqDTO, labelEnum, curDataPermissionIds, teamIdStr);
        List<ConditionPair> conditionPairs = fvPermissions.stream()
            .filter(fv -> StringUtils.isNotBlank(fv.getName()))
            .map(p -> ConditionPair.builder().code(Integer.parseInt(p.getId())).desc(getFvChannelDisplayName(p)).build())
            .collect(Collectors.toList());

        // 二级渠道下的站内渠道，和产品列表权限强关联，如果没有产品列表的权限，就将二级渠道下的站内渠道从返回结果里remove掉
        if (reqDTO.getCurrentLevel() == 2) {
            BusinessListReqDTO businessListReqDTO = new BusinessListReqDTO();
            businessListReqDTO.setOperator(reqDTO.getOperator());
            businessListReqDTO.setBusinessTypeId(reqDTO.getBusinessTypeId());
            businessListReqDTO.setThemeType(reqDTO.getThemeType());
            businessListReqDTO.setDataPermissionType(reqDTO.getDataPermissionType());
            ListResult<ConditionPair> businessList = this.getBusinessList(businessListReqDTO);
            if (businessList != null && CodeEnum.SUCCESS.getCode().equals(businessList.getCode())) {
                if (CollectionUtils.isEmpty(businessList.getDataList())) {
                    if (CollectionUtils.isNotEmpty(conditionPairs)) {
                        conditionPairs.removeIf(pair -> (Integer) pair.getCode() == Constants.INTERNAL_CHANNEL_ID_INT);
                    }
                } else {
                    // 新申请站内渠道-产品端平台的用户, 不会有站内渠道-业务团队, 这里将其补充上
                    boolean flag = conditionPairs.stream().anyMatch(item -> (Integer) item.getCode() == Constants.INTERNAL_CHANNEL_ID_INT);
                    if (!flag) {
                        conditionPairs.add(
                            ConditionPair.builder()
                                .code(251040)
                                .desc("站内渠道(251040)")
                                .build()
                        );
                    }
                }
            }
        }

        return ListResult.createSuccess(conditionPairs);
    }

    public String getFvChannelDisplayName(DataPermissionDTO dataPermission) {
        String idStr = String.format(Constants.DATA_PERMISSION_COMMON_BRACKETS, dataPermission.getId());
        if (dataPermission.getLevel() >= LabelEnum.L3.getLevel()) {
            return String.format(Constants.DATA_PERMISSION_COMMON_LINE, dataPermission.getTeamName(), dataPermission.getName()) + idStr;
        }
        return dataPermission.getName() + idStr;
    }


    private boolean missingTeamIdOrPreLevelId(Integer currentLevel, List<Integer> preLevelIds, List<Integer> teamIds) {
        return LabelEnum.L2.getLevel() < currentLevel && (CollectionUtils.isEmpty(preLevelIds) || CollectionUtils.isEmpty(teamIds));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "产品类型")
    @PostMapping(value = "vipProductType/list")
    public ListResult<CodeDescPair> vipProductTypePairs() {
        return ListResult.createSuccess(VipProductTypeEnum.getPairs());
    }

    @WebLog
    @ApiOperation(value = "条件的枚举类型")
    @PostMapping(value = "enum/list")
    public ListResult<ConditionPair> enumPairs(@RequestBody ConditionEnumReq conditionEnumReq) {
        return conditionConfigService.getThemeEnums(conditionEnumReq.getThemeType(), conditionEnumReq.getCode());
    }

    @WebLog
    @ApiOperation(value = "条件的枚举类型")
    @PostMapping(value = "sql/query")
    public ListResult<ConditionPair> queryMySql(@RequestBody ConditionSQLReq req) {
        log.info(req.getQuery());
        String decodedString = req.getQuery();
        List<ConditionPair> result = conditionGeneralMySQLRepository.query(decodedString);
        return ListResult.createSuccess(result);
    }
}
