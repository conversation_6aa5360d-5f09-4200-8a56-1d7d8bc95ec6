package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.service.TargetGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@RestController
@RequestMapping(value = "order-analysis/targetGroup")
@Api(tags = "指标分组接口")
public class TargetGroupController {

    @Resource
    private TargetGroupService targetGroupService;

    @WebLog(simplify = false)
    @ApiOperation(value = "指标分组列表")
    @GetMapping(value = "list")
    public ListResult<AnalysisTargetGroup> groups(@Validated BaseQry baseQry) {
        return targetGroupService.groups(baseQry);
    }
}
