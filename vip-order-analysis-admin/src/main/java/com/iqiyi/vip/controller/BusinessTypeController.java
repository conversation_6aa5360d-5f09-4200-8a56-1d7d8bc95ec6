package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisBusinessType;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.service.BusinessTypeService;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@RestController
@RequestMapping(value = "order-analysis/businessType")
@Api(tags = "天眼业务类型接口")
public class BusinessTypeController {

    @Resource
    private BusinessTypeService businessTypeService;

    @WebLog(simplify = false)
    @ApiOperation(value = "天眼业务类型列表")
    @GetMapping(value = "list")
    public ListResult<AnalysisBusinessType> list(@RequestParam(value = "themeType",defaultValue = "1") Integer themeType) {
        return businessTypeService.listByThemeType(themeType);
    }
}
