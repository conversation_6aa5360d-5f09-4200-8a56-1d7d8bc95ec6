package com.iqiyi.vip.controller;

import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.iqiyi.vip.api.OaApprovedApi;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.oa.OaApprovedDetail;
import com.iqiyi.vip.dto.oa.OaApprovedResponse;
import com.iqiyi.vip.dto.permission.BaseDataPermissionDTO;
import com.iqiyi.vip.dto.permission.ConfirmApplyDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApplyDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApprovalCallbackDTO;
import com.iqiyi.vip.dto.permission.DataPermissionComposeDTO;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.dto.permission.DataPermissionStatusDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.DataPermissionOaStatusEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.service.DataPermissionService;

import static com.iqiyi.vip.constant.Constants.IQIYI_VIDEO_NAME;
import static com.iqiyi.vip.constant.Constants.TV_PLATFORM_END_KEY;

/**
 * 权限申请
 *
 * <AUTHOR>
 * @date 2022/8/16 17:00
 */
@RestController
@Slf4j
@RequestMapping("order-analysis/permission")
@Api(tags = "数据权限申请页面")
public class DataPermissionController {

    @Resource
    private DataPermissionService dataPermissionService;
    @Resource
    private OaApprovedApi oaApprovedApi;

    @WebLog(simplify = false)
    @GetMapping("auth")
    @ApiOperation(value = "鉴权（是否申请过数据权限）", httpMethod = "GET")
    public CommonResult authentication(@Validated BaseDataPermissionDTO baseDataPermissionDTO) {
        return dataPermissionService.ownedDataPermission(baseDataPermissionDTO) ?
            CommonResult.success() : CommonResult.create(CodeEnum.DATA_PERMISSION_AUTH_LIMIT);
    }

    @WebLog(simplify = false)
    @GetMapping("apply/page")
    @ApiOperation(value = "申请权限页面", httpMethod = "GET")
    public DataResult<DataPermissionComposeDTO> applyPage(@Validated BaseDataPermissionDTO baseDataPermissionDTO) {
        // 1. 数据权限组合
        DataPermissionComposeDTO dataPermissionComposeDTO = dataPermissionService.getBaseDataPermission(baseDataPermissionDTO, LabelEnum.datePermissionCtrlLabel(), false);
        // 2. 已有权限回写
        dataPermissionService.writebackDataPermission(dataPermissionComposeDTO, baseDataPermissionDTO);

        if (dataPermissionComposeDTO.getFvData() != null) {
            List<DataPermissionDTO> permissionListOfLevel2 = dataPermissionComposeDTO.getFvData().getDataPermissionDTOList();
            for (DataPermissionDTO permissionOfLevel2 : permissionListOfLevel2) {
                if (!Constants.INTERNAL_CHANNEL_NAME.equals(permissionOfLevel2.getName())) {
                    permissionOfLevel2.setPermissionName("业务团队+三级渠道(此节点权限较大,慎选)");

                    List<DataPermissionDTO> businessGroupList = permissionOfLevel2.getDataPermissionDTOList();
                    for (DataPermissionDTO businessGroup : businessGroupList) {
                        businessGroup.setName(businessGroup.getName() + "(业务团队)");

                        List<DataPermissionDTO> permissionListOfLevel3 = businessGroup.getDataPermissionDTOList();
                        for (DataPermissionDTO permissionOfLevel3 : permissionListOfLevel3) {
                            permissionOfLevel3.setName(permissionOfLevel3.getName() + "(三级渠道)");
                        }
                    }
                }
            }
        }

        if (dataPermissionComposeDTO.getProData() != null) {
            // 3. 端平台特殊处理
            dataPermissionComposeDTO.getProData().getDataPermissionDTOList().parallelStream().map(d ->
            {
                if (!IQIYI_VIDEO_NAME.equals(d.getName())) {
                    return d;
                }
                d.getDataPermissionDTOList().stream()
                    .map(p -> {
                        p.setName(StringUtils.replace(p.getName(), "有线网TV", "TVA-APK_有线网TV"));
                        return p;
                    })
                    .map(p -> {
                        p.setName(StringUtils.replace(p.getName(), "电信运营商TV", "TVA-APK_电信运营商TV"));
                        return p;
                    })
                    .map(p -> {
                        p.setName(StringUtils.replace(p.getName(), "厂商TV", "TVA-APK_厂商TV"));
                        return p;
                    }).collect(Collectors.toList());
                return d;

            }).collect(Collectors.toList());

            for (DataPermissionDTO dataPermissionDTO : dataPermissionComposeDTO.getProData().getDataPermissionDTOList()) {
                if (!IQIYI_VIDEO_NAME.equals(dataPermissionDTO.getName())) {
                    continue;
                }
                List<DataPermissionDTO> dataPermissionDTOList = dataPermissionDTO.getDataPermissionDTOList();
                dataPermissionDTOList.removeIf(s -> IQIYI_VIDEO_NAME.equals(s.getParentName()) && TV_PLATFORM_END_KEY.equals(s.getName()));
                dataPermissionDTO.setDataPermissionDTOList(dataPermissionDTOList);
            }

            DataPermissionDTO proData = dataPermissionComposeDTO.getProData();
            proData.setPermissionName("产品+端平台(此节点权限较大,慎选)");
            for (DataPermissionDTO dataPermissionDTO : dataPermissionComposeDTO.getProData().getDataPermissionDTOList()) {
                if ("产品".equals(dataPermissionDTO.getParentName())) {
                    dataPermissionDTO.setName(dataPermissionDTO.getName() + "(产品)");
                }
                for (DataPermissionDTO permissionDTO : dataPermissionDTO.getDataPermissionDTOList()) {
                    permissionDTO.setName(permissionDTO.getName() + "(端平台)");
                }
            }
        }

        if (dataPermissionComposeDTO.getVipTypeData() != null) {
            dataPermissionComposeDTO.getVipTypeData().setPermissionName("会员类型+套餐类型(此节点权限较大,慎选)");
            for (DataPermissionDTO permissionDTO : dataPermissionComposeDTO.getVipTypeData().getDataPermissionDTOList()) {
                permissionDTO.setName(permissionDTO.getName() + "(会员类型)");
                for (DataPermissionDTO subPermissionDTO : permissionDTO.getDataPermissionDTOList()) {
                    subPermissionDTO.setName(subPermissionDTO.getName() + "(套餐类型)");
                }
            }
        }
        // 4. 返回结果
        return DataResult.success(dataPermissionComposeDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("apply")
    @ApiOperation(value = "申请权限", httpMethod = "POST")
    public CommonResult apply(@Validated @RequestBody DataPermissionApplyDTO dataPermissionApplyDTO) {
        log.info("apply permission request: {}", dataPermissionApplyDTO);
        // 权限空兼容校验
        if (dataPermissionApplyDTO.getDataPermissionComposeDTO().getFvData() == null
            && dataPermissionApplyDTO.getDataPermissionComposeDTO().getVipTypeData() == null
            && dataPermissionApplyDTO.getDataPermissionComposeDTO().getCompetitorMonitorData() == null) {
            throw new BizRuntimeException(CodeEnum.DATA_PERMISSION_AUTH_LIMIT);
        }

        // 分析主题、申请类型校验
        ThemeTypeEnum themeType = ThemeTypeEnum.findEnumByThemeType(dataPermissionApplyDTO.getThemeType());
        DataPermissionTypeEnum dataPermissionType = DataPermissionTypeEnum.findEnumByDataPermissionType(dataPermissionApplyDTO.getDataPermissionType());
        if (themeType == null || dataPermissionType == null) {
            throw new BizRuntimeException(CodeEnum.DATA_PERMISSION_AUTH_LIMIT);
        }

        // 本次天眼权限模块优化，放开了站内渠道及其子项的权限控制
        if (dataPermissionApplyDTO.getDataPermissionComposeDTO().getProData() == null) {
            DataPermissionDTO fvData = dataPermissionApplyDTO.getDataPermissionComposeDTO().getFvData();
            if (fvData != null && fvData.getDataPermissionDTOList() != null) {
                fvData.setDataPermissionDTOList(fvData.getDataPermissionDTOList().stream()
                    .filter(item -> !Constants.INTERNAL_CHANNEL_ID.equals(item.getId())).collect(Collectors.toList()));
            }
        }

        String operator = dataPermissionApplyDTO.getOperator();
        // 1. 权限校验，是否合法
        final boolean rightful = dataPermissionService.checkUserParamsDataPermissionRightful(dataPermissionApplyDTO.getDataPermissionComposeDTO());
        if (rightful) {
            // 2. 数据权限组合
            DataPermissionComposeDTO dataPermissionComposeDTO = dataPermissionService.getBaseDataPermission(dataPermissionApplyDTO, LabelEnum.datePermissionCtrlLabel(), false);

            // 3. 得到用户本次申请的数据权限描述
            List<String> permissionCns = dataPermissionService.getDataPermissionApply(dataPermissionApplyDTO.getDataPermissionComposeDTO(), dataPermissionComposeDTO);

            // 4. 得到用户已有的数据权限描述
            dataPermissionService.writebackDataPermission(dataPermissionComposeDTO, dataPermissionApplyDTO);
            List<String> datePermissionAlreadyHas = dataPermissionService.getDatePermissionAlreadyHas(dataPermissionComposeDTO);
            if (CollectionUtils.isEmpty(datePermissionAlreadyHas)) {
                datePermissionAlreadyHas.add("无");
            }

            // 4. 提交审批
            OaApprovedDetail detail = null;
            String messageId = "voa-oa-approved-" + UUID.randomUUID();

            try {
                String dataPermissionTypeMsg = dataPermissionType.getMsg();
                if (DataPermissionTypeEnum.COMPETITOR_DATA_PERMISSION.equals(dataPermissionType)) {
                    dataPermissionTypeMsg = "-";
                }

                detail = OaApprovedDetail.builder()
                    .reason(dataPermissionApplyDTO.getApplyReason())
                    .themeType(themeType.getMsg())
                    .permissionType(dataPermissionTypeMsg)
                    .dataPermission(String.join("<br //>", permissionCns))
                    .dataPermissionAlreadyHas(String.join("<br //>", datePermissionAlreadyHas))
                    .desc(Constants.OA_APPROVED_DESC)
                    .messageId(messageId)
                    .build();
                final DataResult<OaApprovedResponse> oaApprovedResponseDataResult = oaApprovedApi.submitApproved(operator, detail, dataPermissionApplyDTO.getThemeType());
                // 5. 保存Oa审批记录
                dataPermissionService.saveDataPermissionApply(dataPermissionApplyDTO, oaApprovedResponseDataResult.getData());
            } catch (BizRuntimeException e) {
                log.error("submitApproved error, operator:{}, detail:{}", operator, detail);
                dataPermissionService.saveDataPermissionApply(dataPermissionApplyDTO, new OaApprovedResponse(null, messageId));
            }
            // 6. 返回结果
            return CommonResult.success();
        }
        // 6. 返回结果
        return CommonResult.create(CodeEnum.NOT_RIGHTFUL);
    }

    @WebLog(simplify = false)
    @GetMapping("approval-callback")
    @ApiOperation(value = "审批权限", httpMethod = "GET")
    public CommonResult approvalCallback(@Validated DataPermissionApprovalCallbackDTO permissionApprovalCallbackDTO) {
        // 1. 保存审批结果
        // 2. 通过则发消息，具体添加权限由异步任务处理
        // 3. 添加devops角色
        dataPermissionService.updateDataPermissionOaApprovalStatus(permissionApprovalCallbackDTO, DataPermissionOaStatusEnum.transfor(permissionApprovalCallbackDTO.getOaStatus()));
        // 4. 返回结果
        return CommonResult.success();
    }

    @WebLog(simplify = false)
    @GetMapping("approval/status")
    @ApiOperation(value = "是否有正在审批权限？", httpMethod = "GET")
    public DataResult<DataPermissionStatusDTO> approvalStatus(@Validated BaseDataPermissionDTO baseDataPermissionDTO) {
        final boolean hasApprovingDataPermission = dataPermissionService.existApprovingDataPermission(baseDataPermissionDTO.getOperator());
        List<DataPermission> dataPermissions = dataPermissionService.getDataPermissions(baseDataPermissionDTO.getOperator());
        return hasApprovingDataPermission ? DataResult.create(CodeEnum.DATA_PERMISSION_AUTH_LIMIT, this.retDataPermissionDTO(dataPermissions))
            : DataResult.success(this.retDataPermissionDTO(dataPermissions));
    }

    // 返回已有数据权限种类、主题种类
    private DataPermissionStatusDTO retDataPermissionDTO(List<DataPermission> dataPermissions) {
        if (CollectionUtils.isEmpty(dataPermissions)) { // 未申请权限
            return DataPermissionStatusDTO.builder().themeTypes(Maps.newHashMap()).build();
        }
        return DataPermissionStatusDTO.builder()
            .themeTypes(dataPermissions.stream()
                .collect(Collectors.groupingBy(DataPermission::getThemeType, Collectors.mapping(DataPermission::getType, Collectors.toList()))))
            .build();
    }

    @WebLog(simplify = false)
    @PostMapping("confirm/apply")
    @ApiOperation(value = "确认申请权限", httpMethod = "POST")
    public DataResult<ConfirmApplyDTO> confirmApply(@Validated @RequestBody DataPermissionApplyDTO dataPermissionApplyDTO) {
        log.info("confirm apply request: {}", dataPermissionApplyDTO);

        // 用户申请的权限项实体, 勾选了的权限项会被前端传过来, 未勾选的权限项不会被前端传过来
        DataPermissionComposeDTO permissionApply = dataPermissionApplyDTO.getDataPermissionComposeDTO();

        // 得到所有数据权限的组合
        DataPermissionComposeDTO permissionAll = dataPermissionService.getBaseDataPermission(dataPermissionApplyDTO, LabelEnum.datePermissionCtrlLabel(), false);

        // 回写用户当前已有的权限
        dataPermissionService.writebackDataPermission(permissionAll, dataPermissionApplyDTO);

        // 本次申请权限的中文逻辑描述, 通过将 permissionApply 和 permissionAll diff来实现
        List<String> permissionApplyCns = dataPermissionService.getDataPermissionApply(permissionApply, permissionAll);

        // 获得用户已有的权限描述
        List<String> permissionAlreadyHas = dataPermissionService.getDatePermissionAlreadyHas(permissionApply);
        if (CollectionUtils.isEmpty(permissionAlreadyHas)) {
            permissionAlreadyHas.add("无");
        }

        ConfirmApplyDTO confirmApplyDTO = ConfirmApplyDTO.builder()
            .permissionApply(permissionApplyCns)
            .permissionAlreadyHas(permissionAlreadyHas)
            .build();

        return DataResult.success(confirmApplyDTO);
    }

    // 后门接口, 用于给用户补权限
    @GetMapping("flushInternalPermissions")
    public String flushInternalPermissions(String oaIdentifier, String account) {
        return dataPermissionService.dealOaApprovedDataPermission(oaIdentifier, account) ? "success" : "fail";
    }
}