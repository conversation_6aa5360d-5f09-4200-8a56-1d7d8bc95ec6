package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.AnalysisConditionPageQryDTO;
import com.iqiyi.vip.dto.condition.ConditionCascadePageQryDTO;
import com.iqiyi.vip.dto.condition.ConditionCascadeReq;
import com.iqiyi.vip.service.ConditionCascadeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Api(tags = "订单分析平台-条件级联管理")
@RestController
@RequestMapping("order-analysis/condition/cascade")
public class ConditionCascadeController {

    @Resource
    private ConditionCascadeService conditionCascadeService;

    @WebLog(simplify = false)
    @ApiOperation(value = "条件级联管理-新增")
    @PostMapping("save")
    public CommonResult save(@RequestBody ConditionCascadeReq req) {
        return conditionCascadeService.save(req);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件级联管理-查询数据列表")
    @GetMapping("list")
    public ListResult<ConditionCascade> list(ConditionCascadeReq req) {
        return ListResult.createSuccess(conditionCascadeService.list(req));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件级联管理-分页查询")
    @GetMapping("pageList")
    public PageListResult<ConditionCascade> pageList(ConditionCascadePageQryDTO query) {
        return conditionCascadeService.pageList(query);
    }
}
