package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopListParam;
import com.iqiyi.vip.dto.competitor.CompetitorShopSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSearchParam;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.service.CompetitorShopService;


@Slf4j
@RestController
@RequestMapping("order-analysis/competitor/shopMonitor")
@Api(tags = "订单分析平台-竞品店铺监控接口")
public class CompetitorShopMonitorController {

    public static final int COMPETITOR_THEME_SUB_TYPE = 3;
    @Resource
    private CompetitorShopService competitorShopService;
    @Resource
    private AvailableDateRepository availableDateRepository;

    @ApiOperation(value = "平台列表", httpMethod = "GET")
    @GetMapping(value = "/platformList")
    public ListResult<String> platformList() {
        return ListResult.createSuccess(competitorShopService.platformList());
    }

    @ApiOperation(value = "品牌列表", httpMethod = "GET")
    @GetMapping(value = "/brandsList")
    public ListResult<String> brandsList(@Valid CompetitorShopListParam param) {
        return ListResult.createSuccess(competitorShopService.brandsList(param.getPlatform(), param.getDate()));
    }

    @ApiOperation(value = "日期范围", httpMethod = "GET")
    @GetMapping(value = "/dateRange")
    public DataResult<CompetitorDateRangeDTO> dateRange() {
        return DataResult.success(competitorShopService.dateRange());
    }

    @ApiOperation(value = "保存竞品店铺监控信息", httpMethod = "POST")
    @PostMapping(value = "/save")
    public CommonResult save(@Valid @RequestBody CompetitorShopSaveDTO param) {
        competitorShopService.save(param);
        String dt = param.getDate();
        AvailableDt dateByDt = availableDateRepository.getDateByDtAndThemeTypeAndThemeSubType(dt, ThemeTypeEnum.COMPETITOR_MONITOR.getCode(), COMPETITOR_THEME_SUB_TYPE);
        if (dateByDt == null) {
            AvailableDt availableDt = AvailableDt.builder()
                .tableDt(dt)
                .status(1)
                .themeType(ThemeTypeEnum.COMPETITOR_MONITOR.getCode())
                .themeSubType(COMPETITOR_THEME_SUB_TYPE)
                .build();
            availableDateRepository.insert(availableDt);
        }
        return CommonResult.success();
    }

    @ApiOperation(value = "搜索店铺监控数据", httpMethod = "GET")
    @GetMapping(value = "/search")
    public ListResult<CompetitorShopDTO> search(@Valid CompetitorShopSearchParam param) {
        return ListResult.createSuccess(competitorShopService.search(param));
    }
} 