package com.iqiyi.vip.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.ChangeListRespDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffRespDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionListRespDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.service.ConfigVersionMangerService;

/**
 * <AUTHOR>
 * @className VersionController
 * @description
 * @date 2022/11/24
 **/
@RestController
@RequestMapping("order-analysis/version")
@Api(tags = "订单分析平台-配置版本管理")
@Slf4j
public class ConfigVersionController {

    @Resource
    private ConfigVersionMangerService configVersionMangerService;

    @WebLog(simplify = false)
    @ApiOperation(value = "版本回滚")
    @PostMapping("switch")
    public CommonResult versionSwitch(@Validated VersionSwitchReqDTO reqDTO) {
        log.info("version rollback request:{}", JSON.toJSONString(reqDTO));
        return configVersionMangerService.versionSwitch(reqDTO);
    }


    @WebLog(simplify = false)
    @ApiOperation(value = "版本对比")
    @PostMapping("diff")
    public ListResult<VersionDiffRespDTO> versionDiff(@Validated VersionDiffReqDTO reqDTO) {
        log.info("version diff request:{}", JSON.toJSONString(reqDTO));
        return configVersionMangerService.versionDiff(reqDTO);
    }


    @WebLog(simplify = false)
    @ApiOperation(value = "版本号列表")
    @PostMapping("versionList")
    public ListResult<VersionListRespDTO> versionList(@Validated VersionListReqDTO reqDTO) {
        log.info("versionList request:{}", JSON.toJSONString(reqDTO));
        return configVersionMangerService.versionList(reqDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "修改记录列表")
    @PostMapping("changeList")
    public ListResult<ChangeListRespDTO> changeList(@Validated ChangeListReqDTO reqDTO) {
        log.info("version rollback request:{}", JSON.toJSONString(reqDTO));
        return configVersionMangerService.changeList(reqDTO);
    }

}
