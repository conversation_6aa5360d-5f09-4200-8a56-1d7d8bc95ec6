package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO;
import com.iqiyi.vip.dto.remain.AlbumMetaDataVO;
import com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO;
import com.iqiyi.vip.service.AlbumMetaDataService;

/**
 * <AUTHOR>
 * @className AlbumMetaDataController
 * @description
 * @date 2024/9/27
 **/
@RestController
@RequestMapping(value = "order-analysis/albumMetaData")
@Api(tags = "内容查询接口")
@Slf4j
public class AlbumMetaDataController {

    @Resource
    private AlbumMetaDataService albumMetaDataService;

    @WebLog(simplify = false)
    @ApiOperation(value = "分页查询", httpMethod = "GET")
    @GetMapping("pageList")
    public PageListResult<AlbumMetaDataVO> pageList(@Validated AlbumMetaDataPageQueryDTO query) {
        return albumMetaDataService.pageList(query);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "根据留存时间查询", httpMethod = "GET")
    @GetMapping("byRemainTime")
    public ListResult<AlbumMetaDataVO> byRemainTime(
        @Valid @NotBlank(message = "startDate不能为空") @RequestParam String startDate,
        @Valid @NotBlank(message = "endDate不能为空") @RequestParam String endDate) {
        return albumMetaDataService.getByRemainTime(startDate, endDate);
    }

    @WebLog(simplify = false)
    @PostMapping("update")
    @ApiOperation(value = "修改内容信息", httpMethod = "POST")
    public DataResult updateUserGroup(@Validated @RequestBody UpdateAlbumMetaDataDTO updateAlbumMetaDataDTO) {
        return DataResult.success(albumMetaDataService.updateMetaData(updateAlbumMetaDataDTO));
    }
}
