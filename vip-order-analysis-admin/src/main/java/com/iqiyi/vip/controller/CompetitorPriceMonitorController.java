package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSaveParam;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorPriceWithAlbumResultDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUserStatusListParam;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.service.CompetitorPriceService;
import com.iqiyi.vip.service.PriceBoardResultDTO;

/**
 * @author: guojing
 * @date: 2024/10/25 16:43
 */
@Slf4j
@RestController
@RequestMapping("order-analysis/competitor/priceMonitor")
@Api(tags = "订单分析平台-竞品价格监控接口")
public class CompetitorPriceMonitorController {

    @Resource
    private CompetitorPriceService competitorPriceService;
    @Resource
    private AvailableDateRepository availableDateRepository;

    @ApiOperation(value = "查询品牌列表", httpMethod = "GET")
    @GetMapping(value = "/brandsList")
    public ListResult<String> brandsList() {
        return ListResult.createSuccess(competitorPriceService.brandsList());
    }

    @ApiOperation(value = "用户状态(生命周期)列表", httpMethod = "GET")
    @GetMapping(value = "/userStatusList")
    public ListResult<String> userStatusList(@Valid CompetitorUserStatusListParam param) {
        return ListResult.createSuccess(competitorPriceService.userStatusList(param));
    }

    @ApiOperation(value = "日期范围", httpMethod = "GET")
    @GetMapping(value = "/dateRange")
    public DataResult<CompetitorDateRangeDTO> dateRange() {
        return DataResult.success(competitorPriceService.dateRange());
    }

    @ApiOperation(value = "搜索价格监控数据", httpMethod = "GET")
    @GetMapping(value = "/search")
    public ListResult<CompetitorPriceDTO> search(@Valid CompetitorPriceSearchParam param) {
        return ListResult.createSuccess(competitorPriceService.search(param));
    }

    @ApiOperation(value = "搜索价格监控数据与内容信息", httpMethod = "GET")
    @GetMapping(value = "/searchWithAlbum")
    public DataResult<CompetitorPriceWithAlbumResultDTO> searchWithAlbum(CompetitorPriceSearchParam param) {
        return DataResult.success(competitorPriceService.searchWithAlbum(param));
    }


    @ApiOperation(value = "priceBoard", httpMethod = "GET")
    @GetMapping(value = "/priceBoard")
    public ListResult<PriceBoardResultDTO> priceBoard(CompetitorPriceSearchParam param) {
        return ListResult.createSuccess(competitorPriceService.priceBoard(param));
    }

    @ApiOperation(value = "保存竞品价格监控信息", httpMethod = "POST")
    @PostMapping(value = "/save")
    public CommonResult save(@Valid @RequestBody CompetitorPriceSaveParam param) {
        competitorPriceService.save(param);
        String dt = param.getDate().substring(0, 10);
        AvailableDt dateByDt = availableDateRepository.getDateByDt(dt, ThemeTypeEnum.COMPETITOR_MONITOR.getCode());
        if (dateByDt == null) {
            AvailableDt availableDt = AvailableDt.builder()
                .tableDt(dt)
                .status(1)
                .themeType(ThemeTypeEnum.COMPETITOR_MONITOR.getCode())
                .build();
            availableDateRepository.insert(availableDt);
        }
        return CommonResult.success();
    }

}
