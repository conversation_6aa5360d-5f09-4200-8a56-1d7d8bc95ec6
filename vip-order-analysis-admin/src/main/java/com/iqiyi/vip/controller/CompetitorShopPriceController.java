package com.iqiyi.vip.controller;

import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.repository.YunheAlbumMetaDataRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.competitor.AlbumContentInfoDTO;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceListParam;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceResultDTO;
import com.iqiyi.vip.dto.competitor.FlagshipShopPriceBoardResult;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceDistributionDTO;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceQueryParam;
import com.iqiyi.vip.dto.competitor.NonFlagshipShopPriceBoardResult;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceDistributionDTO;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceQueryParam;
import com.iqiyi.vip.dto.competitor.NonFlagshipShopPriceTrendDTO;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceTrendQueryParam;
import com.iqiyi.vip.dto.competitor.FlagshipShopPriceTrendDTO;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceTrendQueryParam;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.service.AnalysisResultStorageService;
import com.iqiyi.vip.service.CompetitorShopPriceService;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: guojing
 * @date: 2024/12/25 12:15
 */
@Slf4j
@RestController
@RequestMapping("order-analysis/competitor/shopPrice")
@Api(tags = "订单分析平台-竞品店铺价格信息接口")
public class CompetitorShopPriceController {

    @Resource
    private CompetitorShopPriceService competitorShopPriceService;

    @Resource
    private YunheAlbumMetaDataRepository yunheAlbumMetaDataRepository;

    @Resource
    private AnalysisResultStorageService analysisResultStorageService;

    @ConfigJsonValue("${competitor.shop.userGroup:{}}")
    private Map<String, List<String>> userGroupMap;

    @ApiOperation(value = "平台列表", httpMethod = "GET")
    @GetMapping(value = "/platformList")
    public ListResult<String> platformList() {
        return ListResult.createSuccess(competitorShopPriceService.platformList());
    }

    @ApiOperation(value = "品牌列表", httpMethod = "GET")
    @GetMapping(value = "/brandsList")
    public ListResult<String> brandsList(@Valid CompetitorShopPriceListParam param) {
        return ListResult.createSuccess(competitorShopPriceService.brandsList(param.getPlatform(), param.getDate()));
    }

    @ApiOperation(value = "人群列表", httpMethod = "GET")
    @GetMapping(value = "/userGroupList")
    public ListResult<String> userGroupList() {
        return ListResult.createSuccess(Lists.newArrayList(userGroupMap.keySet()));
    }

    @ApiOperation(value = "日期范围", httpMethod = "GET")
    @GetMapping(value = "/dateRange")
    public DataResult<CompetitorDateRangeDTO> dateRange() {
        return DataResult.success(competitorShopPriceService.dateRange());
    }

    @ApiOperation(value = "保存竞品店价格信息", httpMethod = "POST")
    @PostMapping(value = "/save")
    public CommonResult save(@Valid @RequestBody CompetitorShopPriceSaveDTO param) {
        competitorShopPriceService.save(param);
        return CommonResult.success();
    }

    @ApiOperation(value = "搜索店铺促销信息", httpMethod = "GET")
    @GetMapping(value = "/search")
    public DataResult<CompetitorShopPriceResultDTO> search(@Valid CompetitorShopPriceSearchParam param) {
        return DataResult.success(competitorShopPriceService.search(param));
    }

    @ApiOperation(value = "查询旗舰店价格分布", httpMethod = "GET")
    @GetMapping(value = "/flagshipPriceDistribution")
    public DataResult<FlagshipStorePriceDistributionDTO> flagshipPriceDistribution(@Valid FlagshipStorePriceQueryParam param) {
        return DataResult.success(competitorShopPriceService.queryFlagshipStorePriceDistribution(param));
    }

    @ApiOperation(value = "查询非旗舰店价格分布", httpMethod = "GET")
    @GetMapping(value = "/nonFlagshipPriceDistribution")
    public DataResult<NonFlagshipStorePriceDistributionDTO> nonFlagshipPriceDistribution(@Valid NonFlagshipStorePriceQueryParam param) {
        return DataResult.success(competitorShopPriceService.queryNonFlagshipStorePriceDistribution(param));
    }

    @ApiOperation(value = "查询非旗舰店价格趋势", httpMethod = "GET")
    @GetMapping(value = "/nonFlagshipPriceTrend")
    public DataResult<NonFlagshipShopPriceBoardResult> nonFlagshipPriceTrend(@Valid NonFlagshipStorePriceTrendQueryParam param) {

        CompletableFuture<List<NonFlagshipShopPriceTrendDTO>> nonFlagShipCompletableFuture = CompletableFuture.supplyAsync(() -> competitorShopPriceService.queryNonFlagshipStorePriceTrend(param));

        CompletableFuture<List<AlbumContentInfoDTO>> albumContentsFuture = CompletableFuture.supplyAsync(() -> {
            // 查询专辑内容
            CompetitorPriceSearchParam searchParam = new CompetitorPriceSearchParam();
            searchParam.setBrand(param.getBrand());
            searchParam.setStartTime(param.getStartTime());
            searchParam.setEndTime(param.getEndTime());
            return yunheAlbumMetaDataRepository.search(searchParam);
        });

        try {
            // 等待两个查询都完成
            List<NonFlagshipShopPriceTrendDTO> flagshipShopPriceTrendDTOS = nonFlagShipCompletableFuture.get(10, TimeUnit.SECONDS);
            List<AlbumContentInfoDTO> albumContents = albumContentsFuture.get(10, TimeUnit.SECONDS);

            // 构建返回结果
            NonFlagshipShopPriceBoardResult result = NonFlagshipShopPriceBoardResult.builder()
                .nonFlagshipShopPriceTrend(flagshipShopPriceTrendDTOS)
                .albumContents(albumContents)
                .build();
            return DataResult.success(result);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("查询被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("查询执行失败", e);
        } catch (TimeoutException e) {
            throw new RuntimeException("查询超时", e);
        }
    }
    @ApiOperation(value = "查询旗舰店价格趋势", httpMethod = "GET")
    @GetMapping(value = "/flagshipPriceTrend")
    public DataResult<FlagshipShopPriceBoardResult> flagshipPriceTrend(@Valid FlagshipStorePriceTrendQueryParam param) {
        CompletableFuture<List<AlbumContentInfoDTO>> albumContentsFuture = CompletableFuture.supplyAsync(() -> {
            // 查询专辑内容
            CompetitorPriceSearchParam searchParam = new CompetitorPriceSearchParam();
            searchParam.setBrand(param.getBrand());
            searchParam.setStartTime(param.getStartTime());
            searchParam.setEndTime(param.getEndTime());
            return yunheAlbumMetaDataRepository.search(searchParam);
        });

        CompletableFuture<List<FlagshipShopPriceTrendDTO>> flagShipShopFuture = CompletableFuture.supplyAsync(() -> {
            // 查询竞品价格
            return competitorShopPriceService.queryFlagshipStorePriceTrend(param);
        });

        try {
            // 等待两个查询都完成
            List<FlagshipShopPriceTrendDTO> flagshipShopPriceTrendDTOS = flagShipShopFuture.get(10, TimeUnit.SECONDS);
            List<AlbumContentInfoDTO> albumContents = albumContentsFuture.get(10, TimeUnit.SECONDS);

            // 构建返回结果
            FlagshipShopPriceBoardResult boardResult = FlagshipShopPriceBoardResult.builder()
                .flagshipShopPriceTrend(flagshipShopPriceTrendDTOS)
                .albumContents(albumContents)
                .build();
            return DataResult.success(boardResult);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("查询被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("查询执行失败", e);
        } catch (TimeoutException e) {
            throw new RuntimeException("查询超时", e);
        }
    }
    
    @ApiOperation(value = "下载非旗舰店价格趋势", httpMethod = "GET")
    @GetMapping(value = "/downloadNonFlagshipPriceTrend")
    public void downloadNonFlagshipPriceTrend(@Valid NonFlagshipStorePriceTrendQueryParam param) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        String excelName = competitorShopPriceService.generateNonFlagshipPriceTrendExcel(param, response);
        analysisResultStorageService.downloadResult(excelName, response);
    }
    
    @ApiOperation(value = "下载旗舰店价格趋势", httpMethod = "GET")
    @GetMapping(value = "/downloadFlagshipPriceTrend")
    public void downloadFlagshipPriceTrend(@Valid FlagshipStorePriceTrendQueryParam param) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        String excelName = competitorShopPriceService.generateFlagshipPriceTrendExcel(param, response);
        analysisResultStorageService.downloadResult(excelName, response);
    }
}



