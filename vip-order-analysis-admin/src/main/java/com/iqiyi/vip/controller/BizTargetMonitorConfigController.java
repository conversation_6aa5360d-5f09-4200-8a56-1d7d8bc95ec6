package com.iqiyi.vip.controller;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.BizTargetMonitorConfigDO;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigPageQryDTO;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigSaveDTO;
import com.iqiyi.vip.dto.realmonitor.TimeIntervalPair;
import com.iqiyi.vip.enums.OffsetTimeUnit;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.service.BizTargetMonitorConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Validated
@RestController
@RequestMapping("/order-analysis/bizTargetMonitorConfig")
@Api(tags = "订单分析平台-业务指标监控配置管理")
@Slf4j
public class BizTargetMonitorConfigController {

    @ConfigJsonValue("${biz.target.monitor.whiteList:[\"zhouguojing\",\"guoyang04\",\"chenguilong\",\"felixsun\",\"lixin10\",\"libolin01\"]}")
    private List<String> whiteList;

    @Resource
    private BizTargetMonitorConfigService bizTargetMonitorConfigService;

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置管理-返回执行频率单位列表")
    @GetMapping("/execFrequencyUnit")
    public ListResult<String> execFrequencyUnit() {
        List<String> timeUnits = Arrays.stream(OffsetTimeUnit.values())
            .map(OffsetTimeUnit::name)
            .collect(Collectors.toList());
        return ListResult.createSuccess(timeUnits);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置管理-创建")
    @PostMapping("/create")
    public DataResult<Integer> create(@Validated @RequestBody BizTargetMonitorConfigSaveDTO param) {
        TimeIntervalPair timeIntervalPair = TimeIntervalPair.of(param.getExecFrequency());
        if (timeIntervalPair == null) {
            throw BizRuntimeException.newParamException("执行频率取值不合法");
        }
        int seconds = OffsetTimeUnit.toSeconds(param.getExecFrequency());
        if (seconds < 60) {
            throw BizRuntimeException.newParamException("执行频率取值不合法，不能低于1分钟");
        }
        ThemeTypeEnum themeTypeEnum = ThemeTypeEnum.findEnumByThemeType(param.getThemeType());
        if (themeTypeEnum == null) {
            throw BizRuntimeException.newParamException("不支持的主题类型");
        }
        boolean existByName = bizTargetMonitorConfigService.existByName(param.getName());
        if (existByName) {
            throw BizRuntimeException.newParamException("已存在此名称监控");
        }
        param.setUpdateOpr(param.getCreateOpr());
        Integer monitorConfigId = bizTargetMonitorConfigService.create(param, timeIntervalPair);

        return DataResult.success(monitorConfigId);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置管理-更新")
    @PostMapping("/update")
    public DataResult<Boolean> update(@Validated @RequestBody BizTargetMonitorConfigSaveDTO param) {
        if (param.getId() == null) {
            throw BizRuntimeException.newParamException("参数id不能为空");
        }
        if (StringUtils.isBlank(param.getUpdateOpr())) {
            throw BizRuntimeException.newParamException("更新人不能为空");
        }
        ThemeTypeEnum themeTypeEnum = ThemeTypeEnum.findEnumByThemeType(param.getThemeType());
        if (themeTypeEnum == null) {
            throw BizRuntimeException.newParamException("不支持的主题类型");
        }
        TimeIntervalPair paramTimeIntervalPair = TimeIntervalPair.of(param.getExecFrequency());
        if (paramTimeIntervalPair == null) {
            throw BizRuntimeException.newParamException("执行频率取值不合法");
        }
        int seconds = OffsetTimeUnit.toSeconds(param.getExecFrequency());
        if (seconds < 30) {
            throw BizRuntimeException.newParamException("执行频率取值不合法，不能低于30秒");
        }
        BizTargetMonitorConfigDO monitorDOInDB = bizTargetMonitorConfigService.getDetailById(param.getId());
        if (monitorDOInDB == null) {
            throw BizRuntimeException.newParamException(String.format("监控id:%s不存在", param.getId()));
        }
        if (!Objects.equals(param.getName(), monitorDOInDB.getName())) {
            throw BizRuntimeException.newParamException("监控名称不能修改");
        }
        if (!Objects.equals(param.getTarget(), monitorDOInDB.getTarget())) {
            throw BizRuntimeException.newParamException("指标不能修改");
        }
        if (!Objects.equals(param.getDimensions(), monitorDOInDB.getDimensions())) {
            throw BizRuntimeException.newParamException("维度不能修改");
        }
        if (!Objects.equals(param.getCreateOpr(), monitorDOInDB.getCreateOpr())) {
            throw BizRuntimeException.newParamException("创建人不能修改");
        }
        boolean updated = bizTargetMonitorConfigService.update(param, paramTimeIntervalPair, monitorDOInDB);
        return DataResult.success(updated);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置管理-删除监控")
    @ApiImplicitParam(name = "id", value = "监控id", type = "Integer", required = true)
    @PostMapping("/delete")
    public DataResult<Boolean> delete(@NotNull(message = "id不能为空") Integer id) {
        boolean deleted = bizTargetMonitorConfigService.delete(id);
        return DataResult.success(deleted);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置管理-查询详情")
    @ApiImplicitParam(name = "id", value = "监控id", type = "Integer", required = true)
    @GetMapping("/getDetailById")
    public DataResult<BizTargetMonitorConfigDO> getDetailById(@NotNull(message = "id不能为空") Integer id) {
        BizTargetMonitorConfigDO result = bizTargetMonitorConfigService.getDetailById(id);
        return DataResult.success(result);
    }

    /**
     * 业务目标监控分页查询
     * @param param 请求参数
     */
    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置-分页查询")
    @GetMapping("/pageList")
    public PageListResult<BizTargetMonitorConfigDO> search(@Validated BizTargetMonitorConfigPageQryDTO param) {
        if (param.getPageNo() <= 0) {
            throw BizRuntimeException.newParamException("起始页必能必须大于0");
        }
        return bizTargetMonitorConfigService.search(param);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置-支持的监控条件")
    @ApiImplicitParam(name = "主题", value = "themeType", type = "Integer", required = true)
    @GetMapping("/condition/supportList")
    public ListResult<AnalysisCondition> supportConditionList(@NotNull(message = "themeType不能为空") Integer themeType) {
        return ListResult.createSuccess(bizTargetMonitorConfigService.supportConditionList(themeType));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置-支持的指标条件")
    @ApiImplicitParam(name = "主题", value = "themeType", type = "Integer", required = true)
    @GetMapping("/target/supportList")
    public ListResult<AnalysisTarget> supportTargetList(@NotNull(message = "themeType不能为空") Integer themeType) {
        return ListResult.createSuccess(bizTargetMonitorConfigService.supportTargetList(themeType));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置-支持的维度")
    @ApiImplicitParam(name = "主题", value = "themeType", type = "Integer", required = true)
    @GetMapping("/dimension/supportList")
    public ListResult<AnalysisDimension> supportDimensionList(@NotNull(message = "themeType不能为空") Integer themeType) {
        return ListResult.createSuccess(bizTargetMonitorConfigService.supportDimensionList(themeType));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "业务指标监控配置-可查看指标监控Tab的用户")
    @GetMapping("/whiteList")
    public ListResult<String> whiteList() {
        return ListResult.createSuccess(whiteList);
    }

}
