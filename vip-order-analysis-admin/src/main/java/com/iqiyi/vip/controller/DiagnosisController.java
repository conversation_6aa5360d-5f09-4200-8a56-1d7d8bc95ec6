package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.target.DiagnosisQueryDTO;
import com.iqiyi.vip.dto.target.DownloadDiagnosisDTO;
import com.iqiyi.vip.service.DiagnosisDimensionService;

/**
 * <AUTHOR>
 * @className DiagnosisController
 * @description
 * @date 2023/10/24
 **/
@Api(tags = "订单分析平台-诊断分析", value = "诊断结果查询接口")
@RestController
@RequestMapping("order-analysis")
@Slf4j
public class DiagnosisController {

    @Resource
    private DiagnosisDimensionService diagnosisDimensionService;


    @WebLog
    @PostMapping("/diagnosis")
    @ApiOperation(value = "诊断分析", httpMethod = "POST")
    public DataResult<DiagnosisResultDTO> executeQuery(@Validated @RequestBody DiagnosisQueryDTO diagnosisQueryDTO) {
        log.info("enter executeQuery, diagnosisQueryDTO:{}", diagnosisQueryDTO);
        return diagnosisDimensionService.diagnosis(diagnosisQueryDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("/dimensionLayerTree")
    @ApiOperation(value = "诊断分析", httpMethod = "POST")
    public DataResult<HashMap<String, DimensionLayerNode>> dimensionLayerTree(@RequestBody BaseQry baseQry) {
        log.info("enter executeQuery, BaseQry:{}", baseQry);
        return DataResult.success(diagnosisDimensionService.getDiagnosisDimensionTree(baseQry));
    }

    //诊断结果下载
    @ApiOperation(value = "诊断结果下载")
    @ApiImplicitParam(name = "filename", value = "文件名称", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/diagnosis/download")
    public void download(DownloadDiagnosisDTO downloadDiagnosisDTO) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        diagnosisDimensionService.download(response, downloadDiagnosisDTO);
    }


    @GetMapping("/diagnosis/{filename}")
    public ResponseEntity<InputStreamResource> downloadFile(@PathVariable String filename) throws IOException {

        // 注意：此例子中，预计所有文件都在服务器的 /tmp 目录下，可根据实际情况修改
        File file = new File("/tmp/" + filename);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);

        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        return ResponseEntity.ok()
            .headers(headers)
            .contentLength(file.length())
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(resource);
    }
}
