package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.AnalysisConditionPageQryDTO;
import com.iqiyi.vip.dto.condition.ConditionUpdateDTO;
import com.iqiyi.vip.service.ConditionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Api(tags = "订单分析平台-条件管理")
@RestController
@RequestMapping("order-analysis/condition/config")
public class ConditionConfigController {

    @Resource
    private ConditionConfigService conditionConfigService;

    @WebLog(simplify = false)
    @ApiOperation(value = "条件管理-新增")
    @PostMapping("save")
    public CommonResult save(@RequestBody ConditionUpdateDTO updateDTO) {
        return conditionConfigService.save(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件管理-更新")
    @PostMapping("update")
    public CommonResult update(@RequestBody ConditionUpdateDTO updateDTO) {
        return conditionConfigService.update(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件管理-删除")
    @PostMapping("delete")
    public CommonResult delete(@RequestBody ConditionUpdateDTO updateDTO) {
        return conditionConfigService.delete(updateDTO.getCode(), updateDTO.getOperator());
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件管理-分页查询")
    @GetMapping("pageList")
    public PageListResult<AnalysisCondition> pageList(AnalysisConditionPageQryDTO query) {
        return conditionConfigService.pageList(query);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "条件管理-查询所有有效条件")
    @GetMapping("listAll")
    public ListResult<AnalysisCondition> listAll() {
        return conditionConfigService.listAll();
    }
}
