package com.iqiyi.vip.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.task.AnalysisTaskConfigReqDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskConfigRespDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskDeleteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskPageQryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskReExecuteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskReTaskNameDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskResDTO;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.TaskManagerService;

/**
 * <AUTHOR>
 * @className TaskController
 * @description
 * @date 2022/6/1
 **/
@RestController
@RequestMapping("order-analysis/task")
@Api(tags = "订单分析平台-任务管理")
public class TaskController {

    @Resource
    private TaskManagerService taskManagerService;
    @Resource
    private DataPermissionService dataPermissionService;

    @WebLog
    @GetMapping("pageQuery")
    @ApiOperation(value = "任务分页查询", httpMethod = "GET")
    public PageListResult<AnalysisTaskResDTO> executeQuery(@Validated AnalysisTaskPageQryDTO taskPageQryDTO) {
        return taskManagerService.pageList(taskPageQryDTO, dataPermissionService.ownedHighLevelUser(taskPageQryDTO));
    }

    @WebLog(simplify = false)
    @GetMapping("reExecute")
    @ApiOperation(value = "任务重新执行", httpMethod = "GET")
    public CommonResult reExecute(@Validated AnalysisTaskReExecuteDTO reExecuteDTO) throws JsonProcessingException {
        return taskManagerService.reExecuteTask(reExecuteDTO);
    }

    @WebLog(simplify = false)
    @GetMapping("deleteByTaskId")
    @ApiOperation(value = "删除任务记录", httpMethod = "GET")
    public CommonResult delete(@Validated AnalysisTaskDeleteDTO deleteDTO) {
        return taskManagerService.deleteTaskRecord(deleteDTO);
    }

    @WebLog(simplify = false)
    @PostMapping("reName")
    @ApiOperation(value = "任务重命名", httpMethod = "POST")
    public CommonResult reName(@Validated @RequestBody AnalysisTaskReTaskNameDTO analysisTaskReTaskNameDTO) {
        return taskManagerService.reTaskName(analysisTaskReTaskNameDTO);
    }

    @WebLog(simplify = false)
    @GetMapping("taskConfig")
    @ApiOperation(value = "任务参数查询", httpMethod = "GET")
    public DataResult<AnalysisTaskConfigRespDTO> queryConfig(@Validated AnalysisTaskConfigReqDTO analysisTaskConfigReqDTO)
            throws JsonProcessingException {
        return taskManagerService.queryTaskConfig(analysisTaskConfigReqDTO);
    }

}
