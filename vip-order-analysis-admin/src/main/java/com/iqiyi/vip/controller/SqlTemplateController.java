package com.iqiyi.vip.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.sqltemplate.AnalysisSqlTemplatePageQryDTO;
import com.iqiyi.vip.dto.sqltemplate.SqlTemplateUpdateDTO;
import com.iqiyi.vip.service.SqlTemplteMangerService;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@RestController
@RequestMapping("order-analysis/sql-template")
@Api(tags = "订单分析平台-Sql模板管理")
@Slf4j
public class SqlTemplateController {

    @Resource
    private SqlTemplteMangerService sqlTemplteMangerService;

    @WebLog(simplify = false)
    @ApiOperation(value = "模板管理-新增")
    @PostMapping("save")
    public CommonResult save(SqlTemplateUpdateDTO updateDTO) {
        log.info("sql template save request:{}", JSON.toJSONString(updateDTO));
        return sqlTemplteMangerService.save(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "模板管理-更新")
    @PostMapping("update")
    public CommonResult update(SqlTemplateUpdateDTO updateDTO) {
        log.info("sql template update request:{}", JSON.toJSONString(updateDTO));
        return sqlTemplteMangerService.update(updateDTO);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "模板管理-删除")
    @ApiImplicitParam(name="id", value = "指标id", type = "long", required = true)
    @PostMapping("delete")
    public CommonResult delete(@RequestParam Long id) {
        return sqlTemplteMangerService.delete(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "模板管理-详情")
    @ApiImplicitParam(name="id", value = "指标id", type = "long", required = true)
    @GetMapping("detail")
    public DataResult<AnalysisSqlTemplate> detail(@RequestParam Long id) {
        return sqlTemplteMangerService.detail(id);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "模板管理-分页查询")
    @GetMapping("pageList")
    public PageListResult<AnalysisSqlTemplate> pageList(AnalysisSqlTemplatePageQryDTO query) {
        return sqlTemplteMangerService.pageList(query);
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "指标管理-列表查询")
    @GetMapping("list")
    public ListResult<AnalysisSqlTemplate> list(BaseQry baseQry) {
        return sqlTemplteMangerService.list(baseQry);
    }

}
