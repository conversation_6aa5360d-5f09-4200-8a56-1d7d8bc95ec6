package com.iqiyi.vip;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.qiyi.vip.threadpool.EnableDynamicThreadPool;

/**
 * <AUTHOR>
 */
@EnableWebMvc
@EnableCaching
@SpringBootApplication
@EnableAsync
@EnableAspectJAutoProxy
@EnableTransactionManagement
@EnableDynamicThreadPool
@EnableMethodCache(basePackages = {"com.iqiyi.vip","com.qiyi.vip"})
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }
}
