package com.iqiyi.vip.aspect;

import com.google.common.base.Strings;
import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.service.DataPermissionService;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.iqiyi.vip.enums.CodeEnum.DATA_PERMISSION_UN_OWNED;
import static com.iqiyi.vip.enums.CodeEnum.DATA_PERMISSION_UN_SELECTED;

/**
 * 当用户只有一种权限的时候，不会传 BaseQry.dataPermissionType 参数，需要通过查询数据库判断用户默认权限，继续在下面流程中使用
 * Aspect : https://docs.spring.io/spring-framework/reference/core/aop/ataspectj.html
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/5 17:07
 */
@Aspect
@Component
public class DataPermissionTypeDetermineAspect {

    @Resource
    private DataPermissionService dataPermissionService;

    @Pointcut("execution(public * com.iqiyi.vip.controller.ConditionController.*(..)) ||" +
              " execution(public * com.iqiyi.vip.controller.QueryController.*(..)) &&" +
              "!execution(public * com.iqiyi.vip.controller.QueryController.simpleQuery(..)))") // the pointcut expression
    private void determineDataPermissionTypePointcut() {} // the pointcut signature

    @Before(value = "determineDataPermissionTypePointcut() && args(baseQry)")
    public void determineDataPermissionType(BaseQry baseQry) {
        boolean highestDataPermission = ConditionDataPermissionHandler.getConditionDataPermissionHandler(baseQry).isHighestDataPermission(false, baseQry);
        if (!highestDataPermission) {
            if (baseQry.getDataPermissionType() == null) { // 用户未选择使用的权限类型
                this.determineThemeType(baseQry); // 分析主题动态确定逻辑
                if (baseQry.getThemeType() == null) { // 兼容未获得确定主题，抛出异常
                    throw new BizRuntimeException(DATA_PERMISSION_UN_SELECTED);
                }
                // 主题确定后，再判断当前主题下是否只有一种权限，有则确定使用，否则抛出异常
                List<DataPermission> dataPermissionsByThemeType = dataPermissionService.getDataPermissionsByThemeType(baseQry.getOperator(), baseQry.getThemeType());
                if (baseQry.getDataPermissionType() == null && (CollectionUtils.isEmpty(dataPermissionsByThemeType) || dataPermissionsByThemeType.size() > 1)) {
                    throw new BizRuntimeException(DATA_PERMISSION_UN_SELECTED);
                }
                DataPermission dataPermission = dataPermissionsByThemeType.get(0);
                baseQry.setDataPermissionType(dataPermission.getType());
            } else {
                this.determineThemeType(baseQry);
                this.checkDataPermissionType(baseQry);
            }
        }
    }

    /**
     * 分析主题动态确定逻辑
     * @param baseQry
     */
    private void determineThemeType(BaseQry baseQry) {
        if (baseQry.getThemeType() == null) { // 当用户未传主题的时候，动态判断
            if (!Strings.isNullOrEmpty(baseQry.getOperator())) {
                Integer determineThemeType = dataPermissionService.determineThemeType(baseQry);
                baseQry.setThemeType(determineThemeType);
            }
        } else {
            this.checkThemeType(baseQry);
        }
    }

    private void checkThemeType(BaseQry baseQry) {
        if (!dataPermissionService.ownedThemeType(baseQry)) {
            throw new BizRuntimeException(DATA_PERMISSION_UN_OWNED);
        }
    }

    private void checkDataPermissionType(BaseQry baseQry) {
        if (!dataPermissionService.ownedDataPermissionType(baseQry)) {
            throw new BizRuntimeException(DATA_PERMISSION_UN_OWNED);
        }
    }
}
