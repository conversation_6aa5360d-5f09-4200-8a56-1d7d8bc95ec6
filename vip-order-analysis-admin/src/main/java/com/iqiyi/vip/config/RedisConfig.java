package com.iqiyi.vip.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

/**
 * <AUTHOR>
 * @className RedisConfig
 * @description
 * @date 2024/5/25
 **/
@Configuration
@EnableRedisHttpSession
public class RedisConfig {

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration("vip-test.bjdx.qiyi.redis", 18524);
        configuration.setPassword("ZSKnkI71BWSTUCoNoB4");
        return new LettuceConnectionFactory(configuration);
    }

//    @Bean
//    public SmartRedisConnectionFactory smartRedisConnectionFactory() {
//        return new SmartRedisConnectionFactory("vip_order_analysis_redis");
//    }

}
