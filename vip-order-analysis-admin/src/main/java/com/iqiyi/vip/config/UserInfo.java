package com.iqiyi.vip.config;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/2 22:37
 */
@Component
@Data
public class UserInfo implements UserDetails {

    private Long id;
    private String loginName;
    private String email;
    private String oaAccount;
    private List<? extends GrantedAuthority> authorities;

    private boolean isAccountNonExpired = true;
    private boolean isAccountNonLocked = true;
    private boolean isCredentialsNonExpired = true;
    private boolean isEnabled = true;

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return this.oaAccount;
    }
}
