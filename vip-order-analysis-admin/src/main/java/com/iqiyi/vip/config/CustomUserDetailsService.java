package com.iqiyi.vip.config;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/2 22:17
 */
public class CustomUserDetailsService implements UserDetailsService {

    @Override
    public UserDetails loadUserByUsername(String oaAccount) throws UsernameNotFoundException {
        UserInfo userInfo = new UserInfo();
        userInfo.setOaAccount(oaAccount);
        userInfo.setEmail(oaAccount + "@qiyi.com");
        List<GrantedAuthority> grantedAuthorities = getGrantedAuthorities(oaAccount);
        userInfo.setAuthorities(grantedAuthorities);
        return userInfo;
    }

    private List<GrantedAuthority> getGrantedAuthorities(String oaAccount) {
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_LOGIN"));
        return grantedAuthorities;
    }
}
