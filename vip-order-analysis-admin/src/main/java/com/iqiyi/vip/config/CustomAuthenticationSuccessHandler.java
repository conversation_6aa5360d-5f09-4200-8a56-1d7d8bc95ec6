package com.iqiyi.vip.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/23 14:33
 */
@Component
@Slf4j

public class CustomAuthenticationSuccessHandler implements AuthenticationSuccessHandler {
    private static final String DEVOPS_SYSTEM_CODE = "vip-order-analysis"; // 对接devops权限系统的系统名
    private static final String DEVOPS_TOKEN = "kouri43uriuj4k3o5uifyfioq3iui"; // 对接devops权限系统的token

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,

                                        Authentication authentication) throws IOException, ServletException {
        // 获取已认证的用户信息
        UserInfo userInfo = (UserInfo) authentication.getPrincipal();
        String oaAccount = userInfo.getOaAccount();
        log.info("onAuthenticationSuccess, query " + oaAccount + "from devops");
        checkAndSignUpUserToDevOps(oaAccount);
        log.info("[CustomAuthenticationSuccessHandler] login success, redirect");
        response.sendRedirect("/order");
    }

    public void checkAndSignUpUserToDevOps(String oaAccount) {
        RestTemplate restTemplate = new RestTemplate();
        String permissionUrl = "http://devops.vip.online.qiyi.qae/vip-devops/api/v1/user/userDetails";
        try {
            ResponseEntity<Object> permissionResponse = restTemplate.exchange(
                    permissionUrl + "?oaAccount=" + oaAccount + "&sysCode=" + DEVOPS_SYSTEM_CODE + "&token=" + DEVOPS_TOKEN,
                    HttpMethod.GET, null, Object.class);

            Object permissionRes = permissionResponse.getBody();
            log.info("onAuthenticationSuccess, devops查询用户结果:{} ", permissionRes);
            if (permissionRes instanceof Map && ((Map<?, ?>) permissionRes).containsKey("code")) {
                String permissionCode = (String) ((Map<?, ?>) permissionRes).get("code");
                if ("CM00104".equals(permissionCode)) {
                    log.info("onAuthenticationSuccess, devops查询用户:{}不存在", oaAccount);
                    signUpUserToDevOps(oaAccount);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("onAuthenticationSuccess, devops查询用户:{}出错", oaAccount);
        }
    }

    private void signUpUserToDevOps(String oaAccount) {
        try {
            String createUserUrl = "http://devops.vip.online.qiyi.qae/vip-devops/api/v1/auth/user/add";
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", oaAccount);
            requestBody.put("oaAccount", oaAccount);
            requestBody.put("status", 1);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Object> createUserResponse = restTemplate.exchange(
                    createUserUrl + "?sysCode=" + DEVOPS_SYSTEM_CODE + "&token=" + DEVOPS_TOKEN,
                    HttpMethod.POST, new HttpEntity<>(requestBody), Object.class);

            Object createUserRes = createUserResponse.getBody();
            if (!(createUserRes instanceof Map) || !"A00000".equals(((Map<?, ?>) createUserRes).get("code"))) {
                log.error("onAuthenticationSuccess, devops创建用户:{}出错", oaAccount);
                return;
            }
            log.info("onAuthenticationSuccess, devops创建用户:{}成功", oaAccount);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("onAuthenticationSuccess, devops创建用户:{}出错", oaAccount);
        }
    }
}
