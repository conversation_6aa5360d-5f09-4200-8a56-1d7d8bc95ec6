logging.config=classpath:logback-spring.xml
spring.profiles.active=dev
spring.jackson.serialization.write-dates-as-timestamps=true
spring.cache.type=caffeine
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.mapper-locations=classpath:mapper/*.xml
management.server.port=8099
management.endpoints.web.exposure.include=health,prometheus
log.home=/data/logs

server.max-http-header-size=10240

# iqiyi vip mail config
vip.mail.prefix=vipmessage
vip.mail.name=<EMAIL>
vip.mail.token=8u36q9d63g96hqlr&n7PnZt_682H@

spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

mybatis.type-handlers-package=com.iqiyi.vip.handler.mybatis

jetcache.penetrationProtect=true
jetcache.areaInCacheName=false
jetcache.statIntervalMinutes=3
jetcache.hidePackages=com.alibaba
jetcache.local.default.type=caffeine
jetcache.local.default.keyConvertor=fastjson2
jetcache.local.default.limit=10000
jetcache.local.default.expireAfterWriteInMillis=500000