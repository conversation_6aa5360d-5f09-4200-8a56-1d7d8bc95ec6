#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.hikari.connection-timeout=1000
#spring.datasource.hikari.maximum-pool-size=20
#spring.datasource.hikari.pool-name=DataHikariCP
#spring.datasource.type=com.zaxxer.hikari.HikariDataSource
#spring.datasource.url=******************************************************************
#spring.datasource.username=viptrade
#spring.datasource.password=f1b6e3NHd87d

spring.cache.caffeine.spec=initialCapacity=100,maximumSize=1000,expireAfterWrite=60s

spring.redis.host=redis://vip-test.bjdx.qiyi.redis:18524
spring.redis.port=18524
spring.redis.password=ZSKnkI71BWSTUCoNoB4
spring.redis.timeout=1000
spring.redis.testOnBorrow=true
spring.redis.testOnReturn=true
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.max-wait=1000
spring.redis.jedis.pool.min-idle=20
# iqiyi cloud config
config.application.name=vip-order-analysis
config.application.env=pro
config.application.region=default


oss.client.endpoint=http://bj.oss.qiyi.storage
oss.client.ak=cfsuchh2p49v269mkgg926gb43leo9rp
oss.client.sk=mx24p3u4fpo0mqxgg6n1g9no50cvylku
oss.client.bucket=vip-order-analysis
oss.client.path=target/result/prod/

## rocketMq config
#analysis.async.task.producer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
#analysis.async.task.producer.groupname=PG-analysis_async_task
#analysis.async.task.producer.token=PT-b158046c-a40c-476f-956b-f3d7a35dcef3
#analysis.async.task.producer.topic=analysis_async_task
#analysis.async.task.producer.tag=*
## Oa Approved
#oa.approved.async.task.producer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
#oa.approved.async.task.producer.groupname=PG-oaApproved_async_task
#oa.approved.async.task.producer.token=PT-2eba0fd0-70c2-464e-bceb-9bf246e121ab
#oa.approved.async.task.producer.topic=oaApproved_async_task
#oa.approved.async.task.producer.tag=*
log.home=/data/logs
management.server.port=8099
management.endpoints.web.exposure.include=health,prometheus
# 可选参数，异常指标最大序列数，默认50，用于防止未配置 Rest 风格 URL 的前缀导致的序列数据爆炸
v-eagle.exception.maxSize=50
# 可选参数，Rest URL 的前缀，多个用逗号隔开，例如URL为http://xxx/info/${uid}，则可以配置为http://xxx/info/
v-eagle.exception.restUrlPrefix=
clickhouseconf.token=SMPpgMoeldH0drKhaNK7gif4qeSmBXyr
clickhouseconf.database=vip_order_analysis
clickhouseconf.cluster=bdxs-ck2
clickhouseconf.project=clickhouse
clickhouseconf.env=online
clickhouseconf.url=**************************************************************************;

starrocksconf.url=*******************************************************************************************************;

#logging.level.com.iqiyi.vip.mapper=debug
#CAS服务地址
cas.server.host.url=https://sso.qiyi.com/cas
#CAS服务登录地址
cas.server.host.login_url=${cas.server.host.url}/login
#CAS服务登出地址
cas.server.host.logout_url=${cas.server.host.url}/logout
#应用访问地址
app.server.host.url=http://tianyan.qiyi.domain/
#应用登录地址
app.login.url=/login
#应用登出地址
app.logout.url=/cas/logout
webSecurity.ignoreUrls=/,/order-analysis/permission/flushInternalPermissions,/order-analysis/competitor/priceMonitor/save,/order-analysis/competitor/uiMonitor/save,/order-analysis/task/reExecute,/order-analysis/permission/approval-callback,/actuator/prometheus,/order-analysis/commonQuery,/order-analysis/simpleQuery,/order-analysis/availableDate,/order-analysis/feedback,/order-analysis/competitor/shopMonitor/save,/order-analysis/competitor/shopPrice/save
#webSecurity.ignoreUrls=/**
beidou.token=7f8d89139badec54428549d8045a3540
# Spring Session 存储类型为 Redis
spring.session.store-type=redis
spring.session.timeout=86400

##调度中心部署跟地址：如调度中心集群部署存在多个地址则用逗号分隔。
vip.job.admin.addresses=http://qiyi-job-admin.qiyi.domain
##应用"AppName"，应用心跳注册分组依据，appName需要联系会员营销团队在调度中心注册
vip.job.executor.appname=vip-order-analysis
##执行器通讯TOKEN
vip.job.accessToken=7fdf18f1eca449c1b3bd32f6e75177e2


#QDBM MySQL
mysqldal.nickname=vip_order_analysis

DBM_CONFIG_APPID=qpaas-db-vip-order-analysis-admin-prod
APOLLO_PAAS_TOKEN=d6cb9396-0f27-3125-5f84-00b90556cd11
