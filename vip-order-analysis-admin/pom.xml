<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.example</groupId>
    <artifactId>vip-order-analysis</artifactId>
      <version>1.8.77</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>vip-order-analysis-admin</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>org.example</groupId>
      <artifactId>vip-order-analysis-app</artifactId>
        <version>1.8.77</version>
        <exclusions>
            <exclusion>
                <artifactId>hibernate-validator</artifactId>
                <groupId>org.hibernate.validator</groupId>
            </exclusion>
        </exclusions>
    </dependency>
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-test</artifactId>
      </dependency>
      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-test</artifactId>
      </dependency>
      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
          <scope>test</scope>
      </dependency>
      <dependency>
          <groupId>org.springframework.session</groupId>
          <artifactId>spring-session-data-redis</artifactId>
      </dependency>
      <dependency>
          <groupId>org.springframework.session</groupId>
          <artifactId>spring-session</artifactId>
          <version>1.3.2.RELEASE</version>
      </dependency>
      <dependency>
          <groupId>com.iqiyi.bigdata</groupId>
          <artifactId>pilot-client</artifactId>
          <version>2.6.5</version>
      </dependency>
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-security</artifactId>
      </dependency>
      <dependency>
          <groupId>org.springframework.security</groupId>
          <artifactId>spring-security-cas</artifactId>
      </dependency>
      <dependency>
          <groupId>org.springframework.security</groupId>
          <artifactId>spring-security-taglibs</artifactId>
      </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <fork>true</fork>
        </configuration>
      </plugin>

        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.8.1</version>
            <configuration>
                <!-- 注意以下两项的参数配置 -->
                <source>1.8</source>
                <target>1.8</target>
                <!-- 这一项需要加 -->
                <compilerArgs>
                    <arg>-parameters</arg>
                </compilerArgs>
            </configuration>
        </plugin>

        <plugin>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>swagger-maven-plugin</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <configuration>
                <apiSources>
                    <apiSource>
                        <springmvc>true</springmvc>
                        <locations>
                            <location>com.iqiyi</location> <!-- 要扫描的代码位置 -->
                        </locations>
                        <!-- 一些额外信息，相当于 swagger 中的 ApiInfo -->
                        <info>
                            <title>${project.artifactId}</title>
                            <version>${project.version}</version>
                            <description>${project.artifactId}</description>
                        </info>
                        <outputFormats>json</outputFormats> <!-- 输出格式，json / yaml 或者 json,yaml -->
                        <swaggerDirectory>./target/generated-swagger-api</swaggerDirectory> <!-- 输出位置 -->
                        <swaggerFileName>${project.artifactId}</swaggerFileName> <!-- 输出的文件名 -->
                        <swaggerApiReader>com.iqiyi.vip.docgen.reader.SpringMvcApiReader</swaggerApiReader>
                    </apiSource>
                </apiSources>
            </configuration>
            <executions>
                <execution>
                    <phase>compile</phase>
                    <goals>
                        <goal>generate</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
  </build>

</project>
