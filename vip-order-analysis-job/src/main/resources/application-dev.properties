spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.connection-timeout=1000
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.pool-name=DataHikariCP
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.url=**********************************************************
spring.datasource.username=vip_test
spring.datasource.password=rg_z_6UF)w=Y

spring.cache.caffeine.spec=initialCapacity=100,maximumSize=1000,expireAfterWrite=60s

spring.redis.host=webdemo-test.w.qiyi.redis
spring.redis.port=18222
spring.redis.password=b643ee5d388a911
spring.redis.timeout=1000
spring.redis.testOnBorrow=true
spring.redis.testOnReturn=true
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.max-wait=1000
spring.redis.jedis.pool.min-idle=20

app.name=order-analysis-job
module.name=order-analysis-job

# iqiyi cloud config
cloud.config.app.name=vip-autorenew
cloud.config.app.env=test
cloud.config.app.region=default

