spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.connection-timeout=1000
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.pool-name=DataHikariCP
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.url=**********************************************************
spring.datasource.username=vip_test
spring.datasource.password=rg_z_6UF)w=Y

spring.cache.caffeine.spec=initialCapacity=100,maximumSize=1000,expireAfterWrite=60s

spring.redis.host=webdemo-test.w.qiyi.redis
spring.redis.port=18222
spring.redis.password=b643ee5d388a911
spring.redis.timeout=1000
spring.redis.testOnBorrow=true
spring.redis.testOnReturn=true
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.max-wait=1000
spring.redis.jedis.pool.min-idle=20

app.name=order-analysis-job
module.name=order-analysis-job

# iqiyi cloud config
cloud.config.app.name=vip-autorenew
cloud.config.app.env=test
cloud.config.app.region=default


######## vip-job配置 ###########
##调度中心部署跟地址：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"
vip.job.admin.addresses=http://vip-job-admin.test.qiyi.qae
##执行器"AppName"，执行器心跳注册分组依据；地址信息用于"调度中心请求并触发任务"和"执行器注册"，appName需要联系会员营销团队在调度中心注册
vip.job.executor.appname=vip-order-analysis
##执行器IP默认为空表示自动获取IP，多网卡时可手动设置指定IP，手动设置IP时将会绑定Host
vip.job.executor.ip=
##执行器默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口
vip.job.executor.port=9083
##执行器运行日志文件存储的磁盘位置，需要对该路径拥有读写权限，若job不需要单独文件打印则不需要配置
vip.job.executor.logpath=/data/logs/vip-order-analysis-job/job/
##执行器通讯TOKEN，向会员营销团队申请
vip.job.accessToken=4593a79221a34ce293092bafb7c136ed
## 接入方式，默认虚机
vip.job.access.way=virtual

