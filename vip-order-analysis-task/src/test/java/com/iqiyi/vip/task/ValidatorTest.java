package com.iqiyi.vip.task;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.web.WebAppConfiguration;
import javax.annotation.Resource;
import java.util.HashMap;
import com.iqiyi.vip.job.UpdateAvailableDateJob;
import com.iqiyi.vip.validator.DailyDataCheckValidator;
import com.iqiyi.vip.validator.OrderCountAndFeeTimeRangeValidator;

/**
 * <AUTHOR>
 * @className ValidatorTest
 * @description
 * @date 2023/3/10
 **/
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles(value = "prod")
@AutoConfigureMetrics
public class ValidatorTest {

    @Resource
    private OrderCountAndFeeTimeRangeValidator countAndFeeTimeRangeValidator;


    @Resource
    private DailyDataCheckValidator dailyDataCheckValidator;

    @Resource
    private UpdateAvailableDateJob updateAvailableDateJob;

    @Test
    public void testCk() {
        HashMap<Integer, Boolean> checkResultMap = new HashMap<>();
        countAndFeeTimeRangeValidator.validate(checkResultMap);
        System.out.println(checkResultMap);
    }

    @Test
    public void testDay() {
        HashMap<Integer, Boolean> checkResultMap = new HashMap<>();
        dailyDataCheckValidator.validate(checkResultMap);
        System.out.println(checkResultMap);
    }

    @Test
    public void test() {
        updateAvailableDateJob.simpleDataCheckJobHandler();
    }
}
