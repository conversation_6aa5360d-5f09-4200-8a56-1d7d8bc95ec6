package com.iqiyi.vip.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Splitter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskReExecuteDTO;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.TaskManagerService;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @className CommonAnalysisAsyncTaskTest
 * @description
 * @date 2022/6/7
 **/
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles(value = "prod")
@AutoConfigureMetrics
class CommonAnalysisTaskTest {

    static {
        System.setProperty("env", "pro");
        System.setProperty("app.id", "vip-order-analysis");
    }

    @Resource
    private CommonAnalysisTask commonAnalysisTask;
    @Resource
    private AnalysisTaskRepository taskRepository;

    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private TaskManagerService taskManagerService;

    @Test
    void pro() throws Exception {
        AnalysisTaskExecuteDTO executeDTO = new AnalysisTaskExecuteDTO();
        AnalysisTaskDO task = taskRepository.getTaskByIdAndOperator(313410L, "jiahui");
        Map<String, List<Object>> conditionParamMap = JacksonUtils.getMapObject(task.getCondition());
        ConditionParamContext conditionParamContext = ConditionParamContext.builder().paramMap(conditionParamMap).build();
        executeDTO.setConditionParamContext(conditionParamContext);
        executeDTO.setTaskMD5(task.getUniqueIdentification());
        executeDTO.setOperator(task.getOperator());
        executeDTO.setTaskId(task.getId());
        executeDTO.setTargetCodes(Splitter.on(",").splitToList(task.getTargets()));
        executeDTO.setDimensionCodes(Splitter.on(",").splitToList(task.getDimensions()));
        System.out.println(JSON.toJSONString(executeDTO, true));
        executeDTO.setBusinessTypeId(task.getBusinessTypeId());
        executeDTO.setDataPermissionType(task.getDataPermissionType());
        executeDTO.setThemeType(task.getThemeType());
        commonAnalysisTask.processAsync(executeDTO);
        System.out.println("test");
    }

    @Test
    void pro2() throws Exception {

        String dtoString =
            "{\"businessTypeId\":2, \"dataPermissionType\":1, \"themeType\":1, \"operator\":\"yujianxin\", \"conditionParamContext\":{\n"
                + "            \"paramMap\":{\n"
                + "                \"businessName\":[\"爱奇艺视频\"],\"endKeys\":[],\"teamIds\":[],\"paidTypes\":[],\"payStartTime\":[1703520000000],\"payEndTime\":[1703606399000],\n"
                + "                \"compareStartTime\":[1700928000000],\"compareEndTime\":[1703520000000],\"groupIds\":[],\"vipTypes\":[],\"level2BusinessIds\":[],\n"
                + "                \"level3BusinessIds\":[],\"level4BusinessIds\":[],\"level5BusinessIds\":[],\"level6BusinessIds\":[],\"level7BusinessIds\":[],\"level8BusinessIds\":[],\n"
                + "                \"vipCardType\":[],\"renewType\":[],\"payChannels\":[],\"productTypes\":[],\"renewFlag\":[]},\"groupIds\":[],\"teamIds\":[],\"level2BusinessIds\":[],\n"
                + "            \"payStartTime\":1703520000000, \"compareEndTime\":1703520000000, \"payEndTime\":1703606399000, \"compareStartTime\":1700928000000, \"vipTypes\":[],\n"
                + "            \"bizNames\":[\"爱奇艺视频\"],\"bizEndKeys\":[],\"level8BusinessIds\":[],\"level7BusinessIds\":[],\"level6BusinessIds\":[],\"level5BusinessIds\":[],\n"
                + "            \"level4BusinessIds\":[],\"level3BusinessIds\":[],\"analysisType\":2\n"
            + "        },\"dimensionCodes\":[\"tian_zhuanyi\"],\"targetCodes\":[\n"
            + "        \"TRANSFOR_BEFORE_DHSR_xuanding-separation-TRANSFOR_BEFORE_DHSR_zhuanyi\", \"TRANSFOR_BEFORE_ANALYSIS\"],\"taskName\":\n"
            + "        \"-复制-复制-复制-复制-复制-复制-复制-复制-复制-复制-复制-复制-复制\", \"taskMD5\":\"7fb8ba939ab9ca0953844b93059d850e\", \"taskId\":6408}";
        AnalysisTaskExecuteDTO executeDTO = JSONObject.parseObject(dtoString, AnalysisTaskExecuteDTO.class);
        commonAnalysisTask.processAsync(executeDTO);
    }

    private Long dateToLong(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    @Test
    public void oaApproved() {
        dataPermissionService.dealOaApprovedDataPermission("8490909", "liushujun");
    }

    @Test
    public void reExecuteTaskTest() throws JsonProcessingException {
        AnalysisTaskReExecuteDTO analysisTaskReExecuteDTO = new AnalysisTaskReExecuteDTO();
        analysisTaskReExecuteDTO.setOperator("guojingting");
        analysisTaskReExecuteDTO.setTaskId(4108L);
        taskManagerService.reExecuteTask(analysisTaskReExecuteDTO);
    }
}