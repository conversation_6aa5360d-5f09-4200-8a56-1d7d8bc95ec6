package com.iqiyi.vip.task;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;

import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/3/10 22:39
 */
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles(value = "dev")
@AutoConfigureMetrics
class ScheduleAnalysisTaskTest {

    @Resource
    private ScheduleAnalysisTask scheduleAnalysisTask;

    static {
        System.setProperty("env", "dev");
        System.setProperty("app.id", "vip-order-analysis");
    }

    @Test
    void processAsync() throws Exception {
        String orderCountData = "{\"themeType\":1,\"businessTypeId\":1,\"operator\":\"zhouguojing\",\"conditionParamContext\":{\"paramMap\":{\"payChannels\":[1,2],\"payStartTime\":[1741614300041],\"payEndTime\":[1741614600041]},\"payStartTime\":1741614300041,\"payEndTime\":1741614600041,\"vipTypes\":[],\"groupIds\":[],\"bizNames\":[],\"bizEndKeys\":[],\"teamIds\":[],\"level8BusinessIds\":[],\"level7BusinessIds\":[],\"level6BusinessIds\":[],\"level5BusinessIds\":[],\"level4BusinessIds\":[],\"level3BusinessIds\":[],\"level2BusinessIds\":[],\"abExperiments\":[],\"vipAbTest\":[],\"analysisType\":2},\"dimensionCodes\":[\"vip_pay_channel\"],\"targetCodes\":[\"orderCount_real\"],\"taskName\":\"zgj-测试-111_2025-03-10_21:50:28\",\"taskMD5\":\"ae70963db49a9f1d4e8f67b611e7b3aa\",\"taskId\":7208,\"useCK\":false,\"taskSource\":\"JOB\"}";
        String kedanjiaData = "{\"themeType\":1,\"businessTypeId\":1,\"operator\":\"zhouguojing\",\"conditionParamContext\":{\"paramMap\":{\"vipTypeList\":[1,4,56,58],\"renewType\":[0],\"vipCardType\":[\"月卡\",\"季卡\",\"年卡\"],\"payStartTime\":[1741614300041],\"payEndTime\":[1741614600041]},\"payStartTime\":1741614300041,\"payEndTime\":1741614600041,\"vipTypes\":[],\"groupIds\":[],\"bizNames\":[],\"bizEndKeys\":[],\"teamIds\":[],\"level8BusinessIds\":[],\"level7BusinessIds\":[],\"level6BusinessIds\":[],\"level5BusinessIds\":[],\"level4BusinessIds\":[],\"level3BusinessIds\":[],\"level2BusinessIds\":[],\"abExperiments\":[],\"vipAbTest\":[],\"analysisType\":2},\"dimensionCodes\":[\"vip_type_desc\",\"renew_type_desc\",\"vip_card_type\"],\"targetCodes\":[\"kedanjia_real\"],\"taskName\":\"zgj-客单价-111_2025-03-10_21:50:28\",\"taskMD5\":\"ae70963db49a9f1d4e8f67b611e7b3aa\",\"taskId\":7208,\"useCK\":false,\"taskSource\":\"JOB\"}";
        AnalysisTaskExecuteDTO executeDTO = JacksonUtils.parseObject(orderCountData, AnalysisTaskExecuteDTO.class);
        scheduleAnalysisTask.processAsync(executeDTO);

    }
}