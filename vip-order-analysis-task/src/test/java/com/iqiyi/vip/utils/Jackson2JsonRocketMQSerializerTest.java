package com.iqiyi.vip.utils;

import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;

/**
 * @author: guojing
 * @date: 2025/3/20 18:09
 */
public class Jackson2JsonRocketMQSerializerTest {

    @Test
    void name() {
        String msgContent = "{\"businessTypeId\":1,\"conditionParamContext\":{\"abExperiments\":[],\"analysisType\":2,\"bizEndKeys\":[],\"bizNames\":[],\"groupIds\":[1],\"level2BusinessIds\":[],\"level3BusinessIds\":[],\"level4BusinessIds\":[],\"level5BusinessIds\":[],\"level6BusinessIds\":[],\"level7BusinessIds\":[],\"level8BusinessIds\":[],\"paramMap\":{\"businessName\":[],\"endKeys\":[],\"teamIds\":[],\"paidTypes\":[],\"payStartTime\":[1741276800000],\"payEndTime\":[1741881599999],\"customIncomeType\":[\"vip\"],\"groupIds\":[1],\"vipTypes\":[13,58],\"level2BusinessIds\":[],\"level3BusinessIds\":[],\"level4BusinessIds\":[],\"level5BusinessIds\":[],\"level6BusinessIds\":[],\"level7BusinessIds\":[],\"level8BusinessIds\":[],\"vipCardType\":[],\"renewType\":[],\"payChannels\":[2,1,8],\"renewFlag\":[],\"isOrderUpgrade\":[],\"abExperiment\":[],\"fromCasherType\":[],\"userGroup\":[]},\"payEndTime\":1741881599999,\"payStartTime\":1741276800000,\"teamIds\":[],\"vipAbTest\":[],\"vipTypes\":[13,58]},\"dataPermissionType\":1,\"dimensionCodes\":[\"year_month_day\"],\"operator\":\"zhouguojing\",\"targetCodes\":[\"orderCount\",\"kedanjia\"],\"taskId\":65193,\"taskMD5\":\"66adf23af87893618842f5fac350c418\",\"taskName\":\"zgj-qdbm-test-111\",\"taskSource\":\"UI_MANUAL\",\"themeType\":1,\"useCK\":false}";
        byte[] msgBytes = JacksonUtils.toJsonBytes(msgContent);
        AnalysisTaskExecuteDTO executeDTO = JacksonUtils.parseObjectFromBytes(msgBytes, AnalysisTaskExecuteDTO.class);

//        Jackson2JsonRocketMQSerializer<AnalysisTaskExecuteDTO> serializer = new Jackson2JsonRocketMQSerializer(AnalysisTaskExecuteDTO.class);
//        byte[] bytes = serializer.serialize(executeDTO);
//        AnalysisTaskExecuteDTO deserialize = serializer.deserialize(bytes);
//        System.out.println(JacksonUtils.toJsonString(deserialize));
    }

    @Test
    void test2() {
        String msgContent = "{\"businessTypeId\":1,\"conditionParamContext\":{\"abExperiments\":[],\"analysisType\":2,\"bizEndKeys\":[],\"bizNames\":[],\"groupIds\":[1],\"level2BusinessIds\":[],\"level3BusinessIds\":[],\"level4BusinessIds\":[],\"level5BusinessIds\":[],\"level6BusinessIds\":[],\"level7BusinessIds\":[],\"level8BusinessIds\":[],\"paramMap\":{\"businessName\":[],\"endKeys\":[],\"teamIds\":[],\"paidTypes\":[],\"payStartTime\":[1741276800000],\"payEndTime\":[1741881599999],\"customIncomeType\":[\"vip\"],\"groupIds\":[1],\"vipTypes\":[13,58],\"level2BusinessIds\":[],\"level3BusinessIds\":[],\"level4BusinessIds\":[],\"level5BusinessIds\":[],\"level6BusinessIds\":[],\"level7BusinessIds\":[],\"level8BusinessIds\":[],\"vipCardType\":[],\"renewType\":[],\"payChannels\":[2,1,8],\"renewFlag\":[],\"isOrderUpgrade\":[],\"abExperiment\":[],\"fromCasherType\":[],\"userGroup\":[]},\"payEndTime\":1741881599999,\"payStartTime\":1741276800000,\"teamIds\":[],\"vipAbTest\":[],\"vipTypes\":[13,58]},\"dataPermissionType\":1,\"dimensionCodes\":[\"year_month_day\"],\"operator\":\"zhouguojing\",\"targetCodes\":[\"orderCount\",\"kedanjia\"],\"taskId\":65193,\"taskMD5\":\"66adf23af87893618842f5fac350c418\",\"taskName\":\"zgj-qdbm-test-111\",\"taskSource\":\"UI_MANUAL\",\"themeType\":1,\"useCK\":false}";
//        AnalysisTaskExecuteDTO executeDTO = JacksonUtils.parseObject(msgContent, AnalysisTaskExecuteDTO.class);
        AnalysisTaskExecuteDTO executeDTO = JacksonUtils.parseObject(msgContent, AnalysisTaskExecuteDTO.class);
        System.out.println(executeDTO);
    }
}
