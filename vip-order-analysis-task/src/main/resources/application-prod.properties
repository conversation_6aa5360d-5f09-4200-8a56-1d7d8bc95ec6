#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.hikari.connection-timeout=1000
#spring.datasource.hikari.maximum-pool-size=20
#spring.datasource.hikari.pool-name=DataHikariCP
#spring.datasource.type=com.zaxxer.hikari.HikariDataSource
#spring.datasource.url=******************************************************************
#spring.datasource.username=viptrade
#spring.datasource.password=f1b6e3NHd87d

spring.cache.caffeine.spec=initialCapacity=100,maximumSize=1000,expireAfterWrite=60s

spring.redis.host=webdemo-test.w.qiyi.redis
spring.redis.port=18222
spring.redis.password=b643ee5d388a911
spring.redis.timeout=1000
spring.redis.testOnBorrow=true
spring.redis.testOnReturn=true
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.max-wait=1000
spring.redis.jedis.pool.min-idle=20

spring.application.name=vip-order-analysis-task

# iqiyi cloud config
config.application.name=vip-order-analysis
config.application.env=pro
config.application.region=default


oss.client.endpoint=http://bj.oss.qiyi.storage
oss.client.ak=cfsuchh2p49v269mkgg926gb43leo9rp
oss.client.sk=mx24p3u4fpo0mqxgg6n1g9no50cvylku
oss.client.bucket=vip-order-analysis
oss.client.path=target/result/prod/

# rocketMq config
analysis.async.task.consumer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
analysis.async.task.consumer.groupname=CG-analysis_async_task
analysis.async.task.consumer.token=CT-b260bf97-698c-484d-8f92-3959fac035a7
analysis.async.task.consumer.topic=analysis_async_task
analysis.async.task.consumer.tag=*
# Oa Approved
oa.approved.async.task.consumer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
oa.approved.async.task.consumer.groupname=CG-oaApproved_async_task
oa.approved.async.task.consumer.token=CT-89e73f4d-2ee5-4cad-bb62-a3ccc3c08ef2
oa.approved.async.task.consumer.topic=oaApproved_async_task
oa.approved.async.task.consumer.tag=*

analysis.async.task.producer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
analysis.async.task.producer.groupname=PG-analysis_async_task
analysis.async.task.producer.token=PT-b158046c-a40c-476f-956b-f3d7a35dcef3
analysis.async.task.producer.topic=analysis_async_task
analysis.async.task.producer.tag=*
# Oa Approved
oa.approved.async.task.producer.address=boss-vip-order-analysis-rocketmq-c1-0.rocketmq.qiyi.middle:9876;boss-vip-order-analysis-rocketmq-c2-0.rocketmq.qiyi.middle:9876
oa.approved.async.task.producer.groupname=PG-oaApproved_async_task
oa.approved.async.task.producer.token=PT-2eba0fd0-70c2-464e-bceb-9bf246e121ab
oa.approved.async.task.producer.topic=oaApproved_async_task
oa.approved.async.task.producer.tag=*


##调度中心部署跟地址：如调度中心集群部署存在多个地址则用逗号分隔。
vip.job.admin.addresses=http://qiyi-job-admin.qiyi.domain
##应用"AppName"，应用心跳注册分组依据，appName需要联系会员营销团队在调度中心注册
vip.job.executor.appname=vip-order-analysis
##执行器，不配置时默认为9999
vip.job.executor.port=9083
##执行器运行日志文件存储的磁盘位置，需要对该路径拥有读写权限，若job不需要单独文件打印则不需要配置
vip.job.executor.logpath=/data/logs/vip-order-analysis-task/jobhandler/
### 执行器执行日志清理时间，默认30天
vip.job.executor.logretentiondays=2
##执行器通讯TOKEN，向会员营销团队申请
vip.job.accessToken=7fdf18f1eca449c1b3bd32f6e75177e2
## 接入方式，默认虚机
vip.job.access.way=qke
vip.job.qke.app.id=14194

# iqiyi vip mail config
vip.mail.prefix=vipmessage
vip.mail.name=<EMAIL>
vip.mail.token=8u36q9d63g96hqlr&n7PnZt_682H@
management.server.port=8099
management.endpoints.web.exposure.include=health,prometheus
# 可选参数，异常指标最大序列数，默认50，用于防止未配置 Rest 风格 URL 的前缀导致的序列数据爆炸
v-eagle.exception.maxSize=50
# 可选参数，Rest URL 的前缀，多个用逗号隔开，例如URL为http://xxx/info/${uid}，则可以配置为http://xxx/info/
v-eagle.exception.restUrlPrefix=
clickhouseconf.token=SMPpgMoeldH0drKhaNK7gif4qeSmBXyr
clickhouseconf.database=vip_order_analysis
clickhouseconf.cluster=bdxs-ck2
clickhouseconf.project=clickhouse
clickhouseconf.env=online
clickhouseconf.url=**************************************************************************;
#logging.level.com.iqiyi.vip.mapper=debug

starrocksconf.url=*******************************************************************************************************;

#QDBM MySQL
mysqldal.nickname=vip_order_analysis

DBM_CONFIG_APPID=qpaas-db-vip-order-analysis-task-PROD-BDXS
APOLLO_PAAS_TOKEN=3dcd81bd-5375-8a1f-2122-f63dcdfb88a3