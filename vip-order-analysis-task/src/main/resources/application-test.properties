#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.hikari.connection-timeout=1000
#spring.datasource.hikari.maximum-pool-size=20
#spring.datasource.hikari.pool-name=DataHikariCP
#spring.datasource.type=com.zaxxer.hikari.HikariDataSource
#spring.datasource.url=**********************************************************
#spring.datasource.username=vip_test
#spring.datasource.password=rg_z_6UF)w=Y

spring.cache.caffeine.spec=initialCapacity=100,maximumSize=1000,expireAfterWrite=60s

spring.redis.host=webdemo-test.w.qiyi.redis
spring.redis.port=18222
spring.redis.password=b643ee5d388a911
spring.redis.timeout=1000
spring.redis.testOnBorrow=true
spring.redis.testOnReturn=true
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=20
spring.redis.jedis.pool.max-wait=1000
spring.redis.jedis.pool.min-idle=20


# iqiyi cloud config
config.application.name=vip-order-analysis
config.application.env=dev
config.application.region=default


oss.client.endpoint=http://bj.oss.qiyi.storage
oss.client.ak=cfsuchh2p49v269mkgg926gb43leo9rp
oss.client.sk=mx24p3u4fpo0mqxgg6n1g9no50cvylku
oss.client.bucket=vip-order-analysis
oss.client.path=target/result/test/

# rocketMq config
analysis.async.task.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
analysis.async.task.consumer.groupname=CG-analysis_async_task
analysis.async.task.consumer.token=CT-f0268427-2b76-450f-9963-615dda631beb
analysis.async.task.consumer.topic=analysis_async_task
analysis.async.task.consumer.tag=*
# Oa Approved
oa.approved.async.task.consumer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
oa.approved.async.task.consumer.groupname=CG-oaApproved_async_task
oa.approved.async.task.consumer.token=CT-0b7cfe4e-781c-4f35-afa7-de23efcddfde
oa.approved.async.task.consumer.topic=oaApproved_async_task
oa.approved.async.task.consumer.tag=*

analysis.async.task.producer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
analysis.async.task.producer.groupname=PG-analysis_async_task
analysis.async.task.producer.token=PT-c9d509df-2d27-4635-8e58-f7a982682052
analysis.async.task.producer.topic=analysis_async_task
analysis.async.task.producer.tag=*
# Oa Approved
oa.approved.async.task.producer.address=dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
oa.approved.async.task.producer.groupname=PG-oaApproved_async_task
oa.approved.async.task.producer.token=PT-9681d75c-754e-4c24-8725-d71224e9351d
oa.approved.async.task.producer.topic=oaApproved_async_task
oa.approved.async.task.producer.tag=*

# Oa 审批通过邮件通知人列表
oa.approved.notice.tos=<EMAIL>,<EMAIL>

#QDBM MySQL
mysqldal.nickname=vip_order_analysis