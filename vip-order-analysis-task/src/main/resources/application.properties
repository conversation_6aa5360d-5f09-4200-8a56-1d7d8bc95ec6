logging.config=classpath:logback-spring.xml
spring.profiles.active=dev
spring.jackson.serialization.write-dates-as-timestamps=true
spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.cache.type=caffeine
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.mapper-locations=classpath:mapper/*.xml
#management.server.port=8099
#management.endpoints.web.exposure.include=health,prometheus
log.home=/data/logs
# iqiyi vip mail config
vip.mail.prefix=vipmessage
vip.mail.name=<EMAIL>
vip.mail.token=8u36q9d63g96hqlr&n7PnZt_682H@
# Oa 审批通过邮件通知人列表
oa.approved.notice.tos=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
spring.main.allow-bean-definition-overriding=true


v-eagle.counter.maxSize=2000
v-eagle.gauge.maxSize=2000
