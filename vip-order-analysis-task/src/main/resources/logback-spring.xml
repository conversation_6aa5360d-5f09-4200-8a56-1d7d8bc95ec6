<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <!-- 根据模块ID调整日志文件命名 -->
    <springProperty scope="context" name="LOG_HOME" source="log.home"/>
    <property name="MODULE_NAME" value="vip-order-analysis-task"/>
    <property name="LOG_FILE" value="${LOG_HOME}/${MODULE_NAME}/task.log"/>
    <property name="BUSINESS_LOG_FILE" value="${LOG_HOME}/${MODULE_NAME}/business_data.log"/>
    <property name="BUSINESS_LOG_HISTORY_FILE" value="${LOG_HOME}/${MODULE_NAME}/business_data/business_data.log"/>
    <property name="ERROR_FILE" value="${LOG_HOME}/${MODULE_NAME}/error.log"/>

    <!-- RollingFileAppender滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ERROR_FILE}</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <!-- 只记录ERROR级别日志，添加范围过滤，可以将该类型的日志特殊记录到某个位置 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <!-- 滚动策略，它根据时间来制定滚动策略.既负责滚动也负责触发滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 定义分隔的日志文件的名称规则，后缀以".zip"或".gz"结尾，则开启日志文件压缩 -->
            <fileNamePattern>${ERROR_FILE}.%d{yyyy-MM-dd}</fileNamePattern>
            <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件，假设设置每天滚动，且<maxHistory>是7，
                  则只保存最近7天的文件，删除之前的旧文件。注意，删除旧文件是，那些为了归档而创建的目录也会被删除-->
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <!--异步日志配置-->
    <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志.0表示queque满了，不丢弃，block线程 -->
        <!--<discardingThreshold >0</discardingThreshold>-->
        <!-- 阻塞队列的最大容量,该值会影响性能.默认值为256.
        如服务QPS为80，且没给请求答应一条日志，考虑到80%丢弃策略，则queueSize至少设置为100 -->
        <queueSize>512</queueSize>
        <!--因为性能原因 logback 的 AsyncAppender 默认是不记录该信息，需要开启-->
        <includeCallerData>true</includeCallerData>
        <!--设置为true时，不会让你的应用程序线程发生阻塞，队列如果超过限制，会执行丢弃日志操作-->
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <maxHistory>168</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="BUSINESS_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${BUSINESS_LOG_FILE}</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${BUSINESS_LOG_HISTORY_FILE}.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <maxHistory>168</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_BUSINESS_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="BUSINESS_ROLLING_FILE"/>
    </appender>

    <!-- ConsoleAppender代表输出到控制台 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
    </appender>

    <logger name="business_data_log" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_BUSINESS_ROLLING_FILE"/>
    </logger>
    <!-- 禁止中间件打日志 -->
    <logger name="iqiyi.solar.registry" level="OFF"/>
    <logger name="iqiyi.solar.client" level="OFF"/>
    <logger name="iqiyi.solar.protocol.thrift" level="OFF"/>
    <logger name="iqiyi.atom.zookeeper.natural" level="OFF"/>
    <logger name="iqiyi.solar.transport.impl" level="OFF"/>
    <logger name="org.apache.zookeeper" level="OFF"/>
    <logger name="org.apache.dubbo" level="OFF"/>
    <logger name="com.iqiyi.dubbo.registry" level="OFF"/>

    <!--根据环境参数配置跟节点日志级别，可以引入多个appender规则-->
    <springProfile name="prod">
        <root level="INFO">
                        <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

</configuration>