package com.iqiyi.vip;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.qiyi.vip.threadpool.EnableDynamicThreadPool;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@EnableWebMvc
@EnableDynamicThreadPool
@EnableCaching
@SpringBootApplication
@EnableRetry
public class TaskApplication {
    public static void main(String[] args) {
        SpringApplication.run(TaskApplication.class, args);
    }
}
