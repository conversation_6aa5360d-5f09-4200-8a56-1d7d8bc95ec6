package com.iqiyi.vip.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerSingly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import com.iqiyi.vip.api.DevOpsPermissionApi;
import com.iqiyi.vip.constants.MQSerializerConstants;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.oa.OaApprovedNoticeMsgDTO;
import com.iqiyi.vip.mail.MailManager;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @className AnalysisAsyncTaskConsumer
 * @description
 * @date 2022/6/6
 **/
@Component
@Slf4j
public class OaApprovedAsyncTaskMsgHandler extends MessageListenerSingly {

    @Resource
    private DataPermissionService dataPermissionService;
    @Resource
    private DevOpsPermissionApi devOpsPermissionApi;

    @Resource
    private MailManager mailManager;
    @Value("${oa.approved.notice.tos}")
    private String oaApprovedNoticeTos;
    @Value("${send.email.notice.open.devops.permission:true}")
    private Boolean sendEmailNoticeOpenDevopsPermission;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(MessageExt messageExt, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        final OaApprovedNoticeMsgDTO oaApprovedNoticeMsgDTO = MQSerializerConstants.OA_APPROVED_SERIALIZER.deserialize(messageExt.getBody());
        try {
            final boolean success = dataPermissionService.dealOaApprovedDataPermission(oaApprovedNoticeMsgDTO.getAcId(), oaApprovedNoticeMsgDTO.getAccount());
            if (success) {
                CommonResult commonResult = devOpsPermissionApi.submitUserRole(oaApprovedNoticeMsgDTO.getAccount());
                log.info("devops add user role -> result: {}", JacksonUtils.toJsonString(commonResult));
                if (sendEmailNoticeOpenDevopsPermission) {
                    mailManager.oaApprovedNotice(oaApprovedNoticeMsgDTO.getAccount(), oaApprovedNoticeTos);
                }
            }
        } catch (Exception e) {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            log.error("[order-analysis][oa-approved][async-task] fail. msgBody:{}", msgBody, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

//    @Override
//    public void onMessage(List<RocketMessageExt<OaApprovedNoticeMsgDTO>> list, Object o) {
//        for (RocketMessageExt<OaApprovedNoticeMsgDTO> rocketMessageExt : list) {
//            final OaApprovedNoticeMsgDTO oaApprovedNoticeMsgDTO = rocketMessageExt.getBody();
//            try {
//                final boolean success = dataPermissionService.dealOaApprovedDataPermission(oaApprovedNoticeMsgDTO.getAcId(), oaApprovedNoticeMsgDTO.getAccount());
//                if (success) {
//                    CommonResult commonResult = devOpsPermissionApi.submitUserRole(oaApprovedNoticeMsgDTO.getAccount());
//                    log.info("devops add user role -> result: {}", JacksonUtils.toJsonString(commonResult));
//                    if (sendEmailNoticeOpenDevopsPermission) {
//                        mailManager.oaApprovedNotice(oaApprovedNoticeMsgDTO.getAccount(), oaApprovedNoticeTos);
//                    }
//                }
//            } catch (Exception e) {
//                log.error("[order-analysis][oa-approved][async-task] fail. ", e);
//                throw new BizRuntimeException(CodeEnum.OA_APPROVED_SAVE_DATABASE_ERROR);
//            }
//        }
//    }

}
