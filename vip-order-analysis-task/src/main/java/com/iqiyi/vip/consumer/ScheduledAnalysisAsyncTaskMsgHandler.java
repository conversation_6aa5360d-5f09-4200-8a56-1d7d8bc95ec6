package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerSingly;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;

import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.task.ScheduleAnalysisTask;

/**
 * <AUTHOR>
 * @className AnalysisAsyncTaskConsumer
 * @description
 * @date 2022/6/6
 **/
@Component
@Slf4j
public class ScheduledAnalysisAsyncTaskMsgHandler extends MessageListenerSingly {

    @Resource
    private ScheduleAnalysisTask scheduleAnalysisTask;
    @Resource(name = "scheduleTaskThreadPoolExecutor")
    private ExecutorService scheduleTaskThreadPoolExecutor;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(MessageExt messageExt, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        AnalysisTaskExecuteDTO analysisTaskExecuteDTO = JSON.parseObject(msgBody, AnalysisTaskExecuteDTO.class);
        log.info("scheduledAnalysisTaskExecuteDTO : {}", analysisTaskExecuteDTO);
        try {
            scheduleTaskThreadPoolExecutor.execute(RunnableWrapper.of(() -> {
                try {
                    scheduleAnalysisTask.processAsync(analysisTaskExecuteDTO);
                } catch (Exception e) {
                    log.error("ScheduledAnalysisAsyncTaskMsgHandler has exception, msgBody:{}", msgBody, e);
                    throw new RuntimeException(e);
                }
            }));
        } catch (Exception e) {
            log.error("ScheduledAnalysisAsyncTaskMsgHandler has exception, msgBody:{}", msgBody, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
