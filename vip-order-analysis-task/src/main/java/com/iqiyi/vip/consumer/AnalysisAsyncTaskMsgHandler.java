package com.iqiyi.vip.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerSingly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import com.iqiyi.vip.constants.MQSerializerConstants;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.task.CommonAnalysisTask;

/**
 * <AUTHOR>
 * @className AnalysisAsyncTaskConsumer
 * @description
 * @date 2022/6/6
 **/
@Component
@Slf4j
public class AnalysisAsyncTaskMsgHandler extends MessageListenerSingly {

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(MessageExt messageExt, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        AnalysisTaskExecuteDTO analysisTaskExecuteDTO = MQSerializerConstants.ASYNC_TASK_SERIALIZER.deserialize(messageExt.getBody());
        log.info("analysisTaskExecuteDTO : {}", analysisTaskExecuteDTO);
        try {
            new Thread(() -> {
                try {
                    commonAnalysisTask.processAsync(analysisTaskExecuteDTO);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).start();
        } catch (Exception e) {
            String msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            log.error("AnalysisAsyncTaskConsumer has exception, msgBody:{}", msgBody, e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
