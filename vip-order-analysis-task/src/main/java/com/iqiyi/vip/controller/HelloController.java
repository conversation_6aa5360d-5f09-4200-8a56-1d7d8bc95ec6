package com.iqiyi.vip.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

import com.iqiyi.vip.job.DataCheckJob;

/**
 * HelloController
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class HelloController {

    @Resource
    private DataCheckJob dataCheckJob;

    @GetMapping(value = "/")
    public String helloWorld() {
        log.debug("Hello World!");
        return "vip-order-analysis-task is ok";
    }


    @GetMapping(value = "/test")
    public String test() throws Exception {
        HashMap<Integer, Boolean> map = new HashMap<>();
        dataCheckJob.countAndFeeCheckJobHandler();
        System.out.println(map);
        return "hello";
    }

}
