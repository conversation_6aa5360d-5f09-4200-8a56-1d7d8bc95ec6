package com.iqiyi.vip.job;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.validator.AvailableDateValidator;

/**
 * <AUTHOR>
 * @className UpdateAvailableDateJob
 * @description
 * @date 2024/2/26
 **/
@Component
@Slf4j
public class UpdateAvailableDateJob {

    @Resource
    private AvailableDateValidator availableDateValidator;

    @Job("updateAvailableDateJob")
    public void simpleDataCheckJobHandler() {
        log.info("updateAvailableDateJob start at:{}", DateUtil.now());
        HashMap<Integer, Boolean> resultMap = new HashMap<>();
        availableDateValidator.validate(resultMap);
    }
}
