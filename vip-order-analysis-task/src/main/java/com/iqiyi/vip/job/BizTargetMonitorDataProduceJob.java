package com.iqiyi.vip.job;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.api.QiyiJobApi;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.BizTargetMonitorConfigDO;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigCondition;
import com.iqiyi.vip.dto.target.BaseQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.BusinessTypeEnum;
import com.iqiyi.vip.enums.OffsetTimeUnit;
import com.iqiyi.vip.mq.TaskMsgSender;
import com.iqiyi.vip.service.BizTargetMonitorConfigService;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtils;
import com.iqiyi.vip.utils.TaskUtil;

/**
 * 业务指标监控数据生成任务
 *
 * @author: guojing
 * @date: 2025/3/6 22:44
 */
@Slf4j
@Component
public class BizTargetMonitorDataProduceJob {

    @Resource
    private BizTargetMonitorConfigService bizTargetMonitorConfigService;
    @Resource
    private AnalysisTargetRepository analysisTargetRepository;
    @Resource
    private AnalysisConditionRepository analysisConditionRepository;
    @Resource
    private AnalysisTaskRepository analysisTaskRepository;
    @Resource
    private TaskMsgSender taskMsgSender;

    @Job(QiyiJobApi.BIZ_TARGET_MONITOR_DATA_PRODUCT_JOB_HANDLER)
    public void bizTargetMonitorDataProduceHandler() throws Exception {
        Long jobId = JobHelper.getJobId();
        String jobParam = JobHelper.getJobParam();
        log.info("Start execute BizTargetMonitorDataProduceJob, jobId:{}, jobParam:{}", jobId, jobParam);
        JobHelper.log("Start execute BizTargetMonitorDataProduceJob, jobId:{}, jobParam:{}", jobId, jobParam);
        StopWatch stopWatch = StopWatch.createStarted();
        if (StringUtils.isBlank(jobParam)) {
            handleJobSuccess(jobId, jobParam, "jobParam is null", stopWatch);
            return;
        }
        Map<String, Object> paramMap = parseJobParam(jobId, jobParam);
        if (MapUtils.isEmpty(paramMap)) {
            return;
        }
        Integer monitorConfigId = MapUtils.getInteger(paramMap, "monitorConfigId");
        if (monitorConfigId == null) {
            handleJobSuccess(jobId, jobParam, "param: monitorConfigId not exists", stopWatch);
            return;
        }
        BizTargetMonitorConfigDO bizTargetMonitorConfigDO = bizTargetMonitorConfigService.getDetailById(monitorConfigId);
        if (bizTargetMonitorConfigDO == null || Objects.equals(bizTargetMonitorConfigDO.getStatus(), 0)) {
            handleJobSuccess(jobId, jobParam, "BizTargetMonitorConfig not exists", stopWatch);
            return;
        }

        String targetCode = bizTargetMonitorConfigDO.getTarget();
        List<BizTargetMonitorConfigCondition> conditions = bizTargetMonitorConfigDO.getConditions();
        List<String> conditionKeys = conditions.stream().map(BizTargetMonitorConfigCondition::getKey).collect(Collectors.toList());
        AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(targetCode);
        List<AnalysisCondition> analysisConditions = analysisConditionRepository.batchSelectByCode(conditionKeys);
        if (analysisTarget == null || CollectionUtils.isEmpty(analysisConditions)) {
            handleJobSuccess(jobId, jobParam, "BizTargetMonitorConfig not exist", stopWatch);
            return;
        }

        int execFrequencySeconds = OffsetTimeUnit.toSeconds(bizTargetMonitorConfigDO.getExecFrequency());
        LocalDateTime endDateTime = LocalDateTime.now().withSecond(0);
        LocalDateTime startDateTime = endDateTime.minusSeconds(execFrequencySeconds);
        long payEndTime = endDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long payStartTime = startDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        BaseQueryDTO queryDTO = buildQueryDTO(bizTargetMonitorConfigDO, payStartTime, payEndTime);
        String taskMD5 = TaskUtil.getTaskMD5(queryDTO);
        AnalysisTaskDO task = analysisTaskRepository.getScheduledTaskByUniqueIdentification(taskMD5);
        if (task != null && task.taskExist()) {
            handleJobSuccess(jobId, jobParam, "task already exist", stopWatch);
            return;
        }

        AnalysisTaskDO analysisTaskDO = AnalysisTaskDO.initializeTaskDO(queryDTO, taskMD5, AnalysisTaskSourceEnum.SCHEDULED);
        analysisTaskRepository.addScheduledAnalysisTask(analysisTaskDO);
        // 查询条件、维度、指标mq发放
        AnalysisTaskExecuteDTO taskExecuteDTO = AnalysisTaskExecuteDTO.initializeTaskExecuteDO(queryDTO, taskMD5, analysisTaskDO.getId(), AnalysisTaskSourceEnum.SCHEDULED);
        try {
            taskMsgSender.sendScheduledAsyncTaskMsg(taskExecuteDTO);
        } catch (Exception e) {
            log.error("BizTargetMonitorDataProduceJob send msg has exception, monitorConfigId:{}, taskExecuteDTO:{}", monitorConfigId, JacksonUtils.toJsonString(taskExecuteDTO), e);
            throw e;
        }

        handleJobSuccess(jobId, jobParam, "analysis task success submit exec", stopWatch);
    }

    private Map<String, Object> parseJobParam(Long jobId, String jobParam) {
        try {
            return Stream.of(jobParam.split("&"))
                .filter(pair -> pair.contains("="))
                .map(pair -> pair.split("=", 2))
                .collect(Collectors.toMap(
                    keyValue -> keyValue[0],
                    keyValue -> keyValue[1],
                    (existing, replacement) -> replacement,
                    HashMap::new
                ));
        } catch (Exception e) {
            String errorMsg = "BizTargetMonitorDataProduceJob parse jobParam has exception";
            log.error("{}, jobId:{}, jobParam:{}", errorMsg, jobId, jobParam, e);
            JobHelper.log("{}, jobId:{}, jobParam:{}", errorMsg, jobId, jobParam);
            JobHelper.handleFail(errorMsg);
            return null;
        }
    }

    private BaseQueryDTO buildQueryDTO(BizTargetMonitorConfigDO monitorConfigDO, long payStartTime, long payEndTime
    ) {
        Map<String, List<Object>> conditionParamMap = monitorConfigDO.getConditions().stream()
            .collect(Collectors.toMap(
                BizTargetMonitorConfigCondition::getKey, 
                BizTargetMonitorConfigCondition::getValue
            ));
            
        conditionParamMap.put("payStartTime", Collections.singletonList(payStartTime));
        conditionParamMap.put("payEndTime", Collections.singletonList(payEndTime));
        
        String taskName = monitorConfigDO.getName() + "_" + DateUtils.currentDateTimeStr(DateUtils.yyyyMMddHHmmss_FORMAT);
        
        BaseQueryDTO queryDTO = new BaseQueryDTO();
        queryDTO.setTaskName(taskName);
        queryDTO.setThemeType(monitorConfigDO.getThemeType());
        queryDTO.setTargetCodes(Collections.singletonList(monitorConfigDO.getTarget()));
        queryDTO.setConditionParamMap(conditionParamMap);
        queryDTO.setDimensionCodes(monitorConfigDO.getDimensions());
        queryDTO.setOperator(monitorConfigDO.getCreateOpr());
        queryDTO.setBusinessTypeId(BusinessTypeEnum.BASIC_ANALYSIS.getCode());
        ConditionParamContext paramContext = ConditionParamContext.builder()
            .paramMap(queryDTO.getConditionParamMap()).build();
        queryDTO.setConditionParamContext(paramContext);
        
        return queryDTO;
    }

    private void handleJobSuccess(Long jobId, String jobParam, String reason, StopWatch stopWatch) {
        String message = "BizTargetMonitorDataProduceJob finished[" + reason + "], jobId:{}, jobParam:{}, cost:{}ms";
        log.info(message, jobId, jobParam, stopWatch.getTime());
        JobHelper.log(message, jobId, jobParam, stopWatch.getTime());
        JobHelper.handleSuccess();
    }

}
