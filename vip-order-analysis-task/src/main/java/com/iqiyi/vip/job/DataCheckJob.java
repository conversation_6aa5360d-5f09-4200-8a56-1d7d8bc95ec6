package com.iqiyi.vip.job;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.mail.MailManager;
import com.iqiyi.vip.uitls.model.TableMailContent;
import com.iqiyi.vip.validator.DailyDataCheckValidator;
import com.iqiyi.vip.validator.OrderCountAndFeeValidator;
import com.iqiyi.vip.validator.chain.ValidationChainManager;
import com.iqiyi.vip.validator.chain.ValidationContext;

/**
 * <AUTHOR>
 * @className DataCheckJob
 * @description
 * @date 2022/10/27
 **/
@Component
@Slf4j
public class DataCheckJob {

    @Resource
    private OrderCountAndFeeValidator countAndFeeValidator;

    @Resource
    private DailyDataCheckValidator dailyDataCheckValidator;

    @Resource
    private ValidationChainManager validationChainManager;

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit;

    @Value("${order.count.fee.querySql}")
    private String orderCountAndSumFeeSql;

    @Resource
    private MailManager mailManager;

    @Value("${data.check.result.mail.address:<EMAIL>}")
    private String mailAddressee;

    @Value("${hour.data.check.mail.title:【天眼数据质量校验】【每小时校验：%s】}")
    private String hourlyDataCheckJobMailTitle;

    @Value("${hour.data.check.mail.title:【天眼数据质量校验】【每天校验：%s】}")
    private String dailyDataCheckJobMailTitle;

    /**
     * 简单任务示例（Bean模式）
     */
    @Job("simpleDataCheckJobHandler")
    public void simpleDataCheckJobHandler() throws Exception {
        log.info("simpleDataCheckJobHandler start at:{}", DateUtil.now());
        HashMap<Integer, Boolean> resultMap = new HashMap<>();
        dailyDataCheckValidator.validate(resultMap);

        String date = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        String actTableComment = "订单数据质量校验：" + date;
        List<String> actTableTitles = Lists.newArrayList("校验id", "校验内容", "校验结果");
        TableMailContent tableMailContent = new TableMailContent(actTableComment, actTableTitles, buildTableContentsByCheckResult(resultMap));
        tableMailContent.setNeedSequenceCol(false);
        mailManager.sendMailWithTable(mailAddressee, String.format(dailyDataCheckJobMailTitle, date), Collections.singletonList(tableMailContent));
        log.info("simpleDataCheckJobHandler report send result:{}", tableMailContent);
    }


    /**
     * 简单任务示例（Bean模式）- 使用责任链模式优化
     */
    @Job("countAndFeeCheckJobHandler")
    public void countAndFeeCheckJobHandler() throws Exception {
        log.info("countAndFeeCheckJobHandler start at:{}", DateUtil.now());
        HashMap<Integer, Boolean> resultMap = new HashMap<>();

        // 构建校验上下文
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        ValidationContext context = ValidationContext.builder()
            .resultMap(resultMap)
            .dt(dt)
            .taskId(-1L)
            .offset(offset)
            .orderCountGapLimit(orderCountGapLimit)
            .orderCountAndSumFeeSql(orderCountAndSumFeeSql)
            .build();

        // 执行责任链校验
        validationChainManager.executeValidationChain(context);

        String hourTime = DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH");
        String actTableComment = "订单总数_订单金额校验：" + dt;
        List<String> actTableTitles = Lists.newArrayList("校验id", "校验内容", "校验结果");
        TableMailContent tableMailContent = new TableMailContent(actTableComment, actTableTitles, buildTableContentsByCheckResult(resultMap));
        tableMailContent.setNeedSequenceCol(false);
        mailManager.sendMailWithTable(mailAddressee, String.format(hourlyDataCheckJobMailTitle, hourTime), Collections.singletonList(tableMailContent));
        log.info("countAndFeeCheckJobHandler report send result:{}", tableMailContent);
    }

    private List<List<Object>> buildTableContentsByCheckResult(HashMap<Integer, Boolean> resultMap) {
        List<List<Object>> tableContents = Lists.newArrayList();
        Map<Integer, String> dataCheckItemMap = DataCheckEnum.getAllToMap();
        for (Entry<Integer, Boolean> entry : resultMap.entrySet()) {
            List<Object> tmpList = Lists.newArrayList();
            Integer caseId = entry.getKey();
            tmpList.add(caseId);
            tmpList.add(dataCheckItemMap.get(caseId));
            tmpList.add(MapUtils.getBoolean(resultMap, caseId, true) ? "校验通过" : "校验失败");
            tableContents.add(tmpList);
        }
        return tableContents;
    }
}
