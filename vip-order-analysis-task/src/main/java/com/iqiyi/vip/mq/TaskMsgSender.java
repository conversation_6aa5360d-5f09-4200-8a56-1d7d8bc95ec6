package com.iqiyi.vip.mq;

import org.apache.rocketmq.client.producer.CloudMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/4/18 10:05
 */
@Component
public class TaskMsgSender extends MsgSender {

    @Resource
    private CloudMQProducer scheduledAsyncTaskMsgProducer;

    public boolean sendScheduledAsyncTaskMsg(AnalysisTaskExecuteDTO msgContent) {
        String msgKey = msgContent.getTaskId() != null ? msgContent.getTaskId().toString() : null;
        byte[] msgBytes = JacksonUtils.toJsonBytes(msgContent);
//        byte[] msgBytes = MQSerializerConstants.ASYNC_TASK_SERIALIZER.serialize(msgContent);
        Message msg = new Message(scheduledAsyncTaskMsgProducer.getTopic(), msgBytes);
        if (msgKey != null) {
            msg.setKeys(msgKey);
        }
        return doSend(scheduledAsyncTaskMsgProducer, msg);
    }

}
