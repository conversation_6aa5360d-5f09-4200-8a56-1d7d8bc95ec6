package com.iqiyi.vip.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.CloudMQPushConsumer;
import org.apache.rocketmq.client.producer.CloudMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

import com.iqiyi.vip.consumer.AnalysisAsyncTaskMsgHandler;
import com.iqiyi.vip.consumer.OaApprovedAsyncTaskMsgHandler;
import com.iqiyi.vip.consumer.ScheduledAnalysisAsyncTaskMsgHandler;

/**
 * <AUTHOR>
 * @className RocketMQConfig
 * @description
 * @date 2022/6/6
 **/
@Configuration
@Slf4j
public class TaskRocketMQConfig {

    @Resource
    private AnalysisAsyncTaskMsgHandler analysisAsyncTaskMsgHandler;
    @Resource
    private ScheduledAnalysisAsyncTaskMsgHandler scheduledAnalysisAsyncTaskMsgHandler;
    @Resource
    private OaApprovedAsyncTaskMsgHandler oaApprovedAsyncTaskMsgHandler;

//    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public CloudMQPushConsumer asyncTaskConsumer() {
        CloudMQPushConsumer cloudMQPushConsumer = CloudMQPushConsumer.fromCloudConfig("analysis_async_task_consumer");
        cloudMQPushConsumer.registerMessageListener(analysisAsyncTaskMsgHandler);
        return cloudMQPushConsumer;
    }

//    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public CloudMQPushConsumer oaApprovedAsyncTaskConsumer() {
        CloudMQPushConsumer cloudMQPushConsumer = CloudMQPushConsumer.fromCloudConfig("oaApproved_async_task_consumer");
        cloudMQPushConsumer.registerMessageListener(oaApprovedAsyncTaskMsgHandler);
        return cloudMQPushConsumer;
    }

//    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public CloudMQPushConsumer scheduledAsyncTaskConsumer() {
        CloudMQPushConsumer cloudMQPushConsumer = CloudMQPushConsumer.fromCloudConfig("scheduled_analysis_async_task_consumer");
        cloudMQPushConsumer.registerMessageListener(scheduledAnalysisAsyncTaskMsgHandler);
        return cloudMQPushConsumer;
    }

    @Bean
    public CloudMQProducer scheduledAsyncTaskMsgProducer() {
        return CloudMQProducer.fromCloudConfig("scheduled_analysis_async_task_producer");
    }

}
