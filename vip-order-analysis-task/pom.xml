<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.example</groupId>
    <artifactId>vip-order-analysis</artifactId>
      <version>1.8.77</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>vip-order-analysis-task</artifactId>
  <packaging>jar</packaging>

  <dependencies>
      <dependency>
          <groupId>org.example</groupId>
          <artifactId>vip-order-analysis-app</artifactId>
          <version>1.8.77</version>
      </dependency>
      <dependency>
          <groupId>com.iqiyi.vip</groupId>
          <artifactId>spring-boot-starter-qiyi-job</artifactId>
      </dependency>
      <!--springboot项目都不用引入版本号-->
      <dependency>
          <groupId>org.springframework.retry</groupId>
          <artifactId>spring-retry</artifactId>
      </dependency>
      <!--还是需要aop的支持的-->
      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-aspects</artifactId>
      </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <fork>true</fork>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
