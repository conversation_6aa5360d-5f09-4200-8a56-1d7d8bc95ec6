package com.iqiyi.vip.service;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.domain.repository.CompetitorUiRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPageListParam;
import com.iqiyi.vip.dto.competitor.CompetitorUiDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorUserStatusListParam;

/**
 * @author: guojing
 * @date: 2024/10/30 15:10
 */
@Service
public class CompetitorUiService {

    @Resource
    private CompetitorUiRepository competitorUiRepository;

    public List<String> clientVersionList() {
        return competitorUiRepository.clientVersionList();
    }

    public CompetitorDateRangeDTO dateRange() {
        return competitorUiRepository.dateRange();
    }

    public List<String> brandsList() {
        return competitorUiRepository.brandsList();
    }

    public List<String> pageList(CompetitorPageListParam param) {
        return competitorUiRepository.pageList(param.getBrand(), param.getDate());
    }

    public List<String> userStatusList(CompetitorUserStatusListParam param) {
        return competitorUiRepository.userStatusList(param.getBrand(), param.getDate());
    }

    public boolean save(CompetitorUiSaveDTO param) {
        competitorUiRepository.save(param);
        return true;
    }

    public List<CompetitorUiDTO> search(CompetitorUiSearchParam param) {
        return competitorUiRepository.search(param);
    }


}
