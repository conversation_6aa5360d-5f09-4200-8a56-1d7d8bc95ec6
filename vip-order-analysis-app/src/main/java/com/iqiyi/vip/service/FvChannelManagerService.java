package com.iqiyi.vip.service;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.repository.FvStructureRepository;
import com.iqiyi.vip.dto.condition.FvChannelPair;
import com.iqiyi.vip.dto.condition.FvChannelReqDTO;
import com.iqiyi.vip.dto.condition.FvChannelVO;
import com.iqiyi.vip.enums.LabelEnum;

/**
 * <AUTHOR>
 * @className FvChannelService
 * @description
 * @date 2022/6/13
 **/
@Component
public class FvChannelManagerService {

    @Resource
    private FvStructureRepository fvStructureRepository;

    public List<FvChannelPair> getFvChannelPairsByLevel(Integer level) {
        return fvStructureRepository.getFvChannelsByLevel(level);
    }

    public List<FvChannelPair> getFvByLevelAndTeamId(Integer level, Integer teamId) {
        return fvStructureRepository.getFvByLevelAndTeamId(level, teamId);
    }


    public List<FvChannelVO> buildByFvChannelReqDTO(List<FvChannelReqDTO> fvChannelReqDTOS) {
        if (CollectionUtils.isEmpty(fvChannelReqDTOS) || (fvChannelReqDTOS.size() == 1 && fvChannelReqDTOS.get(0).getId() == null) ) {
            return Collections.emptyList();
        }
        ArrayList<FvChannelVO> channelVOS = new ArrayList<>();
        for (FvChannelReqDTO dto : fvChannelReqDTOS.stream().filter(v -> v.getId() != null).collect(Collectors.toList())) {
            Integer teamId = dto.getTeamId();
            Integer level = dto.getLevel();
            List<FvChannelPair> fvChannelPairs = getFvByLevelAndTeamId(level, teamId);
            FvChannelVO channelVO = new FvChannelVO();
            channelVO.setId(dto.getId());
            channelVO.setTeamId(dto.getTeamId());
            channelVO.setLevel(level);
            FvChannelPair fvChannelPair = fvChannelPairs.stream()
                .filter(fv -> dto.getId().equals(fv.getId()))
                .findFirst().orElse(null);
            String fvName = null;
            if (fvChannelPair != null) {
                if (LabelEnum.L3.getLevel() <= level) {
                    String teamName = fvChannelPair.getTeamName();
                    fvName = String.format("%s_%s(%s)", teamName, fvChannelPair.getName(), fvChannelPair.getId());
                } else {
                    fvName = String.format("%s(%s)", fvChannelPair.getName(), fvChannelPair.getId());
                }
            }
            channelVO.setName(fvName);
            channelVOS.add(channelVO);
        }
        return channelVOS;
    }

}
