package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.BusinessCondition;
import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.BusinessConditionRepository;
import com.iqiyi.vip.domain.repository.ConditionCascadeRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.condition.BusinessConditionDTO;
import com.iqiyi.vip.dto.condition.BusinessConditionUpdateDTO;
import com.iqiyi.vip.dto.condition.ConditionAlias;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.utils.CloudConfigUtils;

/**
 * 分析类型下的条件管理
 *
 * @author: linpeihui
 * @createTime: 2023/08/11
 */
@Component
@Slf4j
public class BusinessConditionService {
    @Resource
    private BusinessConditionRepository businessConditionRepository;
    @Resource
    private AnalysisConditionRepository analysisConditionRepository;
    @Resource
    private ConditionCascadeRepository conditionCascadeRepository;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult sort(BusinessConditionUpdateDTO updateDTO) {
        CommonResult commonResult = validateSort(updateDTO);
        if (!commonResult.getCode().equals(CommonResult.SUCCESS_CODE)) {
            return commonResult;
        }
        businessConditionRepository.deleteByThemeAndBusiness(updateDTO.getThemeId(), updateDTO.getBusinessId());
        List<BusinessCondition> businessConditionList = new ArrayList<>();
        for (BusinessConditionUpdateDTO.ConditionSort conditionSort : updateDTO.getConditionList()) {
            BusinessCondition businessCondition = new BusinessCondition(updateDTO.getThemeId(), updateDTO.getBusinessId(), updateDTO.getOperator(), conditionSort);
            businessConditionList.add(businessCondition);
        }
        businessConditionRepository.batchInsert(businessConditionList);
        return CommonResult.success();
    }

    public List<BusinessConditionDTO> list(BusinessConditionUpdateDTO updateDTO) {
        List<BusinessConditionDTO> businessConditionDTOList = new ArrayList<>();
        List<BusinessCondition> businessConditionList = businessConditionRepository.selectByThemeAndBusiness(updateDTO.getThemeId(), updateDTO.getBusinessId());
        if (CollectionUtils.isEmpty(businessConditionList)) {
            return businessConditionDTOList;
        }
        List<Long> conditionIdList = businessConditionList.stream().map(BusinessCondition::getConditionId).collect(Collectors.toList());
        List<AnalysisCondition> analysisConditionList = analysisConditionRepository.selectByIdList(conditionIdList);
        if (ThemeTypeEnum.AUTO_RENEW_THEME.getCode().equals(updateDTO.getThemeId().intValue())) {
            analysisConditionList.forEach(c -> c.setName(StringUtils.replace(c.getName(), "支付渠道", "签约渠道")));
        }
        if (CollectionUtils.isNotEmpty(analysisConditionList)) {
            Map<Long, AnalysisCondition> conditionMap = analysisConditionList.stream()
                .collect(Collectors.toMap(AnalysisCondition::getId, Function.identity()));
            for (BusinessCondition businessCondition : businessConditionList) {
                AnalysisCondition condition = conditionMap.get(businessCondition.getConditionId());
                if (!CloudConfigUtils.hasConditionAccess(condition.getCode(), updateDTO.getOperator())) {
                    continue;
                }
                businessConditionDTOList.add(buildDto(businessCondition, condition));
            }
        }
        return businessConditionDTOList;
    }

    private BusinessConditionDTO buildDto(BusinessCondition businessCondition, AnalysisCondition condition) {
        BusinessConditionDTO businessConditionDTO = BusinessConditionDTO.builder()
                .id(businessCondition.getId())
                .themeId(businessCondition.getThemeId())
                .businessId(businessCondition.getBusinessId())
                .conditionId(businessCondition.getConditionId())
                .sort(businessCondition.getSort())
                .operator(businessCondition.getOperator())
                .conditionCode(condition.getCode())
                .conditionName(condition.getName())
                .conditionType(condition.getType())
                .conditionRequired(condition.getRequired())
                .conditionDefaultPrompt(condition.getDefaultPrompt())
                .conditionDataLoadAddr(condition.getDataLoadAddr())
                .conditionSelectAll(condition.getSelectAll())
                .conditionHasTipsIcon(condition.getHasTipsIcon())
                .conditionTips(condition.getTips())
                .conditionMultiChoice(condition.getMultiChoice())
                .createTime(businessCondition.getCreateTime())
            .updateTime(businessCondition.getUpdateTime())
            .fieldType(condition.getFieldType())
            .defaultValue(condition.getDefaultValue())
            .build();
        if (CollectionUtils.isNotEmpty(condition.getAliases())) {
            Map<Long, String> aliasMap = condition.getAliases().stream()
                .collect(Collectors.toMap(ConditionAlias::getThemeType, ConditionAlias::getName));
            businessConditionDTO.setConditionName(aliasMap.getOrDefault(businessCondition.getThemeId(), condition.getName()));
        }
        return businessConditionDTO;
    }

    private CommonResult validateSort(BusinessConditionUpdateDTO updateDTO) {
        if (CollectionUtils.isEmpty(updateDTO.getConditionList())) {
            return CommonResult.success();
        }
        List<ConditionCascade> conditionCascadeList = conditionCascadeRepository.list(updateDTO.getThemeId().intValue(), updateDTO.getBusinessId().intValue());
        if (CollectionUtils.isEmpty(conditionCascadeList)) {
            return CommonResult.success();
        }
        Map<Long, Long> cascadeMap = conditionCascadeList.stream().collect(Collectors.toMap(ConditionCascade::getCurrentConditionId, ConditionCascade::getNextConditionId));
        Map<Long, Integer> conditionSortMap = updateDTO.getConditionList().stream().collect(Collectors.toMap(BusinessConditionUpdateDTO.ConditionSort::getId, BusinessConditionUpdateDTO.ConditionSort::getSort));
        Iterator<Map.Entry<Long, Integer>> entries = conditionSortMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<Long, Integer> entry = entries.next();
            Long conditionId = entry.getKey();
            Integer curSort = entry.getValue();
            Long nextConditionId = cascadeMap.get(conditionId);
            if (nextConditionId != null) {
                Integer nextSort = conditionSortMap.get(nextConditionId);
                if (nextSort != null && nextSort < curSort) {
                    AnalysisCondition curCondition = analysisConditionRepository.selectById(conditionId);
                    AnalysisCondition nextCondition = analysisConditionRepository.selectById(nextConditionId);
                    return new CommonResult(CodeEnum.CONFIG_ERROR.getCode(), nextCondition.getName() + " 不能排在 " + curCondition.getName() + " 前面");
                }
            }
        }
        return CommonResult.success();
    }

}
