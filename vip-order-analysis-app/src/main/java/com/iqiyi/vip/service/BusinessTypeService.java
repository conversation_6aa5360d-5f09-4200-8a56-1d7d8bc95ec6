package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.entity.AnalysisBusinessType;
import com.iqiyi.vip.domain.repository.AnalysisBusinessTypeRepository;
import com.iqiyi.vip.dto.base.ListResult;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@Component
@Slf4j
public class BusinessTypeService {

    @Resource
    private AnalysisBusinessTypeRepository analysisBusinessTypeRepository;

    public ListResult<AnalysisBusinessType> listByThemeType(Integer themeType) {
        return ListResult.createSuccess(analysisBusinessTypeRepository.listByThemeType(themeType));
    }
}
