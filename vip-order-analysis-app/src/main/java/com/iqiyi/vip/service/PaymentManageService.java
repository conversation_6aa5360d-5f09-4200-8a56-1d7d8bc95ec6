package com.iqiyi.vip.service;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.domain.entity.PaymentChannel;
import com.iqiyi.vip.domain.entity.PaymentType;
import com.iqiyi.vip.domain.repository.PaymentChannelRepository;
import com.iqiyi.vip.domain.repository.PaymentTypeRepository;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.condition.ConditionPair;

/**
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@Component
@Slf4j
public class PaymentManageService {

    @Resource
    private PaymentChannelRepository paymentChannelRepository;

    @Resource
    private PaymentTypeRepository paymentTypeRepository;

    @Resource
    private CloudConfig cloudConfig;

    @Value("${hign.priority.channel:1,2,3,8,9,33,34,47,48,49}")
    private String hignPriorityPayChannel;

    public ListResult<PaymentChannel> listChannelPayTypes() {
        List<PaymentChannel> paymentChannels = paymentChannelRepository.selectAll()
            .stream()
            .sorted(Comparator.comparing(PaymentChannel::getId))
            .collect(Collectors.toList());
        Set<Long> hignPriorityPayChannelIds = getHignPriorityPayChannelIds();
        ArrayList<PaymentChannel> channels = new ArrayList<>();
        for (PaymentChannel paymentChannel : paymentChannels) {
            if (hignPriorityPayChannelIds.contains(paymentChannel.getId())) {
                channels.add(paymentChannel);
            }
        }
        for (PaymentChannel paymentChannel : paymentChannels) {
            if (!channels.contains(paymentChannel)) {
                channels.add(paymentChannel);
            }
        }
        return ListResult.createSuccess(channels);
    }

    public ListResult<ConditionPair> listChannels() {
        List<PaymentChannel> paymentChannels = paymentChannelRepository.selectAll()
            .stream()
            .sorted(Comparator.comparing(PaymentChannel::getId))
            .collect(Collectors.toList());
        Set<Long> ignoreChannelIds = getIgnoreChannelIds();
        ArrayList<ConditionPair> channels = new ArrayList<>();
        Set<Long> hignPriorityPayChannelIds = getHignPriorityPayChannelIds();
        for (PaymentChannel paymentChannel : paymentChannels) {
            if (hignPriorityPayChannelIds.contains(paymentChannel.getId()) && !ignoreChannelIds.contains(paymentChannel.getId())) {
                ConditionPair pair = ConditionPair.builder().code(paymentChannel.getId()).desc(paymentChannel.getDescription()).build();
                channels.add(pair);
            }
        }
        for (PaymentChannel paymentChannel : paymentChannels) {
            ConditionPair pair = ConditionPair.builder().code(paymentChannel.getId()).desc(paymentChannel.getDescription()).build();
            if (!channels.contains(pair) && !ignoreChannelIds.contains(paymentChannel.getId())) {
                channels.add(pair);
            }
        }
        return ListResult.createSuccess(channels);
    }

    public ListResult<PaymentType> listPaymentTypes(Long payChannel) {
        return ListResult.createSuccess(paymentTypeRepository.selectByChannel(payChannel));
    }

    public ListResult<PaymentChannel> channelPayTypesList() {
        List<PaymentChannel> channels = paymentChannelRepository.selectAll().stream()
            .sorted(Comparator.comparing(PaymentChannel::getId))
            .collect(Collectors.toList());
        ArrayList<PaymentChannel> finalChannels = new ArrayList<>();
        Set<Long> hignPriorityPayChannelIds = getHignPriorityPayChannelIds();
        for (PaymentChannel paymentChannel : channels) {
            if (hignPriorityPayChannelIds.contains(paymentChannel.getId())) {
                finalChannels.add(paymentChannel);
            }
        }
        for (PaymentChannel paymentChannel : channels) {
            if (!finalChannels.contains(paymentChannel)) {
                finalChannels.add(paymentChannel);
            }
        }
        List<PaymentType> types = paymentTypeRepository.selectAll();
        finalChannels.forEach(c -> {
            List<PaymentType> filterTypes = types.stream().filter(t -> Objects.equals(t.getPayChannel(), c.getId())).collect(Collectors.toList());
            c.setPaymentTypes(filterTypes);
        });
        return ListResult.createSuccess(finalChannels);
    }

    private Set<Long> getIgnoreChannelIds() {
        Set<Long> result = Splitter.on(",").splitToList(cloudConfig.getProperty("ignore.channel.ids", ""))
            .stream().map(Long::valueOf)
            .collect(Collectors.toSet());
        return CollectionUtils.isNotEmpty(result) ? result : new HashSet<>();
    }

    private Set<Long> getHignPriorityPayChannelIds() {
        Set<Long> result = Splitter.on(",").splitToList(hignPriorityPayChannel).stream().map(Long::valueOf).collect(Collectors.toSet());
        return CollectionUtils.isNotEmpty(result) ? result : new HashSet<>();
    }
}
