package com.iqiyi.vip.service;

import com.google.common.collect.Lists;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisDimensionGroup;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.domain.entity.GroupDimensionDTO;
import com.iqiyi.vip.domain.factory.AnalysisDimensionFactory;
import com.iqiyi.vip.domain.repository.AnalysisDimensionGroupRepository;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.ConfigChangeLogRepository;
import com.iqiyi.vip.dto.base.*;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionPageQryDTO;
import com.iqiyi.vip.dto.dimension.DimensionUpdateDTO;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConfigTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;
import com.iqiyi.vip.utils.CloudConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Component
@Slf4j
public class DimensionMangerService implements ConfigVersionProcessor {

    @Resource
    private AnalysisDimensionRepository analysisDimensionRepository;

    @Resource
    private AnalysisDimensionGroupRepository analysisDimensionGroupRepository;

    @Resource
    private ConfigChangeLogRepository configChangeLogRepository;

    @Resource
    private DataPermissionService dataPermissionService;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult save(DimensionUpdateDTO updateDTO) {
        try {
            Integer businessTypeId = updateDTO.getBusinessTypeId();
            Integer themeType = updateDTO.getThemeType();
            if (businessTypeId == null || themeType == null) {
                throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
            }

            AnalysisDimension exist = analysisDimensionRepository.selectByThemeType(updateDTO.getCode(), businessTypeId, themeType);
            if (exist != null) {
                throw new BizRuntimeException(CodeEnum.DUPLICATE);
            }
            AnalysisDimension saveDimension = AnalysisDimensionFactory.getSaveDimension(updateDTO);
            Long newId = analysisDimensionRepository.save(saveDimension);
            saveDimension.setId(newId);
            configChangeLogRepository.save(ConfigChangeLog.buildFrom(saveDimension, DEFAULT_VERSION));
            return CommonResult.success();
        } catch (Exception e) {
            log.error("DimensionMangerService save error:", e);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
    }

    public CommonResult update(DimensionUpdateDTO updateDTO) {
        if (updateDTO.getId() == null) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        AnalysisDimension exist = analysisDimensionRepository.selectById(updateDTO.getId());
        if (exist == null) {
            throw new BizRuntimeException(CodeEnum.NOT_EXIST);
        }
        Integer maxVersion = configChangeLogRepository.getMaxVersion(ConfigTypeEnum.DIMENSION.getType(), exist.getId());
        int newVersion = maxVersion == null ? DEFAULT_VERSION : maxVersion + 1;
        AnalysisDimensionFactory.updateExist(exist, updateDTO, newVersion);
        analysisDimensionRepository.update(exist);
        configChangeLogRepository.save(ConfigChangeLog.buildFrom(exist, newVersion));
        return CommonResult.success();
    }

    public CommonResult delete(Long id) {
        AnalysisDimension dimension = analysisDimensionRepository.selectById(id);
        if (dimension != null) {
            analysisDimensionRepository.deleteById(id);
            configChangeLogRepository.delete(ConfigTypeEnum.DIMENSION.getType(), dimension.getId());
        }
        return CommonResult.success();
    }

    public PageListResult<AnalysisDimension> pageList(AnalysisDimensionPageQryDTO query) {
        PageListResult<AnalysisDimension> analysisDimensionPageListResult = analysisDimensionRepository.selectPageList(query);
        if (analysisDimensionPageListResult.getDataList() != null) {
            analysisDimensionPageListResult.getDataList().stream().forEach(v -> {
                AnalysisDimension dimension = (AnalysisDimension) v;
                dimension.setName(TargetDimensionHandler.retDimensionName(dimension));
//                dimension.setCode(TargetDimensionHandler.removeTransCodeSuffix(TargetDimensionHandler.retDimensionCode(dimension)));
            });
        }
        return analysisDimensionPageListResult;
    }

    public DataResult<AnalysisDimension> detail(Long id) {
        AnalysisDimension dimension = analysisDimensionRepository.selectById(id);
        DataResult result = DataResult.success(dimension);
        return result;
    }

    public ListResult<GroupDimensionDTO> groupList(BaseQry baseQry) {
        List<AnalysisDimensionGroup> groups = analysisDimensionGroupRepository.selectByBusinessType(baseQry.getBusinessTypeId(), baseQry.getThemeType());
        if (CollectionUtils.isEmpty(groups)) {
            return ListResult.createSuccess(null);
        }
        List<AnalysisDimension> dimensions = analysisDimensionRepository.selectAll(baseQry);
        Map<Integer, List<AnalysisDimension>> grouping = dimensions
            .stream()
            .filter(d -> hasDimensionAccess(d.getCode(), baseQry))
            .collect(Collectors.groupingBy(AnalysisDimension::getGroup, Collectors.mapping(v -> {
                v.setName(TargetDimensionHandler.retDimensionName(v)); // 订单分析首页维度展示问题处理 "购买类型|,|选定用户购买类型|,|转移用户购买类型 --> 购买类型"
                return v;
            }, Collectors.toList())));

        List<GroupDimensionDTO> resultList = Lists.newArrayList();
        for (AnalysisDimensionGroup group : groups) {
            List<AnalysisDimension> analysisDimensions = grouping.get(group.getId());
            if (CollectionUtils.isEmpty(analysisDimensions)) {
                continue;
            }
            GroupDimensionDTO dimensionDTO = new GroupDimensionDTO();
            dimensionDTO.setGroupId(group.getId());
            dimensionDTO.setGroupName(group.getName());
            dimensionDTO.setSupportMultiple(group.getSupportMultiple());
            dimensionDTO.setBusinessTypeId(group.getBusinessTypeId());
            dimensionDTO.setThemeType(group.getThemeType());
            dimensionDTO.setDimensions(analysisDimensions);
            resultList.add(dimensionDTO);
        }
        return ListResult.createSuccess(resultList);
    }

    private boolean hasDimensionAccess(String dimensionCode, BaseQry baseQry) {
        return CloudConfigUtils.hasDimensionAccess(dimensionCode, baseQry.getOperator()) || dataPermissionService.ownedHighLevelUser(baseQry);
    }

    @Override
    public Integer getProcessType() {
        return ConfigTypeEnum.DIMENSION.getType();
    }

    @Override
    public CommonResult versionSwitch(VersionSwitchReqDTO reqDTO, ConfigChangeLog destinationChangeLog) {
        Long configId = reqDTO.getConfigId();
        AnalysisDimension dimension = analysisDimensionRepository.selectById(configId);
        if (dimension == null) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), String.format("未找到id：%s的维度模板", configId));
        }
        try {
            AnalysisDimensionFactory.versionSwitch(dimension, destinationChangeLog, reqDTO);
            analysisDimensionRepository.update(dimension);
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.create(CodeEnum.ERROR_PARAM);
        }
    }

    @Override
    public ListResult versionDiff(VersionDiffReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult versionList(VersionListReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult changeList(ChangeListReqDTO reqDTO) {
        return null;
    }
}
