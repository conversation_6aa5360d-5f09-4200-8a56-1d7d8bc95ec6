package com.iqiyi.vip.service;

import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;

/**
 * <AUTHOR>
 * @className ConfigVersionProcessor
 * @description
 * @date 2022/11/25
 **/
public interface ConfigVersionProcessor {

    Integer getProcessType();

    CommonResult versionSwitch(VersionSwitchReqDTO reqDTO, ConfigChangeLog destinationChangeLog);

    CommonResult versionDiff(VersionDiffReqDTO reqDTO);

    CommonResult versionList(VersionListReqDTO reqDTO);

    CommonResult changeList(ChangeListReqDTO reqDTO);

}
