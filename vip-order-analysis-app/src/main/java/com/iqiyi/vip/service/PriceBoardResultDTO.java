package com.iqiyi.vip.service;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@ApiModel(value = "价格看板返回对象")
@AllArgsConstructor
@NoArgsConstructor
public class PriceBoardResultDTO {

    @ApiModelProperty(value = "用户分层，新会员、过期0-365、召回、留存会员（自动续费）、留存会员（非自动续费）")
    private String userStatus;

    @ApiModelProperty(value = "爱奇艺基础会员")
    private BasicMember basicMember;

    @ApiModelProperty(value = "爱奇艺黄金会员")
    private GoldMember goldMember;

    @ApiModelProperty(value = "爱奇艺白金会员")
    private PlatinumMember platinumMember;

    @ApiModelProperty(value = "爱奇艺星钻会员")
    private DiamondMember diamondMember;
}

@Data
@Builder
@ApiModel(value = "基础会员")
class BasicMember {
    @ApiModelProperty(value = "连续包月")
    private String consecutiveMonth;

    @ApiModelProperty(value = "连续包月发生变化")
    private boolean consecutiveMonthChanged;

    @ApiModelProperty(value = "连续包年")
    private String consecutiveYear;

    @ApiModelProperty(value = "连续包年发生变化")
    private boolean consecutiveYearChanged;

    @ApiModelProperty(value = "普通包月")
    private String normalMonth;

    @ApiModelProperty(value = "普通包月发生变化")
    private boolean normalMonthChanged;

    @ApiModelProperty(value = "普通包年")
    private String normalYear;

    @ApiModelProperty(value = "普通包年发生变化")
    private boolean normalYearChanged;


}

@Data
@Builder
@ApiModel(value = "爱奇艺黄金会员")
class GoldMember {
    @ApiModelProperty(value = "连续包月")
    private List<PlatformPrice> consecutiveMonth;
    @ApiModelProperty(value = "连续包季")
    private List<PlatformPrice> consecutiveQuarter;
    @ApiModelProperty(value = "连续包年")
    private List<PlatformPrice> consecutiveYear;
    @ApiModelProperty(value = "普通包月")
    private List<PlatformPrice> normalMonth;
    @ApiModelProperty(value = "普通包季")
    private List<PlatformPrice> normalQuarter;
    @ApiModelProperty(value = "普通包年")
    private List<PlatformPrice> normalYear;
}

@Data
@Builder
@ApiModel(value = "平台价格")
class PlatformPrice {
    @ApiModelProperty(value = "平台")
    private String platform;
    @ApiModelProperty(value = "产品")
    private String product;
    @ApiModelProperty(value = "价格")
    private String price;

    @ApiModelProperty(value = "产品")
    private String lastWeekProduct;
    @ApiModelProperty(value = "价格")
    private String lastWeekPrice;
    @ApiModelProperty(value = "描述")
    private String desc;
    @ApiModelProperty(value = "价格同比上周是否变化")
    private boolean changed;
}

@Data
@Builder
@ApiModel(value = "爱奇艺白金会员")
class PlatinumMember {
    @ApiModelProperty(value = "连续包月")
    private List<PlatformPrice> consecutiveMonth;
    @ApiModelProperty(value = "连续包季")
    private List<PlatformPrice> consecutiveQuarter;
    @ApiModelProperty(value = "连续包年")
    private List<PlatformPrice> consecutiveYear;
    @ApiModelProperty(value = "普通包月")
    private List<PlatformPrice> normalMonth;
    @ApiModelProperty(value = "普通包季")
    private List<PlatformPrice> normalQuarter;
    @ApiModelProperty(value = "普通包年")
    private List<PlatformPrice> normalYear;
}

@Data
@Builder
@ApiModel(value = "爱奇艺星钻会员")
class DiamondMember {

    @ApiModelProperty(value = "连续包月")
    private String consecutiveMonth;

    @ApiModelProperty(value = "连续包年")
    private String consecutiveYear;

    @ApiModelProperty(value = "普通包月")
    private String normalMonth;

    @ApiModelProperty(value = "普通包年")
    private String normalYear;
}