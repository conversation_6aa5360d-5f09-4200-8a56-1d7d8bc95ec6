package com.iqiyi.vip.service;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataCheckResult;
import com.iqiyi.vip.domain.repository.DataCheckResultRepository;

/**
 * <AUTHOR>
 * @className DataCheckResultService
 * @description
 * @date 2022/10/31
 **/
@Component
public class DataCheckResultService {

    @Resource
    private DataCheckResultRepository dataCheckResultRepository;

    public List<DataCheckResult> getDataCheckResult(DataCheckResult dataCheckResultDO) {
         return dataCheckResultRepository.getDataCheckResult(dataCheckResultDO);
    }

    public void saveDataCheckResult(DataCheckResult dataCheckResultDO) {
        dataCheckResultRepository.saveDataCheckResult(dataCheckResultDO);
    }
}
