package com.iqiyi.vip.service.Impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.diagnosis.DiagnosisConditionDTO;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.target.DiagnosisQueryDTO;
import com.iqiyi.vip.dto.target.DownloadDiagnosisDTO;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.target.SimpleQueryDTO;
import com.iqiyi.vip.dto.target.SimpleQueryResultDTO;
import com.iqiyi.vip.memory.DataPermissionMemoryImpl;
import com.iqiyi.vip.repository.DiagnosisRepositoryImpl;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.DiagnosisDimensionService;
import com.iqiyi.vip.task.DiagnosisAnalysisTask;

/**
 * <AUTHOR>
 * @className DiagnosisDimensionServiceImpl
 * @description
 * @date 2023/10/26
 **/
@Component
public class DiagnosisDimensionServiceImpl implements DiagnosisDimensionService {

    @Resource
    private DataPermissionMemoryImpl dataPermissionMemory;
    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private DiagnosisAnalysisTask diagnosisAnalysisTask;

    @Resource
    private DiagnosisRepositoryImpl diagnosisRepository;


    @Override
    public HashMap<String, DimensionLayerNode> getDiagnosisDimensionTree(BaseQry baseQry) {
        return dataPermissionMemory.getDimensionLayerRootHashMap("userCount_diagnosis");
    }

    @Override
    public DataResult<DiagnosisResultDTO> diagnosis(DiagnosisQueryDTO queryDTO) {
        prepareParams(queryDTO);
        if (!dataPermissionService.ownedDataPermission(queryDTO)) {
            return new DataResult<>(BaseResponse.CodeEnum.ACCESS_DENY.getCode(), BaseResponse.CodeEnum.ACCESS_DENY.getMsg());
        }
        return diagnosisAnalysisTask.processDiagnosis(queryDTO);
    }

    /**
     * 根据groupCode获取维度层级树第一个节点
     */
    @Override
    public DimensionLayerNode getFirstLayerByGroupCoe(String targetCode, String groupCode) {
        HashMap<String, DimensionLayerNode> dimensionLayerRootHashMap = dataPermissionMemory.getDimensionLayerRootHashMap(targetCode);
        return MapUtils.getObject(dimensionLayerRootHashMap, groupCode);
    }

    @Override
    public List<DimensionLayerNode> getNextDimensionLayersByGroupCodeAndDimensionCode(String groupCode, String dimensionCode, DimensionLayerNode layerNodeRoot) {
        if (layerNodeRoot == null) {
            return new ArrayList<>();
        }
        if (groupCode.equals(layerNodeRoot.getGroupCode()) && dimensionCode.equals(layerNodeRoot.getDimensionCode())) {
            return layerNodeRoot.getChildren();
        }
        for (DimensionLayerNode child : layerNodeRoot.getChildren()) {
            List<DimensionLayerNode> dimensionLayerNodes = getNextDimensionLayersByGroupCodeAndDimensionCode(groupCode, dimensionCode, child);
            if (CollectionUtils.isNotEmpty(dimensionLayerNodes)) {
                return dimensionLayerNodes;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public DimensionLayerNode getDimensionLayersByGroupCodeAndDimensionCode(String groupCode, String dimensionCode, DimensionLayerNode layerNodeRoot) {
        if (layerNodeRoot == null) {
            return null;
        }
        if (groupCode.equals(layerNodeRoot.getGroupCode()) && dimensionCode.equals(layerNodeRoot.getDimensionCode())) {
            return layerNodeRoot;
        }
        for (DimensionLayerNode child : layerNodeRoot.getChildren()) {
            DimensionLayerNode dimensionLayerNode = getDimensionLayersByGroupCodeAndDimensionCode(groupCode, dimensionCode, child);
            if (dimensionLayerNode != null) {
                return dimensionLayerNode;
            }
        }
        return null;
    }

    @Override
    public void download(HttpServletResponse response, DownloadDiagnosisDTO downloadDiagnosisDTO) {
        List<ExcelSheetDataDTO> excelSheetDataDtos = null;
        if (Objects.nonNull(downloadDiagnosisDTO.getTaskId())) {
            excelSheetDataDtos = diagnosisRepository.generateDiagoisisExcel(downloadDiagnosisDTO.getTaskId());
        } else if (StringUtils.isNotBlank(downloadDiagnosisDTO.getUniqueId())) {
            excelSheetDataDtos = diagnosisRepository.generateDiagoisisExcel(downloadDiagnosisDTO.getUniqueId());
        }
        diagnosisAnalysisTask.generateExcel(response, excelSheetDataDtos, downloadDiagnosisDTO);
    }


    private void prepareParams(DiagnosisQueryDTO queryDTO) {
        ConditionParamContext paramContext = ConditionParamContext
            .builder()
            .paramMap(queryDTO.getConditionParamMap())
            .build();
        queryDTO.setConditionParamContext(paramContext);
        //去重
        Set<String> seenGroupCodes = new LinkedHashSet<>();
        List<DiagnosisConditionDTO> filteredList = new ArrayList<>();
        for (DiagnosisConditionDTO dto : queryDTO.getDiagnosisConditions()) {
            if (seenGroupCodes.add(dto.getGroupCode().concat(String.valueOf(dto.getDimensionCode())))) {
                filteredList.add(dto);
            }
        }
        queryDTO.setDiagnosisConditions(filteredList);
    }
}
