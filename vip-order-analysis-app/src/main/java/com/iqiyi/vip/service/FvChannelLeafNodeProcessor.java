package com.iqiyi.vip.service;

import java.util.List;
import java.util.Queue;

import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.LeafNodeProcessTypeEnum;

/**
 * <AUTHOR>
 * @className FvChannelLeafNodeService
 * @description
 * @date 2022/10/10
 **/
public interface FvChannelLeafNodeProcessor {

    LeafNodeProcessTypeEnum getProcessType();

    /**
     * 获取叶节点集合
     * @param executeDTO 用户查询条件
     * @param queryLimits 查询限制条件
     * @param leafNodes 查询条件的叶节点
     * @param ownedHighLevelUser 是否是高级权限
     * @return
     */
     boolean[] findLeafNodes(AnalysisTaskExecuteDTO executeDTO, Queue<DataPermissionNode> queryLimits, List<DataPermissionNode> leafNodes, boolean ownedHighLevelUser, String account);

}
