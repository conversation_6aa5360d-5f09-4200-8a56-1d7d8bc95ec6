package com.iqiyi.vip.service;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.remainV2.DescAndNumPair;
import com.iqiyi.vip.dto.remainV2.LossAndBackAnalysisReq;
import com.iqiyi.vip.dto.remainV2.LossAndBackAnalysisRes;
import com.iqiyi.vip.dto.remainV2.LossAndBackAnalysisResData;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.task.AnalysisTask;
import com.iqiyi.vip.util.DynamicEasyExcelExportUtils;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.ConditionParamUtils;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR> Li
 * @date 2025/5/30 14:28
 */
@Slf4j
@Service
public class LossAndBackAnalysisService {

    @Resource
    private AnalysisTask analysisTask;

    @Resource
    private AnalysisTaskRepository analysisTaskRepository;

    @Resource
    private AnalysisTargetRepository analysisTargetRepository;

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    @Value("${excel.width.adjust:0}")
    private Integer excelWidthAdjust;

    @Resource
    private AnalysisResultStorageService storageService;

    /**
     * 流失分析权限，同留存分析
     */
    public boolean ownRemainBoardPermission(String operator) {
        return CloudConfigUtils.ownRemainBoardAccess(operator);
    }

    public LossAndBackAnalysisRes getLossAndBackData(LossAndBackAnalysisReq reqDTO)
        throws ExecutionException, InterruptedException, JsonProcessingException {
        boolean valid = ownRemainBoardPermission(reqDTO.getOperator());
        if (!valid) {
            throw new RuntimeException(CodeEnum.BASE_INIT_ERROR.getMessage());
        }

        if (requestContainNoDimension(reqDTO)) {
            List<TargetAnalysisQueryDTO> lossAnalysisQueryDTOS = constructLossQueryWithoutDimension(reqDTO);
            List<OriginalQueryResultDTO> lossAnalysisResultDTOS = analysisTask.executeQuery(lossAnalysisQueryDTOS, null);

            List<TargetAnalysisQueryDTO> backAnalysisQueryDTOS = constructBackQueryWithoutDimension(reqDTO);
            List<OriginalQueryResultDTO> backAnalysisResultDTOS = analysisTask.executeQuery(backAnalysisQueryDTOS, null);

            if (!checkQueryResult(lossAnalysisResultDTOS, backAnalysisResultDTOS)) {
                return LossAndBackAnalysisRes.builder()
                    .byDimension(0)
                    .excelName(null)
                    .dataList(null)
                    .build();
            }

            return generateResult(reqDTO, lossAnalysisResultDTOS.get(0), backAnalysisResultDTOS.get(0));
        } else {

            List<TargetAnalysisQueryDTO> lossAnalysisQueryDTOS = constructDimensionQuery(reqDTO, false, 912L);
            List<OriginalQueryResultDTO> lossAnalysisResultDTOS = analysisTask.executeQuery(lossAnalysisQueryDTOS, null);

            List<TargetAnalysisQueryDTO> backAnalysisQueryDTOS = constructDimensionQuery(reqDTO, true, 913L);
            List<OriginalQueryResultDTO> backAnalysisResultDTOS = analysisTask.executeQuery(backAnalysisQueryDTOS, null);

            if (!checkQueryResult(lossAnalysisResultDTOS, backAnalysisResultDTOS)) {
                throw new BizRuntimeException(CodeEnum.TASK_RUN_FAILED.getCode(), CodeEnum.TASK_RUN_FAILED.getMessage());
            }

            if (requestContainOneDimension(reqDTO)) {
                return generateResult(reqDTO, lossAnalysisResultDTOS.get(0), backAnalysisResultDTOS.get(0));
            }

            // 按照维度查询时，将数据存入excel，返回excel下载地址
            AnalysisTaskDO analysisTaskDO = constructTaskInfo(reqDTO);
            analysisTaskRepository.addAnalysisTask(analysisTaskDO);

            List<ExcelSheetDataDTO> lossExcelSheets = convertToExcelData(lossAnalysisResultDTOS);
            List<ExcelSheetDataDTO> backExcelSheets = convertToExcelData(backAnalysisResultDTOS);

            // 调整 Excel表头 和 Sheet Name
            if (CollectionUtils.isNotEmpty(lossExcelSheets)) {
                // 调整sheetName
                ExcelSheetDataDTO lossExcelSheet = lossExcelSheets.get(0);
                lossExcelSheet.setSheetName("流失日-" + DateUtils.format(reqDTO.getLossStartTime(), reqDTO.getLossEndTime()));

                // 调整Excel表头
                LinkedHashMap<String, String> headColumnMap = lossExcelSheet.getHeadColumnMap();
                LinkedHashMap<String, String> changeNameHashMap = new LinkedHashMap<>();
                changeNameHashMap.put("dt", mappingHeadColumnName("dt", true));

                for (Entry<String, String> entry : headColumnMap.entrySet()) {
                    changeNameHashMap.put(entry.getKey(), mappingHeadColumnName(entry.getKey(), true));
                }
                lossExcelSheet.setHeadColumnMap(changeNameHashMap);

                // 调整dataList
                List<LinkedHashMap<String, Object>> lossExcelSheetDataList = lossExcelSheet.getDataList();
                List<LinkedHashMap<String, Object>> changedLossExcelSheetDataList = new ArrayList<>();
                for (LinkedHashMap<String, Object> originalHashMap : lossExcelSheetDataList) {
                    LinkedHashMap<String, Object> changedHashMap = new LinkedHashMap<>();
                    changedHashMap.put("dt", DateUtils.format(reqDTO.getLossStartTime(), reqDTO.getLossEndTime()));
                    changedHashMap.putAll(originalHashMap);
                    changedLossExcelSheetDataList.add(changedHashMap);
                }
                lossExcelSheet.setDataList(changedLossExcelSheetDataList);
            }

            if (CollectionUtils.isNotEmpty(backExcelSheets)) {
                ExcelSheetDataDTO backExcelSheetData = backExcelSheets.get(0);
                backExcelSheetData.setSheetName("回归日-" + DateUtils.format(reqDTO.getBackStartTime(), reqDTO.getBackEndTime()));

                LinkedHashMap<String, String> changeNameHashMap = new LinkedHashMap<>();
                LinkedHashMap<String, String> headColumnMap = backExcelSheetData.getHeadColumnMap();
                for (Entry<String, String> entry : headColumnMap.entrySet()) {
                    changeNameHashMap.put(entry.getKey(), mappingHeadColumnName(entry.getKey(), false));
                }
                backExcelSheetData.setHeadColumnMap(changeNameHashMap);
            }

            List<ExcelSheetDataDTO> finalExcelSheets = mergeSheetData(lossExcelSheets, backExcelSheets);

            // Excel 导出，存储到机器上
            Map<String, List<Object>> conditionParamMap = JacksonUtils.getMapObject(analysisTaskDO.getCondition());
            ConditionParamContext conditionParamContext = ConditionParamContext.builder().paramMap(conditionParamMap).build();

            AnalysisTaskExecuteDTO taskExecuteDTO = AnalysisTaskExecuteDTO.builder()
                .taskId(analysisTaskDO.getId())
                .taskName(analysisTaskDO.getTaskName())
                .conditionParamContext(conditionParamContext)
                .dimensionCodes(
                    StringUtils.isNotBlank(analysisTaskDO.getDimensions()) ? Splitter.on(",").splitToList(analysisTaskDO.getDimensions()) : null)
                .targetCodes(Splitter.on(",").splitToList(analysisTaskDO.getTargets()))
                .operator(analysisTaskDO.getOperator())
                .taskMD5(analysisTaskDO.getUniqueIdentification())
                .businessTypeId(analysisTaskDO.getBusinessTypeId())
                .dataPermissionType(analysisTaskDO.getDataPermissionType())
                .themeType(analysisTaskDO.getThemeType())
                .build();

            String excelFileName = DynamicEasyExcelExportUtils.generateResultExcelFile2(finalExcelSheets, reqDTO.getOperator(), localFilePath
                , code -> analysisTargetRepository.selectByCode(code)
                , code -> {
                    final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
                    if (analysisTarget != null && analysisTarget.getGroup() != null) {
                        return analysisTargetGroupRepository.get(analysisTarget.getGroup());
                    }
                    return null;
                }, taskExecuteDTO.getTaskId(), taskExecuteDTO, excelWidthAdjust);

            //上传到OSS
            String absoluteResultFilePath = localFilePath + "/" + excelFileName;
            storageService.uploadResultFile(new File(absoluteResultFilePath), excelFileName);

            // 清理本地文件, 将任务状态置为成功
            cleanLocalFile(absoluteResultFilePath);
            analysisTaskRepository.reSetTaskStatus(taskExecuteDTO.getTaskId(), TaskStatusEnum.FINISHED.getStatus(), excelFileName, null);

            return LossAndBackAnalysisRes.builder()
                .byDimension(1)
                .excelName(excelFileName)
                .build();
        }
    }

    // 查询指定日期范围的内流失人数
    private List<TargetAnalysisQueryDTO> constructLossQueryWithoutDimension(LossAndBackAnalysisReq reqDTO) {
        Properties properties = new Properties();
        properties.setProperty("lossStartDate", DateUtils.dateDatetimeFormat(reqDTO.getLossStartTime()));
        properties.setProperty("lossEndDate", DateUtils.dateDatetimeFormat(reqDTO.getLossEndTime()));
        properties.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());

        constructConditionWithoutDimension(reqDTO, properties);

        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        queryDTOS.add(
            TargetAnalysisQueryDTO.builder()
                .targetCode("CK_VIP_LOSS_AND_BACK_ANALYSIS")
                .dataBase(properties.getProperty("dateBase"))
                .querySql(analysisTask.parseSqlByProperties(properties, 910L))
                .operator(reqDTO.getOperator())
                .env(System.getProperty("spring.profiles.active"))
                .build()
        );

        return queryDTOS;
    }

    private void constructConditionWithoutDimension(LossAndBackAnalysisReq reqDTO, Properties properties) {
        Map<String, List<Object>> conditionParamMap = reqDTO.getConditionParamMap();
        List<String> conditionSql = new ArrayList<>();
        processCommonConditionParam(conditionParamMap, conditionSql);
        properties.setProperty("baseQueryCondition", String.join(" and ", conditionSql));
    }

    private List<TargetAnalysisQueryDTO> constructBackQueryWithoutDimension(LossAndBackAnalysisReq reqDTO) {
        Properties properties = new Properties();
        properties.setProperty("lossStartDate", DateUtils.dateDatetimeFormat(reqDTO.getLossStartTime()));
        properties.setProperty("lossEndDate", DateUtils.dateDatetimeFormat(reqDTO.getLossEndTime()));
        properties.setProperty("backStartDate", DateUtils.dateDatetimeFormat(reqDTO.getBackStartTime()));
        properties.setProperty("backEndDate", DateUtils.dateDatetimeFormat(reqDTO.getBackEndTime()));
        properties.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());

        constructConditionWithoutDimension(reqDTO, properties);

        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        queryDTOS.add(
            TargetAnalysisQueryDTO.builder()
                .targetCode("CK_VIP_LOSS_AND_BACK_ANALYSIS")
                .dataBase(properties.getProperty("dateBase"))
                .querySql(analysisTask.parseSqlByProperties(properties, 911L))
                .operator(reqDTO.getOperator())
                .env(System.getProperty("spring.profiles.active"))
                .build()
        );

        return queryDTOS;
    }

    private boolean requestContainNoDimension(LossAndBackAnalysisReq reqDTO) {
        return StringUtils.isEmpty(reqDTO.getDimensionCode());
    }

    private boolean checkQueryResult(List<OriginalQueryResultDTO> lossAnalysisQueryResultDTOS, List<OriginalQueryResultDTO> backAnalysisQueryResultDTOS) {
        boolean flag1 = false, flag2 = false;
        if (CollectionUtils.isNotEmpty(lossAnalysisQueryResultDTOS) && lossAnalysisQueryResultDTOS.size() == 1) {
            flag1 = lossAnalysisQueryResultDTOS.get(0).getSuccess();
        }
        if (CollectionUtils.isEmpty(backAnalysisQueryResultDTOS) || backAnalysisQueryResultDTOS.size() == 1) {
            flag2 = backAnalysisQueryResultDTOS.get(0).getSuccess();
        }
        return flag1 && flag2;
    }

    /**
     * 清理本地文件
     */
    public void cleanLocalFile(String absoluteFilePath) {
        File file = new File(absoluteFilePath);
        if (!file.exists() || !file.isFile() || !file.delete()) {
            log.warn("[order-analysis][delete file: {}] fail. ", absoluteFilePath);
        }
    }

    private List<ExcelSheetDataDTO> mergeSheetData(List<ExcelSheetDataDTO> lossExcelSheet, List<ExcelSheetDataDTO> backExcelSheet) {
        List<ExcelSheetDataDTO> finalExcelSheet = new ArrayList<>();
        finalExcelSheet.addAll(lossExcelSheet);
        finalExcelSheet.addAll(backExcelSheet);
        return finalExcelSheet;
    }

    private AnalysisTaskDO constructTaskInfo(LossAndBackAnalysisReq reqDTO) {
        return AnalysisTaskDO.builder()
            .targets("CK_VIP_LOSS_AND_BACK_ANALYSIS")
            .taskName("流失和转化分析")
            .status(TaskStatusEnum.RUNNING.getStatus())
            .dimensions(reqDTO.getDimensionCode())
            .condition(JacksonUtils.toJsonString(reqDTO.getConditionParamMap()))
            .createTime(DateUtil.date())
            .updateTime(DateUtil.date())
            .operator(reqDTO.getOperator())
            .source(AnalysisTaskSourceEnum.UI_MANUAL.getValue())
            .uniqueIdentification(String.valueOf(System.currentTimeMillis()))
            .build();
    }

    /**
     * @param reqDTO 请求参数
     * @param templateId sql模板id
     * @return 查询语句
     * @desc 构造查询语句
     */
    private List<TargetAnalysisQueryDTO> constructDimensionQuery(LossAndBackAnalysisReq reqDTO, boolean backFlag, long templateId) {
        Properties properties = new Properties();
        properties.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());
        properties.setProperty("lossStartDate", DateUtils.dateDatetimeFormat(reqDTO.getLossStartTime()));
        properties.setProperty("lossEndDate", DateUtils.dateDatetimeFormat(reqDTO.getLossEndTime()));
        if (backFlag) {
            properties.setProperty("backStartDate", DateUtils.dateDatetimeFormat(reqDTO.getBackStartTime()));
            properties.setProperty("backEndDate", DateUtils.dateDatetimeFormat(reqDTO.getBackEndTime()));
        }

        constructCondition(reqDTO, properties);
        constructDimension(reqDTO, backFlag, properties);

        ArrayList<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        queryDTOS.add(
            TargetAnalysisQueryDTO.builder()
                .targetCode("CK_VIP_LOSS_AND_BACK_ANALYSIS")
                .dataBase(properties.getProperty("dateBase"))
                .querySql(analysisTask.parseSqlByProperties(properties, templateId))
                .operator(reqDTO.getOperator())
                .env(System.getProperty("spring.profiles.active"))
                .build()
        );

        return queryDTOS;
    }

    private List<ExcelSheetDataDTO> convertToExcelData(List<OriginalQueryResultDTO> queryResultDTOS) {
        return queryResultDTOS.stream()
            .filter(OriginalQueryResultDTO::getSuccess)
            .filter(OriginalQueryResultDTO::commonTargetType)
            .map(ExcelSheetDataDTO::from)
            .collect(Collectors.toList());
    }

    private void constructDimension(LossAndBackAnalysisReq reqDTO, boolean backFlag, Properties properties) {
        List<String> dimensionSql = new ArrayList<>();
        List<String> orderBySql = new ArrayList<>();
        if (backFlag) {
            dimensionSql.add("dt");
            orderBySql.add("dt");
        }
        orderBySql.add("num desc");

        if (StringUtils.isNotBlank(reqDTO.getDimensionCode())) {
            dimensionSql.add(reqDTO.getDimensionCode());
            orderBySql.add(reqDTO.getDimensionCode());
        }

        // 设置properties
        properties.setProperty("dimensionCodes",
            dimensionSql.isEmpty() ? "" : String.join(",", dimensionSql));

        properties.setProperty("orderByCodes", String.join(",", orderBySql));
    }

    private void constructCondition(LossAndBackAnalysisReq reqDTO, Properties properties) {
        Map<String, List<Object>> conditionParamMap = reqDTO.getConditionParamMap();
        List<String> conditionSql = new ArrayList<>();
        if (StringUtils.isNotEmpty(reqDTO.getDimensionCode()) && reqDTO.getDimensionCode().contains("auto_renew")) {
            conditionSql.add("auto_renew != '其他'");
        }
        processCommonConditionParam(conditionParamMap, conditionSql);
        properties.setProperty("baseQueryCondition", String.join(" and ", conditionSql));
    }

    private void processCommonConditionParam(Map<String, List<Object>> conditionParamMap, List<String> conditionSql) {
        if (MapUtils.isEmpty(conditionParamMap)) {
            return;
        }
        for (Entry<String, List<Object>> entry : conditionParamMap.entrySet()) {
            String key = entry.getKey();
            List<Object> value = entry.getValue();

            if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(value)) {
                continue;
            }

            List<String> listValue = ConditionParamUtils.getListValue(conditionParamMap, key, String.class);
            if (CollectionUtils.isEmpty(listValue)) {
                continue;
            }
            conditionSql.add(constructConditionSql(key, listValue));
        }
    }

    private String constructConditionSql(String key, List<String> listValue) {
        return key + " in (" + listValue.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
    }

    private boolean requestContainDimension(LossAndBackAnalysisReq reqDTO) {
        return StringUtils.isNotEmpty(reqDTO.getDimensionCode());
    }

    private boolean requestContainOneDimension(LossAndBackAnalysisReq reqDTO) {
        return StringUtils.isNotEmpty(reqDTO.getDimensionCode()) && !reqDTO.getDimensionCode().contains(",");
    }

    private LossAndBackAnalysisRes generateResult(LossAndBackAnalysisReq reqDTO, OriginalQueryResultDTO lossAnalysisResultDTO, OriginalQueryResultDTO backAnalysisResultDTO) {
        // 构建时间范围字符串
        String dateRange = formatDateRange(reqDTO);
        // 处理流失数据
        LossAndBackAnalysisResData lossData = createLossDataDTO(reqDTO, dateRange, lossAnalysisResultDTO);
        // 处理回归数据
        List<LossAndBackAnalysisResData> backDataList = createBackData(reqDTO, backAnalysisResultDTO);

        // 合并结果
        List<LossAndBackAnalysisResData> dataList = new ArrayList<>();
        dataList.add(lossData);
        dataList.addAll(backDataList);

        LossAndBackAnalysisRes lossAndBackAnalysisRes = new LossAndBackAnalysisRes();
        lossAndBackAnalysisRes.setByDimension(0);
        lossAndBackAnalysisRes.setExcelName(null);
        lossAndBackAnalysisRes.setDataList(dataList);

        return lossAndBackAnalysisRes;
    }

    // 格式化日期范围
    private String formatDateRange(LossAndBackAnalysisReq reqDTO) {
        return DateUtils.format(reqDTO.getLossStartTime(), reqDTO.getLossEndTime());
    }

    // 创建流失数据DTO
    private LossAndBackAnalysisResData createLossDataDTO(LossAndBackAnalysisReq reqDTO, String dateRange, OriginalQueryResultDTO lossResult) {
        LossAndBackAnalysisResData resData = new LossAndBackAnalysisResData();
        resData.setDt(dateRange);
        resData.setIsLoss(1);

        List<DescAndNumPair> descAndNumPairs = new ArrayList<>();
        lossResult.getDataList()
            .forEach(dataMap -> {
                String dimensionDescInfo = StringUtils.isNotEmpty(reqDTO.getDimensionCode())
                    ? (String) dataMap.get(reqDTO.getDimensionCode())
                    : null;
                Long num = (Long) dataMap.get("num");
                descAndNumPairs.add(new DescAndNumPair(dimensionDescInfo, num));
            });
        resData.setDescAndNumPairs(descAndNumPairs);

        return resData;
    }

    // 转换回归数据
    private List<LossAndBackAnalysisResData> createBackData(LossAndBackAnalysisReq reqDTO, OriginalQueryResultDTO backResult) {

        List<LossAndBackAnalysisResData> lossAndBackAnalysisResData = new ArrayList<>();

        Map<String, List<DescAndNumPair>> dtToDescAndNumPairs = new LinkedHashMap<>();

        for (LinkedHashMap<String, Object> dataMap : backResult.getDataList()) {
            String dt = (String) dataMap.get("dt");

            String dimensionDescInfo = StringUtils.isNotEmpty(reqDTO.getDimensionCode())
                ? (String) dataMap.get(reqDTO.getDimensionCode())
                : null;

            Long num = (Long) dataMap.get("num");

            if (dtToDescAndNumPairs.containsKey(dt)) {
                dtToDescAndNumPairs.get(dt).add(new DescAndNumPair(dimensionDescInfo, num));
            } else {
                List<DescAndNumPair> pairs = new ArrayList<>();
                pairs.add(new DescAndNumPair(dimensionDescInfo, num));
                dtToDescAndNumPairs.put(dt, pairs);
            }
        }

        for (Entry<String, List<DescAndNumPair>> entry : dtToDescAndNumPairs.entrySet()) {
            lossAndBackAnalysisResData.add(new LossAndBackAnalysisResData(entry.getKey(), 2, entry.getValue()));
        }

        return lossAndBackAnalysisResData;
    }

    private String mappingHeadColumnName(String columnName, boolean lossFlag) {
        switch (columnName) {
            case "vip_group": return "会员类型";
            case "vip_biz_type": return "套餐类型";
            case "zero_order_type": return "买赠类型";
            case "vip_card_type": return "卡种";
            case "renew_flag": return "生命周期";
            case "uid_layer": return "基石潮汐";
            case "sexandage": return "圈层";
            case "num": return "用户量";
            case "auto_renew": return "自动续费状态";
            case "kr_channel_name": return "KR项目";
            case "dt":
                return lossFlag ? "流失日" : "回归日";
        }
        return columnName;
    }

}
