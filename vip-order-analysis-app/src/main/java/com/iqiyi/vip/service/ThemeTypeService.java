package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.domain.repository.AnalysisThemeTypeRepository;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.theme.ThemeTypeWhiteList;
import com.iqiyi.vip.utils.CloudConfigUtils;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@Component
@Slf4j
public class ThemeTypeService {

    @Resource
    private AnalysisThemeTypeRepository analysisThemeTypeRepository;

    public ListResult<AnalysisThemeType> themeTypes(String operator) {
        Map<Integer, ThemeTypeWhiteList> whiteListMap = CloudConfigUtils.getThemeTypeWhiteList()
            .stream()
            .collect(Collectors.toMap(ThemeTypeWhiteList::getThemeType, Function.identity()));
        List<AnalysisThemeType> themeTypes = analysisThemeTypeRepository.selectAll().stream()
            .filter(t -> !whiteListMap.containsKey(t.getId()) || whiteListMap.get(t.getId()).getOperator().contains(operator))
            .collect(Collectors.toList());
        return ListResult.createSuccess(themeTypes);
    }

    public List<ThemeTypeWhiteList> themeTypeWhiteList() {
        return CloudConfigUtils.getThemeTypeWhiteList();
    }
}
