package com.iqiyi.vip.service;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.domain.repository.UserGroupRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.user.group.UpdateUserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDeleteDTO;
import com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO;
import com.iqiyi.vip.dto.user.group.UserGroupQueryDTO;
import com.iqiyi.vip.enums.CodeEnum;

/**
 * 订单分析平台-分群管理
 *
 * <AUTHOR>
 */
@Component
public class UserGroupService {

    @Resource
    private UserGroupRepository userGroupRepository;


    public List<UserGroupDO> query(UserGroupQueryDTO baseQry) {
        return userGroupRepository.query(baseQry);
    }

    public PageListResult<UserGroupDO> pageList(UserGroupPageQueryDTO pageQryDTO) {
        return userGroupRepository.selectPageList(pageQryDTO);
    }

    public CommonResult deleteTaskRecord(UserGroupDeleteDTO deleteDTO) {
        int count = userGroupRepository.deleteUserGroup(deleteDTO.getUserGroupId(), deleteDTO.getOperator());
        if (count >= 1) {
            return new CommonResult(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMessage());
        } else {
            return new CommonResult(CodeEnum.ERROR_SYSTEM.getCode(), CodeEnum.ERROR_SYSTEM.getMessage());
        }
    }


    public CommonResult addUserGroup(UserGroupDTO userGroupDTO){
        try{
            userGroupRepository.addUserGroup(userGroupDTO);
        }catch (Exception e){
            return new CommonResult(CodeEnum.USER_GROUP_DATA_UPDATE_FAIL.getCode(), CodeEnum.USER_GROUP_DATA_UPDATE_FAIL.getMessage());
        }
        return new CommonResult(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMessage());
    }

    public CommonResult updateUserGroup(UpdateUserGroupDTO updateUserGroupDTO){
        try{
            userGroupRepository.updateUserGroup(updateUserGroupDTO);
        }catch (Exception e){
            return new CommonResult(CodeEnum.USER_GROUP_DATA_UPDATE_FAIL.getCode(), CodeEnum.USER_GROUP_DATA_UPDATE_FAIL.getMessage());
        }
        return new CommonResult(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMessage());
    }
}
