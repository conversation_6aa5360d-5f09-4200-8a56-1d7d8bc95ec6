package com.iqiyi.vip.service;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.domain.entity.GroupTargetDTO;
import com.iqiyi.vip.domain.factory.AnalysisTargetFactory;
import com.iqiyi.vip.domain.repository.AnalysisSqlTemplateRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.ConfigChangeLogRepository;
import com.iqiyi.vip.dto.base.*;
import com.iqiyi.vip.dto.target.AnalysisTargetPageQryDTO;
import com.iqiyi.vip.dto.target.TargetGroupQry;
import com.iqiyi.vip.dto.target.TargetUpdateDTO;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConfigTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;
import com.iqiyi.vip.utils.CloudConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Component
@Slf4j
public class TargetMangerService implements ConfigVersionProcessor {

    @Resource
    private AnalysisTargetRepository analysisTargetRepository;

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;

    @Resource(name = "analysisSqlTemplateRepositoryImpl")
    private AnalysisSqlTemplateRepository analysisSqlTemplateRepository;

    @Resource
    private ConfigChangeLogRepository configChangeLogRepository;

    @Resource
    private DataPermissionService dataPermissionService;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult save(TargetUpdateDTO updateDTO) {
        try {
            Integer businessTypeId = updateDTO.getBusinessTypeId();
            Integer themeType = updateDTO.getThemeType();
            if (businessTypeId == null || themeType == null) {
                throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
            }
            AnalysisTarget exist = analysisTargetRepository.selectByCode(updateDTO.getCode());
            if (exist != null) {
                throw new BizRuntimeException(CodeEnum.DUPLICATE);
            }
            if (analysisSqlTemplateRepository.selectById(updateDTO.getSqlTemplateId()) == null) {
                throw new BizRuntimeException(CodeEnum.NOT_EXIST);
            }
            AnalysisTarget saveTarget = AnalysisTargetFactory.getSaveTarget(updateDTO);
            Long newId = analysisTargetRepository.save(saveTarget);
            saveTarget.setId(newId);
            configChangeLogRepository.save(ConfigChangeLog.buildFrom(saveTarget, DEFAULT_VERSION));
            return CommonResult.success();
        } catch (Exception e) {
            log.error("TargetMangerService error", e);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
    }

    public CommonResult update(TargetUpdateDTO updateDTO) {
        if (updateDTO.getId() == null) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        //校验sql模板是否存在
        if (analysisSqlTemplateRepository.selectById(updateDTO.getSqlTemplateId()) == null) {
            throw new BizRuntimeException(CodeEnum.NOT_EXIST);
        }
        AnalysisTarget exist = analysisTargetRepository.selectById(updateDTO.getId());
        if (exist == null) {
            throw new BizRuntimeException(CodeEnum.NOT_EXIST);
        }
        Integer maxVersion = configChangeLogRepository.getMaxVersion(ConfigTypeEnum.TARGET.getType(), exist.getId());
        int newVersion = maxVersion == null ? DEFAULT_VERSION : maxVersion + 1;
        AnalysisTargetFactory.updateExist(exist, updateDTO, newVersion);
        analysisTargetRepository.update(exist);
        configChangeLogRepository.save(ConfigChangeLog.buildFrom(exist, newVersion));
        return CommonResult.success();
    }

    public CommonResult delete(Long id) {
        AnalysisTarget target = analysisTargetRepository.selectById(id);
        if (target != null) {
            analysisTargetRepository.deleteById(id);
            configChangeLogRepository.delete(ConfigTypeEnum.TARGET.getType(), target.getId());
        }
        return CommonResult.success();
    }

    public PageListResult<AnalysisTarget> pageList(AnalysisTargetPageQryDTO query) {
        PageListResult<AnalysisTarget> analysisTargetPageListResult = analysisTargetRepository.selectPageList(query);
        if (analysisTargetPageListResult.getDataList() != null) {
            analysisTargetPageListResult.getDataList().stream().forEach(v -> {
                AnalysisTarget target = (AnalysisTarget) v;
                target.setName(TargetDimensionHandler.retTargetName(target));
//                target.setCode(TargetDimensionHandler.removeTransCodeSuffix(TargetDimensionHandler.retTargetCode(target)));
            });
        }
        return analysisTargetPageListResult;
    }

    public DataResult<AnalysisTarget> detail(Long id) {
        AnalysisTarget target = analysisTargetRepository.selectById(id);
        DataResult result = DataResult.success(target);
        return result;
    }

    public ListResult<AnalysisTarget> list(BaseQry baseQry) {
        return ListResult.createSuccess(analysisTargetRepository.selectAll(baseQry));
    }

    public ListResult<GroupTargetDTO> groupList(TargetGroupQry targetGroupQry) {
        List<AnalysisTargetGroup> groups = analysisTargetGroupRepository.selectByBusinessType(targetGroupQry.getBusinessTypeId(), targetGroupQry.getType(), targetGroupQry.getThemeType());
        if (CollectionUtils.isEmpty(groups)) {
            return ListResult.createSuccess(null);
        }
        List<AnalysisTarget> targets = analysisTargetRepository.selectAll(targetGroupQry);

        Map<Integer, List<AnalysisTarget>> grouping = targets.stream()
            .filter(t -> hasTargetAccess(t.getCode(), targetGroupQry))
            .collect(Collectors.groupingBy(AnalysisTarget::getGroup, Collectors.mapping(v -> {
                v.setName(TargetDimensionHandler.retTargetName(v)); // 订单分析首页维度展示问题处理 "购买类型|,|选定用户购买类型|,|转移用户购买类型 --> 购买类型"
                return v;
            }, Collectors.toList())));

        List<GroupTargetDTO> groupTargetDTOList = new ArrayList<>();
        for (AnalysisTargetGroup group : groups) {
            List<AnalysisTarget> analysisTargets = grouping.get(group.getId());
            if (CollectionUtils.isEmpty(analysisTargets)) {
                continue;
            }
            GroupTargetDTO groupTargetDTO = new GroupTargetDTO();
            groupTargetDTO.setGroupId(group.getId());
            groupTargetDTO.setGroupName(group.getName());
            groupTargetDTO.setParent(group.getParent());
            groupTargetDTO.setType(group.getType());
            groupTargetDTO.setBusinessTypeId(group.getBusinessTypeId());
            groupTargetDTO.setTargets(analysisTargets);
            groupTargetDTOList.add(groupTargetDTO);
        }
        return ListResult.createSuccess(groupTargetDTOList);
    }

    private boolean hasTargetAccess(String targetCode, BaseQry baseQry) {
        return CloudConfigUtils.hasTargetAccess(targetCode, baseQry.getOperator()) || dataPermissionService.ownedHighLevelUser(baseQry);
    }

    @Override
    public Integer getProcessType() {
        return ConfigTypeEnum.TARGET.getType();
    }

    @Override
    public CommonResult versionSwitch(VersionSwitchReqDTO reqDTO, ConfigChangeLog destinationChangeLog) {
        Long configId = reqDTO.getConfigId();
        AnalysisTarget target = analysisTargetRepository.selectById(configId);
        if (target == null) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), String.format("未找到id：%s的指标", configId));
        }
        try {
            AnalysisTargetFactory.versionSwitch(target, destinationChangeLog, reqDTO);
            analysisTargetRepository.update(target);
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.create(CodeEnum.ERROR_PARAM);
        }
    }

    @Override
    public ListResult versionDiff(VersionDiffReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult versionList(VersionListReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult changeList(ChangeListReqDTO reqDTO) {
        return null;
    }
}
