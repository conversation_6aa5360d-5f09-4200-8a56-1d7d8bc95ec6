package com.iqiyi.vip.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.repository.CompetitorPriceRepository;
import com.iqiyi.vip.domain.repository.YunheAlbumMetaDataRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceProduct;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSaveParam;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;
import com.iqiyi.vip.dto.competitor.CompetitorPriceWithAlbumResultDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUserStatusListParam;
import com.iqiyi.vip.dto.competitor.AlbumContentInfoDTO;
import com.iqiyi.vip.dto.competitor.UserStatusType.ProductInfo.SaleInfo;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import com.iqiyi.vip.util.CompetitorPriceUtil;
import com.iqiyi.vip.utils.DateUtils;

/**
 * @author: guojing
 * @date: 2024/10/30 15:10
 */
@Service
@Slf4j
public class CompetitorPriceService {

    @ConfigJsonValue("${need.clean.price.detail:true}")
    private boolean needCleanPriceDetail;

    @Resource
    private CompetitorPriceRepository competitorPriceRepository;
    
    @Resource
    private YunheAlbumMetaDataRepository yunheAlbumMetaDataRepository;
    
//    // 黑白名单配置（所有黑白名单放在一个配置中）
    @ConfigJsonValue("${competitor.price.product.lists:{}}")
    private Map<String, List<String>> productLists;

    @ConfigJsonValue("${userStatus.match.white.list:{}}")
    private Map<String, List<String>> userStatusMatchWhiteList;

    @ConfigJsonValue("${product.name.map:{}}")
    private Map<String, String> productNameMap;
    
    // 爱奇艺价格配置
    @Value("${competitor.price.iqiyi.config:{}}")
    private String iqiyiPriceConfig;

    public List<String> brandsList() {
        return competitorPriceRepository.brandsList();
    }

    public List<String> userStatusList(CompetitorUserStatusListParam param) {
        return competitorPriceRepository.userStatusList(param.getBrand(), param.getDate());
    }

    public CompetitorDateRangeDTO dateRange() {
        return competitorPriceRepository.dateRange();
    }

    public List<CompetitorPriceDTO> search(CompetitorPriceSearchParam param) {
        if (StringUtils.isBlank(param.getBrand()) && StringUtils.isBlank(param.getDate()) && StringUtils.isBlank(param.getUserStatus())) {
            return Collections.emptyList();
        }
        return competitorPriceRepository.search(param);
    }

    /**
     * 根据专辑信息搜索竞品价格
     * @param param 搜索参数，包含品牌、日期、用户状态、开始时间和结束时间
     * @return 竞品价格与专辑内容信息结果
     */
    public CompetitorPriceWithAlbumResultDTO searchWithAlbum(CompetitorPriceSearchParam param) {
        if (StringUtils.isAllBlank(param.getBrand(), param.getDate(), param.getUserStatus())) {
            return new CompetitorPriceWithAlbumResultDTO();
        }

        // 使用CompletableFuture实现并行查询
        CompletableFuture<List<CompetitorPriceDTO>> competitorPricesFuture = CompletableFuture.supplyAsync(() -> {
            // 查询竞品价格
            return competitorPriceRepository.search(param);
        });

        CompletableFuture<List<AlbumContentInfoDTO>> albumContentsFuture = CompletableFuture.supplyAsync(() -> {
            // 查询专辑内容
            return yunheAlbumMetaDataRepository.search(param);
        });

        try {
            // 等待两个查询都完成
            List<CompetitorPriceDTO> competitorPrices = competitorPricesFuture.get(10, TimeUnit.SECONDS);
            List<AlbumContentInfoDTO> albumContents = albumContentsFuture.get(10, TimeUnit.SECONDS);

            // 构建返回结果
            return CompetitorPriceWithAlbumResultDTO.builder()
                    .competitorPrices(competitorPrices)
                    .albumContents(albumContents)
                    .build();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("查询被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("查询执行失败", e);
        } catch (TimeoutException e) {
            throw new RuntimeException("查询超时", e);
        }
    }

    public List<PriceBoardResultDTO> priceBoard(CompetitorPriceSearchParam param) {
        String date = param.getDate();
        if (StringUtils.isBlank(date)) {
            return Collections.emptyList();
        }


        // 查询所有符合条件的价格数据，只使用date
        List<CompetitorPriceDTO> allPrices = competitorPriceRepository.search(
            CompetitorPriceSearchParam.builder()
                .date(date)
                .build()
        );

        String lastWeekDate = DateUtils.calculateDate(date, -7);
        List<CompetitorPriceDTO> lastWeekPrices = competitorPriceRepository.search(
            CompetitorPriceSearchParam.builder()
                .date(lastWeekDate)
                .build()
        );

        boolean needNormalCard = param.isNeedNormalCard();

        // 定义5种用户状态
        List<String> userStatusList = Arrays.asList(
            "非会员新用户",
            "VIP过期0-365天",
            "VIP过期365天以上",
            "会员自动续费",
            "会员非自动续费"
        );

        // 获取黑白名单配置
        List<String> vipBlacklist = productLists.getOrDefault("vip_blacklist", Collections.emptyList());
        List<String> svipBlacklist = productLists.getOrDefault("svip_blacklist", Collections.emptyList());
        List<String> consecutiveMonthList = productLists.getOrDefault("consecutive_month", Collections.emptyList());
        List<String> consecutiveQuarterList = productLists.getOrDefault("consecutive_quarter", Collections.emptyList());
        List<String> consecutiveYearList = productLists.getOrDefault("consecutive_year", Collections.emptyList());
        List<String> normalMonthList = productLists.getOrDefault("normal_month", Collections.emptyList());
        List<String> normalQuarterList = productLists.getOrDefault("normal_quarter", Collections.emptyList());
        List<String> normalYearList = productLists.getOrDefault("normal_year", Collections.emptyList());

        // 获取爱奇艺价格配置
//        Map<String, Object> iqiyiPriceConfig = getIqiyiPriceConfig();

        // 平台顺序和简称映射 - 修改为固定的顺序：爱奇艺、优酷、腾讯、芒果
        List<String> platformOrder = Arrays.asList("爱奇艺",  "腾讯", "优酷","芒果");
        Map<String, String> platformAbbr = new HashMap<>();
        platformAbbr.put("爱奇艺", "aqy");
        platformAbbr.put("腾讯", "TX");
        platformAbbr.put("优酷", "YK");
        platformAbbr.put("芒果", "MG");

        // 创建结果列表
        List<PriceBoardResultDTO> resultList = new ArrayList<>();

        // 为每种用户状态创建一个PriceBoardResultDTO对象
        for (String userStatus : userStatusList) {
            // 筛选当前用户状态的价格数据
            List<CompetitorPriceDTO> statusPrices = allPrices.stream()
                .filter(price -> matchUserStatus(price.getUserStatus(), userStatus))
                .collect(Collectors.toList());

            List<CompetitorPriceDTO> lastWeekStatusPrices = lastWeekPrices.stream()
                .filter(price -> matchUserStatus(price.getUserStatus(), userStatus))
                .collect(Collectors.toList());

            // 构建基础会员（爱奇艺独有）
            BasicMember basicMember = buildBasicMember(iqiyiPriceConfig, userStatus, needNormalCard);

            // 构建黄金会员
            GoldMember goldMember = buildGoldMember(
                statusPrices,
                lastWeekStatusPrices,
                Lists.newArrayList("VIP", "vip", "会员"),
                vipBlacklist,
                consecutiveMonthList,
                consecutiveQuarterList,
                consecutiveYearList,
                normalMonthList,
                normalQuarterList,
                normalYearList,
                iqiyiPriceConfig,
                userStatus,
                platformOrder,
                platformAbbr,
                needNormalCard
            );

//            // 构建白金会员
            PlatinumMember platinumMember = buildPlatinumMember(
                statusPrices,
                lastWeekStatusPrices,
                Lists.newArrayList("SVIP", "svip", "全屏会员"),
                svipBlacklist,
                consecutiveMonthList,
                consecutiveQuarterList,
                consecutiveYearList,
                normalMonthList,
                normalQuarterList,
                normalYearList,
                iqiyiPriceConfig,
                userStatus,
                platformOrder,
                platformAbbr,
                needNormalCard
            );

            // 构建钻石会员（爱奇艺独有）
            DiamondMember diamondMember = buildDiamondMember(iqiyiPriceConfig, userStatus, needNormalCard);

            // 构建价格看板结果
            PriceBoardResultDTO dto = PriceBoardResultDTO.builder()
                .userStatus(userStatus)
                .basicMember(basicMember)
                .goldMember(goldMember)
                .platinumMember(platinumMember)
                .diamondMember(diamondMember)
                .build();

            resultList.add(dto);
        }

        return resultList;
    }
    
    /**
     * 匹配用户状态
     * @param priceUserStatus 价格数据中的用户状态
     * @param targetUserStatus 目标用户状态
     * @return 是否匹配
     */
    private boolean matchUserStatus(String priceUserStatus, String targetUserStatus) {
        // 根据业务需求实现匹配逻辑
        if (priceUserStatus == null || "SVIP（自动续费）".equals(priceUserStatus)) {
            return false;
        }
        
        // 精确匹配
        List<String> whiteList = userStatusMatchWhiteList.get(targetUserStatus);
        if (priceUserStatus.equals(targetUserStatus) || (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(priceUserStatus))) {
            return true;
        }
        return false;
    }
    
    /**
     * 构建基础会员（爱奇艺独有）
     * @param iqiyiConfig 爱奇艺配置
     * @param userStatus 用户状态
     * @param needNormalCard 是否需要普通卡
     * @return 基础会员
     */
    private BasicMember buildBasicMember(String iqiyiConfig, String userStatus, boolean needNormalCard) {
        Map<String, SaleInfo> products = CompetitorPriceUtil.getMemberProductInfo(iqiyiConfig, "basicMember", userStatus);
        if (products == null) {
            return null;
        }
        
        return BasicMember.builder()
            .consecutiveMonth(products.get("consecutiveMonth").getPrice() + "元")
            .consecutiveYear(products.get("consecutiveYear").getPrice() + "元")
            .normalMonth(needNormalCard ? products.get("normalMonth").getPrice() + "元" : null)
            .normalYear(needNormalCard ? products.get("normalYear").getPrice() + "元" : null)
            .build();
    }
    
    /**
     * 构建黄金会员
     * @param prices 价格数据
     * @param vipTypes 会员类型
     * @param productBlacklist 产品黑名单
     * @param consecutiveMonthList 连续包月白名单
     * @param consecutiveQuarterList 连续包季白名单
     * @param consecutiveYearList 连续包年白名单
     * @param normalMonthList 普通包月白名单
     * @param normalQuarterList 普通包季白名单
     * @param normalYearList 普通包年白名单
     * @param jsonConfig 爱奇艺配置JSON字符串
     * @param userStatus 用户状态
     * @param platformOrder 平台顺序
     * @param platformAbbr 平台简称
     * @param needNormalCard 是否需要普通卡
     * @return 黄金会员
     */
    private GoldMember buildGoldMember(
            List<CompetitorPriceDTO> prices,
            List<CompetitorPriceDTO> lastWeekPrices,
            List<String> vipTypes,
            List<String> productBlacklist,
            List<String> consecutiveMonthList,
            List<String> consecutiveQuarterList,
            List<String> consecutiveYearList,
            List<String> normalMonthList,
            List<String> normalQuarterList,
            List<String> normalYearList,
            String jsonConfig,
            String userStatus,
            List<String> platformOrder,
            Map<String, String> platformAbbr,
            boolean needNormalCard) {
        
        // 使用通用方法构建会员信息
        return buildMember(
            prices,
            lastWeekPrices,
            vipTypes,
            productBlacklist,
            consecutiveMonthList,
            consecutiveQuarterList,
            consecutiveYearList,
            normalMonthList,
            normalQuarterList,
            normalYearList,
            jsonConfig,
            "goldMember",
            userStatus,
            platformOrder,
            platformAbbr,
            needNormalCard,
            GoldMember.builder()
        );
    }
    
    /**
     * 构建白金会员
     * @param prices 价格数据
     * @param vipTypes 会员类型
     * @param productBlacklist 产品黑名单
     * @param consecutiveMonthList 连续包月白名单
     * @param consecutiveQuarterList 连续包季白名单
     * @param consecutiveYearList 连续包年白名单
     * @param normalMonthList 普通包月白名单
     * @param normalQuarterList 普通包季白名单
     * @param normalYearList 普通包年白名单
     * @param jsonConfig 爱奇艺配置JSON字符串
     * @param userStatus 用户状态
     * @param platformOrder 平台顺序
     * @param platformAbbr 平台简称
     * @param needNormalCard 是否需要普通卡
     * @return 白金会员
     */
    private PlatinumMember buildPlatinumMember(
            List<CompetitorPriceDTO> prices,
            List<CompetitorPriceDTO> lastWeekPrices,
            List<String> vipTypes,
            List<String> productBlacklist,
            List<String> consecutiveMonthList,
            List<String> consecutiveQuarterList,
            List<String> consecutiveYearList,
            List<String> normalMonthList,
            List<String> normalQuarterList,
            List<String> normalYearList,
            String jsonConfig,
            String userStatus,
            List<String> platformOrder,
            Map<String, String> platformAbbr,
            boolean needNormalCard) {
        
        // 使用通用方法构建会员信息
        return buildMember(
            prices,
            lastWeekPrices,
            vipTypes,
            productBlacklist,
            consecutiveMonthList,
            consecutiveQuarterList,
            consecutiveYearList,
            normalMonthList,
            normalQuarterList,
            normalYearList,
            jsonConfig,
            "platinumMember",
            userStatus,
            platformOrder,
            platformAbbr,
            needNormalCard,
            PlatinumMember.builder()
        );
    }
    
    /**
     * 构建钻石会员（爱奇艺独有）
     * @param jsonConfig 爱奇艺配置JSON字符串
     * @param userStatus 用户状态
     * @param needNormalCard 是否需要普通卡
     * @return 钻石会员
     */
    private DiamondMember buildDiamondMember(String jsonConfig, String userStatus, boolean needNormalCard) {
        Map<String, SaleInfo> products = CompetitorPriceUtil.getMemberProductInfo(jsonConfig, "diamondMember", userStatus);
        if (products == null) {
            return null;
        }
        
        DiamondMember.DiamondMemberBuilder builder = DiamondMember.builder();
        
        // 获取连续包月价格
        if (products.containsKey("consecutiveMonth")) {
            builder.consecutiveMonth(products.get("consecutiveMonth").getPrice() + "元");
        }
        
        // 获取连续包年价格
        if (products.containsKey("consecutiveYear")) {
            builder.consecutiveYear(products.get("consecutiveYear").getPrice()+ "元");
        }
        
        // 如果需要普通卡，获取普通包月和包年价格
        if (needNormalCard) {
            if (products.containsKey("normalMonth")) {
                builder.normalMonth(products.get("normalMonth").getPrice()+ "元");
            }
            
            if (products.containsKey("normalYear")) {
                builder.normalYear(products.get("normalYear").getPrice()+ "元");
            }
        }
        
        return builder.build();
    }
    
    /**
     * 通用方法：构建会员信息
     * @param prices 价格数据
     * @param vipTypes 会员类型
     * @param productBlacklist 产品黑名单
     * @param consecutiveMonthList 连续包月白名单
     * @param consecutiveQuarterList 连续包季白名单
     * @param consecutiveYearList 连续包年白名单
     * @param normalMonthList 普通包月白名单
     * @param normalQuarterList 普通包季白名单
     * @param normalYearList 普通包年白名单
     * @param jsonConfig 爱奇艺配置JSON字符串
     * @param memberType 会员类型（如goldMember, platinumMember）
     * @param userStatus 用户状态
     * @param platformOrder 平台顺序
     * @param platformAbbr 平台简称
     * @param needNormalCard 是否需要普通卡
     * @param builder 会员构建器
     * @return 构建的会员对象
     */
    private <T, B> T buildMember(
            List<CompetitorPriceDTO> prices,
            List<CompetitorPriceDTO> lastWeekPrices,
            List<String> vipTypes,
            List<String> productBlacklist,
            List<String> consecutiveMonthList,
            List<String> consecutiveQuarterList,
            List<String> consecutiveYearList,
            List<String> normalMonthList,
            List<String> normalQuarterList,
            List<String> normalYearList,
            String jsonConfig,
            String memberType,
            String userStatus,
            List<String> platformOrder,
            Map<String, String> platformAbbr,
            boolean needNormalCard,
            Object builder) {
        
        // 筛选指定会员类型的价格数据
        List<CompetitorPriceDTO> vipPrices = prices.stream()
            .filter(price -> vipTypes.contains(price.getVipType()))
            .collect(Collectors.toList());


        List<CompetitorPriceDTO> lastWeekVipPrices = lastWeekPrices.stream()
            .filter(price -> vipTypes.contains(price.getVipType()))
            .collect(Collectors.toList());

        
        // 过滤黑名单产品
        if (productBlacklist != null && !productBlacklist.isEmpty()) {
            vipPrices = vipPrices.stream()
                .filter(price -> {
                    String product = price.getProduct();
                    if (product == null) {
                        return false;
                    }
                    return productBlacklist.stream().noneMatch(product::contains);
                })
                .collect(Collectors.toList());

            lastWeekVipPrices = lastWeekVipPrices.stream()
                .filter(price -> {
                    String product = price.getProduct();
                    if (product == null) {
                        return false;
                    }
                    return productBlacklist.stream().noneMatch(product::contains);
                })
                .collect(Collectors.toList());
        }
        
        // 获取爱奇艺会员配置
        Map<String, SaleInfo> products = CompetitorPriceUtil.getMemberProductInfo(jsonConfig, memberType, userStatus);
        
        // 通用设置会员价格的方法
        if (builder instanceof GoldMember.GoldMemberBuilder || builder instanceof PlatinumMember.PlatinumMemberBuilder) {
            // 获取设置方法
            Object memberBuilder = builder;
            
            try {
                // 设置连续包月价格
                List<PlatformPrice> consecutiveMonthPrices = buildPlatformPriceList(
                    vipPrices,
                    lastWeekVipPrices,
                    consecutiveMonthList, 
                    products, 
                    "consecutiveMonth", 
                    platformOrder, 
                    platformAbbr,
                    null
                );
                memberBuilder.getClass().getMethod("consecutiveMonth", List.class).invoke(memberBuilder, consecutiveMonthPrices);
                
                // 设置连续包季价格
                List<PlatformPrice> consecutiveQuarterPrices = buildPlatformPriceList(
                    vipPrices,
                    lastWeekVipPrices,
                    consecutiveQuarterList, 
                    products, 
                    "consecutiveQuarter", 
                    platformOrder, 
                    platformAbbr,
                    null
                );
                memberBuilder.getClass().getMethod("consecutiveQuarter", List.class).invoke(memberBuilder, consecutiveQuarterPrices);
                
                // 设置连续包年价格
                List<PlatformPrice> consecutiveYearPrices = buildPlatformPriceList(
                    vipPrices,
                    lastWeekVipPrices,
                    consecutiveYearList, 
                    products, 
                    "consecutiveYear", 
                    platformOrder, 
                    platformAbbr,
                    null
                );
                memberBuilder.getClass().getMethod("consecutiveYear", List.class).invoke(memberBuilder, consecutiveYearPrices);
                
                // 如果需要普通卡，设置普通月季年价格
                if (needNormalCard) {
                    // 设置普通包月价格
                    List<PlatformPrice> normalMonthPrices = buildPlatformPriceList(
                        vipPrices,
                        lastWeekVipPrices,
                        normalMonthList, 
                        products, 
                        "normalMonth", 
                        platformOrder, 
                        platformAbbr,
                        consecutiveMonthList  // 排除连续包月产品
                    );
                    memberBuilder.getClass().getMethod("normalMonth", List.class).invoke(memberBuilder, normalMonthPrices);
                    
                    // 设置普通包季价格
                    List<PlatformPrice> normalQuarterPrices = buildPlatformPriceList(
                        vipPrices,
                        lastWeekVipPrices,
                        normalQuarterList, 
                        products, 
                        "normalQuarter", 
                        platformOrder, 
                        platformAbbr,
                        consecutiveQuarterList  // 排除连续包季产品
                    );
                    memberBuilder.getClass().getMethod("normalQuarter", List.class).invoke(memberBuilder, normalQuarterPrices);
                    
                    // 设置普通包年价格
                    List<PlatformPrice> normalYearPrices = buildPlatformPriceList(
                        vipPrices,
                        lastWeekVipPrices,
                        normalYearList, 
                        products, 
                        "normalYear", 
                        platformOrder, 
                        platformAbbr,
                        consecutiveYearList  // 排除连续包年产品
                    );
                    memberBuilder.getClass().getMethod("normalYear", List.class).invoke(memberBuilder, normalYearPrices);
                }
                
                // 构建并返回结果
                return (T) memberBuilder.getClass().getMethod("build").invoke(memberBuilder);
            } catch (Exception e) {
                // 反射调用异常时返回null
                log.error("buildMember Exception :", e);
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 构建平台价格列表
     * @param prices 价格数据
     * @param productWhitelist 产品白名单
     * @param products 爱奇艺产品配置
     * @param priceType 价格类型
     * @param platformOrder 平台顺序
     * @param platformAbbr 平台简称
     * @param excludeProductList 需要排除的产品列表（可选）
     * @return 平台价格列表
     */
    private List<PlatformPrice> buildPlatformPriceList(
            List<CompetitorPriceDTO> prices,
            List<CompetitorPriceDTO> lastWeekPrices,
            List<String> productWhitelist, 
            Map<String, SaleInfo> products, 
            String priceType,
            List<String> platformOrder,
            Map<String, String> platformAbbr,
            List<String> excludeProductList) {
        
        // 创建结果列表，按照platformOrder的顺序预先分配空间
        List<PlatformPrice> result = new ArrayList<>(platformOrder.size());
        
        // 添加爱奇艺价格
        if (products != null && products.containsKey(priceType)) {
            SaleInfo saleInfo = products.get(priceType);
            if (saleInfo != null) {
                String product = saleInfo.getProduct();
                String price = saleInfo.getPrice();
                
                PlatformPrice iqiyiPrice = PlatformPrice.builder()
                    .platform("爱奇艺")
                    .product(product)
                    .price(price)
                    .desc("aqy：" + product + price + "元")
                    .build();
                result.add(iqiyiPrice);
            }
        }
        
        // 添加其他平台价格
        if (prices != null && !prices.isEmpty() && productWhitelist != null && !productWhitelist.isEmpty()) {
            // 筛选符合白名单的产品，并排除指定的产品
            Map<String, CompetitorPriceDTO> brandPriceMap = getStringCompetitorPriceDTOMap(prices, productWhitelist, excludeProductList);
            Map<String, CompetitorPriceDTO> lastWeekBrandPriceMap = getStringCompetitorPriceDTOMap(lastWeekPrices, productWhitelist, excludeProductList);

            // 按照platformOrder的顺序添加其他平台价格
            for (String platform : platformOrder) {
                // 跳过爱奇艺，因为已经单独处理
                if ("爱奇艺".equals(platform)) {
                    continue;
                }
                
                CompetitorPriceDTO priceDTO = brandPriceMap.get(platform);
                Optional<CompetitorPriceDTO> lastWeekPriceDTO = Optional.ofNullable(lastWeekBrandPriceMap.get(platform));
                if (priceDTO != null) {
                    String brand = priceDTO.getBrand();
                    String product = priceDTO.getProduct();
                    String price = priceDTO.getPrice();
                    String lastWeekPrice = lastWeekPriceDTO.map(CompetitorPriceDTO::getPrice).orElse(null);
                    String lastWeekProduct = lastWeekPriceDTO.map(CompetitorPriceDTO::getProduct).orElse(null);
                    boolean same = StringUtils.equals(price, lastWeekPrice);
                    String abbr = platformAbbr.getOrDefault(brand, brand);
                    String desc = same || lastWeekPrice == null  ? abbr + "：" + getProductReplaceName(product) + price + "元" : abbr + "：" + getProductReplaceName(lastWeekProduct) + lastWeekPrice + " -> " +  getProductReplaceName(product) + price + "元";
                    PlatformPrice platformPrice = PlatformPrice.builder()
                        .platform(brand)
                        .product(product)
                        .price(price)
                        .lastWeekProduct(lastWeekProduct)
                        .lastWeekPrice(lastWeekPrice)
                        .desc(desc)
                        .changed(!same && lastWeekPrice != null)
                        .build();
                    result.add(platformPrice);
                }
            }
        }
        return result;
    }
    private String getProductReplaceName(String product) {
        String s = productNameMap.get(product);
        if (StringUtils.isBlank(s)) {
            return product;
        }
        return s;
    }

    @NotNull
    private static Map<String, CompetitorPriceDTO> getStringCompetitorPriceDTOMap(List<CompetitorPriceDTO> prices, List<String> productWhitelist, List<String> excludeProductList) {
        List<CompetitorPriceDTO> filteredPrices = prices.stream()
            .filter(price -> {
                String product = price.getProduct();
                if (product == null) {
                    return false;
                }

                // 检查是否符合白名单
                boolean matchesWhitelist = productWhitelist.stream().anyMatch(product::contains);

                // 如果有排除列表，检查是否需要排除
                boolean shouldExclude = excludeProductList != null && !excludeProductList.isEmpty() &&
                                       excludeProductList.stream().anyMatch(product::contains);

                return matchesWhitelist && !shouldExclude;
            })
            .collect(Collectors.toList());

        // 使用Stream API按品牌分组，取每个品牌最低价格的产品
        Map<String, CompetitorPriceDTO> brandPriceMap = filteredPrices.stream()
            .filter(price -> StringUtils.isNotBlank(price.getBrand()) && !price.getBrand().equals("爱奇艺"))
            .filter(price -> StringUtils.isNotBlank(price.getPrice()))
            .collect(Collectors.toMap(
                CompetitorPriceDTO::getBrand,
                price -> price,
                (existing, replacement) -> {
                    try {
                        double existingPrice = Double.parseDouble(existing.getPrice());
                        double replacementPrice = Double.parseDouble(replacement.getPrice());
                        return replacementPrice < existingPrice ? replacement : existing;
                    } catch (NumberFormatException e) {
                        // 解析异常时保留现有价格
                        return existing;
                    }
                }
            ));
        return brandPriceMap;
    }

    public boolean save(CompetitorPriceSaveParam param) {
        List<CompetitorPriceDTO> competitorPriceDTOS = param.getProductPrices().stream()
            .map(competitorPriceProduct -> buildFrom(param, competitorPriceProduct))
            .collect(Collectors.toList());
        competitorPriceRepository.save(competitorPriceDTOS);
        return true;
    }

    private CompetitorPriceDTO buildFrom(CompetitorPriceSaveParam saveParam, CompetitorPriceProduct product) {
        return CompetitorPriceDTO.builder()
            .date(saveParam.getDate())
            .brand(saveParam.getBrand())
            .userStatus(saveParam.getUserStatus())
            .vipType(saveParam.getVipType())
            .product(product.getProduct())
            .price(product.getPrice())
            .build();
    }

}
