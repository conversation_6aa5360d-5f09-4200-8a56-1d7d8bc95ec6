package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;

import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.repository.TaskQueryInfoRepository;

/**
 * <AUTHOR>
 * @className QueryInfoService
 * @description
 * @date 2023/1/31
 **/
@Service
@Slf4j
public class TaskQueryInfoService {

    @Resource
    private TaskQueryInfoRepository taskQueryInfoRepository;


    public void saveOrUpdate(TaskQueryInfo queryInfo) {
        saveOrUpdateInternal(queryInfo, false);
    }

    public void saveOrUpdateScheduledQueryInfo(TaskQueryInfo queryInfo) {
        saveOrUpdateInternal(queryInfo, true);
    }

    private void saveOrUpdateInternal(TaskQueryInfo queryInfo, boolean isScheduled) {
        Long taskId = queryInfo.getTaskId();
        TaskQueryInfo existing = isScheduled
            ? taskQueryInfoRepository.searchByScheduledTaskId(taskId)
            : taskQueryInfoRepository.searchByTaskId(taskId);
        Date now = new Date();
        queryInfo.setUpdateTime(now);
        if (existing == null) {
            queryInfo.setCreateTime(now);
            if (isScheduled) {
                taskQueryInfoRepository.addScheduled(queryInfo);
            } else {
                taskQueryInfoRepository.add(queryInfo);
            }
            return;
        }

        if (isScheduled) {
            taskQueryInfoRepository.updateByScheduledTaskId(queryInfo);
        } else {
            taskQueryInfoRepository.updateByTaskId(queryInfo);
        }

    }


}
