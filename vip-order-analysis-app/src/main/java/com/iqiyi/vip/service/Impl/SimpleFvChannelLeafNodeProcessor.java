package com.iqiyi.vip.service.Impl;

import com.google.common.base.Supplier;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.FvChannelReqDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.LeafNodeProcessTypeEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.FvChannelLeafNodeProcessor;
import com.iqiyi.vip.task.CommonAnalysisTask;

/**
 * <AUTHOR>
 * @className SimpleFvChannelLeafNodeProcessor
 * @description
 * @date 2022/10/10
 **/
@Component
public class SimpleFvChannelLeafNodeProcessor implements FvChannelLeafNodeProcessor {

    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public LeafNodeProcessTypeEnum getProcessType() {
        return LeafNodeProcessTypeEnum.ONLY_FINAL_FV;
    }

    @Override
    public boolean[] findLeafNodes(AnalysisTaskExecuteDTO executeDTO, Queue<DataPermissionNode> queryLimits, List<DataPermissionNode> leafNodes, boolean ownedHighLevelUser, String account) {

        boolean dataPermission4o = false;
        boolean dataPermission4i = false;
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        Integer dataPermissionType = executeDTO.getDataPermissionType();
        List<String> level2BusinessIds = conditionParamContext.getLevel2BusinessIds()
            .stream()
            .filter(Objects::nonNull)
            .map(String::valueOf)
            .distinct()
            .collect(Collectors.toList());
        final boolean noQueryParams = CollectionUtils.isEmpty(level2BusinessIds) || ObjectUtils.isEmpty(level2BusinessIds.get(0));
        if (noQueryParams && ownedHighLevelUser) {
            return new boolean[]{dataPermission4o, dataPermission4i};
        }
        level2BusinessIds = noQueryParams ? dataPermissionService.getOwnedDataPermissionIds(ConditionEnum.BUSINESS_LEVEL, LabelEnum.L2, executeDTO)
            : level2BusinessIds;
        final List<DataPermissionNode> baseLayeredDataPermissions = dataPermissionService.getBaseLayeredDataPermissions(ConditionEnum.BUSINESS_LEVEL, LabelEnum.L2);
        for (DataPermissionNode dataPermissionNode : baseLayeredDataPermissions) {
            if (dataPermissionNode != null && level2BusinessIds.contains(dataPermissionNode.getId())) {
                queryLimits.offer(dataPermissionNode); // 将二级渠道加入限制队列
                dataPermission4o = dataPermission4o || Constants.unMainStation(dataPermissionNode.getId()); // 二级渠道条件包含站外渠道
                dataPermission4i = dataPermission4i || !Constants.unMainStation(dataPermissionNode.getId()); // 二级渠道条件包含站内渠道
            }
        }

        final Map<LabelEnum, Supplier<List<FvChannelReqDTO>>> labelEnumSupplierMap = CommonAnalysisTask.initUserPermissionParamsGetFuncMap(Maps.newEnumMap(LabelEnum.class), conditionParamContext);
        for (LabelEnum labelEnum : new LabelEnum[]{LabelEnum.L8, LabelEnum.L7, LabelEnum.L6, LabelEnum.L5, LabelEnum.L4, LabelEnum.L3}) {
            // 倒序寻找显示指定的最低一级渠道
            Supplier<List<FvChannelReqDTO>> listSupplier = labelEnumSupplierMap.get(labelEnum);
            List<FvChannelReqDTO> fvChannelReqDTOS = listSupplier.get();
            if (CollectionUtils.isEmpty(fvChannelReqDTOS) || (fvChannelReqDTOS.size() == 1 && Objects.isNull(fvChannelReqDTOS.get(0).getId()))) {
                //当前层级未指定
                continue;
            }

            fvChannelReqDTOS = fvChannelReqDTOS.stream().filter(v -> v.getId() != null).collect(Collectors.toList());
            // 与已申请权限取交集 -- 渠道
            ConditionDataPermissionHandler permissionHandler = ConditionDataPermissionHandler.getConditionDataPermissionHandler(executeDTO);

            List<String> fvChannelIds = fvChannelReqDTOS.stream()
                .map(FvChannelReqDTO::getId)
                .collect(Collectors.toList());

            final List<String> fvIds4OwnedDataPermission = permissionHandler
                .intersectionWithOwnedDataPermissions(
                    executeDTO,
                    fvChannelIds,
                    ConditionEnum.BUSINESS_LEVEL,
                    labelEnum
                );

            if (!conditionParamContext.getLevel2BusinessIds().contains(Integer.parseInt(Constants.INTERNAL_CHANNEL_ID))) {
                fvChannelReqDTOS = fvChannelReqDTOS.stream().filter(v -> fvIds4OwnedDataPermission.contains(v.getId())).collect(Collectors.toList());
            }

            //查找当前层级的所有数据
            List<DataPermissionNode> layeredDataPermissions = dataPermissionService.getBaseLayeredDataPermissions(ConditionEnum.BUSINESS_LEVEL, labelEnum);
            ArrayList<DataPermissionNode> fvDataPermissionNodeList = new ArrayList<>();
            ArrayList<DataPermissionNode> teamDataPermissionNodeList = new ArrayList<>();
            for (FvChannelReqDTO fv : fvChannelReqDTOS) {
                DataPermissionNode dataPermissionNode = convertFromFvChannelReq(fv, layeredDataPermissions);
                if (dataPermissionNode == null) {
                    continue;
                }
                //将当前节点作为叶子节点
                fvDataPermissionNodeList.add(dataPermissionNode);
            }
            leafNodes.addAll(fvDataPermissionNodeList);

            for (DataPermissionNode fvDataPermissionNode : fvDataPermissionNodeList) {
                DataPermissionNode teamDataPermissionNode = dataPermissionService.findT(fvDataPermissionNode);
                if (teamDataPermissionNode != null) {
                    teamDataPermissionNodeList.add(teamDataPermissionNode);
                }
            }
            List<DataPermissionNode> distinctTeamIdNode = teamDataPermissionNodeList
                .stream()
                .distinct()
                .collect(Collectors.toList());
            leafNodes.addAll(distinctTeamIdNode);
            //已处理完团队信息和显示指定的层级信息，无需再循环
            break;
        }
        return new boolean[]{dataPermission4o, dataPermission4i};
    }


    /**
     * 将当前层级的节点转化成DataPermissionNode
     * @param reqDTO
     * @param baseLayeredDataPermissions
     * @return
     */
    private DataPermissionNode convertFromFvChannelReq(FvChannelReqDTO reqDTO, List<DataPermissionNode> baseLayeredDataPermissions) {
        if (reqDTO == null) {
            return null;
        }
        return baseLayeredDataPermissions
            .stream()
            .filter(node -> node.getId().equals(reqDTO.getId()) && node.getTeamId().equals(String.valueOf(reqDTO.getTeamId())))
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(null);
    }

}
