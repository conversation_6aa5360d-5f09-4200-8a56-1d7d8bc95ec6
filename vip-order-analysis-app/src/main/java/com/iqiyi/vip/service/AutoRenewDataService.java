package com.iqiyi.vip.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.api.ChartApi;
import com.iqiyi.vip.domain.repository.AutorenewSetLogRepository;
import com.iqiyi.vip.domain.repository.OrderRepository;
import com.iqiyi.vip.dto.funnel.AutoRenewCancelDTO;
import com.iqiyi.vip.dto.funnel.CancelFunnelDataDTO;
import com.iqiyi.vip.dto.pie.AutoRenewCancelSceneDTO;
import com.iqiyi.vip.utils.JacksonUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022/3/17 上午 11:38
 */


@Slf4j
@Component
public class AutoRenewDataService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private AutorenewSetLogRepository autorenewSetLogRepository;

    @Resource
    private ChartApi chartApi;

    @Resource
    private CloudConfig cloudConfig;

    public AutoRenewCancelDTO getYesterdayDutNum() {
        log.info("task getYesterdayDutNum start at:{}, offset:{}", DateUtil.now(), 0);
        DateTime yesterday = DateUtil.offsetDay(DateUtil.date(), -1);
        String beginTime = DateUtil.beginOfDay(yesterday).toString();
        String endTime = DateUtil.endOfDay(yesterday).toString();
        TimeInterval timer = DateUtil.timer();
//        int dutUserNum = orderRepository.getDutUserNum("normal", beginTime, endTime);
        int dutUserNum = 0;
        log.info("task getYesterdayDutNum end at:{}, cost:{}s, result:{}, ", DateUtil.now(), timer.intervalSecond() ,dutUserNum);
        return new AutoRenewCancelDTO(0, dutUserNum);
    }

    private AutoRenewCancelDTO getCancelNum(Integer offset) {
        log.info("task getCancelNum start at:{}, offset:{}", DateUtil.now(), offset);
        String dt = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -2));
        String deadlineBeginTime =  DateUtil.formatDate(DateUtil.offsetDay(DateUtil.date(), -1));
        String deadlineEndTime = DateUtil.today();

        String operateTime = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.offsetDay(DateUtil.date(), -1), -offset));
        TimeInterval timer = DateUtil.timer();
        int cancelNumByRange = autorenewSetLogRepository.getCancelNumByRange(dt, operateTime, deadlineBeginTime, deadlineEndTime);
        log.info("task getCancelNum end at:{}, cost:{}s, result:{}, ", DateUtil.now(), timer.intervalSecond(), cancelNumByRange);
        return new AutoRenewCancelDTO(offset, cancelNumByRange);
    }

    private List<AutoRenewCancelSceneDTO> getCancelSceneData(String dt, String beginTime, String endTime) {
        return autorenewSetLogRepository.getCancelSceneData(dt, beginTime, endTime);
    }

    public void cancelScenePie(String beginTime, String endTime, String dateTitle, String filePath) {
        log.info("cancelScenePie beginTime:{}, endTime:{}, dateTitle:{}, filePath:{}", beginTime, endTime, dateTitle, filePath);
        String chartFormWork = cloudConfig.getProperty("autorenew.cancel.scene.chart.formwork", "");
        if (StringUtils.isBlank(chartFormWork)) {
            return;
        }
        JsonNode jsonNode = JacksonUtils.parseObject(chartFormWork, JsonNode.class);
        JsonNode titleNode = jsonNode.get("option").get("title");
        ObjectNode titleObjectNode = (ObjectNode) titleNode;
        DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -1);
        String dt = DateUtil.formatDate(dateTime);
        titleObjectNode.put("text", dateTitle + "自动续费取消场景分布");
        titleObjectNode.put("subtext", "By会员交易组");

        JsonNode seriesNode = jsonNode.get("option").get("series");
        ObjectNode dataObjectNode = (ObjectNode) seriesNode.get(0);
        List<AutoRenewCancelSceneDTO> cancelSceneData = getCancelSceneData(dt, beginTime, endTime);
        JSONArray jsonArray = JSONUtil.parseArray(cancelSceneData);
        dataObjectNode.putPOJO("data", jsonArray);
        Map<String, Object> paramMap = JacksonUtils.beanToMap(jsonNode);
        chartApi.downloadCharts(paramMap, "自动续费取消场景饼状图", filePath);
    }

    public String yesterdayCancelScenePie(String bathPath) {
        DateTime today = DateUtil.date();
        DateTime yesterDay = DateUtil.offsetDay(today, -1);
        String beginTime = DateUtil.beginOfDay(yesterDay).toString();
        String endTime = DateUtil.endOfDay(yesterDay).toString();

        String dateTitle = DateUtil.formatDate(yesterDay);
        String filePath = bathPath + dateTitle + "_cancelScenePie.jpg";
        cancelScenePie(beginTime, endTime, dateTitle, filePath);
        return filePath;
    }

    public String rencent30DayCancelPie() {
        DateTime today = DateUtil.date();
        DateTime yesterday = DateUtil.offsetDay(today, -1);
        DateTime monthAgo = DateUtil.offsetDay(today, -31);
        String beginTime = DateUtil.beginOfDay(DateUtil.offsetDay(today, -1)).toString();
        String endTime = DateUtil.endOfDay(monthAgo).toString();

        String dateTitle = DateUtil.formatDate(monthAgo) + "-" + DateUtil.formatDate(yesterday);
        String filePath = "/data/logs/chart/" + dateTitle + "_30dayCancelScenePie.jpg";
        cancelScenePie(beginTime, endTime, dateTitle, filePath);
        return filePath;
    }

    public void rencent15CancelPie() {
        DateTime today = DateUtil.date();
        DateTime yesterday = DateUtil.offsetDay(today, -1);
        DateTime monthAgo = DateUtil.offsetDay(today, -16);
        String beginTime = DateUtil.beginOfDay(DateUtil.offsetDay(today, -1)).toString();
        String endTime = DateUtil.endOfDay(monthAgo).toString();

        String dateTitle = DateUtil.formatDate(monthAgo) + "-" + DateUtil.formatDate(yesterday);
        String filePath = "/data/logs/chart/" + dateTitle + "_15dayCancelScenePie.jpg";
        cancelScenePie(beginTime, endTime, dateTitle, filePath);
    }

    public String getCancelFunnel(String bathPath) {
        log.info("cancelFunnel start at:{}", DateUtil.now());
        String chartFormWork = cloudConfig.getProperty("autorenew.funnel.chart.formwork", "");
        if (StringUtils.isBlank(chartFormWork)) {
            return null;
        }
        DateTime today = DateUtil.date();
        DateTime yesterday = DateUtil.offsetDay(today, -1);
        String deadlineDay = DateUtil.formatDate(yesterday);
        JsonNode jsonNode = JacksonUtils.parseObject(chartFormWork, JsonNode.class);
        JsonNode titleNode = jsonNode.get("option").get("title");
        ObjectNode titleObjectNode = (ObjectNode) titleNode;
        String titleText = deadlineDay + "日到期自动续费用户前30天取消漏斗";
        titleObjectNode.put("text", titleText);
        titleObjectNode.put("subtext", "By会员交易组");

        JsonNode seriesNode = jsonNode.get("option").get("series");
        ObjectNode dataObjectNode = (ObjectNode) seriesNode.get(0);
        List<CancelFunnelDataDTO> funnelData = getAutoRenewFunnelData();
        JSONArray jsonArray = JSONUtil.parseArray(funnelData);
        dataObjectNode.putPOJO("data", jsonArray);
        Map<String, Object> paramMap = JacksonUtils.beanToMap(jsonNode);
        String filePath = bathPath + deadlineDay + "_30CancelFunnel.jpeg";
        chartApi.downloadCharts(paramMap, "自动续费取消场漏斗图", filePath);
        return filePath;
    }


    public List<CancelFunnelDataDTO> getAutoRenewFunnelData() {
        log.info("getAutoRenewFunnelData start at:{}", DateUtil.now());
        TimeInterval timer = DateUtil.timer();
        List<CancelFunnelDataDTO> funnelData = new ArrayList<>();
        AutoRenewCancelDTO yesterdayDutData = getYesterdayDutNum();
        Integer dutNum = yesterdayDutData.getCancelNum();
        AutoRenewCancelDTO offset30 = getCancelNum(30);


        CancelFunnelDataDTO funnelData30 = new CancelFunnelDataDTO();
        int totalNum = offset30.getCancelNum() + dutNum;
        funnelData30.setValue(100);
        funnelData30.setName("到期前30天自动续费用户数：" + totalNum);
        funnelData.add(funnelData30);

        CancelFunnelDataDTO funnelDataLast = new CancelFunnelDataDTO();
        funnelDataLast.setValue(20);
        funnelDataLast.setName("到期当天发起续费任务用户数：" + dutNum + "\n\n到期前30内共取消自动续费用户数: " + offset30.getCancelNum());
        funnelData.add(funnelDataLast);


        AutoRenewCancelDTO offset21 = getCancelNum(21);
        CancelFunnelDataDTO funnelData21 = new CancelFunnelDataDTO();
        int currentNum = offset21.getCancelNum() + dutNum;
        funnelData21.setValue(80);
        funnelData21.setName("到期前21天自动续费用户数：" + currentNum + "\n\n已取消自动续费用户数: " + (totalNum - currentNum));
        funnelData.add(funnelData21);

        AutoRenewCancelDTO offset14 = getCancelNum(14);
        CancelFunnelDataDTO funnelData14 = new CancelFunnelDataDTO();
        currentNum = offset14.getCancelNum() + dutNum;
        funnelData14.setValue(60);
        funnelData14.setName("到期前14天自动续费用户数：" + currentNum + "\n\n已取消自动续费用户数: " + (totalNum - currentNum));
        funnelData.add(funnelData14);

        AutoRenewCancelDTO offset7 = getCancelNum(7);
        CancelFunnelDataDTO funnelData7 = new CancelFunnelDataDTO();
        currentNum = offset7.getCancelNum() + dutNum;
        funnelData7.setValue(40);
        funnelData7.setName("到期前7天自动续费用户数：" + currentNum + "\n\n已取消自动续费用户数: " + (totalNum - currentNum));
        funnelData.add(funnelData7);
        log.info("getAutoRenewFunnelData end at:{}, cost:{}", DateUtil.now(), timer.intervalSecond());
        return funnelData;
    }

    public static void main(String[] args) {
        DateTime dt = DateUtil.offsetDay(DateUtil.date(), -1);
        String beginTime = DateUtil.formatDate(dt);
    }
}
