package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.domain.repository.ConfigChangeLogRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.ChangeListRespDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffRespDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionListRespDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConfigTypeEnum;
import com.iqiyi.vip.handler.ConfigVersionHandler;

/**
 * <AUTHOR>
 * @className ConfigVersionMangerService
 * @description
 * @date 2022/11/24
 **/
@Component
@Slf4j
public class ConfigVersionMangerService {

    @Resource
    private ConfigChangeLogRepository configChangeLogRepository;

    @Resource
    private ConfigVersionHandler configVersionHandler;

    public CommonResult versionSwitch(VersionSwitchReqDTO reqDTO) {
        Integer configType = reqDTO.getConfigType();
        Long configId = reqDTO.getConfigId();
        Integer version = reqDTO.getVersion();
        Integer currentVersion = reqDTO.getCurrentVersion();
        if (currentVersion.equals(version)) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), String.format("当前版本已是%s, 无需切换", version));
        }
        List<ConfigChangeLog> configChangeLogs = configChangeLogRepository.getConfigChangeLogs(configType, configId, null);
        ConfigChangeLog destinationChangeLog;
        if (version != null) {
            destinationChangeLog = configChangeLogs
                .stream()
                .filter(configChangeLog -> configChangeLog.getVersion().equals(version))
                .findFirst().orElse(null);
        } else {
            destinationChangeLog = configChangeLogs
                .stream()
                .max(Comparator.comparing(ConfigChangeLog::getUpdateTime))
                .orElse(null);
        }
        if (destinationChangeLog == null) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), "未找到要切换的版本，无法切换");
        }
        ConfigVersionProcessor processor = configVersionHandler.getProcessor(configType);
        if (processor == null) {
            return CommonResult.create(CodeEnum.ERROR_PARAM);
        }
        return processor.versionSwitch(reqDTO, destinationChangeLog);
    }

    public ListResult<VersionDiffRespDTO> versionDiff(VersionDiffReqDTO reqDTO) {
        Integer configType = reqDTO.getConfigType();
        Long configId = reqDTO.getConfigId();
        Integer diffVersion = reqDTO.getDiffVersion();
        Integer currentVersion = reqDTO.getCurrentVersion();
        List<ConfigChangeLog> currentConfigChangeLogs = configChangeLogRepository.getConfigChangeLogs(configType, configId, currentVersion);
        if (CollectionUtils.isEmpty(currentConfigChangeLogs)) {
            return new ListResult(CodeEnum.ERROR_PARAM.getCode(), String.format("未找到id：%s的指标模板", configId));
        }
        ConfigChangeLog currentConfigChangeLog = currentConfigChangeLogs.get(0);
        ConfigChangeLog diffConfigChangeLog;
        if (diffVersion == null) {
            diffConfigChangeLog = configChangeLogRepository.getConfigChangeLogs(configType, configId, null)
                .stream()
                .filter(configChangeLog -> !configChangeLog.getVersion().equals(currentVersion))
                .max(Comparator.comparing(ConfigChangeLog::getUpdateTime))
                .orElse(null);
        } else {
            diffConfigChangeLog = configChangeLogRepository.getConfigChangeLogs(configType, configId, diffVersion)
                .stream()
                .max(Comparator.comparing(ConfigChangeLog::getUpdateTime))
                .orElse(null);
        }
        ArrayList<VersionDiffRespDTO> diffRespDTOS = new ArrayList<>();
        VersionDiffRespDTO diffRespDTO = VersionDiffRespDTO.builder()
            .configType(configType)
            .configId(configId)
            .version(diffConfigChangeLog == null ? null : diffConfigChangeLog.getVersion())
            .value(diffConfigChangeLog == null ? null : diffConfigChangeLog.getConfigValue())
            .configName(diffConfigChangeLog == null ? null : diffConfigChangeLog.getConfigName())
            .operator(diffConfigChangeLog == null ? null : diffConfigChangeLog.getOperator())
            .operateTime(diffConfigChangeLog == null ? null : diffConfigChangeLog.getUpdateTime().getTime())
            .configCode(diffConfigChangeLog == null ? null : diffConfigChangeLog.getConfigCode())
            .configDesc(diffConfigChangeLog == null ? null : diffConfigChangeLog.getConfigDesc())
            .configGroupId(diffConfigChangeLog == null ? null : diffConfigChangeLog.getConfigGroupId())
            .templateId(diffConfigChangeLog == null ? null : diffConfigChangeLog.getTemplateId())
            .commitNote(diffConfigChangeLog == null ? null : diffConfigChangeLog.getCommitNote())
            .build();
        diffRespDTOS.add(diffRespDTO);
        VersionDiffRespDTO currentDiffRespDTO = VersionDiffRespDTO.builder()
            .configType(configType)
            .configId(configId)
            .version(currentConfigChangeLog.getVersion())
            .value(currentConfigChangeLog.getConfigValue())
            .configName(currentConfigChangeLog.getConfigName())
            .operator(currentConfigChangeLog.getOperator())
            .operateTime(currentConfigChangeLog.getUpdateTime().getTime())
            .configCode(currentConfigChangeLog.getConfigCode())
            .configDesc(currentConfigChangeLog.getConfigDesc())
            .configGroupId(currentConfigChangeLog.getConfigGroupId())
            .templateId(currentConfigChangeLog.getTemplateId())
            .commitNote(currentConfigChangeLog == null ? null : currentConfigChangeLog.getCommitNote())
            .build();
        diffRespDTOS.add(currentDiffRespDTO);
        return ListResult.createSuccess(diffRespDTOS);
    }

    public ListResult<VersionListRespDTO> versionList(VersionListReqDTO reqDTO) {
        Long configId = reqDTO.getConfigId();
        Integer configType = reqDTO.getConfigType();
        ConfigTypeEnum typeEnum = ConfigTypeEnum.of(configType);
        if (typeEnum == null) {
            return new ListResult<>(CodeEnum.ERROR_PARAM.getCode(), "非法的配置类型");
        }
        List<ConfigChangeLog> configChangeLogs = configChangeLogRepository.getConfigChangeLogs(configType, configId, null);
        List<VersionListRespDTO> versionListRespDTOS = configChangeLogs.stream()
            .sorted(Comparator.comparing(ConfigChangeLog::getVersion).reversed())
            .map(c -> new VersionListRespDTO(configType, configId, c.getVersion()))
            .collect(Collectors.toList());
        return ListResult.createSuccess(versionListRespDTOS);
    }

    public ListResult<ChangeListRespDTO> changeList(ChangeListReqDTO reqDTO) {
        Integer configType = reqDTO.getConfigType();
        ConfigTypeEnum typeEnum = ConfigTypeEnum.of(configType);
        if (typeEnum == null) {
            return new ListResult<>(CodeEnum.ERROR_PARAM.getCode(), "非法的配置类型");
        }
        Long configId = reqDTO.getConfigId();
        List<ConfigChangeLog> configChangeLogs = configChangeLogRepository.getConfigChangeLogs(configType, configId, null);
        List<ChangeListRespDTO> changeListRespDTOS = configChangeLogs.stream()
            .sorted(Comparator.comparing(ConfigChangeLog::getVersion).reversed())
            .map(this::buildChangeListRespDTO)
            .collect(Collectors.toList());
        return ListResult.createSuccess(changeListRespDTOS);
    }

    private ChangeListRespDTO buildChangeListRespDTO(ConfigChangeLog log) {
        return ChangeListRespDTO.builder()
            .id(log.getId())
            .configType(log.getConfigType())
            .configId(log.getConfigId())
            .configName(log.getConfigName())
            .operator(log.getOperator())
            .configDesc(log.getConfigDesc())
            .operator(log.getOperator())
            .operateTime(log.getUpdateTime().getTime())
            .configValue(log.getConfigValue())
            .commitNote(log.getCommitNote())
            .version(log.getVersion())
            .build();
    }


}
