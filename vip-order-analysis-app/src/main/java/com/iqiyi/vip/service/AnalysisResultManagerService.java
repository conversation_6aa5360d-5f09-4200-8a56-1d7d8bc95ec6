package com.iqiyi.vip.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.schema.ValidateResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.domain.repository.FeedbackRepository;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.target.AvailableDateQueryDTO;
import com.iqiyi.vip.dto.target.AvailableDateResultDTO;
import com.iqiyi.vip.dto.target.BaseQueryDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.SimpleQueryDTO;
import com.iqiyi.vip.dto.target.SimpleQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.BusinessTypeEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.enums.MemberRetentionSubThemeTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;
import com.iqiyi.vip.mq.MsgSender;
import com.iqiyi.vip.task.CommonAnalysisTask;
import com.iqiyi.vip.utils.JacksonUtils;
import com.iqiyi.vip.utils.TaskUtil;
import static com.iqiyi.vip.domain.entity.AnalysisTaskDO.initializeTaskDO;
import static com.iqiyi.vip.dto.target.SimpleQueryDTO.getParamMap;
import static com.mysql.cj.util.TimeUtil.DATE_FORMATTER;
import com.iqiyi.vip.domain.model.QueryFeedback;
import com.iqiyi.vip.dto.target.FeedbackDTO;

/**
 * @className AnalysisResultManagerService
 * @description
 * <AUTHOR>
 * @date 2022/5/16
 **/
@Component
@Slf4j
public class AnalysisResultManagerService {

    public static final String BEGIN_DATE = "2011-03-14";
    @Resource
    private AnalysisTaskRepository analysisTaskRepository;
    @Resource
    private MsgSender msgSender;
    @Resource
    private CloudConfig cloudConfig;
    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private AvailableDateRepository availableDateRepository;

    @Resource
    private FeedbackRepository feedbackRepository;

    @Value("${availableDate.tip:（数据生产延迟问题已通知技术人员）}")
    private String availableDateTip;
    @Autowired
    private AnalysisTargetRepository analysisTargetRepository;
    @Autowired
    private CommonAnalysisTask commonAnalysisTask;

    @Resource
    protected TaskQueryInfoService taskQueryInfoService;

    @Autowired
    private AlterService alterService;

    @Autowired
    private AnalysisDimensionRepository analysisDimensionRepository;

    @Autowired
    private AnalysisConditionRepository conditionRepository;


    @ConfigJsonValue("${simple.query.sql.clean.text:[]}")
    private List<String> querySqlCleanText;

    @ConfigJsonValue("${feed.back.receivers:['chenguilong']}")
    private List<String> feedBackReceivers;

    @ConfigJsonValue("${simple.query.need.data.tip:true}")
    private boolean simpleQueryNeedDataTip;

    public CommonResult executeQuery(BaseQueryDTO queryDTO) {
        prepareParams(queryDTO);
        String operator = queryDTO.getOperator();
        if (!cloudConfig.getProperty("access.white.list", "").contains(operator)
            && !dataPermissionService.ownedDataPermission(queryDTO)) {
            return new CommonResult(BaseResponse.CodeEnum.ACCESS_DENY.getCode(), BaseResponse.CodeEnum.ACCESS_DENY.getMsg());
        }

        if (queryDTO.getBusinessTypeId() != null && queryDTO.getBusinessTypeId().equals(2)) {
            // 转移分析 自定义时间合规性判断
            ConditionParamContext conditionParamContext = queryDTO.getConditionParamContext();
            if (queryDTO.getTargetCodes() != null && !queryDTO.getTargetCodes().isEmpty()) {
                if (queryDTO.getTargetCodes().get(0).toLowerCase().contains("before")) {
                    Long payStartTime = conditionParamContext.getPayStartTime();
                    Long compareEndTime = conditionParamContext.getCompareEndTime();
                    if (payStartTime < compareEndTime) {
                        return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), "溯源分析与自定义时间冲突，请重新设置");
                    }
                } else {
                    Long payEndTime = conditionParamContext.getPayEndTime();
                    Long compareStartTime = conditionParamContext.getCompareStartTime();
                    if (payEndTime > compareStartTime) {
                        return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), "流向分析与自定义时间冲突，请重新设置");
                    }
                }
            }
        }

        String taskMD5 = TaskUtil.getTaskMD5(queryDTO);
        AnalysisTaskDO task = analysisTaskRepository.getTaskByUniqueIdentification(taskMD5);
        if (task != null && task.taskExist()) {
            return new CommonResult(CodeEnum.IDEMPOTENT.getCode(), "已存在相同参数的指标分析任务，任务id为：" + task.getId());
        }
        // 查询条件、维度、指标落库
        AnalysisTaskDO analysisTaskDO = AnalysisTaskDO.initializeTaskDO(queryDTO, taskMD5, AnalysisTaskSourceEnum.UI_MANUAL);
        analysisTaskRepository.addAnalysisTask(analysisTaskDO);
        // 查询条件、维度、指标mq发放
        AnalysisTaskExecuteDTO taskExecuteDTO = AnalysisTaskExecuteDTO.initializeTaskExecuteDO(queryDTO, taskMD5, analysisTaskDO.getId(), AnalysisTaskSourceEnum.UI_MANUAL);
//        javaObjectRocketMQTemplate.send(taskExecuteDTO);
        msgSender.sendAsyncTaskMsg(taskExecuteDTO);
        return new CommonResult(CodeEnum.SUCCESS.getCode(), "查询时间预估10分钟左右，查询结果稍后会发到您的邮箱中，请耐心等候");
    }

    public AvailableDateResultDTO availableDate(AvailableDateQueryDTO queryDTO) {
        // 设置默认主题类型
        Integer themeType = Optional.ofNullable(queryDTO)
            .map(AvailableDateQueryDTO::getThemeType)
            .orElse(ThemeTypeEnum.ORDER_THEME.getCode());

        Integer themeSubType = Optional.ofNullable(queryDTO)
            .map(AvailableDateQueryDTO::getSubThemeType)
            .orElse(null);

        // 根据不同子类型处理
        if (themeSubType == null) {
            return getResultByThemeType(themeType);
        } else if (MemberRetentionSubThemeTypeEnum.LOSS_ANALYSIS.getCode().equals(themeSubType)) {
            return getResultByThemeTypeAndSubType(themeType, themeSubType);
        } else if (MemberRetentionSubThemeTypeEnum.LOSS_AND_BACK_ANALYSIS.getCode().equals(themeSubType)) {
            return getRangeResultByThemeTypeAndSubType(themeType, themeSubType);
        }

        return getRangeResultByThemeTypeAndSubType(themeType, themeSubType);
    }

    private AvailableDateResultDTO getResultByThemeType(Integer themeType) {
        AvailableDt availableDt = availableDateRepository.maxDate(themeType);
        String maxDt = Optional.ofNullable(availableDt)
            .map(AvailableDt::getTableDt)
            .orElse(BEGIN_DATE);

        return buildResult(BEGIN_DATE, maxDt);
    }

    private AvailableDateResultDTO getResultByThemeTypeAndSubType(Integer themeType, Integer themeSubType) {
        AvailableDt availableDt = availableDateRepository.maxDateByThemeTypeAndThemeSubType(themeType, themeSubType);
        String maxDt = Optional.ofNullable(availableDt)
            .map(AvailableDt::getTableDt)
            .orElse(BEGIN_DATE);

        return buildResult(BEGIN_DATE, maxDt);
    }

    private AvailableDateResultDTO getRangeResultByThemeTypeAndSubType(Integer themeType, Integer themeSubType) {
        List<AvailableDt> availableDts = availableDateRepository.dateRangeByThemeTypeAndThemeSubType(themeType, themeSubType);

        if (CollectionUtils.isEmpty(availableDts)) {
            return buildResult(BEGIN_DATE, BEGIN_DATE);
        }

        String startDateStr = availableDts.get(availableDts.size() - 1).getTableDt();
        String endDateStr = availableDts.get(0).getTableDt();

        return buildResult(startDateStr, endDateStr);
    }

    private AvailableDateResultDTO buildResult(String startDateStr, String endDateStr) {
        try {
            Date startDate = DateUtil.parseDate(startDateStr);
            Date endDate = DateUtil.parseDate(endDateStr);

            return AvailableDateResultDTO.builder()
                .startDate(DateUtil.beginOfDay(startDate).getTime())
                .endDate(DateUtil.endOfDay(endDate).getTime())
                .tip(buildAvailableDate(endDateStr))
                .build();
        } catch (Exception e) {
            // 记录日志并处理异常
            log.error("日期解析错误: start={}, end={}", startDateStr, endDateStr, e);
            return null; // 或返回默认值
        }
    }

    private String buildAvailableDate(String maxDt) {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        boolean isAfter9 = hour >= 9;

        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 格式化为字符串
        String expectedDt = sdf.format(calendar.getTime());
        if (!expectedDt.equals(maxDt) && isAfter9) {
            return availableDateTip;
        }
        return null;
    }

    private void prepareParams(BaseQueryDTO queryDTO) {
        Map<String, List<Object>> paramMap = queryDTO.getConditionParamMap();
        if (toBTask(paramMap)) {
            paramMap.remove("customIncomeType");
        }
        ConditionParamContext paramContext = ConditionParamContext.builder()
            .paramMap(paramMap).build();
        queryDTO.setConditionParamContext(paramContext);
    }

    private boolean toBTask(Map<String, List<Object>> paramMap) {
        if (paramMap == null) return false;
        return Stream.of("vipOrderCardType", "vipOrderVipTypes", "vipOrderGroupIds", "orderType")
            .anyMatch(key -> paramMap.containsKey(key) && !paramMap.get(key).isEmpty());
    }


    private void handleBizNames(ConditionParamContext paramContext) {
        List<String> bizNames = paramContext.getBizNames();
        List<String> bizEndKeys = paramContext.getBizEndKeys();
        if (CollectionUtils.isEmpty(bizNames) && CollectionUtils.isNotEmpty(bizEndKeys)) {
            // 获取所有bizEndKeys的前缀并去重
            List<String> prefixes = bizEndKeys.stream()
                .map(key -> key.split("_")[0])
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

            // 获取businessName条件对象
            AnalysisCondition businessNameCondition = conditionRepository.selectByCode("businessName");
            
            if (businessNameCondition == null) {
                // 如果条件对象没查到，bizNames的值就是前缀们
                paramContext.getParamMap().put("businessName", new ArrayList<>(prefixes));
            } else {
                // 获取条件对象的json配置中的code列表
                List<String> conditionCodes = businessNameCondition.getEnums().stream()
                    .map(enumItem -> String.valueOf(enumItem.getCode()))
                    .collect(Collectors.toList());
                
                // 获取前缀与条件对象code的交集
                List<String> intersection = prefixes.stream()
                    .filter(conditionCodes::contains)
                    .collect(Collectors.toList());
                
                paramContext.getParamMap().put("businessName", new ArrayList<>(intersection));
            }
        }
    }

    public DataResult<SimpleQueryResultDTO> simpleQuery(SimpleQueryDTO queryDTO) throws UnsupportedEncodingException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<String, List<Object>> paramMap = getParamMap(queryDTO);
        ConditionParamContext paramContext = new ConditionParamContext(paramMap);
        // 处理bizNames
        handleBizNames(paramContext);
        ValidateResult validateResult = validateParams(paramContext);
        AnalysisTaskDO analysisTaskDO = initializeTaskDO(queryDTO, paramContext);
        analysisTaskRepository.addAnalysisTask(analysisTaskDO);
        String query = queryDTO.getQuery();
        String originalQuery = queryDTO.getOriginalQuery();
        // 保存query和answer到QueryFeedback表
        if (StringUtils.isNotBlank(query)) {
            QueryFeedback feedback = new QueryFeedback();
            feedback.setTaskId(analysisTaskDO.getId());
            feedback.setUniqueId(analysisTaskDO.getUniqueIdentification());
            feedback.setQuery(URLDecoder.decode(query, StandardCharsets.UTF_8.name()));
            feedback.setOriginalQuery(URLDecoder.decode(originalQuery, StandardCharsets.UTF_8.name()));
            feedback.setPipelineId(queryDTO.getPipelineId());
            feedback.setStatus(-1);
            feedback.setOperator(analysisTaskDO.getOperator());
            feedbackRepository.save(feedback);
        }
        if (!validateResult.isSuccess()) {
            return getSimpleFailedDataResult(paramContext, paramMap, validateResult);
        }
        // 查询条件、维度、指标mq发放
        AnalysisTaskExecuteDTO taskExecuteDTO = buildExecuteDO(analysisTaskDO, queryDTO, paramMap);
        Long taskId = analysisTaskDO.getId();
        String operator = analysisTaskDO.getOperator();
        try {
            List<AnalysisTarget> analysisTargets = taskExecuteDTO.getTargetCodes().stream()
                .map(analysisTargetRepository::selectByCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            List<TargetAnalysisQueryDTO> analysisQueryDTOS = commonAnalysisTask.constructTargetAnalysisQueryDTO(taskExecuteDTO, analysisTargets, true);

            TaskQueryInfo taskQueryInfo = TaskQueryInfo.buildFrom(taskId, JacksonUtils.toJsonString(analysisQueryDTOS));
            taskQueryInfoService.saveOrUpdate(taskQueryInfo);

            List<OriginalQueryResultDTO> queryResultDTOS = commonAnalysisTask.executeQuery(analysisQueryDTOS, taskId);
            // 此次是指标失败
            stopWatch.stop();
            if (CollectionUtils.isEmpty(queryResultDTOS) || queryResultDTOS.stream().noneMatch(OriginalQueryResultDTO::getSuccess)) {
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            } else {
                // 任务成功
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            }
            SimpleQueryResultDTO simpleQueryResultDTO = getSimpleQueryResultDTO(queryResultDTOS, analysisQueryDTOS, analysisTaskDO);
            if (simpleQueryNeedDataTip) {
                String dataTip = buildDataTip(taskExecuteDTO.getTargetCodes(), taskExecuteDTO.getDimensionCodes(), paramMap);
                log.info("dataTip:{}", dataTip);
                simpleQueryResultDTO.setDataDesc(dataTip);
            }
            log.info("queryResultDTOS:{}", JacksonUtils.toJsonString(queryResultDTOS));

            return DataResult.success(simpleQueryResultDTO);
        } catch (Exception e) {
            stopWatch.stop();
            log.error("processAsync error ! taskId:{}, operator:{}", taskId, operator, e);
            analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            alterService.sendHotChat(String.format("指标分析失败！任务id：%s, 操作人：%s, 运行环境：%s", taskId, operator, System.getProperty("spring.profiles.active")));
            return getSimpleFailedDataResult(paramContext, paramMap, validateResult);
        }
    }

    @NotNull
    private DataResult<SimpleQueryResultDTO> getSimpleFailedDataResult(ConditionParamContext paramContext, Map<String, List<Object>> paramMap, ValidateResult validateResult) {
        SimpleQueryResultDTO resultDTO = new SimpleQueryResultDTO();
        String dataTip = buildDataTip(paramContext.getTargetCodes(), paramContext.getDimensionCodes(), paramMap);
        log.info("dataTip:{}", dataTip);
        resultDTO.setDataDesc(dataTip);
        DataResult<SimpleQueryResultDTO> dataResult = new DataResult<>(CodeEnum.ERROR_PARAM.getCode(), validateResult.getMessage());
        dataResult.setData(resultDTO);
        return dataResult;
    }

    public ValidateResult validateParams(ConditionParamContext paramContext) {
        Long payStartTime = paramContext.getPayStartTime();
        Long payEndTime = paramContext.getPayEndTime();
        if (payStartTime == null || payEndTime == null) {
            return new ValidateResult(false, "请明确下时间范围", new Object[0]);
        }

        List<String> operator = paramContext.getSimpleQueryOperator();
        if (CollectionUtils.isEmpty(operator)) {
            return new ValidateResult(false, "未获取到账号信息", new Object[0]);
        }

        String tableDt = availableDateRepository.maxDate(ThemeTypeEnum.ORDER_THEME.getCode()).getTableDt();
        // 1. 转换日期字符串为 LocalDate
        LocalDate beginDate = parseDate(BEGIN_DATE);
        LocalDate maxTableDate = parseDate(tableDt);

        // 2. 转换时间戳为 LocalDate（假设时间戳为毫秒）
        LocalDate startDate = convertTimestampToLocalDate(payStartTime);
        LocalDate endDate = convertTimestampToLocalDate(payEndTime);

        // 3. 校验规则
        if (startDate.isBefore(beginDate)) {
            return new ValidateResult(false, "起始时间超出范围", new Object[0]);
        }

        if (endDate.isAfter(maxTableDate)) {
            return new ValidateResult(false, "结束时间超出范围，当前能支持的最近日期是：" + tableDt, new Object[0]);
        }
        return new ValidateResult(true, null, new Object[0]);
    }

    // 时间戳转 LocalDate（处理空值和时区）
    private LocalDate convertTimestampToLocalDate(Long timestamp) {
        if (timestamp == null) {
            throw new IllegalArgumentException("时间戳不能为null");
        }
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.systemDefault()) // 使用系统默认时区（可根据需求调整）
                .toLocalDate();
    }

    // 字符串转 LocalDate（处理格式错误）
    private LocalDate parseDate(String dateStr) {
        try {
            return LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式无效: " + dateStr + "，应为 " + DATE_FORMATTER.format(LocalDate.now()));
        }
    }

    @NotNull
    private SimpleQueryResultDTO getSimpleQueryResultDTO(List<OriginalQueryResultDTO> queryResultDTOS,
            List<TargetAnalysisQueryDTO> analysisQueryDTOS, AnalysisTaskDO analysisTaskDO) {
        OriginalQueryResultDTO queryResultDTO = queryResultDTOS.get(0);
        String querySql = analysisQueryDTOS.get(0).getQuerySql();
        ArrayList<String> headers = new ArrayList<>();
        LinkedHashMap<String, String> headColumnMap = queryResultDTO.getHeadColumnMap();
        for (Entry<String, String> entry : headColumnMap.entrySet()) {
            Integer businessTypeId = analysisTaskDO.getBusinessTypeId();
            Integer themeType = analysisTaskDO.getThemeType();
            String targetName = TargetDimensionHandler.getTargetName(businessTypeId, analysisTargetRepository,
                    entry.getKey(), null);
            String dimensionName = TargetDimensionHandler
                    .getDimensionName(businessTypeId, themeType, analysisDimensionRepository, entry.getKey(),
                            entry.getValue());
            String name = targetName == null ? dimensionName : targetName;
            headers.add(name.trim());
        }
        ArrayList<List<Object>> result = new ArrayList<>();
        List<LinkedHashMap<String, Object>> dataList = queryResultDTO.getDataList();
        for (LinkedHashMap<String, Object> linkedHashMap : dataList) {
            ArrayList<Object> row = new ArrayList<>();
            for (Entry<String, Object> entry : linkedHashMap.entrySet()) {
                Object entryValue = entry.getValue();
                if (entryValue instanceof Double) {
                    entryValue = String.format("%.2f", entryValue);
                }
                row.add(entryValue);
            }
            result.add(row);
        }
        // 清洗sql
        for (String cleanText : querySqlCleanText) {
            if (StringUtils.isBlank(cleanText)) {
                continue;
            }
            querySql = querySql.replace(cleanText, "");
        }

        SimpleQueryResultDTO simpleQueryResultDTO = new SimpleQueryResultDTO(headers, result.stream().limit(6).collect(Collectors.toList()), result.size(), null, analysisTaskDO.getId());
        return simpleQueryResultDTO;
    }

    public String buildDataTip(List<String> targetCodes, List<String> dimensionCodes,
            Map<String, List<Object>> paramMap) {
        // 查询指标名称
        String targetName = "";
        if (!targetCodes.isEmpty()) {
            AnalysisTarget target = analysisTargetRepository.selectByCode(targetCodes.get(0));
            if (target != null) {
                targetName = target.getName();
            }
        }

        // 查询维度名称
        String dimensionName = "";
        if (!dimensionCodes.isEmpty()) {
            AnalysisDimension dimension = analysisDimensionRepository
                    .selectByCodes(Lists.newArrayList(dimensionCodes.get(0)), BusinessTypeEnum.BASIC_ANALYSIS.getCode(),
                            ThemeTypeEnum.ORDER_THEME.getCode())
                    .get(0);
            if (dimension != null) {
                dimensionName = dimension.getName();
            }
        }

        // 构建条件描述
        StringBuilder conditionDesc = new StringBuilder();
        ConditionParamContext paramContext = new ConditionParamContext(paramMap);
        Long payStartTime = paramContext.getPayStartTime();
        Long payEndTime = paramContext.getPayEndTime();
        if (payStartTime != null && payEndTime != null) {

            String startDate = new SimpleDateFormat("yyyy-MM-dd").format(payStartTime);
            String endDate = new SimpleDateFormat("yyyy-MM-dd").format(payEndTime);
            conditionDesc.append("支付时间：").append(startDate);
            if (!startDate.equals(endDate)) {
                conditionDesc.append("-").append(endDate);
            }
            conditionDesc.append("、");
        }

        ArrayList<String> filerList = Lists.newArrayList("targetCodes", "dimensionCodes", "uid", "payStartTime",
                "payEndTime");
        paramMap.forEach((key, values) -> {
            if (filerList.contains(key.trim())) {
                return;
            }
            AnalysisCondition condition = conditionRepository.selectByCode(key);
            if (condition != null) {
                conditionDesc.append(condition.getName()).append("：");
                // 根据 enums 的 code 获取对应的 desc 值
                List<String> descValues = values.stream()
                        .map(value -> condition.getEnums().stream()
                                .filter(enumItem -> String.valueOf(enumItem.getCode()).equals(value.toString()))
                                .map(ConditionPair::getDesc)
                                .findFirst()
                                .orElse(String.valueOf(value))) // 如果找不到 desc，使用原始值
                        .collect(Collectors.toList());
                conditionDesc.append(String.join("，", descValues));
                conditionDesc.append("、");
            }
        });

        if (conditionDesc.length() > 0) {
            conditionDesc.delete(conditionDesc.length() - 1, conditionDesc.length());
        }

        // 拼接并返回 dataDesc
        if (StringUtils.isNotBlank(dimensionName)) {
            return String.format("指标：%s\n维度：%s\n条件：%s", targetName, dimensionName, conditionDesc);
        } else {
            return String.format("指标：%s\n条件：%s", targetName, conditionDesc);
        }
    }

    public static AnalysisTaskExecuteDTO buildExecuteDO(AnalysisTaskDO analysisTaskDO, SimpleQueryDTO queryDTO, Map<String, List<Object>> paramMap)
            throws UnsupportedEncodingException {
        ConditionParamContext paramContext = new ConditionParamContext(paramMap);
        List<String> targetCodes = paramContext.getTargetCodes();
        if (CollectionUtils.isEmpty(targetCodes)) {
            log.error("targetCodes or dimensionCodes is empty");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "指标为空");
        }
        List<String> operator = paramContext.getSimpleQueryOperator();
        if (CollectionUtils.isEmpty(operator)) {
            log.error("operator is empty");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "未获取到用户账号");
        }

        Long payStartTime = paramContext.getPayStartTime();
        Long payEndTime = paramContext.getPayEndTime();
        if (payStartTime == null || payEndTime == null) {
            log.error("payStartTime or payEndTime is null");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "未获取到支付时间");
        }
        String customIncomeType = paramContext.getCustomIncomeType();
        if (StringUtils.isBlank(customIncomeType)) {
            paramMap.put("customIncomeType", Collections.singletonList("vip"));
        }

        return AnalysisTaskExecuteDTO.builder()
                .taskId(analysisTaskDO.getId())
                .conditionParamContext(paramContext)
                .dimensionCodes(paramContext.getDimensionCodes())
                .targetCodes(targetCodes.stream().map(String::valueOf).collect(Collectors.toList()))
                .operator(operator.get(0))
                .taskMD5(analysisTaskDO.getUniqueIdentification())
                .businessTypeId(queryDTO.getBusinessTypeId())
                .dataPermissionType(queryDTO.getDataPermissionType())
                .themeType(queryDTO.getThemeType())
                .taskSource(AnalysisTaskSourceEnum.AGENT)
                .build();
    }

    public CommonResult saveFeedback(FeedbackDTO feedbackDTO) {
        //  查询任务
        AnalysisTaskDO task = findTask(feedbackDTO);

        // 查询或创建反馈记录
        QueryFeedback feedback = feedbackRepository.findByTaskId(task.getId());
        if (feedback == null) {
            feedback = new QueryFeedback();
            feedback.setTaskId(task.getId());
            feedback.setUniqueId(task.getUniqueIdentification());
        }

        // 更新反馈信息
        Integer status = feedbackDTO.getStatus();
        if (status != null) {
            feedback.setStatus(status);
            feedback.setFeedback(feedbackDTO.getFeedback());
            sendFeedbackNotification(task, feedback);
        } else {
            feedback.setAnswer(feedbackDTO.getAnswer());
            feedback.setFeedback(feedbackDTO.getFeedback());
        }

        // 保存反馈
        feedbackRepository.save(feedback);
        return CommonResult.success();
    }

    private AnalysisTaskDO findTask(FeedbackDTO feedbackDTO) {
        // 优先使用taskId查询
        AnalysisTaskDO task = analysisTaskRepository.getTaskById(feedbackDTO.getTaskId());
        // 如果taskId查询不到，且提供了taskUniqueId，则尝试使用taskUniqueId查询
        if (task == null && StringUtils.isNotBlank(feedbackDTO.getTaskUniqueId())) {
            task = analysisTaskRepository.getTaskByUniqueIdentification(feedbackDTO.getTaskUniqueId());
            if (task == null) {
                String taskUniqueId = Arrays.asList(feedbackDTO.getTaskUniqueId().split("@"))
                        .stream()
                        .filter(c -> c.contains("-"))
                        .findFirst()
                        .orElse(null);
                if (StringUtils.isNotBlank(taskUniqueId)) {
                    task = analysisTaskRepository.getTaskByUniqueIdentification(taskUniqueId);
                }
            }
        }
        return task;
    }

    private void sendFeedbackNotification(AnalysisTaskDO task, QueryFeedback feedback) {
        if (task == null || CollectionUtils.isEmpty(feedBackReceivers) || StringUtils.isBlank(feedback.getOriginalQuery())) {
            return;
        }

        String operator = task.getOperator();
        StringBuilder format = new StringBuilder();
        Integer status = feedback.getStatus();
        boolean notHappy = status.equals(0);
        format.append(String.format("%s对任务：%s %s", operator, task.getId(), notHappy ? "不满意" : "满意"));
        
        if (feedback != null) {
            String pipelineId = feedback.getPipelineId();
            if (StringUtils.isNotBlank(pipelineId)) {
                format.append("\n分析模式：").append(pipelineId);
            }
            String feedbackStr = feedback.getFeedback();
            if (StringUtils.isNotBlank(feedbackStr) && notHappy) {
                format.append("\n反馈意见：").append(feedback.getFeedback());
            }
            format.append("\n原始问句：").append(feedback.getOriginalQuery().split("@")[0]);
            format.append("\n规范化的问句：").append(feedback.getQuery().split("@")[0]);
            format.append("\n返回答案：\n").append(feedback.getAnswer());
        }
        alterService.sendHotChat(format.toString(), Joiner.on(",").join(feedBackReceivers));
    }
}
