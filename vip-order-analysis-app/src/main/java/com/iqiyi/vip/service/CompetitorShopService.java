package com.iqiyi.vip.service;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import com.iqiyi.vip.domain.repository.CompetitorShopRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSearchParam;

/**
 * @author: guojing
 * @date: 2024/12/25 10:40
 */
@Service
public class CompetitorShopService {

    @Resource
    private CompetitorShopRepository competitorShopRepository;

    public List<String> platformList() {
        return competitorShopRepository.platformList();
    }

    public CompetitorDateRangeDTO dateRange() {
        return competitorShopRepository.dateRange();
    }

    /**
     * 查询品牌列表
     *
     * @param platform 平台
     * @param dateTimestamp 日期时间戳
     * @return 品牌列表
     */
    public List<String> brandsList(String platform, Long dateTimestamp) {
        String date = null;
        if (dateTimestamp != null) {
            date = Instant.ofEpochMilli(dateTimestamp)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return competitorShopRepository.brandsList(platform, date);
    }

    public boolean save(CompetitorShopSaveDTO param) {
        competitorShopRepository.save(param);
        return true;
    }

    /**
     * 查询店铺监控数据
     *
     * @param param 查询参数
     * @return 店铺监控数据列表
     */
    public List<CompetitorShopDTO> search(CompetitorShopSearchParam param) {
        String date = null;
        if (param.getDate() != null) {
            date = Instant.ofEpochMilli(param.getDate())
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        CompetitorShopSearchParam newParam = CompetitorShopSearchParam.builder()
            .platform(param.getPlatform())
            .brand(param.getBrand())
            .storeType(param.getStoreType())
            .build();
        
        return competitorShopRepository.search(newParam, date);
    }
} 