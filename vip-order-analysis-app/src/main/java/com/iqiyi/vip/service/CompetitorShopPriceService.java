package com.iqiyi.vip.service;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.repository.CompetitorShopPriceRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceResultDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSearchParam;
import com.iqiyi.vip.dto.competitor.FlagshipPriceTrendExportDTO;
import com.iqiyi.vip.dto.competitor.FlagshipShopPriceTrendDTO;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceDistributionDTO;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceQueryParam;
import com.iqiyi.vip.dto.competitor.FlagshipStorePriceTrendQueryParam;
import com.iqiyi.vip.dto.competitor.NonFlagshipPriceTrendExportDTO;
import com.iqiyi.vip.dto.competitor.NonFlagshipShopPriceTrendDTO;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceDistributionDTO;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceQueryParam;
import com.iqiyi.vip.dto.competitor.NonFlagshipStorePriceTrendQueryParam;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.util.DynamicEasyExcelExportUtils;
import java.util.function.Function;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.utils.DateUtils;

/**
 * 竞品店铺价格服务
 */
@Service
@Slf4j
public class CompetitorShopPriceService {

    public static final int DIFF_DAY_OFFSET = 7;
    public static final String STUDENT_GROUP = "学生用户";
    @Resource
    private CompetitorShopPriceRepository competitorShopPriceRepository;

    /**
     * 旗舰店卡种白名单列表
     */
    @ConfigJsonValue("${competitor.flagship.cardtype.whitelist:['VIP月卡','VIP季卡','VIP年卡','VIP包月','VIP包季','VIP包年','京东联名卡','VIP45天卡']}")
    private List<String> flagshipCardTypeWhiteList;

    /**
     * 非旗舰店卡种白名单列表
     */
    @ConfigJsonValue("${competitor.nonflagship.cardtype.whitelist:['VIP周卡', 'VIP月卡', 'VIP季卡', 'VIP年卡', 'SVIP月卡', 'SVIP季卡', 'SVIP年卡', '星钻月卡', '星钻年卡']}")
    private List<String> nonFlagshipCardTypeWhiteList;

    @ConfigJsonValue("${competitor.shop.userGroup:{}}")
    private Map<String, List<String>> userGroupMap;

    @Value("${excel.width.adjust:0}")
    private Integer excelWidthAdjust;

    /**
     * 查询所有平台
     *
     * @return 平台列表
     */
    public List<String> platformList() {
        return competitorShopPriceRepository.platformList();
    }

    /**
     * 查询日期范围
     *
     * @return 日期范围
     */
    public CompetitorDateRangeDTO dateRange() {
        return competitorShopPriceRepository.dateRange();
    }

    /**
     * 查询品牌列表
     *
     * @param platform 平台
     * @param dateTimestamp 日期时间戳
     * @return 品牌列表
     */
    public List<String> brandsList(String platform, Long dateTimestamp) {
        String date = null;
        if (dateTimestamp != null) {
            date = Instant.ofEpochMilli(dateTimestamp)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return competitorShopPriceRepository.brandsList(platform, date);
    }

    /**
     * 保存价格信息
     *
     * @param param 保存参数
     * @return 是否成功
     */
    public boolean save(CompetitorShopPriceSaveDTO param) {
        competitorShopPriceRepository.save(param);
        return true;
    }

    /**
     * 根据条件查询
     *
     * @param param 查询参数
     * @return 价格信息结果
     */
    public CompetitorShopPriceResultDTO search(CompetitorShopPriceSearchParam param) {
        // 转换日期格式
        String date = null;
        if (param.getDate() != null) {
            date = Instant.ofEpochMilli(param.getDate())
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        // 创建新的查询参数，使用转换后的日期
        CompetitorShopPriceSearchParam newParam = CompetitorShopPriceSearchParam.builder()
            .platform(param.getPlatform())
            .brand(param.getBrand())
            .storeType(param.getStoreType())
            .build();
        
        // 查询原始数据
        List<CompetitorShopPriceDTO> originalList = competitorShopPriceRepository.search(newParam, date);
        String userGroup = param.getUserGroup();
        if (StringUtils.isNotBlank(userGroup) && CollectionUtils.isNotEmpty(userGroupMap.get(userGroup))) {
            // 如果指定了人群，过滤数据
            List<String> userGroupValues = userGroupMap.get(userGroup);
            originalList = originalList.stream()
                .filter(dto -> {
                    String dtoUserGroup = dto.getUserGroup();
                    if (StringUtils.isBlank(dtoUserGroup)) {
                        return false;
                    }
                    // 判断userGroupValues中是否有元素是dtoUserGroup的子串
                    return userGroupValues.stream().anyMatch(value -> dtoUserGroup.contains(value));
                })
                .collect(Collectors.toList());
        }

        CompetitorShopPriceResultDTO result = new CompetitorShopPriceResultDTO();

        // 如果查询结果为空，返回空结果
        if (originalList == null || originalList.isEmpty()) {
            return result;
        }

        // 判断店铺类型
        Integer storeType = param.getStoreType();

        // 根据店铺类型处理数据
        if (storeType != null && storeType == 1) {
            // 旗舰店处理逻辑
            result.setFlagshipItems(processFlagshipStoreData(originalList));
        } else if (storeType != null && storeType == 0) {
            // 非旗舰店处理逻辑
            result.setNonFlagshipItems(processNonFlagshipStoreData(originalList));
        }
        // 对旗舰店和非旗舰店数据进行排序
        if (result.getFlagshipItems() != null && !result.getFlagshipItems().isEmpty()) {
            result.getFlagshipItems().sort((a, b) -> {
                // 首先按会员类型排序：VIP > SVIP > JUMP
                int vipTypeCompare = compareVipType(a.getVipType(), b.getVipType());
                if (vipTypeCompare != 0) {
                    return vipTypeCompare;
                }
                // 会员类型相同时，按产品名称排序
                return a.getProduct().compareTo(b.getProduct());
            });
        }
        
        if (result.getNonFlagshipItems() != null && !result.getNonFlagshipItems().isEmpty()) {
            result.getNonFlagshipItems().sort((a, b) -> {
                // 首先按会员类型排序：VIP > SVIP > JUMP
                int vipTypeCompare = compareVipType(a.getVipType(), b.getVipType());
                if (vipTypeCompare != 0) {
                    return vipTypeCompare;
                }
                // 会员类型相同时，按产品名称排序
                return a.getProduct().compareTo(b.getProduct());
            });
        }
        return result;
    }

    /**
     * 处理旗舰店数据
     *
     * @param originalList 原始数据列表
     * @return 旗舰店商品信息列表
     */
    private List<CompetitorShopPriceResultDTO.FlagshipStoreItem> processFlagshipStoreData(List<CompetitorShopPriceDTO> originalList) {
        List<CompetitorShopPriceResultDTO.FlagshipStoreItem> flagshipItems = new ArrayList<>();

        // 遍历原始数据，提取旗舰店信息
        for (CompetitorShopPriceDTO dto : originalList) {
            // 构建旗舰店商品信息
            CompetitorShopPriceResultDTO.FlagshipStoreItem item = buildFlagshipStoreItem(dto);
            flagshipItems.add(item);
        }

        return flagshipItems;
    }

    /**
     * 构建旗舰店商品信息项
     *
     * @param dto 价格DTO
     * @return 旗舰店商品信息项
     */
    private CompetitorShopPriceResultDTO.FlagshipStoreItem buildFlagshipStoreItem(CompetitorShopPriceDTO dto) {
        // 格式化价格，保留1位小数
        String formattedPrice = formatPrice(dto.getPrice());

        // 构建旗舰店商品信息
        return CompetitorShopPriceResultDTO.FlagshipStoreItem.builder()
            .brand(dto.getBrand())
            .vipType(dto.getVipTypeNormalName())
            .product(dto.getProduct())
            .price(formattedPrice)
            .priceText(dto.getPriceText())
            .build();
    }

    /**
     * 处理非旗舰店数据
     *
     * @param originalList 原始数据列表
     * @return 非旗舰店商品信息列表
     */
    private List<CompetitorShopPriceResultDTO.NonFlagshipStoreItem> processNonFlagshipStoreData(List<CompetitorShopPriceDTO> originalList) {
        // 按会员类型和商品类型分组
        Map<String, List<CompetitorShopPriceDTO>> groupedProducts = groupProductsByVipTypeAndProduct(originalList);

        // 处理分组数据
        return processGroupedProducts(groupedProducts);
    }

    /**
     * 按会员类型和商品类型分组
     *
     * @param dtoList 所有价格DTO
     * @return 分组后的价格DTO
     */
    private Map<String, List<CompetitorShopPriceDTO>> groupProductsByVipTypeAndProduct(List<CompetitorShopPriceDTO> dtoList) {
        return dtoList.stream().collect(Collectors.groupingBy(p -> p.getVipType() + "-" + p.getProduct()));
    }

    /**
     * 处理分组后的产品数据
     *
     * @param groupedProducts 分组后的产品数据
     * @return 非旗舰店商品信息列表
     */
    private List<CompetitorShopPriceResultDTO.NonFlagshipStoreItem> processGroupedProducts(Map<String, List<CompetitorShopPriceDTO>> groupedProducts) {
        List<CompetitorShopPriceResultDTO.NonFlagshipStoreItem> nonFlagshipItems = new ArrayList<>();

        for (Map.Entry<String, List<CompetitorShopPriceDTO>> entry : groupedProducts.entrySet()) {
            List<CompetitorShopPriceDTO> group = entry.getValue();
            if (!group.isEmpty()) {
                // 构建非旗舰店商品信息
                CompetitorShopPriceResultDTO.NonFlagshipStoreItem item = buildNonFlagshipStoreItem(group);
                nonFlagshipItems.add(item);
            }
        }

        return nonFlagshipItems;
    }

    /**
     * 构建非旗舰店商品信息项
     *
     * @param group 同一分组的价格DTO
     * @return 非旗舰店商品信息项
     */
    private CompetitorShopPriceResultDTO.NonFlagshipStoreItem buildNonFlagshipStoreItem(List<CompetitorShopPriceDTO> group) {
        CompetitorShopPriceDTO firstItem = group.get(0);

        // 计算平均价格
        BigDecimal avgPrice = calculateAvgPrice(group);

        // 计算最低价格
        BigDecimal minPrice = calculateMinPrice(group);

        // 构建非旗舰店商品信息
        return CompetitorShopPriceResultDTO.NonFlagshipStoreItem.builder()
            .brand(firstItem.getBrand())
            .vipType(firstItem.getVipTypeNormalName())
            .product(firstItem.getProduct())
            .avgPrice(formatDecimal(avgPrice))
            .minPrice(formatDecimal(minPrice))
            .build();
    }

    /**
     * 格式化价格，保留1位小数
     */
    private String formatPrice(String priceStr) {
        try {
            BigDecimal price = new BigDecimal(priceStr);
            return formatDecimal(price);
        } catch (Exception e) {
            return priceStr;
        }
    }

    /**
     * 格式化价格和促销信息
     *
     * @param price 价格
     * @param promotion 促销信息
     * @return 格式化后的价格和促销信息
     */
    private String formatPriceWithPromotion(String price, String promotion) {
        if (price == null || price.isEmpty()) {
            return "";
        }

        String formattedPrice = formatPrice(price);

        if (promotion == null || promotion.isEmpty()) {
            return formattedPrice;
        }

        return formattedPrice + "（" + promotion + "）";
    }

    /**
     * 格式化小数，保留1位小数
     */
    private String formatDecimal(BigDecimal value) {
        return value.setScale(1, RoundingMode.HALF_UP).toString();
    }

    /**
     * 计算平均价格
     */
    private BigDecimal calculateAvgPrice(List<CompetitorShopPriceDTO> products) {
        if (products == null || products.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal sum = BigDecimal.ZERO;
        int validCount = 0;
        
        for (CompetitorShopPriceDTO product : products) {
            try {
                BigDecimal price = new BigDecimal(product.getPrice());
                sum = sum.add(price);
                validCount++;
            } catch (Exception e) {
                // 忽略无效价格
            }
        }
        
        if (validCount > 0) {
            return sum.divide(new BigDecimal(validCount), 2, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 计算最低价格
     */
    private BigDecimal calculateMinPrice(List<CompetitorShopPriceDTO> products) {
        if (products == null || products.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal minPrice = null;
        
        for (CompetitorShopPriceDTO product : products) {
            try {
                BigDecimal price = new BigDecimal(product.getPrice());
                if (minPrice == null || price.compareTo(minPrice) < 0) {
                    minPrice = price;
                }
            } catch (Exception e) {
                // 忽略无效价格
            }
        }
        
        return minPrice != null ? minPrice : BigDecimal.ZERO;
    }

    /**
     * 查找最低价格的产品
     *
     * @param products 产品列表
     * @return 最低价格的产品
     */
    private CompetitorShopPriceDTO findLowestPriceProduct(List<CompetitorShopPriceDTO> products) {
        if (products == null || products.isEmpty()) {
            return null;
        }
        
        return products.stream()
            .filter(product -> {
                try {
                    // 过滤掉无法解析为BigDecimal的价格
                    new BigDecimal(product.getPrice());
                    return true;
                } catch (Exception e) {
                    return false;
                }
            })
            .min((p1, p2) -> {
                try {
                    BigDecimal price1 = new BigDecimal(p1.getPrice());
                    BigDecimal price2 = new BigDecimal(p2.getPrice());
                    return price1.compareTo(price2);
                } catch (Exception e) {
                    return 0;
                }
            })
            .orElse(null);
    }

    /**
     * 查询旗舰店价格分布
     */
    public FlagshipStorePriceDistributionDTO queryFlagshipStorePriceDistribution(FlagshipStorePriceQueryParam param) {
        Long date = param.getDate();
        String platform = param.getPlatform();

        // 转换日期格式
        String startDate = Instant.ofEpochMilli(date)
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 查询当天价格数据，如果指定了平台则只查询该平台
        List<CompetitorShopPriceDTO> priceList = competitorShopPriceRepository.searchByDateRange(startDate, startDate, 1, platform, null)
            .stream()
            .filter(c -> {
                List<String> strings = userGroupMap.get(STUDENT_GROUP);
                return strings.stream().noneMatch(string -> StringUtils.equals(string, c.getUserGroup()));
            })
            .collect(Collectors.toList());

        // 计算前一天的日期
        String previousDate = Instant.ofEpochMilli(date)
            .atZone(ZoneId.systemDefault())
            .minusDays(DIFF_DAY_OFFSET)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 查询前一天价格数据，如果指定了平台则只查询该平台
        List<CompetitorShopPriceDTO> previousPriceList = competitorShopPriceRepository.searchByDateRange(previousDate, previousDate, 1, platform, null)
            .stream()
            .filter(c -> {
                List<String> strings = userGroupMap.get(STUDENT_GROUP);
                return strings.stream().noneMatch(string -> StringUtils.equals(string, c.getUserGroup()));
            })
            .collect(Collectors.toList());

        // 获取平台列表：如果指定了平台则只包含该平台，否则从查询结果中获取所有平台
        List<String> platforms;
        if (StringUtils.isNotBlank(platform)) {
            platforms = Collections.singletonList(platform);
        } else {
            platforms = priceList.stream()
                .map(CompetitorShopPriceDTO::getPlatform)
                .distinct()
                .collect(Collectors.toList());
        }

        // 构建价格分布数据
        return buildFlagshipPriceDistribution(platforms, priceList, previousPriceList);
    }
    
    /**
     * 构建旗舰店价格分布数据
     */
    private FlagshipStorePriceDistributionDTO buildFlagshipPriceDistribution(List<String> platforms, List<CompetitorShopPriceDTO> priceList, List<CompetitorShopPriceDTO> previousPriceList) {
        FlagshipStorePriceDistributionDTO result = FlagshipStorePriceDistributionDTO.builder()
            .platforms(new ArrayList<>())
            .build();

        // 处理每个平台
        for (String platform : platforms) {
            // 过滤出当前平台的价格数据
            List<CompetitorShopPriceDTO> platformPrices = priceList.stream()
                .filter(dto -> platform.equals(dto.getPlatform()))
                .collect(Collectors.toList());
            
            if (platformPrices.isEmpty()) {
                continue;
            }
            
            // 构建平台价格信息
            FlagshipStorePriceDistributionDTO.PlatformPriceInfo platformInfo = FlagshipStorePriceDistributionDTO.PlatformPriceInfo.builder()
                .platform(platform)
                .cardTypes(new ArrayList<>())
                .build();
            
            // 处理每个卡种
            for (String cardType : flagshipCardTypeWhiteList) {
                // 构建卡种信息
                boolean noneMatch = platformPrices.stream().noneMatch(p -> matchCardType(cardType, p));
                if (noneMatch) {
                    continue;
                }
                FlagshipStorePriceDistributionDTO.CardTypeInfo cardTypeInfo = FlagshipStorePriceDistributionDTO.CardTypeInfo.builder()
                    .cardType(cardType)
                    .userGroups(extractUserGroupPriceInfos(platformPrices, cardType, previousPriceList))
                    .build();
                
                platformInfo.getCardTypes().add(cardTypeInfo);
            }
            if (CollectionUtils.isNotEmpty(platformInfo.getCardTypes())) {
                result.getPlatforms().add(platformInfo);
            }
        }
        
        return result;
    }

    /**
     * 提取用户组价格信息
     */
    private List<FlagshipStorePriceDistributionDTO.UserGroupPriceInfo> extractUserGroupPriceInfos(List<CompetitorShopPriceDTO> platformPrices, String cardType, List<CompetitorShopPriceDTO> previousPriceList) {
        List<FlagshipStorePriceDistributionDTO.UserGroupPriceInfo> userGroupInfos = new ArrayList<>();
        
        // 为每个用户组创建价格信息
        Set<String> userGroups = userGroupMap.keySet();
        for (String userGroup : userGroups) {
            if (StringUtils.equals(STUDENT_GROUP, userGroup)) {
                continue;
            }
            FlagshipStorePriceDistributionDTO.UserGroupPriceInfo userGroupInfo = FlagshipStorePriceDistributionDTO.UserGroupPriceInfo.builder()
                .userGroup(userGroup)
                .brandPrices(new ArrayList<>())
                .build();
            
            // 填充每个品牌的价格信息
            for (String brand : Lists.newArrayList("腾讯", "优酷", "芒果")) {
                fillBrandPriceInfo(platformPrices, cardType, userGroup, brand, userGroupInfo, previousPriceList);
            }
            
            userGroupInfos.add(userGroupInfo);
        }
        
        return userGroupInfos;
    }
    
    /**
     * 填充品牌价格信息
     */
    private void fillBrandPriceInfo(List<CompetitorShopPriceDTO> platformPrices, String cardType, String userGroup, String brand, FlagshipStorePriceDistributionDTO.UserGroupPriceInfo userGroupInfo, List<CompetitorShopPriceDTO> previousPriceList) {
        // 过滤出符合条件的价格信息
        List<String> userGroupStrings = userGroupMap.get(userGroup);
        List<CompetitorShopPriceDTO> brandPrices = platformPrices.stream()
            .filter(dto -> brand.equals(dto.getBrand())
                && isContains(cardType, dto)
                && dto.matchUserGroup(userGroup, userGroupStrings)
            )
            .collect(Collectors.toList());
            
        // 过滤出前一天符合条件的价格信息
        List<CompetitorShopPriceDTO> previousBrandPrices = previousPriceList.stream()
            .filter(dto -> brand.equals(dto.getBrand())
                && isContains(cardType, dto)
                && dto.matchUserGroup(userGroup, userGroupStrings)
            )
            .collect(Collectors.toList());

        if (!brandPrices.isEmpty()) {
            // 找到最低价格的产品
            CompetitorShopPriceDTO lowestPriceProduct = findLowestPriceProduct(brandPrices);
            
            // 找到前一天最低价格的产品
            CompetitorShopPriceDTO previousLowestPriceProduct = null;
            if (!previousBrandPrices.isEmpty()) {
                previousLowestPriceProduct = findLowestPriceProduct(previousBrandPrices);
            }
            
            // 构建品牌价格信息
            FlagshipStorePriceDistributionDTO.BrandPriceInfo brandPriceInfo = FlagshipStorePriceDistributionDTO.BrandPriceInfo.builder()
                .brand(brand)
                .price(lowestPriceProduct != null ? formatPrice(lowestPriceProduct.getPrice()) : "-")
                .priceText(lowestPriceProduct != null ? lowestPriceProduct.getPriceText() : null)
                .build();
                
            // 比较当前价格和前一天价格，设置changed和finalText
            if (lowestPriceProduct != null) {
                String currentPrice = formatPrice(lowestPriceProduct.getPrice());
                String currentPriceText = lowestPriceProduct.getPriceText();
                
                if (previousLowestPriceProduct != null) {
                    String previousPrice = formatPrice(previousLowestPriceProduct.getPrice());
                    String previousPriceText = previousLowestPriceProduct.getPriceText();
                    
                    // 判断价格是否发生变化
                    boolean priceChanged = !currentPrice.equals(previousPrice);
                    brandPriceInfo.setChanged(priceChanged);
                    
                    // 设置finalText
                    if (priceChanged) {
                        StringBuilder finalText = new StringBuilder();
                        finalText.append(previousPrice);
                        
                        // 添加前一天的促销信息
                        if (StringUtils.isNotBlank(previousPriceText)) {
                            finalText.append("（").append(previousPriceText).append("）");
                        }
                        
                        // 添加箭头
                        finalText.append(" -> ");
                        
                        // 添加当前价格
                        finalText.append(currentPrice);
                        
                        // 添加当前促销信息
                        if (StringUtils.isNotBlank(currentPriceText)) {
                            finalText.append("（").append(currentPriceText).append("）");
                        }
                        
                        brandPriceInfo.setFinalText(finalText.toString());
                    } else {
                        // 价格没有变化，finalText与price相同
                        brandPriceInfo.setFinalText(currentPrice);
                        if (StringUtils.isNotBlank(currentPriceText)) {
                            brandPriceInfo.setFinalText(currentPrice + "（" + currentPriceText + "）");
                        }
                    }
                } else {
                    // 没有前一天数据，设置默认值
                    brandPriceInfo.setChanged(false);
                    
                    if (StringUtils.isNotBlank(currentPriceText)) {
                        brandPriceInfo.setFinalText(currentPrice + "（" + currentPriceText + "）");
                    } else {
                        brandPriceInfo.setFinalText(currentPrice);
                    }
                }
            } else {
                // 当前价格为空，设置默认值
                brandPriceInfo.setChanged(false);
                brandPriceInfo.setFinalText("-");
            }
            
            userGroupInfo.getBrandPrices().add(brandPriceInfo);
        } else {
            // 如果没有价格信息，添加一个空的品牌价格信息
            FlagshipStorePriceDistributionDTO.BrandPriceInfo brandPriceInfo = FlagshipStorePriceDistributionDTO.BrandPriceInfo.builder()
                .brand(brand)
                .price("-")
                .priceText(null)
                .changed(false)
                .finalText("-")
                .build();
            userGroupInfo.getBrandPrices().add(brandPriceInfo);
        }
    }

    private static boolean isContains(String cardType, CompetitorShopPriceDTO dto) {
        String key = dto.getVipTypeNormalName().trim() + dto.getProduct().trim();
        return StringUtils.equals(cardType, key);
    }

    /**
     * 查询非旗舰店价格分布
     *
     * @param param 查询参数
     * @return 非旗舰店价格分布
     */
    public NonFlagshipStorePriceDistributionDTO queryNonFlagshipStorePriceDistribution(NonFlagshipStorePriceQueryParam param) {
        // 转换日期格式
        String date = null;
        if (param.getDate() != null) {
            date = Instant.ofEpochMilli(param.getDate())
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        String platform = param.getPlatform();
        // 获取平台列表：如果指定了平台则只包含该平台，否则查询所有平台
        List<String> platforms;
        if (StringUtils.isNotBlank(platform)) {
            platforms = Collections.singletonList(platform);
        } else {
            platforms = platformList();
        }

        // 构建查询参数，如果指定了平台则添加平台过滤条件
        CompetitorShopPriceSearchParam searchParam = CompetitorShopPriceSearchParam.builder()
            .storeType(0) // 非旗舰店
            .platform(platform) // 添加平台过滤
            .build();
        
        // 查询价格数据
        List<CompetitorShopPriceDTO> priceList = competitorShopPriceRepository.search(searchParam, date);
        
        // 构建非旗舰店价格分布
        return buildNonFlagshipPriceDistribution(platforms, priceList);
    }

    /**
     * 构建非旗舰店价格分布
     *
     * @param platforms 平台列表
     * @param priceList 价格数据列表
     * @return 非旗舰店价格分布
     */
    private NonFlagshipStorePriceDistributionDTO buildNonFlagshipPriceDistribution(List<String> platforms, List<CompetitorShopPriceDTO> priceList) {
        List<NonFlagshipStorePriceDistributionDTO.PlatformPriceInfo> platformInfos = new ArrayList<>();

        // 遍历平台
        for (String platform : platforms) {
            // 过滤当前平台的价格数据
            List<CompetitorShopPriceDTO> platformPrices = priceList.stream()
                .filter(price -> platform.equals(price.getPlatform()))
                .collect(Collectors.toList());

            List<NonFlagshipStorePriceDistributionDTO.CardTypeInfo> cardTypeInfos = new ArrayList<>();

            // 遍历非旗舰店卡种白名单
            for (String cardType : nonFlagshipCardTypeWhiteList) {
                // 计算当前卡种下各品牌的价格
                boolean noneMatch = platformPrices.stream().noneMatch(p -> matchCardType(cardType, p));
                if (noneMatch) {
                    continue;
                }
                List<CompetitorShopPriceDTO> matchCardTypes = platformPrices.stream()
                    .filter(p -> matchCardType(cardType, p))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(matchCardTypes)) {
                    continue;
                }
                NonFlagshipStorePriceDistributionDTO.BrandPriceInfo brandPrices = calculateBrandPrices(platformPrices, cardType);
                if (StringUtils.isAllBlank(brandPrices.getIqiyiMinPrice(), brandPrices.getIqiyiAvgPrice())
                    && StringUtils.isAllBlank(brandPrices.getTencentMinPrice(), brandPrices.getTencentAvgPrice())
                    && StringUtils.isAllBlank(brandPrices.getYoukuMinPrice(), brandPrices.getYoukuAvgPrice())
                    && StringUtils.isAllBlank(brandPrices.getManguoMinPrice(), brandPrices.getManguoAvgPrice())) {
                    continue;
                }

                // 创建卡种信息
                NonFlagshipStorePriceDistributionDTO.CardTypeInfo cardTypeInfo = NonFlagshipStorePriceDistributionDTO.CardTypeInfo.builder()
                    .cardType(cardType)
                    .brandPrices(brandPrices)
                    .build();

                cardTypeInfos.add(cardTypeInfo);
            }

            // 创建平台信息
            NonFlagshipStorePriceDistributionDTO.PlatformPriceInfo platformInfo = NonFlagshipStorePriceDistributionDTO.PlatformPriceInfo.builder()
                .platform(platform)
                .cardTypes(cardTypeInfos)
                .build();
            platformInfos.add(platformInfo);
        }

        // 创建并返回结果
        return NonFlagshipStorePriceDistributionDTO.builder().platforms(platformInfos).build();
    }

    private static boolean matchCardType(String cardType, CompetitorShopPriceDTO dto) {
        String key = dto.getVipTypeNormalName().trim() + dto.getProduct().trim();
        return StringUtils.equals(cardType, key);
    }

    /**
     * 计算品牌价格
     */
    private NonFlagshipStorePriceDistributionDTO.BrandPriceInfo calculateBrandPrices(List<CompetitorShopPriceDTO> platformPrices, String cardType) {
        // 构建品牌价格信息
        NonFlagshipStorePriceDistributionDTO.BrandPriceInfo brandPriceInfo = NonFlagshipStorePriceDistributionDTO.BrandPriceInfo.builder()
            .cardType(cardType)
            .iqiyiAvgPrice("")
            .iqiyiMinPrice("")
            .tencentAvgPrice("")
            .tencentMinPrice("")
            .youkuAvgPrice("")
            .youkuMinPrice("")
            .manguoAvgPrice("")
            .manguoMinPrice("")
            .build();
        
        // 先过滤出符合当前卡种的价格数据
        List<CompetitorShopPriceDTO> cardTypePrices = platformPrices.stream()
            .filter(p -> matchCardType(cardType, p))
            .collect(Collectors.toList());
        
        // 提取爱奇艺价格信息
        List<CompetitorShopPriceDTO> iqiyiPrices = cardTypePrices.stream()
            .filter(dto -> "爱奇艺".equals(dto.getBrand()))
            .collect(Collectors.toList());
            
        if (!iqiyiPrices.isEmpty()) {
            // 计算平均价格和最低价格
            BigDecimal avgPrice = calculateAvgPrice(iqiyiPrices);
            BigDecimal minPrice = calculateMinPrice(iqiyiPrices);
            
            // 设置爱奇艺价格
            brandPriceInfo.setIqiyiAvgPrice(formatDecimal(avgPrice));
            brandPriceInfo.setIqiyiMinPrice(formatDecimal(minPrice));
        }
        
        // 提取腾讯价格信息
        List<CompetitorShopPriceDTO> tencentPrices = cardTypePrices.stream()
            .filter(dto -> "腾讯".equals(dto.getBrand()))
            .collect(Collectors.toList());
            
        if (!tencentPrices.isEmpty()) {
            // 计算平均价格和最低价格
            BigDecimal avgPrice = calculateAvgPrice(tencentPrices);
            BigDecimal minPrice = calculateMinPrice(tencentPrices);
            
            // 设置腾讯价格
            brandPriceInfo.setTencentAvgPrice(formatDecimal(avgPrice));
            brandPriceInfo.setTencentMinPrice(formatDecimal(minPrice));
        }
        
        // 提取优酷价格信息
        List<CompetitorShopPriceDTO> youkuPrices = cardTypePrices.stream()
            .filter(dto -> "优酷".equals(dto.getBrand()))
            .collect(Collectors.toList());
            
        if (!youkuPrices.isEmpty()) {
            // 计算平均价格和最低价格
            BigDecimal avgPrice = calculateAvgPrice(youkuPrices);
            BigDecimal minPrice = calculateMinPrice(youkuPrices);
            
            // 设置优酷价格
            brandPriceInfo.setYoukuAvgPrice(formatDecimal(avgPrice));
            brandPriceInfo.setYoukuMinPrice(formatDecimal(minPrice));
        }
        
        // 提取芒果价格信息
        List<CompetitorShopPriceDTO> manguoPrices = cardTypePrices.stream()
            .filter(dto -> "芒果".equals(dto.getBrand()))
            .collect(Collectors.toList());
            
        if (!manguoPrices.isEmpty()) {
            // 计算平均价格和最低价格
            BigDecimal avgPrice = calculateAvgPrice(manguoPrices);
            BigDecimal minPrice = calculateMinPrice(manguoPrices);
            
            // 设置芒果价格
            brandPriceInfo.setManguoAvgPrice(formatDecimal(avgPrice));
            brandPriceInfo.setManguoMinPrice(formatDecimal(minPrice));
        }
        
        return brandPriceInfo;
    }

    /**
     * 查询非旗舰店价格趋势
     *
     * @param param 查询参数
     * @return 非旗舰店价格趋势列表
     */
    public List<NonFlagshipShopPriceTrendDTO> queryNonFlagshipStorePriceTrend(NonFlagshipStorePriceTrendQueryParam param) {
        // 将时间戳转换为日期字符串
        String startDate = Instant.ofEpochMilli(param.getStartTime())
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        String endDate = Instant.ofEpochMilli(param.getEndTime())
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 一次性查询所有数据
        List<CompetitorShopPriceDTO> allPriceList = competitorShopPriceRepository.searchByDateRange(
            startDate, 
            endDate, 
            0, // 非旗舰店
            param.getPlatform(), 
            param.getBrand()
        );
        
        // 如果没有数据，返回空列表
        if (allPriceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按日期分组
        Map<String, List<CompetitorShopPriceDTO>> pricesByDate = allPriceList.stream()
            .filter(p -> nonFlagshipCardTypeWhiteList.contains(p.buildBrandPriceKey()))
            .collect(Collectors.groupingBy(CompetitorShopPriceDTO::getDate));
        
        // 存储每天的价格趋势数据
        List<NonFlagshipShopPriceTrendDTO> trendList = new ArrayList<>();
        
        // 处理每一天的数据
        for (Map.Entry<String, List<CompetitorShopPriceDTO>> entry : pricesByDate.entrySet()) {
            String date = entry.getKey();
            List<CompetitorShopPriceDTO> priceList = entry.getValue();
            
            // 处理当天的价格趋势
            NonFlagshipShopPriceTrendDTO dayTrend = processDayPriceTrend(date, priceList);
            trendList.add(dayTrend);
        }
        
        // 按日期排序
        trendList.sort((a, b) -> a.getDate().compareTo(b.getDate()));
        
        return trendList;
    }

    /**
     * 处理每天的价格趋势
     */
    private NonFlagshipShopPriceTrendDTO processDayPriceTrend(String date, List<CompetitorShopPriceDTO> priceList) {
        NonFlagshipShopPriceTrendDTO trendDTO = NonFlagshipShopPriceTrendDTO.builder()
            .date(date)
            .cardTypeList(new ArrayList<>())
            .build();
        
        // 按buildBrandPriceKey分组
        Map<String, List<CompetitorShopPriceDTO>> groupedByCardType = priceList.stream()
            .collect(Collectors.groupingBy(CompetitorShopPriceDTO::buildBrandPriceKey));
        
        // 处理每个分组
        for (Map.Entry<String, List<CompetitorShopPriceDTO>> entry : groupedByCardType.entrySet()) {
            String cardTypeKey = entry.getKey();
            List<CompetitorShopPriceDTO> cardTypePrices = entry.getValue();
            
            // 按品牌分组
            Map<String, List<CompetitorShopPriceDTO>> brandPrices = cardTypePrices.stream()
                .collect(Collectors.groupingBy(CompetitorShopPriceDTO::getBrand));
            
            // 处理每个品牌
            for (Map.Entry<String, List<CompetitorShopPriceDTO>> brandEntry : brandPrices.entrySet()) {
                String brand = brandEntry.getKey();
                List<CompetitorShopPriceDTO> prices = brandEntry.getValue();
                
                if (!prices.isEmpty()) {
                    // 计算平均价格和最低价格
                    BigDecimal avgPrice = calculateAvgPrice(prices);
                    BigDecimal minPrice = calculateMinPrice(prices);
                    
                    // 创建卡种价格信息
                    NonFlagshipShopPriceTrendDTO.CardTypePriceInfo priceInfo = NonFlagshipShopPriceTrendDTO.CardTypePriceInfo.builder()
                        .fullCardType(cardTypeKey)
                        .brand(brand)
                        .avgPrice(formatDecimal(avgPrice))
                        .minPrice(formatDecimal(minPrice))
                        .build();
                    
                    // 添加到结果列表
                    trendDTO.getCardTypeList().add(priceInfo);
                }
            }
        }
        
        return trendDTO;
    }

    /**
     * 查询旗舰店价格趋势
     *
     * @param param 查询参数
     * @return 旗舰店价格趋势列表
     */
    public List<FlagshipShopPriceTrendDTO> queryFlagshipStorePriceTrend(FlagshipStorePriceTrendQueryParam param) {
        // 将时间戳转换为日期字符串
        String startDate = Instant.ofEpochMilli(param.getStartTime())
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        String endDate = Instant.ofEpochMilli(param.getEndTime())
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 一次性查询所有数据
        List<CompetitorShopPriceDTO> allPriceList = competitorShopPriceRepository.searchByDateRange(
            startDate, 
            endDate, 
            1, // 旗舰店
            param.getPlatform(), 
            param.getBrand()
        );
        
        // 如果没有数据，返回空列表
        if (allPriceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按日期分组
        Map<String, List<CompetitorShopPriceDTO>> pricesByDate = allPriceList.stream()
            .collect(Collectors.groupingBy(CompetitorShopPriceDTO::getDate));
        
        // 存储每天的价格趋势数据
        List<FlagshipShopPriceTrendDTO> trendList = new ArrayList<>();
        
        // 处理每一天的数据
        for (Map.Entry<String, List<CompetitorShopPriceDTO>> entry : pricesByDate.entrySet()) {
            String date = entry.getKey();
            List<CompetitorShopPriceDTO> priceList = entry.getValue();
            
            // 处理当天的价格趋势
            FlagshipShopPriceTrendDTO dayTrend = processDayFlagshipPriceTrend(date, priceList);
            trendList.add(dayTrend);
        }
        
        // 按日期排序
        trendList.sort((a, b) -> a.getDate().compareTo(b.getDate()));
        
        return trendList;
    }

    /**
     * 处理单天的旗舰店价格趋势数据
     */
    private FlagshipShopPriceTrendDTO processDayFlagshipPriceTrend(String date, List<CompetitorShopPriceDTO> priceList) {
        // 创建结果对象
        FlagshipShopPriceTrendDTO trendDTO = FlagshipShopPriceTrendDTO.builder()
            .date(date)
            .cardTypeList(new ArrayList<>())
            .build();
        
        // 按buildBrandPriceKey和userGroup分组
        Map<String, List<CompetitorShopPriceDTO>> groupedPrices = new HashMap<>();
        
        // 遍历价格列表，按卡种和人群分组
        for (CompetitorShopPriceDTO dto : priceList) {
            String cardTypeKey = dto.buildBrandPriceKey();
            String userGroup = dto.getUserGroup();
            
            // 找出userGroup对应的userGroupMap中的key
            String userGroupKey = null;
            for (Map.Entry<String, List<String>> entry : userGroupMap.entrySet()) {
                if (entry.getValue().contains(userGroup)) {
                    userGroupKey = entry.getKey();
                    break;
                }
            }
            
            // 如果找不到对应的userGroupKey，使用原始userGroup
            if (userGroupKey == null) {
                continue;
            }
            
            // 组合key: cardTypeKey + userGroupKey
            String combinedKey = cardTypeKey + userGroupKey;
            
            // 添加到分组中
            List<CompetitorShopPriceDTO> group = groupedPrices.computeIfAbsent(combinedKey, k -> new ArrayList<>());
            group.add(dto);
        }
        
        // 处理每个分组
        for (Map.Entry<String, List<CompetitorShopPriceDTO>> entry : groupedPrices.entrySet()) {
            String combinedKey = entry.getKey();
            List<CompetitorShopPriceDTO> group = entry.getValue();
            
            // 按品牌分组
            Map<String, List<CompetitorShopPriceDTO>> brandPrices = group.stream()
                .collect(Collectors.groupingBy(CompetitorShopPriceDTO::getBrand));
            
            // 处理每个品牌
            for (Map.Entry<String, List<CompetitorShopPriceDTO>> brandEntry : brandPrices.entrySet()) {
                String brand = brandEntry.getKey();
                List<CompetitorShopPriceDTO> prices = brandEntry.getValue();
                
                if (!prices.isEmpty()) {
                    // 旗舰店直接展示价格，不需要计算平均价和最低价
                    // 找出价格最低的记录
                    CompetitorShopPriceDTO lowestPriceItem = findLowestPriceProduct(prices);
                    
                    if (lowestPriceItem != null) {
                        // 创建卡种价格信息
                        FlagshipShopPriceTrendDTO.CardTypePriceInfo priceInfo = FlagshipShopPriceTrendDTO.CardTypePriceInfo.builder()
                            .fullCardType(combinedKey)
                            .brand(brand)
                            .price(lowestPriceItem.getPrice())
                            .priceText(lowestPriceItem.getPriceText())
                            .build();
                        
                        // 添加到结果列表
                        trendDTO.getCardTypeList().add(priceInfo);
                    }
                }
            }
        }
        
        return trendDTO;
    }

    // 预定义会员类型顺序：VIP > SVIP > JUMP
    private static final List<String> VIP_TYPE_ORDER = Arrays.asList("VIP", "SVIP", "JUMP");

    /**
     * 比较会员类型，按照VIP>SVIP>JUMP的顺序排序
     * 
     * @param vipType1 会员类型1
     * @param vipType2 会员类型2
     * @return 比较结果
     */
    private int compareVipType(String vipType1, String vipType2) {
        return Integer.compare(getVipTypeIndex(vipType1), getVipTypeIndex(vipType2));
    }
    
    /**
     * 获取会员类型在预定义顺序中的索引
     * 
     * @param vipType 会员类型
     * @return 索引，如果不在列表中或为null返回列表大小（最低优先级）
     */
    private int getVipTypeIndex(String vipType) {
        if (StringUtils.isEmpty(vipType)) {
            return VIP_TYPE_ORDER.size();
        }
        
        String upperType = StringUtils.upperCase(vipType);
        
        // 需要特殊处理SVIP，避免被误判为VIP
        if (StringUtils.contains(upperType, "SVIP")) {
            return VIP_TYPE_ORDER.indexOf("SVIP");
        }
        
        for (String type : VIP_TYPE_ORDER) {
            if (StringUtils.contains(upperType, type)) {
                return VIP_TYPE_ORDER.indexOf(type);
            }
        }
        
        return VIP_TYPE_ORDER.size(); // 不在列表中的类型排在最后
    }

    @Resource
    private AnalysisResultStorageService analysisResultStorageService;
    
    @Value("${local.file.path:/data/logs}")
    private String localFilePath;
    
    /**
     * 下载非旗舰店价格趋势数据
     * 
     * @param param 查询参数
     * @param response HTTP响应对象
     */
    public String generateNonFlagshipPriceTrendExcel(NonFlagshipStorePriceTrendQueryParam param, HttpServletResponse response) {
        try {
            // 1. 获取非旗舰店价格趋势数据
            List<NonFlagshipShopPriceTrendDTO> trendList = queryNonFlagshipStorePriceTrend(param);
            if (CollectionUtils.isEmpty(trendList)) {
                return null;
            }
            
            // 2. 将数据打平到导出DTO中
            List<NonFlagshipPriceTrendExportDTO> exportDataList = new ArrayList<>();
            for (NonFlagshipShopPriceTrendDTO dayTrend : trendList) {
                String date = dayTrend.getDate();
                
                for (NonFlagshipShopPriceTrendDTO.CardTypePriceInfo priceInfo : dayTrend.getCardTypeList()) {
                    // 解析fullCardType获取卡种
                    String fullCardType = priceInfo.getFullCardType();
                    
                    // 构建导出DTO
                    NonFlagshipPriceTrendExportDTO exportDTO = NonFlagshipPriceTrendExportDTO.builder()
                        .date(date)
                        .platform(param.getPlatform())
                        .brand(priceInfo.getBrand())
                        .cardType(fullCardType)
                        .minPrice(priceInfo.getMinPrice())
                        .avgPrice(priceInfo.getAvgPrice())
                        .storeName("非旗舰店")  
                        .build();
                    exportDataList.add(exportDTO);
                }
            }
            
            // 3. 转换为ExcelSheetDataDTO
            ExcelSheetDataDTO excelSheetDataDTO = createNonFlagshipExcelSheetData(exportDataList, "非旗舰店价格趋势");
            
            // 4. 生成Excel并下载
            return generateAndUploadExcel(Collections.singletonList(excelSheetDataDTO), response, "非旗舰店价格趋势");
        } catch (Exception e) {
            log.error("下载非旗舰店价格趋势数据失败", e);
            return null;
        }
    }
    
    /**
     * 下载旗舰店价格趋势数据
     * 
     * @param param 查询参数
     * @param response HTTP响应对象
     */
    public String generateFlagshipPriceTrendExcel(FlagshipStorePriceTrendQueryParam param, HttpServletResponse response) {
        try {
            // 1. 获取旗舰店价格趋势数据
            List<FlagshipShopPriceTrendDTO> trendList = queryFlagshipStorePriceTrend(param);
            if (CollectionUtils.isEmpty(trendList)) {
                return null;
            }
            
            // 2. 将数据打平到导出DTO中
            List<FlagshipPriceTrendExportDTO> exportDataList = new ArrayList<>();
            for (FlagshipShopPriceTrendDTO dayTrend : trendList) {
                String date = dayTrend.getDate();
                
                for (FlagshipShopPriceTrendDTO.CardTypePriceInfo priceInfo : dayTrend.getCardTypeList()) {
                    // 构建导出DTO
                    FlagshipPriceTrendExportDTO exportDTO = FlagshipPriceTrendExportDTO.builder()
                        .date(date)
                        .platform(param.getPlatform())
                        .brand(priceInfo.getBrand())
                        .cardType(priceInfo.getFullCardType())
                        .price(priceInfo.getPrice())
                        .priceText(priceInfo.getPriceText())
                        .build();
                    exportDataList.add(exportDTO);
                }
            }
            
            // 3. 转换为ExcelSheetDataDTO
            ExcelSheetDataDTO excelSheetDataDTO = createFlagshipExcelSheetData(exportDataList, "旗舰店价格趋势");
            
            // 4. 生成Excel并下载
            return generateAndUploadExcel(Collections.singletonList(excelSheetDataDTO), response, "旗舰店价格趋势");
        } catch (Exception e) {
            log.error("下载旗舰店价格趋势数据失败", e);
            return null;
        }
    }
    
    /**
     * 创建非旗舰店Excel表格数据
     * 
     * @param exportDataList 导出数据列表
     * @param sheetName 表格名称
     * @return ExcelSheetDataDTO
     */
    private ExcelSheetDataDTO createNonFlagshipExcelSheetData(List<NonFlagshipPriceTrendExportDTO> exportDataList, String sheetName) {
        // 1. 创建表头映射
        LinkedHashMap<String, String> headColumnMap = new LinkedHashMap<>();
        headColumnMap.put("date", "日期");
        headColumnMap.put("platform", "平台");
        headColumnMap.put("brand", "品牌");
        headColumnMap.put("cardType", "卡种");
        headColumnMap.put("minPrice", "最低价");
        headColumnMap.put("avgPrice", "平均价");
        headColumnMap.put("storeName", "店铺名称");
        // 2. 创建数据列表
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        for (NonFlagshipPriceTrendExportDTO exportDTO : exportDataList) {
            LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("date", exportDTO.getDate());
            dataMap.put("platform", exportDTO.getPlatform());
            dataMap.put("brand", exportDTO.getBrand());
            dataMap.put("cardType", exportDTO.getCardType());
            dataMap.put("minPrice", exportDTO.getMinPrice());
            dataMap.put("avgPrice", exportDTO.getAvgPrice());
            dataMap.put("storeName", exportDTO.getStoreName());
            dataList.add(dataMap);
        }
        
        // 3. 创建ExcelSheetDataDTO
        return ExcelSheetDataDTO.builder()
            .sheetName(sheetName)
            .headColumnMap(headColumnMap)
            .dataList(dataList)
            .build();
    }
    
    /**
     * 创建旗舰店Excel表格数据
     * 
     * @param exportDataList 导出数据列表
     * @param sheetName 表格名称
     * @return ExcelSheetDataDTO
     */
    private ExcelSheetDataDTO createFlagshipExcelSheetData(List<FlagshipPriceTrendExportDTO> exportDataList, String sheetName) {
        // 1. 创建表头映射
        LinkedHashMap<String, String> headColumnMap = new LinkedHashMap<>();
        headColumnMap.put("date", "日期");
        headColumnMap.put("platform", "平台");
        headColumnMap.put("brand", "品牌");
        headColumnMap.put("cardType", "卡种");
        headColumnMap.put("price", "价格");
        headColumnMap.put("priceText", "促销信息");
        
        // 2. 创建数据列表
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        for (FlagshipPriceTrendExportDTO exportDTO : exportDataList) {
            LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("date", exportDTO.getDate());
            dataMap.put("platform", exportDTO.getPlatform());
            dataMap.put("brand", exportDTO.getBrand());
            dataMap.put("cardType", exportDTO.getCardType());
            dataMap.put("price", exportDTO.getPrice());
            dataMap.put("priceText", exportDTO.getPriceText());
            dataList.add(dataMap);
        }
        
        // 3. 创建ExcelSheetDataDTO
        return ExcelSheetDataDTO.builder()
            .sheetName(sheetName)
            .headColumnMap(headColumnMap)
            .dataList(dataList)
            .build();
    }
    
    /**
     * 生成Excel并下载
     * 
     * @param excelSheetDataDTOS Excel表格数据列表
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    private String generateAndUploadExcel(List<ExcelSheetDataDTO> excelSheetDataDTOS, HttpServletResponse response, String fileName) {
        try {
            // 创建函数式接口实现
            Function<String, AnalysisTarget> targetFunction = code -> null;
            Function<String, AnalysisTargetGroup> groupFunction = code -> null;
            
            // 创建任务执行DTO
            AnalysisTaskExecuteDTO taskExecuteDTO = new AnalysisTaskExecuteDTO();
            taskExecuteDTO.setTaskId(System.currentTimeMillis());
            taskExecuteDTO.setTaskName(fileName);
            taskExecuteDTO.setOperator("system");
            
            // 创建条件上下文
            Map<String, List<Object>> paramMap = new HashMap<>();
            // 设置开始时间和结束时间
            paramMap.put("payStartTime", Collections.singletonList(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L));
            paramMap.put("payEndTime", Collections.singletonList(System.currentTimeMillis()));
            
            ConditionParamContext conditionParamContext = ConditionParamContext.builder()
                .paramMap(paramMap)
                .build();
            taskExecuteDTO.setConditionParamContext(conditionParamContext);
            
            // 1. 生成Excel文件
            String excelFileName = DynamicEasyExcelExportUtils.generateResultExcelFile2(
                excelSheetDataDTOS,
                "system",
                localFilePath,
                targetFunction,
                groupFunction,
                Long.valueOf(DateUtils.formatMillisToString(System.currentTimeMillis())),
                taskExecuteDTO,
                null  // 允许excelWidthAdjust为null
            );
            
            // 2. 上传到OSS
            String absoluteResultFilePath = localFilePath + "/" + excelFileName;
            File resultFile = new File(absoluteResultFilePath);
            if (resultFile.exists()) {
                analysisResultStorageService.uploadResultFile(resultFile, excelFileName);
                
                // 3. 下载文件
                cleanLocalFile(absoluteResultFilePath);
                return excelFileName;
            } else {
                log.error("生成Excel文件失败，文件不存在: {}", absoluteResultFilePath);
            }
        } catch (Exception e) {
            log.error("生成Excel并下载失败", e);
        }
        return null;
    }
    
    /**
     * 清理本地文件
     * 
     * @param filePath 文件路径
     */
    private void cleanLocalFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error("清理本地文件失败: {}", filePath, e);
        }
    }
}