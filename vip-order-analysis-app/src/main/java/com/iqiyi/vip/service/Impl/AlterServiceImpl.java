package com.iqiyi.vip.service.Impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import com.iqiyi.solar.config.client.CloudConfig;

import com.iqiyi.vip.client.FastHttpClient;
import com.iqiyi.vip.service.AlterService;


/**
 * 奇眼-告警服务使用手册wiki： http://wiki.qiyi.domain/pages/viewpage.action?pageId=62947995
 *
 * @Author: Lin Peihui
 * @Date: 2022/9/15
 */
@Component
public class AlterServiceImpl implements AlterService {

    private static final String system = "vip-job";
    private static final String token = "5356srfrtw3w4tgrwr2fdg3r2t3t";
    private static final String RELIAO_NOTICE_URL = "http://devops.vip.online.qiyi.qae/vip-devops/api/v2/alert/simpleNotice";

    @Resource
    private FastHttpClient fastHttpClient;
    @Resource
    private CloudConfig cloudConfig;


    @Override
    public boolean sendHotChat(String content) {
        String uids = cloudConfig.getProperty("hot_chat_receiver", "chenguilong");
        if (StringUtils.isBlank(uids)) {
            return true;
        }
        return sendHotChat(content, uids);
    }

    @Override
    public boolean sendHotChat(String content, String receivers) {
        Map<String, Object> params = new HashMap<>();
        params.put("noticeUsers", receivers);
        String title = "天眼告警";
        params.put("title", title);
        params.put("content", content);
        params.put("triggerTimeStamp", System.currentTimeMillis());
        params.put("noticeChannel", "hotchat");
        params.put("sysCode", system);
        params.put("token", token);
        Map result = fastHttpClient.getRemoteResponse(RELIAO_NOTICE_URL, params);
        return "A00000".equals(result.get("code"));
    }
}
