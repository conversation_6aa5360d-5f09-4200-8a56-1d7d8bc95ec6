package com.iqiyi.vip.service;

import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@Component
@Slf4j
public class TargetGroupService {

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;

    public ListResult<AnalysisTargetGroup> groups(BaseQry baseQry) {
        Integer businessTypeId = baseQry.getBusinessTypeId();
        Integer themeType = baseQry.getThemeType();
        if (businessTypeId == null || themeType == null) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        return ListResult.createSuccess(analysisTargetGroupRepository.selectByBusinessType(businessTypeId, themeType));
    }
}
