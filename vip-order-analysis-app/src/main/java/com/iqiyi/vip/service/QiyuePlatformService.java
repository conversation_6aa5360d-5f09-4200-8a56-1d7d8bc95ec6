package com.iqiyi.vip.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Lists;

import com.iqiyi.vip.domain.entity.QiyuePlatform;
import com.iqiyi.vip.domain.factory.QiyuePlatformFactory;
import com.iqiyi.vip.domain.repository.QiyuePlatformRepository;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.platform.PlatformGroup;
import com.iqiyi.vip.enums.PlatformGroupEnum;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
@Service
@Slf4j
public class QiyuePlatformService {

    @Resource
    private QiyuePlatformRepository qiyuePlatformRepository;

    public ListResult<PlatformGroup> listByGroup() {
        List<QiyuePlatform> qiyuePlatformList = qiyuePlatformRepository.selectAll();

        List<PlatformGroup> platformGroups = Lists.newArrayList();
        List<QiyuePlatform> qiyuePlatformListFilter = qiyuePlatformList.stream().filter(p -> p.getGroupId() != null).collect(Collectors.toList());
        Map<Integer, List<QiyuePlatform>> map = qiyuePlatformListFilter.stream().collect(Collectors.groupingBy(QiyuePlatform::getGroupId));
        for (List<QiyuePlatform> list : map.values()) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            Integer groupId = list.get(0).getGroupId();
            PlatformGroup group = new PlatformGroup(groupId, PlatformGroupEnum.getName(groupId), QiyuePlatformFactory.convertToView(list));
            platformGroups.add(group);
        }
        return ListResult.createSuccess(platformGroups);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiyuePlatform_IdAndName_selectAll", cacheType = CacheType.LOCAL)
    public List<CodeDescPair> selectAll() {
        List<QiyuePlatform> platforms = qiyuePlatformRepository.selectAll();
        if (CollectionUtils.isEmpty(platforms)) {
            return Collections.emptyList();
        }
        return platforms.stream()
            .map(platform -> new CodeDescPair(platform.getId(), platform.getName()))
            .collect(Collectors.toList());
    }

}
