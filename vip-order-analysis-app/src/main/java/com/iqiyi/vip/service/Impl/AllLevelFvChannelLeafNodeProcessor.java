package com.iqiyi.vip.service.Impl;

import com.google.common.base.Supplier;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.stream.Collectors;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataNode;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.FvChannelReqDTO;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.LeafNodeProcessTypeEnum;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.FvChannelLeafNodeProcessor;
import com.iqiyi.vip.task.CommonAnalysisTask;
import com.iqiyi.vip.utils.DataPermissionNodeTrans;

/**
 * <AUTHOR>
 * @className AllLevelFvChannelLeafNodeProcessor
 * @description
 * @date 2022/10/10
 **/
@Component
public class AllLevelFvChannelLeafNodeProcessor implements FvChannelLeafNodeProcessor {

    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public LeafNodeProcessTypeEnum getProcessType() {
        return LeafNodeProcessTypeEnum.ALL_FV_LEVEl;
    }

    /**
     * 获取叶节点集合
     * @param executeDTO 用户查询条件
     * @param queryLimits 查询限制条件
     * @param leafNodes 查询条件的叶节点
     * @param ownedHighLevelUser 是否是高级权限
     * @return
     */
    public boolean[] findLeafNodes(AnalysisTaskExecuteDTO executeDTO, Queue<DataPermissionNode> queryLimits, List<DataPermissionNode> leafNodes, boolean ownedHighLevelUser, String account) {
        boolean dataPermission4o = false;
        boolean dataPermission4i = false;
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        List<String> level2BusinessIds = conditionParamContext.getLevel2BusinessIds()
            .stream()
            .filter(Objects::nonNull)
            .map(String::valueOf)
            .collect(Collectors.toList());
        final boolean noQueryParams =
            CollectionUtils.isEmpty(level2BusinessIds) || ObjectUtils.isEmpty(level2BusinessIds.get(0));
        if (noQueryParams && ownedHighLevelUser) {
            return new boolean[]{dataPermission4o, dataPermission4i};
        }
        level2BusinessIds = noQueryParams ? dataPermissionService.getOwnedDataPermissionIds(ConditionEnum.BUSINESS_LEVEL, LabelEnum.L2, executeDTO)
            : level2BusinessIds;
        final List<DataPermissionNode> baseLayeredDataPermissions = dataPermissionService.getBaseLayeredDataPermissions(ConditionEnum.BUSINESS_LEVEL, LabelEnum.L2);
        for (DataPermissionNode dataPermissionNode : baseLayeredDataPermissions) {
            if (dataPermissionNode != null && level2BusinessIds.contains(dataPermissionNode.getId())) {
                queryLimits.offer(dataPermissionNode); // 将二级渠道加入限制队列
                dataPermission4o = dataPermission4o || Constants.unMainStation(dataPermissionNode.getId()); // 二级渠道条件包含站外渠道
                dataPermission4i = dataPermission4i || !Constants.unMainStation(dataPermissionNode.getId()); // 二级渠道条件包含站内渠道
            }
        }

        DataPermissionNode tmp = null;
        final Map<LabelEnum, Supplier<List<FvChannelReqDTO>>> labelEnumSupplierMap = CommonAnalysisTask.initUserPermissionParamsGetFuncMap(Maps.newEnumMap(LabelEnum.class), conditionParamContext);
        while ((tmp = queryLimits.poll()) != null) { // 遍历叶子节点
            final List<DataNode> collect = tmp.getSubDataNodeList() == null ? null : tmp.getSubDataNodeList()
                .stream()
                .filter(v -> {
                    final List<FvChannelReqDTO> fvChannelReqDTOS = Optional.ofNullable(labelEnumSupplierMap.getOrDefault(((DataPermissionNode) v).getLabel(),
                        Lists::newArrayList).get()).orElseGet(Lists::newArrayList); // 获取用户权限参数列表
                    return fvChannelReqDTOS.contains(
                        FvChannelReqDTO.builder()
                            .teamId(((DataPermissionNode) v).getTeamId() == null ? null : Integer.valueOf(((DataPermissionNode) v).getTeamId()))
                            .id(v.getId())
                            .build());
                }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) { // 找到的叶节点
                boolean gt = tmp.getLabel().getLevel() >= LabelEnum.datePermissionCtrlLabel().getLevel();
                if (ownedHighLevelUser || gt) { // 高等级或者Label已经大于数据权限层级
                    leafNodes.add(tmp); // 叶节点
                    if (gt) { // 当大于三级后，需要把三级对应的TeamId加入叶节点
                        leafNodes.add(dataPermissionService.findT(tmp));
                        continue;
                    }
                    if (tmp.getLabel() == LabelEnum.T) { // 当团队是叶节点，把父节点（二级）加入叶节点
                        leafNodes.add((DataPermissionNode) tmp.getParent());
                    }
                } else { // 剩余遍历所有3层级已拥有的数据权限节点
                    DataPermissionDTO findLeafNodeTmp = null;
                    Queue<DataPermissionDTO> queue = new LinkedList<>();
                    queue.offer(DataPermissionNodeTrans.copyDataPermissionNode(tmp));
                    while ((findLeafNodeTmp = queue.poll()) != null) {
                        if (findLeafNodeTmp.getLevel() == LabelEnum.datePermissionCtrlLabel().getLevel()) {
                            final DataPermissionNode dataPermissionNode = DataPermissionNodeTrans.copyDataPermissionDTO(findLeafNodeTmp);
                            leafNodes.add(dataPermissionNode);
                            gt = dataPermissionNode.getLabel().getLevel() >= LabelEnum.datePermissionCtrlLabel().getLevel();
                            if (gt) { // 当大于三级后，需要把三级对应的TeamId加入叶节点
                                leafNodes.add(dataPermissionService.findT(dataPermissionNode));
                                continue;
                            }
                            continue;
                        }
                        final LabelEnum nextLabelEnum = LabelEnum.levelOf(findLeafNodeTmp.getLevel()).getNextLabelEnum();
                        final String parentId = nextLabelEnum == LabelEnum.L3 ? findLeafNodeTmp.getParentId() : findLeafNodeTmp.getId();
                        final ArrayList<String> teamIds =
                            nextLabelEnum.getLevel() >= LabelEnum.L3.getLevel() ? Lists.newArrayList(findLeafNodeTmp.getTeamId())
                                : Lists.newArrayList();
                        final List<DataPermissionDTO> dataPermissionDTOS = dataPermissionService.nextOwnedDataPermissions(executeDTO, ConditionEnum.BUSINESS_LEVEL
                            , nextLabelEnum
                            , Lists.newArrayList(parentId)
                            , teamIds);
                        // 是否满权限
                        final boolean fullDataPermission = dataPermissionService.fullDataPermission(dataPermissionDTOS,
                            dataPermissionService.getBaseLayeredDataPermissions(ConditionEnum.BUSINESS_LEVEL, nextLabelEnum, parentId, teamIds)
                                .stream()
                                .map(DataPermissionNodeTrans::copyDataPermissionNode)
                                .collect(Collectors.toList())
                        );
                        if (fullDataPermission) { // 当满权限
                            final DataPermissionNode dataPermissionNode = DataPermissionNodeTrans.copyDataPermissionDTO(findLeafNodeTmp);
                            leafNodes.add(dataPermissionNode);
                            gt = dataPermissionNode.getLabel().getLevel() >= LabelEnum.datePermissionCtrlLabel().getLevel();
                            if (gt) { // 当大于三级后，需要把三级对应的TeamId加入叶节点
                                leafNodes.add(dataPermissionService.findT(dataPermissionNode));
                                continue;
                            }
                            if (dataPermissionNode.getLabel() == LabelEnum.T) {
                                leafNodes.add((DataPermissionNode) dataPermissionNode.getParent());
                            }
                            continue;
                        }
                        if (dataPermissionDTOS != null) {
                            dataPermissionDTOS.forEach(queue::offer);
                        }
                    }
                }
            } else {
                collect.forEach(v -> queryLimits.offer((DataPermissionNode) v)); // tmp非叶节点，则将tmp下的用户参数子节点集合加入队列
            }
        }
        return new boolean[]{dataPermission4o, dataPermission4i};
    }

}
