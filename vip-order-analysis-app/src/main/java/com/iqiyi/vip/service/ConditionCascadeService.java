package com.iqiyi.vip.service;

import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.domain.repository.ConditionCascadeRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.BusinessConditionUpdateDTO;
import com.iqiyi.vip.dto.condition.ConditionCascadePageQryDTO;
import com.iqiyi.vip.dto.condition.ConditionCascadeReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Component
@Slf4j
public class ConditionCascadeService {
    @Resource
    private ConditionCascadeRepository conditionCascadeRepository;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult save(ConditionCascadeReq req) {
        try {
            CommonResult commonResult = validateConfig(req);
            if (!commonResult.getCode().equals(CommonResult.SUCCESS_CODE)) {
                return commonResult;
            }

            conditionCascadeRepository.deleteByThemeAndBusiness(req.getThemeId(), req.getBusinessId());
            if (CollectionUtils.isEmpty(req.getConditionList())) {
                return CommonResult.success();
            }

            for (ConditionCascadeReq.ConditionCascade cascade : req.getConditionList()) {
                conditionCascadeRepository.save(new ConditionCascade(req, cascade));
            }
            return CommonResult.success();
        } catch (Exception e) {
            log.error("Save condition cascade error", e);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
    }

    public List<ConditionCascade> list(ConditionCascadeReq req) {
        return conditionCascadeRepository.list(req.getThemeId(), req.getBusinessId());
    }

    public PageListResult<ConditionCascade> pageList(ConditionCascadePageQryDTO query) {
        return conditionCascadeRepository.selectPageList(query);
    }

    private CommonResult validateConfig(ConditionCascadeReq updateDTO) {
        if (CollectionUtils.isEmpty(updateDTO.getConditionList())) {
            return CommonResult.success();
        }
        Set<String> uniqueKeySet = new HashSet<>();
        for (ConditionCascadeReq.ConditionCascade cascade : updateDTO.getConditionList()) {
            String uniqueKey1 = cascade.getCurrentId() + "_" + cascade.getNextId();
            String uniqueKey2 = cascade.getNextId() + "_" + cascade.getCurrentId();
            if (uniqueKeySet.contains(uniqueKey1) || uniqueKeySet.contains(uniqueKey2)) {
                return new CommonResult(CodeEnum.CONFIG_ERROR.getCode(), "出现闭环的级联关系，请修改后再提交");
            } else {
                uniqueKeySet.add(uniqueKey1);
                uniqueKeySet.add(uniqueKey2);
            }
        }
        return CommonResult.success();
    }

}
