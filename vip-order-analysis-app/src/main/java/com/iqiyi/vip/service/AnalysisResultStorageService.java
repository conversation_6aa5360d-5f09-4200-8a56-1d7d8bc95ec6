package com.iqiyi.vip.service;

import com.iqiyi.oss.model.CannedAccessControlList;
import com.iqiyi.oss.model.OSSObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

import com.iqiyi.oss.OSS;
import com.iqiyi.oss.model.OSSObject;
import com.iqiyi.oss.model.PutObjectResult;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.enums.CodeEnum;

/**
 * <AUTHOR>
 * @date 6/1/22
 * @apiNote
 */
@Service
@Slf4j
public class AnalysisResultStorageService {

    @Resource
    private OSS ossClient;

    @Value("${oss.client.bucket}")
    private String bucketName;

    @Value("${oss.client.path}")
    private String path;

    public CommonResult deleteResult(String fileName) {
        try {
            ossClient.deleteObject(bucketName, path + fileName);
            return CommonResult.success();
        } catch (Exception e) {
            log.error("deleteResult fileName:{} error:", fileName ,e);
            return CommonResult.create(CodeEnum.OSS_ERROR);
        }
    }

    public CommonResult uploadResult(byte[] result, String fileName) {
        try {
            String objName = path + fileName;
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, objName, new ByteArrayInputStream(result), result.length);
            log.info("analysis result upload result:{}", putObjectResult.getETag());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("uploadResult fileName:{} error:", fileName ,e);
            return CommonResult.create(CodeEnum.OSS_ERROR);
        }
    }

    public CommonResult uploadResultFile(File file, String fileName) {
        try {
            String objName = path + fileName;
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, objName, file);
            log.info("analysis result upload result:{}", putObjectResult.getETag());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("analysis result upload error, fileName:{}, error:", fileName, e);
            return CommonResult.create(CodeEnum.OSS_ERROR);
        }
    }

    public CommonResult uploadResultFileWithMetaData(File file, String fileName) {
        try {
            OSSObjectMetadata metadata = new OSSObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);

            String objName = path + fileName;
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, objName, file, metadata);
            log.info("analysis result upload result:{}", putObjectResult.getETag());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("analysis result upload error, fileName:{}, error:", fileName, e);
            return CommonResult.create(CodeEnum.OSS_ERROR);
        }
    }

    public void downloadResult(String fileName, HttpServletResponse response) {
        OSSObject ossObject = null;
        InputStream in = null;
        OutputStream out = null;
        try {

            String key = path + fileName;
            ossObject = ossClient.getObject(bucketName, key);

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            in = ossObject.getObjectContent();

            out = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
            out.flush();
        } catch (Exception e) {
            log.error("analysis result download filename:{}, exception:", fileName, e);
        } finally {
            try {
                out.close();
            } catch (Exception e) {
            }
            try {
                in.close();
            } catch (Exception e) {
            }
            try {
                ossObject.close();
            } catch (Exception e) {
            }
        }
    }
}
