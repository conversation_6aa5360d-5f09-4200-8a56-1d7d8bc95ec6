package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.BusinessConditionRepository;
import com.iqiyi.vip.domain.repository.ConditionCascadeRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.AnalysisConditionPageQryDTO;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.dto.condition.ConditionUpdateDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;


/**
 * 条件配置
 *
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Component
@Slf4j
public class ConditionConfigService {
    @Resource
    private AnalysisConditionRepository analysisConditionRepository;
    @Resource
    private BusinessConditionRepository businessConditionRepository;
    @Resource
    private ConditionCascadeRepository conditionCascadeRepository;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult save(ConditionUpdateDTO updateDTO) {
        try {
            AnalysisCondition exist = analysisConditionRepository.selectByCode(updateDTO.getCode());
            if (exist != null) {
                return new CommonResult(CodeEnum.DUPLICATE.getCode(), "字段标识重复，请输入其他标识");
            }
            exist = analysisConditionRepository.selectByName(updateDTO.getName().trim());
            if (exist != null && exist.getName().trim().equals(updateDTO.getName().trim())) {
                return new CommonResult(CodeEnum.DUPLICATE.getCode(), "字段名称重复，请输入其他名称");
            }

            //TODO 别名重复的校验

            analysisConditionRepository.save(new AnalysisCondition(updateDTO));
            return CommonResult.success();
        } catch (Exception e) {
            log.error("Save condition config error", e);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
    }

    public CommonResult update(ConditionUpdateDTO updateDTO) {
        if (updateDTO.getId() == null) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        AnalysisCondition exist = analysisConditionRepository.selectById(updateDTO.getId());
        if (exist == null) {
            return new CommonResult(CodeEnum.NOT_EXIST.getCode(), "数据不存在");
        }

        AnalysisCondition duplicatedCodeCondition = analysisConditionRepository.selectByCode(updateDTO.getCode());
        if (duplicatedCodeCondition != null && !duplicatedCodeCondition.getId().equals(updateDTO.getId())) {
            return new CommonResult(CodeEnum.DUPLICATE.getCode(), "字段标识重复，请输入其他标识");
        }
        duplicatedCodeCondition = analysisConditionRepository.selectByName(updateDTO.getName().trim());
        if (duplicatedCodeCondition != null && !duplicatedCodeCondition.getId().equals(updateDTO.getId())) {
            return new CommonResult(CodeEnum.DUPLICATE.getCode(), "字段名称重复，请输入其他名称");
        }

        exist.modify(updateDTO);
        analysisConditionRepository.update(exist);
        return CommonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult delete(String code, String operator) {
        AnalysisCondition condition = analysisConditionRepository.selectByCode(code);
        if (condition != null) {
            analysisConditionRepository.delete(code, operator);
            businessConditionRepository.deleteByConditionId(condition.getId());
            conditionCascadeRepository.deleteByConditionId(condition.getId());
        }
        return CommonResult.success();
    }

    public PageListResult<AnalysisCondition> pageList(AnalysisConditionPageQryDTO query) {
        return analysisConditionRepository.selectPageList(query);
    }

    public ListResult<AnalysisCondition> listAll() {
        List<AnalysisCondition> analysisConditionList = analysisConditionRepository.selectAll();
        return ListResult.createSuccess(analysisConditionList);
    }

    /**
     * 获取指定主题下 条件的枚举值
     * @param code
     * @return
     */
    public ListResult<ConditionPair> getThemeEnums(Integer themeType, String code) {
        AnalysisCondition condition = analysisConditionRepository.selectByCode(code);
        if (condition == null) {
            return ListResult.createSuccess(null);
        }
        List<ConditionPair> enums = condition.getEnums();
        if (CollectionUtils.isEmpty(enums)) {
            return ListResult.createSuccess(null);
        }
        enums = enums.stream()
            .filter(x -> (x.getExcludedThemeType() == null || !x.getExcludedThemeType().equals(themeType)))
            .collect(Collectors.toList());
        return ListResult.createSuccess(enums);
    }
}
