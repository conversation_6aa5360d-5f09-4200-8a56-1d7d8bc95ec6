package com.iqiyi.vip.service;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.remainV2.DescAndNumPair;
import com.iqiyi.vip.dto.remainV2.ExpireAnalysisReq;
import com.iqiyi.vip.dto.remainV2.ExpireAnalysisRes;
import com.iqiyi.vip.dto.remainV2.ExpireAnalysisResData;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.task.AnalysisTask;
import com.iqiyi.vip.util.DynamicEasyExcelExportUtils;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.ConditionParamUtils;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR> Li
 * @date 2025/5/29 11:24
 */
@Slf4j
@Service
public class ExpireAnalysisService {

    @Resource
    private AnalysisTask analysisTask;

    @Resource
    private AnalysisTaskRepository analysisTaskRepository;

    @Resource
    private AnalysisTargetRepository analysisTargetRepository;

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    @Value("${excel.width.adjust:0}")
    private Integer excelWidthAdjust;

    @Resource
    private AnalysisResultStorageService storageService;

    /**
     * 流失分析权限，同留存分析
     */
    public boolean ownRemainBoardPermission(String operator) {
        return CloudConfigUtils.ownRemainBoardAccess(operator);
    }

    public ExpireAnalysisRes getExpireAnalysisData(ExpireAnalysisReq reqDTO) throws ExecutionException, InterruptedException, JsonProcessingException {
        boolean valid = ownRemainBoardPermission(reqDTO.getOperator());
        if (!valid) {
            throw new RuntimeException(CodeEnum.BASE_INIT_ERROR.getMessage());
        }

        List<TargetAnalysisQueryDTO> analysisQueryDTOS = constructQueryDTO(reqDTO);
        // result example: num, dimension(if exist), dt
        List<OriginalQueryResultDTO> queryResultDTOS = analysisTask.executeQuery(analysisQueryDTOS, null);
        if (CollectionUtils.isEmpty(queryResultDTOS) || queryResultDTOS.stream().noneMatch(OriginalQueryResultDTO::getSuccess)) {
            log.error("queryResultDTOS is null or empty");
            return ExpireAnalysisRes.builder()
                .byDimension(0)
                .excelName(null)
                .dataList(null)
                .build();
        }

        if (requestContainNoDimension(reqDTO) || requestContainOneDimension(reqDTO)) {
            return constructResultData(reqDTO, queryResultDTOS.get(0));
        } else {
            AnalysisTaskDO analysisTaskDO = constructTaskInfo(reqDTO);
            analysisTaskRepository.addAnalysisTask(analysisTaskDO);
            long taskId = analysisTaskDO.getId();

            // 按照维度查询时，将数据存入excel，并返回excel下载地址
            List<ExcelSheetDataDTO> excelSheetDataDTOS = convertToExcelData(queryResultDTOS);
            if (CollectionUtils.isEmpty(excelSheetDataDTOS) || excelSheetDataDTOS.size() > 1 || exceededExcelLineLimit(excelSheetDataDTOS)) {
                log.error("generate excel failed");
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, null);
                return null;
            }

            // Excel表头更换成中文名称
            ExcelSheetDataDTO excelSheetData = excelSheetDataDTOS.get(0);
            LinkedHashMap<String, String> changeNameHashMap = new LinkedHashMap<>();
            LinkedHashMap<String, String> headColumnMap = excelSheetData.getHeadColumnMap();
            for (Entry<String, String> entry : headColumnMap.entrySet()) {
                changeNameHashMap.put(entry.getKey(), mappingHeadColumnName(entry.getKey()));
            }
            excelSheetData.setHeadColumnMap(changeNameHashMap);

            // Excel 导出，存储到机器上
            Map<String, List<Object>> conditionParamMap = JacksonUtils.getMapObject(analysisTaskDO.getCondition());
            ConditionParamContext conditionParamContext = ConditionParamContext.builder().paramMap(conditionParamMap).build();

            AnalysisTaskExecuteDTO taskExecuteDTO = AnalysisTaskExecuteDTO.builder()
                .taskId(analysisTaskDO.getId())
                .taskName(analysisTaskDO.getTaskName())
                .conditionParamContext(conditionParamContext)
                .dimensionCodes(
                    StringUtils.isNotBlank(analysisTaskDO.getDimensions()) ? Splitter.on(",").splitToList(analysisTaskDO.getDimensions()) : null)
                .targetCodes(Splitter.on(",").splitToList(analysisTaskDO.getTargets()))
                .operator(analysisTaskDO.getOperator())
                .taskMD5(analysisTaskDO.getUniqueIdentification())
                .businessTypeId(analysisTaskDO.getBusinessTypeId())
                .dataPermissionType(analysisTaskDO.getDataPermissionType())
                .themeType(analysisTaskDO.getThemeType())
                .build();

            String excelFileName = DynamicEasyExcelExportUtils.generateResultExcelFile2(excelSheetDataDTOS, reqDTO.getOperator(), localFilePath
                , code -> analysisTargetRepository.selectByCode(code)
                , code -> {
                    final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
                    if (analysisTarget != null && analysisTarget.getGroup() != null) {
                        return analysisTargetGroupRepository.get(analysisTarget.getGroup());
                    }
                    return null;
                }, taskId, taskExecuteDTO, excelWidthAdjust);

            //上传到OSS
            String absoluteResultFilePath = localFilePath + "/" + excelFileName;
            storageService.uploadResultFile(new File(absoluteResultFilePath), excelFileName);

            // 清理本地文件, 将任务状态置为成功
            cleanLocalFile(absoluteResultFilePath);
            analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), excelFileName, null);

            // 返回 OSS 文件下载地址
            return ExpireAnalysisRes.builder()
                .byDimension(1)
                .dataList(null)
                .excelName(excelFileName)
                .build();
        }
    }

    /**
     * 将查询结果转换为 Excel 所需的数据格式
     *
     * @param queryResultDTOS 查询结果列表
     * @return Excel 数据列表
     */
    private List<ExcelSheetDataDTO> convertToExcelData(List<OriginalQueryResultDTO> queryResultDTOS) {
        return queryResultDTOS.stream()
            .filter(OriginalQueryResultDTO::getSuccess)
            .filter(OriginalQueryResultDTO::commonTargetType)
            .map(ExcelSheetDataDTO::from)
            .collect(Collectors.toList());
    }

    /**
     * 清理本地文件
     */
    public void cleanLocalFile(String absoluteFilePath) {
        File file = new File(absoluteFilePath);
        if (!file.exists() || !file.isFile() || !file.delete()) {
            log.warn("[order-analysis][delete file: {}] fail. ", absoluteFilePath);
        }
    }

    private boolean requestContainNoDimension(ExpireAnalysisReq reqDTO) {
        return StringUtils.isEmpty(reqDTO.getDimensionCode());
    }

    private boolean requestContainOneDimension(ExpireAnalysisReq reqDTO) {
        return StringUtils.isNotEmpty(reqDTO.getDimensionCode()) && !reqDTO.getDimensionCode().contains(",");
    }

    private boolean exceededExcelLineLimit(List<ExcelSheetDataDTO> excelSheetDataDTOS) {
        return excelSheetDataDTOS.stream().anyMatch(c -> c.getDataList() != null && c.getDataList().size() >= 1048570);
    }

    private List<TargetAnalysisQueryDTO> constructQueryDTO(ExpireAnalysisReq reqDTO) {
        Properties properties = new Properties();

        properties.setProperty("date", getCalculatedDate());
        properties.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());

        constructCondition(reqDTO, properties);
        constructDimension(reqDTO, properties);

        ArrayList<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        queryDTOS.add(
            TargetAnalysisQueryDTO.builder()
                .targetCode("CK_VIP_LOSS_ANALYSIS")
                .dataBase(properties.getProperty("dateBase"))
                .querySql(analysisTask.parseSqlByProperties(properties, 909L))
                .operator(reqDTO.getOperator())
                .env(System.getProperty("spring.profiles.active"))
                .build()
        );

        return queryDTOS;
    }

    public static String getCalculatedDate() {
        LocalDateTime now = LocalDateTime.now();
        LocalTime judgeTime = LocalTime.of(20, 0);
        LocalDate resultDate = now.toLocalTime().compareTo(judgeTime) >= 0
            ? now.toLocalDate().minusDays(1)
            : now.toLocalDate().minusDays(2);
        return resultDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    private AnalysisTaskDO constructTaskInfo(ExpireAnalysisReq reqDTO) {
        return AnalysisTaskDO.builder()
            .taskName("流失分析")
            .targets("CK_VIP_EXPIRE_ANALYSIS")
            .condition(JacksonUtils.toJsonString(reqDTO.getConditionParamMap()))
            .dimensions(reqDTO.getDimensionCode())
            .status(TaskStatusEnum.RUNNING.getStatus())
            .createTime(DateUtil.date())
            .updateTime(DateUtil.date())
            .operator(reqDTO.getOperator())
            .uniqueIdentification(String.valueOf(System.currentTimeMillis()))
            .source(AnalysisTaskSourceEnum.UI_MANUAL.getValue())
            .build();
    }

    private void constructCondition(ExpireAnalysisReq reqDTO, Properties properties) {
        List<String> conditionParam = processConditionParam(reqDTO);
        properties.setProperty("baseQueryCondition", String.join(" and ", conditionParam));
    }

    private List<String> processConditionParam(ExpireAnalysisReq reqDTO) {
        Map<String, List<Object>> conditionParamMap = reqDTO.getConditionParamMap();
        List<String> conditionSql = new ArrayList<>();

        if (StringUtils.isNotEmpty(reqDTO.getDimensionCode()) && reqDTO.getDimensionCode().contains("auto_renew")) {
            conditionSql.add("auto_renew != '其他'");
        }

        if (MapUtils.isEmpty(conditionParamMap)) {
            String deadlineField = getDeadlineField(reqDTO);
            String deadlineSQL = deadlineField + " <= " + "'" + DateUtils.dateDatetimeFormat(reqDTO.getEndTime()) + "'";
            conditionSql.add(deadlineSQL);
            return conditionSql;
        }

        for (Entry<String, List<Object>> entry : conditionParamMap.entrySet()) {
            String key = entry.getKey();
            List<Object> value = entry.getValue();

            if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(value)) {
                continue;
            }

            List<String> listValue = ConditionParamUtils.getListValue(conditionParamMap, key, String.class);
            if (CollectionUtils.isEmpty(listValue)) {
                continue;
            }
            String sql = key + " in (" + listValue.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
            conditionSql.add(sql);
        }

        String deadlineField = getDeadlineField(reqDTO);
        String deadlineSQL = deadlineField + "<= " + "'" + DateUtils.dateDatetimeFormat(reqDTO.getEndTime()) + "'";
        conditionSql.add(deadlineSQL);

        return conditionSql;
    }

    private String getDeadlineField(ExpireAnalysisReq reqDTO) {
        Map<String, List<Object>> conditionParamMap = reqDTO.getConditionParamMap();
        String dimensionCode = reqDTO.getDimensionCode();
        if (StringUtils.isEmpty(dimensionCode)) {
            if (MapUtils.isEmpty(conditionParamMap)) {
                return "deadline_uid";
            }
            if (conditionParamMap.size() == 1 && conditionParamMap.containsKey("vip_group")) {
                return "deadline_vip_group";
            }
            return "deadline_vip_biz_type";
        } else {
            if ("vip_group".equals(dimensionCode)) {
                if (MapUtils.isEmpty(conditionParamMap)) {
                    return "deadline_vip_group";
                }
                if (conditionParamMap.size() == 1 && conditionParamMap.containsKey("vip_group")) {
                    return "deadline_vip_group";
                }
            }
            return "deadline_vip_biz_type";
        }
    }

    private void constructDimension(ExpireAnalysisReq reqDTO, Properties properties) {
        List<String> dimensionSql = new ArrayList<>();
        List<String> orderBySql = new ArrayList<>();

        String deadlineField = getDeadlineField(reqDTO);
        dimensionSql.add(deadlineField);
        orderBySql.add(deadlineField);
        orderBySql.add("num desc");

        if (StringUtils.isNotBlank(reqDTO.getDimensionCode())) {
            dimensionSql.add(reqDTO.getDimensionCode());
            orderBySql.add(reqDTO.getDimensionCode());
        }

        properties.setProperty("dimensionCodes", String.join(",", dimensionSql));
        properties.setProperty("orderByCodes", String.join(",", orderBySql));
    }

    public ExpireAnalysisRes constructResultData(ExpireAnalysisReq reqDTO, OriginalQueryResultDTO queryResult) {
        ExpireAnalysisRes expireAnalysisRes = new ExpireAnalysisRes();
        expireAnalysisRes.setByDimension(0);

        String deadlineField = getDeadlineField(reqDTO);
        List<ExpireAnalysisResData> lossAnalysisResDataList = queryResult.getDataList()
            .stream()
            .map(dataMap -> {
                ExpireAnalysisResData resData = new ExpireAnalysisResData();

                // process dt
                resData.setDt((String) dataMap.get(deadlineField));

                // process descAndNumPairs
                List<DescAndNumPair> descAndNumPairs = new ArrayList<>();
                String dimensionDescInfo = null;
                if (StringUtils.isNotEmpty(reqDTO.getDimensionCode())) {
                    dimensionDescInfo = (String) dataMap.get(reqDTO.getDimensionCode());
                }
                Long num = (Long) dataMap.get("num");
                descAndNumPairs.add(new DescAndNumPair(dimensionDescInfo, num));
                resData.setDescAndNumPairs(descAndNumPairs);

                return resData;
            })
            .collect(Collectors.toList());

        expireAnalysisRes.setDataList(aggregateByDate(lossAnalysisResDataList));

        return expireAnalysisRes;
    }

    /**
     * 合并相同 dt 的 descAndNumPairs
     */
    public static List<ExpireAnalysisResData> aggregateByDate(List<ExpireAnalysisResData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用传统的Map收集数据
        Map<String, List<DescAndNumPair>> groupedData = new LinkedHashMap<>();
        for (ExpireAnalysisResData item : dataList) {
            if (item.getDt() != null) {
                groupedData.computeIfAbsent(item.getDt(), k -> new ArrayList<>())
                    .addAll(item.getDescAndNumPairs());
            }
        }

        // 转换为结果列表
        return groupedData.entrySet().stream()
            .map(entry -> {
                ExpireAnalysisResData result = new ExpireAnalysisResData();
                result.setDt(entry.getKey());
                result.setDescAndNumPairs(entry.getValue());
                return result;
            })
            .collect(Collectors.toList());
    }

    private String mappingHeadColumnName(String columnName) {
        switch (columnName) {
            case "vip_group": return "会员类型";
            case "vip_biz_type": return "套餐类型";
            case "zero_order_type": return "买赠类型";
            case "vip_card_type": return "卡种";
            case "renew_flag": return "生命周期";
            case "uid_layer": return "基石潮汐";
            case "sexandage": return "圈层";
            case "num": return "到期用户数";
            case "auto_renew": return "自动续费状态";
            case "kr_channel_name": return "KR项目";
            case "deadline_uid":
            case "deadline_vip_biz_type":
            case "deadline_vip_group":
                return "到期日";
        }
        return columnName;
    }
}
