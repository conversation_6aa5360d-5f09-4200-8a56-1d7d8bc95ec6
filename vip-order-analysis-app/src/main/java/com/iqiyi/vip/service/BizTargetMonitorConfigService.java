package com.iqiyi.vip.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.api.QiyiJobApi;
import com.iqiyi.vip.api.ZeusApi;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.domain.entity.BizTargetMonitorConfigDO;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisThemeTypeRepository;
import com.iqiyi.vip.domain.repository.BizTargetMonitorConfigRepository;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigCondition;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigPageQryDTO;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigSaveDTO;
import com.iqiyi.vip.dto.realmonitor.TimeIntervalPair;
import com.iqiyi.vip.dto.zeus.ZeusMonitorQuery;
import com.iqiyi.vip.dto.zeus.ZeusMonitorQueryCondition;
import com.iqiyi.vip.dto.zeus.ZeusMonitorSaveParam;
import com.iqiyi.vip.dto.zeus.ZeusSmartAlertSaveParam;
import com.iqiyi.vip.enums.BusinessTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.MonitorUtils;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BizTargetMonitorConfigService {

    private static final int ZEUS_METRIC_TEMPLATE_ID = 19;

    @ConfigJsonValue("${target.to.zeus.template.id.map:{\"orderCount_real\":19,\"kedanjia_real\":20}}")
    private Map<String, Integer> targetCodeToZeusTmpIdMap;
    @ConfigJsonValue("${biz.target.monitor.support.conditions:[\"vipTypeList\",\"payChannels\",\"level2BusinessIds\",\"qiyuePlatform\",\"renewType\",\"vipCardType\",\"renewFlag\"]}")
    private List<String> supportConditionCodes;
    @ConfigJsonValue("${biz.target.monitor.support.targets:[\"orderCount_real\",\"kedanjia_real\"]}")
    private List<String> supportTargetCodes;
    @ConfigJsonValue("${biz.target.monitor.support.dimensions:[\"vip_type_desc\",\"vip_pay_channel\",\"level2_business_name\",\"platform_name\",\"renew_type_desc\",\"vip_card_type\",\"renew_flag_desc\"]}")
    private List<String> supportDimensionCodes;
    @ConfigJsonValue("${biz.target.monitor.alert.receivers:[\"zhouguojing\",\"guoyang04\",\"chenguilong\",\"felixsun\",\"lixin10\",\"libolin01\"]}")
    private List<String> bizTargetMonitorAlertReceivers;
    @Value("${spring.profiles.active:}")
    private String activeProfile;
    @Value("${spring.application.name:vip-order-analysis-task}")
    private String applicationName;
    @Value("${zeus.monitor.pageList.url:http://devops.vip.online.qiyi.qae/#/alert/zeus/monitor/list?menuCode=monitor_list&monitorId=}")
    private String zeusMonitorPageListUrl;
    @Value("${qiyi.job.page.url:http://qiyi-job-admin.qiyi.domain/jobinfo?jobGroup=7}")
    private String qiyiJobPageUrl;

    @Resource
    private BizTargetMonitorConfigRepository bizTargetMonitorConfigRepository;
    @Resource
    private AnalysisThemeTypeRepository analysisThemeTypeRepository;
    @Resource
    private AnalysisTargetRepository analysisTargetRepository;
    @Resource
    private AnalysisConditionRepository analysisConditionRepository;
    @Resource
    private AnalysisDimensionRepository analysisDimensionRepository;
    @Resource
    private QiyiJobApi qiyiJobApi;
    @Resource
    private ZeusApi zeusApi;

    public int create(BizTargetMonitorConfigSaveDTO createParam, TimeIntervalPair timeIntervalPair) {
        String target = createParam.getTarget();
        AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(target);
        if (analysisTarget == null) {
            throw BizRuntimeException.newParamException(String.format("指标code:%s不存在", createParam.getTarget()));
        }
        List<BizTargetMonitorConfigCondition> conditions = createParam.getConditions();
        List<String> conditionKeys = conditions.stream().map(BizTargetMonitorConfigCondition::getKey).collect(Collectors.toList());
        Map<String, AnalysisCondition> conditionMap = analysisConditionRepository.batchSelectByCode(conditionKeys).stream()
            .collect(Collectors.toMap(AnalysisCondition::getCode, analysisCondition -> analysisCondition));
        for (BizTargetMonitorConfigCondition condition : conditions) {
            AnalysisCondition analysisCondition = conditionMap.get(condition.getKey());
            if (analysisCondition == null) {
                throw BizRuntimeException.newParamException(String.format("条件code:%s不存在", condition.getKey()));
            }
        }
        int monitorConfigId = bizTargetMonitorConfigRepository.create(createParam);

        String executorParam = "monitorConfigId=" + monitorConfigId;
        Integer qiyiJobId = qiyiJobApi.createJob(createParam.getName(), createParam.getCreateOpr(), timeIntervalPair, executorParam, bizTargetMonitorAlertReceivers);
        if (qiyiJobId != null) {
            bizTargetMonitorConfigRepository.updateJobIdById(monitorConfigId, qiyiJobId);
            if (activeProfile.contains("prod")) {
                qiyiJobApi.resumeJob(qiyiJobId);
            }
        }

        BizTargetMonitorConfigDO monitorConfigDOFromDB = bizTargetMonitorConfigRepository.getDetailById(monitorConfigId);
        ZeusMonitorSaveParam monitorSaveParam = buildZeusMonitorSaveParam(createParam, monitorConfigDOFromDB, analysisTarget, bizTargetMonitorAlertReceivers);
        Integer monitorId = zeusApi.createMonitor(createParam.getCreateOpr(), monitorSaveParam);
        if (monitorId != null) {
            bizTargetMonitorConfigRepository.updateMonitorIdById(monitorConfigId, monitorId);
        }

        return monitorConfigId;
    }

    private ZeusMonitorSaveParam buildZeusMonitorSaveParam(BizTargetMonitorConfigSaveDTO saveParam,
        BizTargetMonitorConfigDO monitorConfigDOFromDB,
        AnalysisTarget analysisTarget,
        List<String> alertReceivers
    ) {
        ThemeTypeEnum themeTypeEnum = ThemeTypeEnum.findEnumByThemeType(saveParam.getThemeType());
        String themeTypeName = themeTypeEnum.name();
        String dimensionPart = CollectionUtils.isNotEmpty(saveParam.getDimensions()) ? String.join("_", saveParam.getDimensions()) : null;
        String ruleName = MonitorUtils.genRuleName(themeTypeName, analysisTarget.getCode(), dimensionPart, monitorConfigDOFromDB.getCreateTime());
        String metricName = MonitorUtils.genMetricName(themeTypeName, analysisTarget.getCode(), dimensionPart);

        ZeusMonitorQueryCondition queryCondition = ZeusMonitorQueryCondition.builder()
            .key("application")
            .operator("=")
            .values(Collections.singletonList(applicationName))
            .build();
        List<ZeusMonitorQueryCondition> queryConditions = Collections.singletonList(queryCondition);

        String groupByPart = CollectionUtils.isNotEmpty(saveParam.getDimensions()) ? String.join(",", saveParam.getDimensions()) : null;
        ZeusMonitorQuery monitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(MapUtils.getInteger(targetCodeToZeusTmpIdMap, saveParam.getTarget(), ZEUS_METRIC_TEMPLATE_ID))
            .source(metricName)
            .conditions(queryConditions)
            .groupBy(groupByPart)
            .build();
        ZeusSmartAlertSaveParam alertSaveParam = ZeusSmartAlertSaveParam.builder()
            .monitorId(monitorConfigDOFromDB != null ? monitorConfigDOFromDB.getMonitorId() : null)
            .checkFrequency(saveParam.getExecFrequency())
            .duration("10m")
            .receivers(String.join(",", alertReceivers))
            .ruleName(ruleName)
            .build();
        return ZeusMonitorSaveParam.builder()
            .id(monitorConfigDOFromDB != null ? monitorConfigDOFromDB.getMonitorId() : null)
            .name(saveParam.getName() + "-" + analysisTarget.getName())
            .themeTypeName(themeTypeEnum.name().toLowerCase())
            .targetName(analysisTarget.getName())
            .query(Collections.singletonList(monitorQuery))
            .smartAlertParam(alertSaveParam)
            .build();
    }

    public boolean update(BizTargetMonitorConfigSaveDTO updateParam, TimeIntervalPair paramTimeIntervalPair, BizTargetMonitorConfigDO monitorConfigDOFromDB) {
        List<BizTargetMonitorConfigCondition> conditions = updateParam.getConditions();
        List<String> conditionKeys = conditions.stream().map(BizTargetMonitorConfigCondition::getKey).collect(Collectors.toList());
        Map<String, AnalysisCondition> conditionMap = analysisConditionRepository.batchSelectByCode(conditionKeys).stream()
            .collect(Collectors.toMap(AnalysisCondition::getCode, analysisCondition -> analysisCondition));
        for (BizTargetMonitorConfigCondition condition : conditions) {
            AnalysisCondition analysisCondition = conditionMap.get(condition.getKey());
            if (analysisCondition == null) {
                throw BizRuntimeException.newParamException(String.format("指标code:%s不存在", condition.getKey()));
            }
        }

        bizTargetMonitorConfigRepository.update(updateParam);
        Integer jobId = monitorConfigDOFromDB.getJobId();
        TimeIntervalPair timeIntervalPairInDB = TimeIntervalPair.of(monitorConfigDOFromDB.getExecFrequency());
        if (jobId != null && !Objects.equals(paramTimeIntervalPair, timeIntervalPairInDB)) {
            String executorParam = "monitorConfigId=" + monitorConfigDOFromDB.getId();
            qiyiJobApi.updateJob(jobId, paramTimeIntervalPair, executorParam);
        }

        AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(monitorConfigDOFromDB.getTarget());
        ZeusMonitorSaveParam monitorSaveParam = buildZeusMonitorSaveParam(updateParam, monitorConfigDOFromDB, analysisTarget, bizTargetMonitorAlertReceivers);
        if (monitorConfigDOFromDB.getMonitorId() != null) {
            zeusApi.updateMonitor(updateParam.getUpdateOpr(), monitorSaveParam);
        } else {
            Integer monitorId = zeusApi.createMonitor(updateParam.getUpdateOpr(), monitorSaveParam);
            if (monitorId != null) {
                bizTargetMonitorConfigRepository.updateMonitorIdById(monitorConfigDOFromDB.getId(), monitorId);
            }
        }
        return true;
    }

    public boolean delete(Integer id) {
        if (id == null) {
            return false;
        }
        BizTargetMonitorConfigDO bizTargetMonitorConfigDO = bizTargetMonitorConfigRepository.getDetailById(id);
        if (bizTargetMonitorConfigDO.getJobId() != null) {
            qiyiJobApi.deleteJob(bizTargetMonitorConfigDO.getJobId());
        }
        if (bizTargetMonitorConfigDO.getMonitorId() != null) {
            zeusApi.deleteMonitor(bizTargetMonitorConfigDO.getCreateOpr(), bizTargetMonitorConfigDO.getMonitorId());
        }
        bizTargetMonitorConfigRepository.delete(id);
        return true;
    }

    public boolean existByName(String name) {
        return bizTargetMonitorConfigRepository.existByName(name);
    }

    public BizTargetMonitorConfigDO getDetailById(Integer id) {
        if (id == null) {
            return null;
        }
        BizTargetMonitorConfigDO result = bizTargetMonitorConfigRepository.getDetailById(id);
        if (result != null) {
            AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(result.getTarget());
            result.setTargetName(analysisTarget.getName());
            result.setThemeTypeName(analysisTarget.getThemeTypeName());
            if (result.getMonitorId() != null) {
                result.setZeusMonitorPageListUrl(zeusMonitorPageListUrl + result.getMonitorId());
            }
            if (result.getJobId() != null) {
                result.setJobConfigUrl(qiyiJobPageUrl);
            }
        }
        return result;
    }

    public PageListResult<BizTargetMonitorConfigDO> search(BizTargetMonitorConfigPageQryDTO param) {
        PageListResult<BizTargetMonitorConfigDO> pageListResult = bizTargetMonitorConfigRepository.search(param);
        if (CollectionUtils.isEmpty(pageListResult.getDataList())) {
            return pageListResult;
        }
        Set<String> targetList = pageListResult.getDataList().stream().map(BizTargetMonitorConfigDO::getTarget).collect(Collectors.toSet());
        List<AnalysisThemeType> analysisThemeTypes = analysisThemeTypeRepository.selectAll();
        List<AnalysisTarget> analysisTargets = analysisTargetRepository.selectByCodes(new ArrayList<>(targetList));
        Map<Integer, String> themeTypeIdToNameMap = analysisThemeTypes.stream()
            .collect(Collectors.toMap(AnalysisThemeType::getId, AnalysisThemeType::getName));
        Map<String, String> targetCodeToNameMap = analysisTargets.stream()
            .collect(Collectors.toMap(AnalysisTarget::getCode, AnalysisTarget::getName));
        pageListResult.getDataList().forEach(bizTargetMonitorConfigDO -> {
            bizTargetMonitorConfigDO.setThemeTypeName(themeTypeIdToNameMap.get(bizTargetMonitorConfigDO.getThemeType()));
            bizTargetMonitorConfigDO.setTargetName(targetCodeToNameMap.get(bizTargetMonitorConfigDO.getTarget()));
            if (bizTargetMonitorConfigDO.getMonitorId() != null) {
                bizTargetMonitorConfigDO.setZeusMonitorPageListUrl(zeusMonitorPageListUrl + bizTargetMonitorConfigDO.getMonitorId());
            }
            if (bizTargetMonitorConfigDO.getJobId() != null) {
                bizTargetMonitorConfigDO.setJobConfigUrl(qiyiJobPageUrl);
            }
        });
        return pageListResult;
    }

    public List<AnalysisCondition> supportConditionList(Integer themeType) {
        return analysisConditionRepository.batchSelectByCode(supportConditionCodes);
    }

    public List<AnalysisTarget> supportTargetList(Integer themeType) {
        return analysisTargetRepository.selectByCodes(supportTargetCodes);
    }

    public List<AnalysisDimension> supportDimensionList(Integer themeType) {
        return analysisDimensionRepository.selectByCodes(supportDimensionCodes, BusinessTypeEnum.BASIC_ANALYSIS.getCode(), themeType);
    }

}
