package com.iqiyi.vip.service;

import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.domain.factory.AnalysisSqlTemplateFactory;
import com.iqiyi.vip.domain.repository.AnalysisSqlTemplateRepository;
import com.iqiyi.vip.domain.repository.ConfigChangeLogRepository;
import com.iqiyi.vip.dto.base.*;
import com.iqiyi.vip.dto.sqltemplate.AnalysisSqlTemplatePageQryDTO;
import com.iqiyi.vip.dto.sqltemplate.SqlTemplateUpdateDTO;
import com.iqiyi.vip.dto.version.ChangeListReqDTO;
import com.iqiyi.vip.dto.version.VersionDiffReqDTO;
import com.iqiyi.vip.dto.version.VersionListReqDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConfigTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLDecoder;

import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Component
@Slf4j
public class SqlTemplteMangerService implements ConfigVersionProcessor {

    @Resource(name = "analysisSqlTemplateRepositoryImpl")
    private AnalysisSqlTemplateRepository analysisSqlTemplateRepository;

    @Resource
    private ConfigChangeLogRepository configChangeLogRepository;

    @Transactional(rollbackFor = Exception.class)
    public CommonResult save(SqlTemplateUpdateDTO updateDTO) {
        try {
            updateDTO.setValue(URLDecoder.decode(updateDTO.getValue(), "utf-8"));
            AnalysisSqlTemplate saveTemplate = AnalysisSqlTemplateFactory.getSaveTemplate(updateDTO);
            Long newId = analysisSqlTemplateRepository.save(saveTemplate);
            saveTemplate.setId(newId);
            configChangeLogRepository.save(ConfigChangeLog.buildFrom(saveTemplate, DEFAULT_VERSION));
            return CommonResult.success();
        } catch (Exception e) {
            log.info("SqlTemplteMangerService save error", e);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonResult update(SqlTemplateUpdateDTO updateDTO) {
        if (updateDTO.getId() == null) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        AnalysisSqlTemplate exist = analysisSqlTemplateRepository.selectById(updateDTO.getId());
        if (exist == null) {
            throw new BizRuntimeException(CodeEnum.NOT_EXIST);
        }
        try {
            updateDTO.setValue(URLDecoder.decode(updateDTO.getValue(), "utf-8"));
            Integer maxVersion = configChangeLogRepository.getMaxVersion(ConfigTypeEnum.SQL_TEMPLATE.getType(), exist.getId());
            int newVersion = maxVersion == null ? DEFAULT_VERSION : maxVersion + 1;
            AnalysisSqlTemplateFactory.updateExist(exist, updateDTO, newVersion);
            analysisSqlTemplateRepository.update(exist);
            configChangeLogRepository.save(ConfigChangeLog.buildFrom(exist, newVersion));
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.create(CodeEnum.ERROR_PARAM);
        }
    }

    public CommonResult delete(Long id) {
        AnalysisSqlTemplate sqlTemplate = analysisSqlTemplateRepository.selectById(id);
        if (sqlTemplate != null) {
            analysisSqlTemplateRepository.delete(id);
            configChangeLogRepository.delete(ConfigTypeEnum.SQL_TEMPLATE.getType(), sqlTemplate.getId());
        }
        return CommonResult.success();
    }

    public PageListResult<AnalysisSqlTemplate> pageList(AnalysisSqlTemplatePageQryDTO query) {
        return analysisSqlTemplateRepository.selectPageList(query);
    }

    public DataResult<AnalysisSqlTemplate> detail(Long id) {
        AnalysisSqlTemplate template = analysisSqlTemplateRepository.selectById(id);
        DataResult result = DataResult.success(template);
        return result;
    }

    public ListResult<AnalysisSqlTemplate> list(BaseQry baseQry) {
        return ListResult.createSuccess(analysisSqlTemplateRepository.selectAll(baseQry));
    }

    @Override
    public Integer getProcessType() {
        return ConfigTypeEnum.SQL_TEMPLATE.getType();
    }

    @Override
    public CommonResult versionSwitch(VersionSwitchReqDTO reqDTO, ConfigChangeLog destinationChangeLog) {
        Long configId = reqDTO.getConfigId();
        AnalysisSqlTemplate sqlTemplate = analysisSqlTemplateRepository.selectById(configId);
        if (sqlTemplate == null) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), String.format("未找到id：%s的指标模板", configId));
        }
        try {
            AnalysisSqlTemplateFactory.versionSwitch(sqlTemplate, destinationChangeLog, reqDTO);
            analysisSqlTemplateRepository.update(sqlTemplate);
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.create(CodeEnum.ERROR_PARAM);
        }
    }

    @Override
    public CommonResult versionDiff(VersionDiffReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult versionList(VersionListReqDTO reqDTO) {
        return null;
    }

    @Override
    public ListResult changeList(ChangeListReqDTO reqDTO) {
        return null;
    }
}
