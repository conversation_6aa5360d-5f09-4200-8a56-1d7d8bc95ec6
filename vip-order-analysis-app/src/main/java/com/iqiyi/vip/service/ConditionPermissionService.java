package com.iqiyi.vip.service;

import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @className ConditionPermissionService
 * @description
 * @date 2022/8/26
 **/
@Slf4j
@Component
public class ConditionPermissionService {
    @Resource
    private DataPermissionService dataPermissionService;

    public List<DataPermissionDTO> getProPermissions(BaseQry baseQry, LabelEnum labelEnum, List<String> curDataPermissionIds) {
        ConditionEnum proConditionEnum = ConditionEnum.PRO;
        return dataPermissionService.nextOwnedDataPermissions(baseQry, proConditionEnum, labelEnum, curDataPermissionIds, null);
    }

    public List<DataPermissionDTO> getFvPermissions(BaseQry baseQry, LabelEnum labelEnum, List<String> curDataPermissionIds, List<String> teamIds) {
        ConditionEnum fvConditionEnum = ConditionEnum.BUSINESS_LEVEL;
        return dataPermissionService.nextOwnedDataPermissions(baseQry, fvConditionEnum, labelEnum, curDataPermissionIds, teamIds);
    }

    public List<DataPermissionDTO> getVipType(BaseQry baseQry, LabelEnum labelEnum, List<String> curDataPermissionIds) {
        ConditionEnum vipTypeConditionEnum = ConditionEnum.VIP_TYPE;
        return dataPermissionService.nextOwnedDataPermissions(baseQry, vipTypeConditionEnum, labelEnum, curDataPermissionIds, null);
    }
}
