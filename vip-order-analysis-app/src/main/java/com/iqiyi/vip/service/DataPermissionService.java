package com.iqiyi.vip.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import com.iqiyi.vip.api.OaApprovedApi;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataNode;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.domain.entity.DataPermissionOa;
import com.iqiyi.vip.domain.memory.DataPermissionMemory;
import com.iqiyi.vip.domain.repository.DataPermissionRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.oa.OaApprovedNoticeMsgDTO;
import com.iqiyi.vip.dto.oa.OaApprovedNoticeMsgDTO.OaApprovedNoticeMsgDTOBuilder;
import com.iqiyi.vip.dto.oa.OaApprovedResponse;
import com.iqiyi.vip.dto.permission.BaseDataPermissionDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApplyDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApprovalCallbackDTO;
import com.iqiyi.vip.dto.permission.DataPermissionComposeDTO;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.dto.permission.LayeredDataPermissionDTO;
import com.iqiyi.vip.dto.permission.LayeredDataPermissionSourceDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.DataPermissionCheckedEnum;
import com.iqiyi.vip.enums.DataPermissionOaStatusEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.mq.MsgSender;
import com.iqiyi.vip.utils.DataPermissionNodeTrans;

/**
 * <AUTHOR>
 * @date 2022/8/17 14:03
 */
@Slf4j
@Component
public class DataPermissionService {

    @Resource
    private DataPermissionMemory dataPermissionMemory;
    @Resource
    private DataPermissionRepository dataPermissionRepository;
    @Resource
    private MsgSender msgSender;

    @Resource
    private OaApprovedApi oaApprovedApi;

    /**
     * 存在正在审批的数据权限申请
     * @param account
     * @return
     */
    public boolean existApprovingDataPermission(String account) {
        return dataPermissionRepository.existApprovingDataPermission(account) > 0;
    }

    /**
     * 数据权限组合获取(页面展示权限层级，labelEnum == LabelEnum.L3 表示展示三级权限)
     * @param labelEnum
     * @return
     */
    public DataPermissionComposeDTO getBaseDataPermission(BaseQry baseQry, LabelEnum labelEnum, boolean allPermission) {
        boolean fvDataPermission = DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode().equals(baseQry.getDataPermissionType());
        boolean vipTypePermission = DataPermissionTypeEnum.VIP_TYPE_DATA_PERMISSION.getCode().equals(baseQry.getDataPermissionType());
        boolean competitorDataPermission = DataPermissionTypeEnum.COMPETITOR_DATA_PERMISSION.getCode().equals(baseQry.getDataPermissionType());
        return DataPermissionComposeDTO.builder()
                .fvData(allPermission ? dataPermissionMemory.getFvBaseDataPermission(labelEnum) :
                        fvDataPermission ? dataPermissionMemory.getFvBaseDataPermission(labelEnum) : null)
                .proData(allPermission ? dataPermissionMemory.getProBaseDataPermission() :
                        fvDataPermission ? dataPermissionMemory.getProBaseDataPermission() : null)
                .vipTypeData(allPermission ? dataPermissionMemory.getVipTypeBaseDataPermission() :
                        vipTypePermission ? dataPermissionMemory.getVipTypeBaseDataPermission() : null)
                .competitorMonitorData(allPermission ? dataPermissionMemory.getCompetitorMonitorBaseDataPermission() :
                        competitorDataPermission ? dataPermissionMemory.getCompetitorMonitorBaseDataPermission() : null)
                .build();
    }

    /**
     * 已拥有权限判断
     * @param baseQry
     * @return false 未拥有权限，true 已拥有权限
     */
    public boolean ownedDataPermission(BaseQry baseQry) {
        return  ownedHighLevelUser(baseQry) ||
                dataPermissionRepository.ownedDataPermission(baseQry.getOperator()) > 0;
    }

    /**
     * 拥有高级权限用户
     * @param baseQry
     * @return
     */
    public boolean ownedHighLevelUser(BaseQry baseQry) {
        return ConditionDataPermissionHandler.getConditionDataPermissionHandler(baseQry).isHighestDataPermission(false, baseQry);
    }

    /**
     * 获取下层级已拥有数据权限列表
     * @param baseQry  订单分析平台唯一账号标识
     * @param conditionEnum 分类
     * @param labelEnum 标签
     * @param curDataPermissionIds 父层级数据权限集合
     * @param teamIds 过滤3级渠道（包含）后的数据权限，只返回属于这个团队集合中的数据权限
     * @return
     */
    public List<DataPermissionDTO> nextOwnedDataPermissions(BaseQry baseQry, ConditionEnum conditionEnum, LabelEnum labelEnum, List<String> curDataPermissionIds, List<String> teamIds) {
        List<DataPermissionDTO> dataPermissionDTOS = ConditionDataPermissionHandler.getConditionDataPermissionHandler(conditionEnum)
                .nextOwnedDataPermissions(baseQry, conditionEnum, labelEnum, curDataPermissionIds, teamIds);
        return dataPermissionDTOS == null ? null :
                labelEnum == LabelEnum.T ? new ArrayList<>(dataPermissionDTOS.stream().collect(Collectors.toMap(DataPermissionDTO::getId, v -> v, (v1, v2) -> v1)).values()) :
                        dataPermissionDTOS;
    }

    /**
     * 获取下层级已拥有数据权限列表
     * @param conditionEnum 分类
     * @param labelEnum 标签
     * @param curDataPermissionIds 父层级数据权限集合
     * @param teamIds 过滤3级渠道（包含）后的数据权限，只返回属于这个团队集合中的数据权限
     * @param permissionNodePredicate 权限过滤
     * @return
     */
    public List<DataPermissionDTO> nextOwnedDataPermissions(ConditionEnum conditionEnum, LabelEnum labelEnum,
                                                             List<String> curDataPermissionIds, List<String> teamIds, Predicate<DataNode> permissionNodePredicate) {
        final LabelEnum labelEnumFinal = labelEnum == LabelEnum.T ? LabelEnum.L2 : LabelEnum.levelOf(labelEnum.getLevel() - 1); // 兼容前端可读性
        final List<DataPermissionNode> baseDataLayeredDataPermission = dataPermissionMemory.getBaseDataLayeredDataPermission(conditionEnum, labelEnumFinal); // 父节点基础数据权限集合
        return baseDataLayeredDataPermission.stream() // 父节点基础数据过滤  --  过滤数据来源前端传参
                .filter(data -> {
                    boolean bl = true;
                    if (LabelEnum.L1 == labelEnumFinal || LabelEnum.P0 == labelEnumFinal
                        || LabelEnum.VT0 == labelEnumFinal) { // 特殊逻辑：分类的第一层级数据，需要特殊处理，因为会没有后两个参数
                        return bl;
                    }
                    if (conditionEnum == ConditionEnum.BUSINESS_LEVEL && labelEnumFinal.getLevel() > LabelEnum.L2.getLevel()) {
                        bl = bl && !CollectionUtils.isEmpty(teamIds) && teamIds.contains(data.getTeamId());
                    }
                    bl = bl && !CollectionUtils.isEmpty(curDataPermissionIds) && curDataPermissionIds.contains(data.getId());
                    return data.getSubDataNodeList() != null && bl;
                })
            .flatMap(
                data -> LabelEnum.L3 != labelEnum ?
                    data.getSubDataNodeList().stream() : // Fv 2级查询3级兼容
                    data.getSubDataNodeList().stream()
                        .flatMap(data2 -> data2.getSubDataNodeList() == null ? Lists.<DataPermissionNode>newArrayList().stream()
                            : data2.getSubDataNodeList().stream())
                        .filter(v -> CollectionUtils.isEmpty(teamIds) || ((DataPermissionNode) v).getTeamId() == null
                            || teamIds.contains(((DataPermissionNode) v).getTeamId()))) // 子节点节后基础数据过滤  --  过滤数据来源用户已申请权限
                .filter(permissionNodePredicate == null ? v -> true : permissionNodePredicate)
                .map(data -> DataPermissionNodeTrans.copyDataPermissionNode((DataPermissionNode) data))
                .collect(Collectors.toList());
    }

    /**
     * 查找当前节点的团队
     * @param tmp
     * @return
     */
    public DataPermissionNode findT(DataPermissionNode tmp) {
        if (tmp.getLabel() == LabelEnum.T) {
            return tmp;
        }
        while ((tmp = (DataPermissionNode) tmp.getParent()) != null) {
            if (tmp.getLabel() == LabelEnum.T) {
                return tmp;
            }
        }
        return null;
    }

    /**
     * 获取用户已拥有权限
     * @param conditionEnum
     * @param labelEnum
     * @param baseQry
     * @return
     */
    public List<String> getOwnedDataPermissionIds(ConditionEnum conditionEnum, LabelEnum labelEnum, BaseQry baseQry) {

        // 1. 获取用户已申请的权限记录
        DataPermission userAppliedPermission = dataPermissionRepository.getDataPermission(
            baseQry.getOperator(),
            baseQry.getThemeType(),
            baseQry.getDataPermissionType()
        );

        // 2. 将JSON格式的分层权限转换为DTO对象
        String layeredPermissionJson = userAppliedPermission.getLayeredPermission();
        LayeredDataPermissionDTO layeredPermission = LayeredDataPermissionDTO.getJson2LayeredDataPermissionDTO(layeredPermissionJson);

        // 3. 提取指定层级和标签下的权限数据
        String fieldPrefix = conditionEnum.getFieldPrefix();
        List<DataPermissionDTO> targetLayerPermissions = layeredPermission
            .getDataPermissions4LayeredOwned(fieldPrefix, labelEnum);

        // 4. 转换为ID列表并返回
        return targetLayerPermissions.stream()
            .map(DataPermissionDTO::getId)
            .collect(Collectors.toList());
    }


    /**
     * 获取用户已拥有权限
     */
    public List<DataPermissionDTO> getOwnedDataPermissions(ConditionEnum conditionEnum, LabelEnum labelEnum, BaseQry baseQry) {
        final DataPermission dataPermission = dataPermissionRepository.getDataPermission(baseQry.getOperator(), baseQry.getThemeType(), baseQry.getDataPermissionType()); // 用户已申请权限
        return new ArrayList<>(LayeredDataPermissionDTO.getJson2LayeredDataPermissionDTO(dataPermission.getLayeredPermission()) // 获取已申请分层权限数据
            .getDataPermissions4LayeredOwned(conditionEnum.getFieldPrefix(), labelEnum));
    }

    /**
     * 交集，保证用户提交的权限都合法，与已申请权限取交集
     *
     * @param conditionDataPermissions 用户前端提交的查询条件
     */
    public List<String> intersectionWithOwnedDataPermissions(List<String> conditionDataPermissions, ConditionEnum conditionEnum, LabelEnum labelEnum, BaseQry baseQry) {
        List<String> ownedDataPermissionIds = this.getOwnedDataPermissionIds(conditionEnum, labelEnum, baseQry);
        if (CollectionUtils.isEmpty(ownedDataPermissionIds)) {
            return conditionDataPermissions;
        }
        if (CollectionUtils.isEmpty(conditionDataPermissions)) {
            return ownedDataPermissionIds;
        }
        conditionDataPermissions.retainAll(ownedDataPermissionIds);
        return conditionDataPermissions;
    }

    /**
     * 获取用户已拥有权限
     * @param conditionEnum
     * @param baseQry
     * @return
     */
    public List<DataPermissionDTO> getOwnedDataPermission(ConditionEnum conditionEnum, BaseQry baseQry) {
        final DataPermission dataPermission = dataPermissionRepository.getDataPermission(baseQry.getOperator(), baseQry.getThemeType(), baseQry.getDataPermissionType()); // 用户已申请权限
        final DataPermissionComposeDTO dataPermissionComposeDTO = JSON.parseObject(dataPermission.getPermission(), DataPermissionComposeDTO.class);
        return conditionEnum == ConditionEnum.BUSINESS_LEVEL ?
            dataPermissionComposeDTO.getFvData().getDataPermissionDTOList()
            : dataPermissionComposeDTO.getProData() == null ? Lists.newArrayList() : dataPermissionComposeDTO.getProData().getDataPermissionDTOList();
    }

    public List<DataPermissionNode> getBaseLayeredDataPermissions(ConditionEnum conditionEnum, LabelEnum labelEnum) {
        return dataPermissionMemory.getBaseDataLayeredDataPermission(conditionEnum, labelEnum);
    }

    public List<DataPermissionNode> getBaseLayeredDataPermissions(ConditionEnum conditionEnum, LabelEnum labelEnum, String parentId, List<String> teamIds) {
        return dataPermissionMemory.getBaseDataLayeredDataPermission(conditionEnum, labelEnum).stream()
            .filter(v -> v.getParent() == null || v.getLabel() == LabelEnum.L3 ?
                v.getParent().getParent().getId().equals(parentId) : v.getParent().getId().equals(parentId)
                && (ObjectUtils.isEmpty(v.getTeamId()) || CollectionUtils.isEmpty(teamIds) || teamIds.contains(v.getTeamId()))
            ).collect(Collectors.toList());
    }

    /**
     * 回写已申请数据权限
     * @param dataPermissionComposeDTO  数据库基础数据权限
     * @param baseDataPermissionDTO
     */
    public void writebackDataPermission(DataPermissionComposeDTO dataPermissionComposeDTO, BaseDataPermissionDTO baseDataPermissionDTO) {
        String account = baseDataPermissionDTO.getOperator();
        if (ObjectUtils.isEmpty(account)) {
            return;
        }
        // 单次只渲染一个主题的一个权限类型
        final DataPermission dataPermission = dataPermissionRepository.getDataPermission(account, baseDataPermissionDTO.getThemeType(), baseDataPermissionDTO.getDataPermissionType());
        if (dataPermission != null) {
            final DataPermissionComposeDTO dataPermissionComposeDTOAlready = JSON.parseObject(dataPermission.getPermission(), DataPermissionComposeDTO.class); // 树结构权限
            final LayeredDataPermissionDTO layeredDataPermissionDTO = LayeredDataPermissionDTO.getJson2LayeredDataPermissionDTO(dataPermission.getLayeredPermission()); // 分层权限
            // 渠道
            if (dataPermissionComposeDTO.getFvData() != null && dataPermissionComposeDTOAlready.getFvData() != null) {
                final DataPermissionDTO fvData = dataPermissionComposeDTO.getFvData();
                dealCheckedStatus(dataPermissionComposeDTOAlready.getFvData());
                final Map<LabelEnum, Map<String, DataPermissionDTO>> ownedDataPermissionMap = this.initDataPermissionDTO4Checked(dataPermissionComposeDTOAlready.getFvData()); // FV
                this.updateAlreadyAuthorized(fvData, layeredDataPermissionDTO, ConditionEnum.BUSINESS_LEVEL.getFieldPrefix(), ownedDataPermissionMap); // 回写权限
            }
            // 产品端平台
            if (dataPermissionComposeDTO.getProData() != null && dataPermissionComposeDTOAlready.getProData() != null) {
                final DataPermissionDTO proData = dataPermissionComposeDTO.getProData();
                dealCheckedStatus(dataPermissionComposeDTOAlready.getProData());
                final Map<LabelEnum, Map<String, DataPermissionDTO>> ownedDataPermissionMap = this.initDataPermissionDTO4Checked(dataPermissionComposeDTOAlready.getProData()); // 产品
                this.updateAlreadyAuthorized(proData, layeredDataPermissionDTO, ConditionEnum.PRO.getFieldPrefix(), ownedDataPermissionMap); // 回写权限
            }
            // 会员类型
            if (dataPermissionComposeDTO.getVipTypeData() != null && dataPermissionComposeDTOAlready.getVipTypeData() != null) {
                final DataPermissionDTO vipTypeData = dataPermissionComposeDTO.getVipTypeData();
                dealCheckedStatus(dataPermissionComposeDTOAlready.getVipTypeData());
                final Map<LabelEnum, Map<String, DataPermissionDTO>> ownedDataPermissionMap = this.initDataPermissionDTO4Checked(dataPermissionComposeDTOAlready.getVipTypeData()); // 会员类型
                this.updateAlreadyAuthorized(vipTypeData, layeredDataPermissionDTO, ConditionEnum.VIP_TYPE.getFieldPrefix(), ownedDataPermissionMap); // 回写权限
            }
            // 竞品监控
            if (dataPermissionComposeDTO.getCompetitorMonitorData() != null && dataPermissionComposeDTOAlready.getCompetitorMonitorData() != null) {
                final DataPermissionDTO competitorMonitorData = dataPermissionComposeDTO.getCompetitorMonitorData();
                final Map<LabelEnum, Map<String, DataPermissionDTO>> ownedDataPermissionMap = this.initDataPermissionDTO4Checked(dataPermissionComposeDTOAlready.getCompetitorMonitorData()); // 竞品监控
                this.updateAlreadyAuthorized(competitorMonitorData, layeredDataPermissionDTO, ConditionEnum.COMPETITOR_MONITOR.getFieldPrefix(), ownedDataPermissionMap); // 回写权限
            }
        }
    }

    /**
     * 初始化已申请权限 - 铺平
     * @param dataPermissionDTO
     * @return
     */
    private Map<LabelEnum, Map<String, DataPermissionDTO>> initDataPermissionDTO4Checked(DataPermissionDTO dataPermissionDTO) {
        Map<LabelEnum, Map<String, DataPermissionDTO>> map = new EnumMap<>(LabelEnum.class);
        if (dataPermissionDTO == null || ObjectUtils.isEmpty(dataPermissionDTO.getName()) || ObjectUtils.isEmpty(dataPermissionDTO.getId())) {
            return map;
        }
        Queue<DataPermissionDTO> queue = new LinkedList<>();
        queue.offer(dataPermissionDTO);
        DataPermissionDTO cur = null;
        while ((cur = queue.poll()) != null) {
            final LabelEnum labelEnum = LabelEnum.levelOf(cur.getLevel());
            map.computeIfAbsent(labelEnum, le -> new HashMap<>()).putIfAbsent(cur.identifier(), cur); 
            if (!CollectionUtils.isEmpty(cur.getDataPermissionDTOList())) {
                cur.getDataPermissionDTOList().stream().forEach(queue::offer);
            }
        }
        return map;
    }

    /**
     * 回写权限
     * @param cur 当前节点
     * @param layeredDataPermissionDTO 用户已拥有分层权限集合
     * @param fieldPrefix 分层权限属性前缀
     */
    private void updateAlreadyAuthorized(DataPermissionDTO cur, LayeredDataPermissionDTO layeredDataPermissionDTO, String fieldPrefix, final Map<LabelEnum, Map<String, DataPermissionDTO>> ownedDataPermissionMap) {
        if (cur != null) {
            cur.setAlreadyAuthorized(layeredDataPermissionDTO.containsDataPermissions4LayeredOwned(fieldPrefix, LabelEnum.levelOf(cur.getLevel()), cur));
            cur.setChecked(ownedDataPermissionMap.getOrDefault(LabelEnum.levelOf(cur.getLevel()),
                Maps.newHashMap()).getOrDefault(cur.identifier(),
                DataPermissionDTO.builder().checked(DataPermissionCheckedEnum.UN_CHECKED.getCode()).build()).getChecked());
            if (cur.getDataPermissionDTOList() != null) {
                for (DataPermissionDTO dataPermissionDTO : cur.getDataPermissionDTOList()) {
                    this.updateAlreadyAuthorized(dataPermissionDTO, layeredDataPermissionDTO, fieldPrefix, ownedDataPermissionMap);
                }
            }
        }
    }

    /**
     * 保存审批记录
     * @param dataPermissionApplyDTO
     * @return
     */
    public boolean saveDataPermissionApply(DataPermissionApplyDTO dataPermissionApplyDTO, OaApprovedResponse oaApprovedResponse) {
        return dataPermissionRepository.saveDataPermissionOa(dataPermissionApplyDTO, oaApprovedResponse) > 0;
    }

    /**
     * 更新审批记录
     * @param callbackDTO
     * @param dataPermissionOaStatus
     * @return
     */
//    @Transactional
    public boolean updateDataPermissionOaApprovalStatus(DataPermissionApprovalCallbackDTO callbackDTO, DataPermissionOaStatusEnum dataPermissionOaStatus) {
        String acId = callbackDTO.getAcId();
        List<DataPermissionOa> dataPermissionOas = dataPermissionRepository.getDataPermissionOa(acId, null);
        if (CollectionUtils.isEmpty(dataPermissionOas)) {
            String messageId = oaApprovedApi.queryMessageId(acId);
            dataPermissionOas = dataPermissionRepository.getDataPermissionOa(null, messageId);
            if (!CollectionUtils.isEmpty(dataPermissionOas) && StringUtils.isNotBlank(messageId)) {
                // 补全acId
                dataPermissionRepository.updateDataPermissionOaAcIdByMessageId(acId, messageId);
            }
        }
        boolean success = dataPermissionRepository.updateDataPermissionOaApprovalStatus(callbackDTO, dataPermissionOaStatus) > 0;
        if (dataPermissionOaStatus == DataPermissionOaStatusEnum.APPROVED) {
            final OaApprovedNoticeMsgDTOBuilder builder = OaApprovedNoticeMsgDTO.builder().acId(acId);
            try {
                // 发送Oa审批通过消息，异步处理数据权限落库，devops 角色权限添加
                final String account = dataPermissionOas.get(0).getAccount();
                builder.account(account);
//                oaApprovedRocketMQTemplate.send(builder.build());
                msgSender.sendOaApprovedAsyncTaskMsg(builder.build());
            } catch (Exception e) {
                log.error("[order-analysis][oa-approved-message] fail. ", e);
//                oaApprovedRocketMQTemplate.send(builder.build());
                msgSender.sendOaApprovedAsyncTaskMsg(builder.build());
            }
        }
        return success;
    }

    /**
     * 校验权限是否逐层申请（出现权限断层，则返回异常）
     * @param dataPermissionComposeDTO
     * @return
     */
    public boolean checkUserParamsDataPermissionRightful(DataPermissionComposeDTO dataPermissionComposeDTO) {
        if (dataPermissionComposeDTO != null) {
            final DataPermissionDTO fvData = dataPermissionComposeDTO.getFvData();
            final DataPermissionDTO proData = dataPermissionComposeDTO.getProData();
            final DataPermissionDTO vipTypeData = dataPermissionComposeDTO.getVipTypeData();
            final DataPermissionDTO competitorMonitorData = dataPermissionComposeDTO.getCompetitorMonitorData();
            return this.checkUserParamsDataPermissionRightful(fvData, LabelEnum.L1.getLevel())
                    && this.checkUserParamsDataPermissionRightful(proData, LabelEnum.P0.getLevel())
                    && this.checkUserParamsDataPermissionRightful(vipTypeData, LabelEnum.VT0.getLevel())
                    && this.checkUserParamsDataPermissionRightful(competitorMonitorData, LabelEnum.COMPETITOR_MONITOR.getLevel());
        }
        return false;
    }

    private boolean checkUserParamsDataPermissionRightful(DataPermissionDTO cur, int level) {
        if (cur != null && cur.getLevel() != null && (cur.getLevel().intValue() == level || cur.getLevel().intValue() == LabelEnum.T.getLevel())) {
            if (cur.getDataPermissionDTOList() == null) {
                return true;
            }
            for (DataPermissionDTO dataPermissionDTO : cur.getDataPermissionDTOList()) {
                if (!this.checkUserParamsDataPermissionRightful(dataPermissionDTO,
                    cur.getLevel().intValue() == LabelEnum.T.getLevel() ? level : level + 1)) { // 逐层+1判断是否断层
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取申请数据权限（中文）
     *
     * @param dataPermissionComposeDTO 数据库基础数据权限
     * @param dataPermissionApplyDTO 用户申请数据权限
     */
    public String getApprovedDataPermissionCns(DataPermissionComposeDTO dataPermissionComposeDTO, DataPermissionApplyDTO dataPermissionApplyDTO) {
        LayeredDataPermissionDTO layeredDataPermissionDTO = new LayeredDataPermissionDTO();
        // 分层权限数据维护
        DataPermissionComposeDTO dataPermissionComposeDTOApproved = dataPermissionApplyDTO.getDataPermissionComposeDTO();
        layeredDataPermissionDTO.createLayeredDataPermissions(
            LayeredDataPermissionSourceDTO.builder()
                .fieldPrefix(ConditionEnum.BUSINESS_LEVEL.getFieldPrefix())
                .dataPermissionDTO(dataPermissionComposeDTOApproved.getFvData())
                .build(),
            LayeredDataPermissionSourceDTO.builder()
                .fieldPrefix(ConditionEnum.PRO.getFieldPrefix())
                .dataPermissionDTO(dataPermissionComposeDTOApproved.getProData())
                .build(),
            LayeredDataPermissionSourceDTO.builder()
                .fieldPrefix(ConditionEnum.VIP_TYPE.getFieldPrefix())
                .dataPermissionDTO(dataPermissionComposeDTOApproved.getVipTypeData())
                .build(),
            LayeredDataPermissionSourceDTO.builder()
                .fieldPrefix(ConditionEnum.COMPETITOR_MONITOR.getFieldPrefix())
                .dataPermissionDTO(dataPermissionComposeDTOApproved.getCompetitorMonitorData())
                .build()
        );
        // Oa申请数据权限中文展示    格式：站外销售渠道》大客户组》电商》开发渠道》直客，直客下全选，别的节点没有选，展示站外销售渠道》大客户组》电商》开发渠道》直客
        final ArrayList<String> dataPermissionCns = new ArrayList<>();
        this.approvedDataInfoCn(dataPermissionApplyDTO, dataPermissionCns);
        this.approvedDataPermissionsCn(dataPermissionComposeDTO.getFvData(), layeredDataPermissionDTO, ConditionEnum.BUSINESS_LEVEL.getFieldPrefix(), new StringBuilder(), dataPermissionCns);
        this.approvedDataPermissionsCn(dataPermissionComposeDTO.getProData(), layeredDataPermissionDTO, ConditionEnum.PRO.getFieldPrefix(), new StringBuilder(), dataPermissionCns);
        this.approvedDataPermissionsCn(dataPermissionComposeDTO.getVipTypeData(), layeredDataPermissionDTO, ConditionEnum.VIP_TYPE.getFieldPrefix(), new StringBuilder(), dataPermissionCns);
        this.approvedDataPermissionsCn(dataPermissionComposeDTO.getCompetitorMonitorData(), layeredDataPermissionDTO, ConditionEnum.COMPETITOR_MONITOR.getFieldPrefix(), new StringBuilder(), dataPermissionCns);
        return String.join("<br //>", dataPermissionCns);
    }

    /**
     * 获取申请数据权限（中文）
     *
     * @param permissionAll 数据库基础数据权限
     * @param permissionApply 用户申请数据权限
     */
    public List<String> getDataPermissionApply(DataPermissionComposeDTO permissionApply, DataPermissionComposeDTO permissionAll) {
        List<String> permissionApplyCns = new ArrayList<>();

        if (permissionApply.getFvData() != null) {
            DataPermissionDTO applyFvData = permissionApply.getFvData();
            DataPermissionDTO allFvData = permissionAll.getFvData();

            // key:二级渠道id, value: 该渠道下的业务团队数量
            Map<String, Integer> idToNums2 = new HashMap<>();
            // key: 二级渠道id_业务团队id, value: 该二级渠道-业务团队下, 三级渠道的数量
            Map<String, Integer> idToNums3 = new HashMap<>();

            for (DataPermissionDTO permissionOfLevel2 : allFvData.getDataPermissionDTOList()) {
                idToNums2.put(permissionOfLevel2.getId(), permissionOfLevel2.getDataPermissionDTOList().size());
                for (DataPermissionDTO businessGroup : permissionOfLevel2.getDataPermissionDTOList()) {
                    idToNums3.put(permissionOfLevel2.getId() + "_" + businessGroup.getId(), businessGroup.getDataPermissionDTOList().size());
                }
            }

            // 二级渠道
            for (DataPermissionDTO permissionOfLevel2 : applyFvData.getDataPermissionDTOList()) {
                // 不显示二级渠道下-站内渠道这一项
                if (Constants.INTERNAL_CHANNEL_ID.equals(permissionOfLevel2.getId())) {
                    continue;
                }
                if (isAllChecked(permissionOfLevel2.getChecked())) {
                    continue;
                }
                if (isUnChecked(permissionOfLevel2.getChecked())) {
                    if (Objects.equals(permissionOfLevel2.getDataPermissionDTOList().size(), idToNums2.get(permissionOfLevel2.getId()))) {
                        permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)" + ": 其下的业务团队全部勾选");
                    } else {
                        // 业务团队
                        for (DataPermissionDTO businessGroup : permissionOfLevel2.getDataPermissionDTOList()) {
                            if (Objects.equals(businessGroup.getDataPermissionDTOList().size(), idToNums3.get(
                                permissionOfLevel2.getId() + "_" + businessGroup.getId()))) {
                                permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)" + "->" + businessGroup.getName() + ": 其下的三级渠道全部勾选");
                            } else {
                                // 三级渠道
                                for (DataPermissionDTO permissionOfLevel3 : businessGroup.getDataPermissionDTOList()) {
                                    permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)" + "->" + businessGroup.getName() + "->" + permissionOfLevel3.getName());
                                }
                            }
                        }
                    }
                }
                if (isPartChecked(permissionOfLevel2.getChecked())) {
                    for (DataPermissionDTO businessGroup : permissionOfLevel2.getDataPermissionDTOList()) {
                        if (isAllChecked(businessGroup.getChecked())) {
                            continue;
                        }
                        if (isUnChecked(businessGroup.getChecked())) {
                            // 业务团队
                            if (Objects.equals(businessGroup.getDataPermissionDTOList().size(), idToNums3.get(
                                permissionOfLevel2.getId() + "_" + businessGroup.getId()))) {
                                permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)" + "->" + businessGroup.getName() + ": 其下的三级渠道全部勾选");
                            } else {
                                // 三级渠道
                                for (DataPermissionDTO permissionOfLevel3 : businessGroup.getDataPermissionDTOList()) {
                                    permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)->" + businessGroup.getName() + "->" + permissionOfLevel3.getName());
                                }
                            }
                        }
                        if (isPartChecked(businessGroup.getChecked())) {
                            // 三级渠道是最底层了，不会有部分勾选
                            for (DataPermissionDTO permissionOfLevel3 : businessGroup.getDataPermissionDTOList()) {
                                if (isAllChecked(permissionOfLevel3.getChecked())) {
                                    continue;
                                }
                                if (isUnChecked(permissionOfLevel3.getChecked())) {
                                    permissionApplyCns.add(permissionOfLevel2.getName() + "(二级渠道)->" + businessGroup.getName() + "->" + permissionOfLevel3.getName());
                                }
                            }
                        }
                    }
                }
            }
        }

        if (permissionApply.getProData() != null) {
            DataPermissionDTO applyProData = permissionApply.getProData();
            DataPermissionDTO allProData = permissionAll.getProData();

            // key: 产品id, value: 端平台数量
            Map<String, Integer> idToNums = new HashMap<>();
            for (DataPermissionDTO product : allProData.getDataPermissionDTOList()) {
                idToNums.put(product.getId(), product.getDataPermissionDTOList().size());
            }

            if (isUnChecked(applyProData.getChecked())) {
                if (Objects.equals(applyProData.getDataPermissionDTOList().size(), allProData.getDataPermissionDTOList().size())) {
                    permissionApplyCns.add("站内渠道(二级渠道): 其下的产品全部勾选");
                } else {
                    for (DataPermissionDTO applyProduct : applyProData.getDataPermissionDTOList()) {
                        if (Objects.equals(applyProduct.getDataPermissionDTOList().size(), idToNums.get(applyProduct.getId()))) {
                            permissionApplyCns.add("站内渠道(二级渠道)" + "->" + applyProduct.getName() + ": 其下的端平台全部勾选");
                        } else {
                            for (DataPermissionDTO pro : applyProduct.getDataPermissionDTOList()) {
                                permissionApplyCns.add("站内渠道(二级渠道)" + "->" + applyProduct.getName() + "->" + pro.getName());
                            }
                        }
                    }
                }
            }

            if (isPartChecked(applyProData.getChecked())) {
                for (DataPermissionDTO applyProduct : applyProData.getDataPermissionDTOList()) {
                    if (isAllChecked(applyProduct.getChecked())) {
                        continue;
                    }
                    if (isUnChecked(applyProduct.getChecked())) {
                        if (Objects.equals(applyProduct.getDataPermissionDTOList().size(), idToNums.get(applyProduct.getId()))) {
                            permissionApplyCns.add("站内渠道(二级渠道)" + "->" + applyProduct.getName() + ": 其下的端平台全部勾选");
                        } else {
                            for (DataPermissionDTO platform : applyProduct.getDataPermissionDTOList()) {
                                permissionApplyCns.add("站内渠道(二级渠道)" + "->" + applyProduct.getName() + "->" + platform.getName());
                            }
                        }
                    }
                    if (isPartChecked(applyProduct.getChecked())) {
                        for (DataPermissionDTO platform : applyProduct.getDataPermissionDTOList()) {
                            if (isUnChecked(platform.getChecked())) {
                                permissionApplyCns.add("站内渠道(二级渠道)" + "->" + applyProduct.getName() + "->" + platform.getName());
                            }
                        }
                    }
                }
            }

        }

        if (permissionApply.getVipTypeData() != null) {
            DataPermissionDTO applyVipType = permissionApply.getVipTypeData();
            DataPermissionDTO allVipType = permissionAll.getVipTypeData();

            Map<String, Integer> idToNums = new HashMap<>();
            for (DataPermissionDTO permissionDTO : allVipType.getDataPermissionDTOList()) {
                idToNums.put(permissionDTO.getId(), permissionDTO.getDataPermissionDTOList().size());
            }

            if (isUnChecked(applyVipType.getChecked())) {
                for (DataPermissionDTO permissionDTO : applyVipType.getDataPermissionDTOList()) {
                    if (Objects.equals(permissionDTO.getDataPermissionDTOList().size(), idToNums.get(permissionDTO.getId()))) {
                        permissionApplyCns.add(permissionDTO.getName() + ": 其下的套餐类型全部勾选");
                    } else {
                        for (DataPermissionDTO vipType : permissionDTO.getDataPermissionDTOList()) {
                            permissionApplyCns.add(permissionDTO.getName() + "->" + vipType.getName());
                        }
                    }
                }
            }

            if (isPartChecked(applyVipType.getChecked())) {
                for (DataPermissionDTO vipType : applyVipType.getDataPermissionDTOList()) {
                    if (isUnChecked(vipType.getChecked())) {
                        if (Objects.equals(vipType.getDataPermissionDTOList().size(), idToNums.get(vipType.getId()))) {
                            permissionApplyCns.add(vipType.getName() + ": 其下的套餐类型全部勾选");
                        } else {
                            for (DataPermissionDTO packageType : vipType.getDataPermissionDTOList()) {
                                permissionApplyCns.add(vipType.getName() + "->" + packageType.getName());
                            }
                        }
                    }
                    if (isPartChecked(vipType.getChecked())) {
                        for (DataPermissionDTO packageType : vipType.getDataPermissionDTOList()) {
                            if (isUnChecked(packageType.getChecked())) {
                                permissionApplyCns.add(vipType.getName() + "->" + packageType.getName());
                            }
                        }
                    }
                }
            }

        }

        if (permissionApply.getCompetitorMonitorData() != null) {
            permissionApplyCns.add("竞品监控");
        }

        return permissionApplyCns;
    }

    public List<String> getDatePermissionAlreadyHas(DataPermissionComposeDTO permissionAll) {
        List<String> permissionAlreadyHas = new ArrayList<>();

        if (permissionAll.getFvData() != null) {
            DataPermissionDTO fvData = permissionAll.getFvData();

            for (DataPermissionDTO permissionOfLevel2 : fvData.getDataPermissionDTOList()) {
                if (Constants.INTERNAL_CHANNEL_ID.equals(permissionOfLevel2.getId())) {
                    continue;
                }
                if (permissionOfLevel2.getAlreadyAuthorized() == null || Boolean.FALSE.equals(permissionOfLevel2.getAlreadyAuthorized())) {
                    continue;
                }
                // 全部选中
                if (Objects.equals(permissionOfLevel2.getChecked(), DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode())) {
                    permissionAlreadyHas.add(permissionOfLevel2.getName() + "(二级渠道)" + ": 其下的业务团队全部勾选");
                    continue;
                }
                for (DataPermissionDTO businessGroup : permissionOfLevel2.getDataPermissionDTOList()) {
                    if (businessGroup.getAlreadyAuthorized() == null || Boolean.FALSE.equals(businessGroup.getAlreadyAuthorized())) {
                        continue;
                    }
                    if (Objects.equals(businessGroup.getChecked(), DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode())) {
                        permissionAlreadyHas.add(permissionOfLevel2.getName() + "(二级渠道)" + "->" + businessGroup.getName() + ": 其下的三级渠道全部勾选");
                        continue;
                    }
                    for (DataPermissionDTO permissionOfLevel3 : businessGroup.getDataPermissionDTOList()) {
                        if (permissionOfLevel3.getAlreadyAuthorized() == null || Boolean.FALSE.equals(permissionOfLevel3.getAlreadyAuthorized())) {
                            continue;
                        }
                        permissionAlreadyHas.add(permissionOfLevel2.getName() + "(二级渠道)" + "->" + businessGroup.getName() + "->" + permissionOfLevel3.getName());
                    }
                }
            }
        }

        if (permissionAll.getProData() != null) {
            DataPermissionDTO proData = permissionAll.getProData();
            for (DataPermissionDTO product : proData.getDataPermissionDTOList()) {
                if (product.getAlreadyAuthorized() == null || Boolean.FALSE.equals(product.getAlreadyAuthorized())) {
                    continue;
                }
                if (Objects.equals(product.getChecked(), DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode())) {
                    permissionAlreadyHas.add("站内渠道(二级渠道)" + "->" + product.getName() + ": 其下的端平台全部勾选");
                    continue;
                }
                for (DataPermissionDTO platform : product.getDataPermissionDTOList()) {
                    if (platform.getAlreadyAuthorized() == null || Boolean.FALSE.equals(platform.getAlreadyAuthorized())) {
                        continue;
                    }
                    permissionAlreadyHas.add("站内渠道(二级渠道)" + "->" + product.getName() + "->" + platform.getName());
                }
            }
        }

        if (permissionAll.getVipTypeData() != null) {
            DataPermissionDTO vipTypeData = permissionAll.getVipTypeData();
            // 会员类型
            for (DataPermissionDTO vipType : vipTypeData.getDataPermissionDTOList()) {
                if (vipType.getAlreadyAuthorized() == null || Boolean.FALSE.equals(vipType.getAlreadyAuthorized())) {
                    continue;
                }
                if (Objects.equals(vipType.getChecked(), DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode())) {
                    permissionAlreadyHas.add(vipType.getName() + ": 其下的套餐类型全部勾选");
                    continue;
                }

                // 套餐类型
                for (DataPermissionDTO packageType : vipType.getDataPermissionDTOList()) {
                    if (packageType.getAlreadyAuthorized() == null || Boolean.FALSE.equals(packageType.getAlreadyAuthorized())) {
                        continue;
                    }
                    permissionAlreadyHas.add(vipType.getName() + "->" + packageType.getName());
                }
            }
        }

        if (permissionAll.getCompetitorMonitorData() != null) {
            Boolean competitionPermission = permissionAll.getCompetitorMonitorData().getAlreadyAuthorized();
            if (Boolean.TRUE.equals(competitionPermission)) {
                permissionAlreadyHas.add("竞品监控");
            }
        }

        return permissionAlreadyHas;
    }

    private boolean isAllChecked(Integer checkedStatus) {
        if (checkedStatus != null && checkedStatus.equals(DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode())) {
            return true;
        }
        return false;
    }

    private boolean isUnChecked(Integer checkedStatus) {
        if (checkedStatus == null || checkedStatus.equals(DataPermissionCheckedEnum.UN_CHECKED.getCode())) {
            return true;
        }
        return false;
    }

    private boolean isPartChecked(Integer checkedStatus) {
        if (checkedStatus != null && checkedStatus.equals(DataPermissionCheckedEnum.CHILDREN_PART_CHECKED.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * 增加OA申请申请的主题与权限类型
     */
    private void approvedDataInfoCn(DataPermissionApplyDTO dataPermissionApplyDTO, ArrayList<String> dataPermissionCns) {
        ThemeTypeEnum themeTypeEnum = ThemeTypeEnum.findEnumByThemeType(dataPermissionApplyDTO.getThemeType());
        if (themeTypeEnum != null) {
            dataPermissionCns.add("申请权限主题：" + themeTypeEnum.getMsg());
        }
    }

    /**
     * 获取申请数据权限列表（中文）
     */
    private void approvedDataPermissionsCn(DataPermissionDTO cur, LayeredDataPermissionDTO layeredDataPermissionDTO, String fieldPrefix, StringBuilder stringBuilder, List<String> dataPermissionCns) {
        if (cur == null || ObjectUtils.isEmpty(cur.getId()) || ObjectUtils.isEmpty(cur.getName())) {
            return ;
        }
        if (layeredDataPermissionDTO.containsDataPermissions4LayeredOwned(fieldPrefix, LabelEnum.levelOf(cur.getLevel()), cur)) {
            stringBuilder.append(cur.getName()).append(stringBuilder.length() == 0 ? "" : " 》"); // 格式：站外销售渠道》大客户组》电商》开发渠道》直客，直客下全选，别的节点没有选，展示站外销售渠道》大客户组》电商》开发渠道》直客
            if (!this.fullDataPermission2NextLevel(layeredDataPermissionDTO, fieldPrefix, cur)) {
                if (CollectionUtils.isEmpty(cur.getDataPermissionDTOList())) {
                    dataPermissionCns.add(stringBuilder.toString());
                    return ;
                }
                for (DataPermissionDTO dataPermissionDTO : cur.getDataPermissionDTOList()) {
                    this.approvedDataPermissionsCn(dataPermissionDTO, layeredDataPermissionDTO, fieldPrefix, new StringBuilder(stringBuilder), dataPermissionCns);
                }
            } else {
                String message = stringBuilder.toString().trim();
                dataPermissionCns.add(message.endsWith("》") ? message.substring(0, message.length() - 1) : message);
                return;
            }
        }
    }

    /**
     * 满权限
     * @param layeredDataPermissionDTO
     * @param cur
     * @param fieldPrefix
     * @return true 满权限 false 非满权限
     */
    private boolean fullDataPermission2NextLevel(LayeredDataPermissionDTO layeredDataPermissionDTO, String fieldPrefix, DataPermissionDTO cur) {
        if (LabelEnum.levelOf(cur.getLevel()).getNextLabelEnum() == null) {
            return true;
        }
        return this.fullDataPermission(
            layeredDataPermissionDTO.getDataPermissions4LayeredOwned(fieldPrefix, LabelEnum.levelOf(cur.getLevel()).getNextLabelEnum(), cur),
            cur.getDataPermissionDTOList()
        );
    }

    public boolean fullDataPermission(final List<DataPermissionDTO> dataPermissions, List<DataPermissionDTO> dataPermissionDTOList) {
        if (CollectionUtils.isEmpty(dataPermissions) && CollectionUtils.isEmpty(dataPermissionDTOList) || (dataPermissions == null
            || dataPermissionDTOList == null)) {
            return false;
        }
        return matchData(dataPermissions, dataPermissionDTOList)
            && matchData(dataPermissions.stream().flatMap(d -> d.getDataPermissionDTOList().stream()).collect(Collectors.toList()),
                dataPermissionDTOList.stream().flatMap(d -> d.getDataPermissionDTOList().stream()).collect(Collectors.toList()));
    }

    private boolean matchData(List<DataPermissionDTO> dataPermissions, List<DataPermissionDTO> dataPermissionDTOList) {
        return dataPermissions.stream()
            .map(v -> NumberUtils.isDigits(v.getId()) ? Integer.parseInt(v.getId()) : this.fullDataPermissionSum(v.getId().toCharArray()))
            .mapToInt(Integer::valueOf)
            .sum()
            >=
            dataPermissionDTOList.stream()
                .map(v -> NumberUtils.isDigits(v.getId()) ? Integer.parseInt(v.getId()) : this.fullDataPermissionSum(v.getId().toCharArray()))
                .mapToInt(Integer::valueOf).sum();
    }

    private int fullDataPermissionSum(char[] chars) {
        int sum = 0;
        for (char c : chars) {
            sum += c;
        }
        return sum;
    }

    /**
     * 处理已审批通过的数据权限申请
     * @param acId
     * @return
     */
    public boolean dealOaApprovedDataPermission(String acId, String account) {
        LayeredDataPermissionDTO layeredDataPermissionDTO = new LayeredDataPermissionDTO();
        final List<DataPermissionOa> dataPermissionOas = dataPermissionRepository.getDataPermissionOa(acId, null);
        boolean bl = true;
        for (DataPermissionOa dataPermissionOa : dataPermissionOas) { // 多种权限遍历落库
            // 账号维护 null，该场景存在于用户oa 审批通过快于数据库落库
            // 历史出现过一次 oa 权限审批通过，但是权限未开通的情况：权限申请的步骤是：1、提交 oa 申请。2、数据库权限落库。3.审批通过后开通权限。下面举例一个真实出现问题的场景：
            // 方颖申请权限，线上出现的步骤却是：1、3、2，导致步骤3的时候失败了，因而引起开通权限的时候没有成功。已经增加如下步骤 account 如果为 null，重新通过 oa 记录重新赋值。
            if (StringUtils.isBlank(account)) {
                account = dataPermissionOa.getAccount();
            }
            final DataPermissionComposeDTO dataPermissionComposeDTO = JSON.parseObject(dataPermissionOa.getPermission(), DataPermissionComposeDTO.class);
            boolean isFvDataPermission = DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode().equals(dataPermissionOa.getType()); // 渠道、产品端平台权限
            DataPermissionTypeEnum permissionTypeEnum = DataPermissionTypeEnum.findEnumByDataPermissionType(dataPermissionOa.getType());
            if (permissionTypeEnum == DataPermissionTypeEnum.FV_DATA_PERMISSION) {
                dataPermissionComposeDTO.setVipTypeData(null);
                dataPermissionComposeDTO.setCompetitorMonitorData(null);
            }
            if (permissionTypeEnum == DataPermissionTypeEnum.VIP_TYPE_DATA_PERMISSION) {
                dataPermissionComposeDTO.setFvData(null);
                dataPermissionComposeDTO.setProData(null);
                dataPermissionComposeDTO.setCompetitorMonitorData(null);
            }
            if (permissionTypeEnum == DataPermissionTypeEnum.COMPETITOR_DATA_PERMISSION) {
                dataPermissionComposeDTO.setFvData(null);
                dataPermissionComposeDTO.setProData(null);
                dataPermissionComposeDTO.setVipTypeData(null);
            }
            this.dealCheckedStatus(dataPermissionComposeDTO.getFvData()); // 渠道
            this.dealCheckedStatus(dataPermissionComposeDTO.getProData()); // 产品
            this.dealCheckedStatus(dataPermissionComposeDTO.getVipTypeData()); // 会员类型
            // 分层权限数据维护
            layeredDataPermissionDTO.createLayeredDataPermissions(
                    isFvDataPermission ? LayeredDataPermissionSourceDTO.builder().fieldPrefix(ConditionEnum.BUSINESS_LEVEL.getFieldPrefix()).dataPermissionDTO(dataPermissionComposeDTO.getFvData()).build() : null,
                    isFvDataPermission ? LayeredDataPermissionSourceDTO.builder().fieldPrefix(ConditionEnum.PRO.getFieldPrefix()).dataPermissionDTO(dataPermissionComposeDTO.getProData()).build() : null,
                    isFvDataPermission ? null : LayeredDataPermissionSourceDTO.builder().fieldPrefix(ConditionEnum.VIP_TYPE.getFieldPrefix()).dataPermissionDTO(dataPermissionComposeDTO.getVipTypeData()).build()
            );
            // 权限落库
            final DataPermission dataPermission = dataPermissionRepository.getDataPermission(account, dataPermissionOa.getThemeType(), dataPermissionOa.getType());
            log.info("oa: {}, dadaPermission: {}", JSON.toJSONString(dataPermissionOa), JSON.toJSONString(dataPermission));
            final DataPermission dataPermissionTmp = DataPermission.builder()
                    .account(dataPermissionOa.getAccount())
                    .permission(JSON.toJSONString(dataPermissionComposeDTO))
                    .layeredPermission(JSON.toJSONString(layeredDataPermissionDTO))
                    .status(1)
                    .type(dataPermissionOa.getType())
                    .themeType(dataPermissionOa.getThemeType())
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
            bl = bl && (dataPermission == null ? dataPermissionRepository.saveDataPermission(dataPermissionTmp) > 0 :
                    dataPermissionRepository.updateDataPermission(dataPermissionTmp) > 0);
        }
        return bl;
    }

    /**
     * 处理每个数据权限节点下是否已经申请全部权限
     * @param dataPermissionDTO
     */
    private void dealCheckedStatus(DataPermissionDTO dataPermissionDTO) {
        if (dataPermissionDTO == null) {
            return ;
        }
        if (CollectionUtils.isEmpty(dataPermissionDTO.getDataPermissionDTOList())) {
            dataPermissionDTO.setChecked(DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode());
            return;
        }
        for (DataPermissionDTO dto : dataPermissionDTO.getDataPermissionDTOList()) {
            this.dealCheckedStatus(dto);
        }
        final LabelEnum labelEnum = LabelEnum.levelOf(dataPermissionDTO.getLevel());

        DataPermissionNode dataPermissionNode = dataPermissionMemory.getBaseDataLayeredDataPermission4One(labelEnum.getConditionEnum(), labelEnum, dataPermissionDTO.getId());
        if (dataPermissionNode == null) {
            return;
        }

        boolean checked = this.fullDataPermission(dataPermissionDTO.getDataPermissionDTOList(),
            dataPermissionNode.getSubDataNodeList().stream()
                .map(v -> DataPermissionNodeTrans.copyDataPermissionNode((DataPermissionNode) v))
                .collect(Collectors.toList())); // 是否全部选中判断
        int checkedTmp = DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode();
        for (DataPermissionDTO dto : dataPermissionDTO.getDataPermissionDTOList()) { // 判断子节点是否已经全部选中
            checkedTmp &= dto.getChecked();
        }
        dataPermissionDTO.setChecked(checked && checkedTmp == DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode() ?
            DataPermissionCheckedEnum.CHILDREN_ALL_CHECKED.getCode() : DataPermissionCheckedEnum.CHILDREN_PART_CHECKED.getCode());
    }

    public List<DataPermission> getDataPermissions(String account) {
        return dataPermissionRepository.getDataPermissions(account);
    }

    public List<DataPermission> getDataPermissionsByThemeType(String account, Integer themeType) {
        return dataPermissionRepository.getDataPermissionsByThemeType(account, themeType);
    }

    public Integer determineThemeType(BaseQry baseQry) {
        List<DataPermission> dataPermissions = this.getDataPermissions(baseQry.getOperator());
        if (dataPermissions != null && dataPermissions.stream().map(DataPermission::getThemeType).distinct().count() == 1) {
            return dataPermissions.get(0).getThemeType();
        }
        return null;
    }

    /**
     * 用户已申请 用户传参的主题
     * @param baseQry
     * @return
     */
    public boolean ownedThemeType(BaseQry baseQry) {
        List<DataPermission> dataPermissions = this.getDataPermissions(baseQry.getOperator());
        return dataPermissions != null && dataPermissions.stream()
                .filter(v -> v.getThemeType() != null && v.getThemeType().equals(baseQry.getThemeType())).count() > 0;
    }

    /**
     * 用户已申请 用户传参的数据权限类型
     * @param baseQry
     * @return
     */
    public boolean ownedDataPermissionType(BaseQry baseQry) {
        List<DataPermission> dataPermissions = this.getDataPermissions(baseQry.getOperator());
        return this.ownedThemeType(baseQry) && dataPermissions.stream()
                .filter(v -> baseQry.getThemeType() == null || baseQry.getThemeType().equals(v.getThemeType()))
                .filter(v -> v.getType() != null && v.getType().equals(baseQry.getDataPermissionType())).count() > 0;
    }

}
