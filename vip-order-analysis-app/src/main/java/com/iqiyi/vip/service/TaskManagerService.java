package com.iqiyi.vip.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.PaymentType;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.PaymentTypeRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.BusinessObjectDTO;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.FvChannelReqDTO;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionVO;
import com.iqiyi.vip.dto.target.AnalysisTargetVO;
import com.iqiyi.vip.dto.task.AnalysisTaskConfigReqDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskConfigRespDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskDeleteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskPageQryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskReExecuteDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskReTaskNameDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskResDTO;
import com.iqiyi.vip.enums.AgreementTypeEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.PriceInsurancePeriodEnum;
import com.iqiyi.vip.enums.SignTypeEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.enums.VipCardTypeEnum;
import com.iqiyi.vip.mq.MsgSender;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @className TaskMangerService
 * @description
 * @date 2022/6/1
 **/
@Component
public class TaskManagerService {

    public static final ImmutableSet<String> LEVEL_BUSINESS_IDS = ImmutableSet.of(
        "level2BusinessIds","level3BusinessIds","level4BusinessIds",
        "level5BusinessIds","level6BusinessIds","level7BusinessIds",
        "level8BusinessIds"
    );

    public static final String BUSINESS_OBJECTS_NAME = "businessObjects";

    public static final ImmutableMap<String, String> OLD_TO_NEW_CONDITION_CODE_MAP = ImmutableMap.of(
        "vipCardTypes","vipCardType",
        "renewFlags","renewFlag",
        "renewTypes","renewType",
        "agreementNames","agreementTypes"
    );


    @Resource
    private AnalysisTaskRepository analysisTaskRepository;

    @Resource
//    private RocketMQTemplate<AnalysisTaskExecuteDTO> javaObjectRocketMQTemplate;
    private MsgSender msgSender;

    @Resource
    private AnalysisResultStorageService storageService;

    @Resource
    private AnalysisTargetRepository targetRepository;

    @Resource
    private AnalysisDimensionRepository dimensionRepository;

    @Resource
    private PaymentTypeRepository paymentTypeRepository;


    public PageListResult<AnalysisTaskResDTO> pageList(AnalysisTaskPageQryDTO query, boolean isHighLevelUser) {
        return analysisTaskRepository.getPageListResult(query, isHighLevelUser);
    }

    public CommonResult reExecuteTask(AnalysisTaskReExecuteDTO reExecuteDTO) throws JsonProcessingException {
        Long taskId = reExecuteDTO.getTaskId();
        String operator = reExecuteDTO.getOperator();
        AnalysisTaskDO task = analysisTaskRepository.getTaskByIdAndOperator(taskId, operator);
        if (task == null) {
            return new CommonResult(CodeEnum.NOT_EXIST.getCode(), CodeEnum.NOT_EXIST.getMessage());
        }
        if (TaskStatusEnum.RUNNING.getStatus().equals(task.getStatus()) || TaskStatusEnum.READY.getStatus().equals(task.getStatus())) {
            return new CommonResult(CodeEnum.IDEMPOTENT.getCode(), String.format("任务: %s 正在进行中，请等待", task.getId()));
        }
        Map<String, List<Object>> conditionMap = getConditionMap(task.getCondition());
        addProductTypes(task, conditionMap);
        AnalysisTaskExecuteDTO taskExecuteDTO = AnalysisTaskExecuteDTO.builder()
            .taskId(taskId)
            .conditionParamContext(ConditionParamContext.builder().paramMap(conditionMap).build())
            .dimensionCodes(Splitter.on(",").splitToList(task.getDimensions()))
            .targetCodes(Splitter.on(",").splitToList(task.getTargets()))
            .operator(operator)
            .taskMD5(task.getUniqueIdentification())
            .businessTypeId(task.getBusinessTypeId())
            .dataPermissionType(task.getDataPermissionType())
            .themeType(task.getThemeType())
            .build();
//        javaObjectRocketMQTemplate.send(taskExecuteDTO);
        msgSender.sendAsyncTaskMsg(taskExecuteDTO);
        if (StringUtils.isNotBlank(task.getResult())) {
            storageService.deleteResult(task.getResult());
        }
        analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.RUNNING.getStatus(), null, null);
        return new CommonResult(CodeEnum.SUCCESS.getCode(), "任务已重新执行，对应任务id：" + task.getId());
    }

    private void addProductTypes(AnalysisTaskDO task, Map<String, List<Object>> conditionMap) {
        Date createTime = task.getCreateTime();
        if (createTime.getTime() <= 1706439955859L) {
            conditionMap.put("vipProductTypes", Lists.newArrayList(1));
        }
    }

    public CommonResult deleteTaskRecord(AnalysisTaskDeleteDTO deleteDTO) {
        Long taskId = deleteDTO.getTaskId();
        String operator = deleteDTO.getOperator();
        AnalysisTaskDO task = analysisTaskRepository.getTaskByIdAndOperator(taskId, operator);
        if (task == null) {
            return new CommonResult(CodeEnum.NOT_EXIST.getCode(), CodeEnum.NOT_EXIST.getMessage());
        }
        String result = task.getResult();
        if (StringUtils.isNotBlank(result)) {
            storageService.deleteResult(result);
        }
        analysisTaskRepository.deleteTaskRecord(taskId);
        return new CommonResult(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMessage());
    }

    public CommonResult reTaskName(AnalysisTaskReTaskNameDTO analysisTaskReTaskNameDTO) {
        Long taskId = analysisTaskReTaskNameDTO.getTaskId();
        String operator = analysisTaskReTaskNameDTO.getOperator();
        AnalysisTaskDO task = analysisTaskRepository.getTaskByIdAndOperator(taskId, operator);
        if (task == null) {
            return new CommonResult(CodeEnum.NOT_EXIST.getCode(), CodeEnum.NOT_EXIST.getMessage());
        }
        analysisTaskRepository.reTaskName(taskId, analysisTaskReTaskNameDTO.getTaskName(), analysisTaskReTaskNameDTO.getOperator());
        return new CommonResult(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMessage());
    }

    public DataResult<AnalysisTaskConfigRespDTO> queryTaskConfig(AnalysisTaskConfigReqDTO reqDTO) throws JsonProcessingException {
        Long taskId = reqDTO.getTaskId();
        String operator = reqDTO.getOperator();
        AnalysisTaskDO taskDO = analysisTaskRepository.getTaskByIdAndOperator(taskId, operator);
        if (taskDO == null) {
            return new DataResult<>(CodeEnum.NOT_EXIST.getCode(), CodeEnum.NOT_EXIST.getMessage());
        }
//        ConditionParamContext conditionParamContext = JacksonUtils.parseObject(taskDO.getCondition(), ConditionParamContext.class);
        Map<String, List<Object>> finalMap = getConditionMap(taskDO.getCondition());
        Integer themeType = taskDO.getThemeType();
        Integer businessTypeId = taskDO.getBusinessTypeId();
        AnalysisTaskConfigRespDTO configRespDTO = AnalysisTaskConfigRespDTO.builder()
            .taskName(taskDO.getTaskName())
            .taskId(taskId)
            .conditionParamVO(finalMap)
            .targetList(Splitter.on(",").splitToList(taskDO.getTargets())
                .stream()
                .map(this::getAnalysisTargetVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()))
            .dimensionList(StringUtils.isNotBlank(taskDO.getDimensions()) ? Splitter.on(",").splitToList(taskDO.getDimensions())
                .stream()
                .map(code -> getAnalysisDimensionVO(code, businessTypeId, themeType))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()) : Collections.emptyList())
            .businessTypeId(businessTypeId)
            .dataPermissionType(taskDO.getDataPermissionType())
            .themeType(themeType)
            .build();
        return DataResult.success(configRespDTO);
    }

    private Map<String, List<Object>> getConditionMap(String taskCondition) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> originalMap = mapper.readValue(taskCondition, Map.class);
        Map<String, List<Object>> finalMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {

            String entryKey = entry.getKey().trim();
            //将历史code值转为新的code值
            entryKey = OLD_TO_NEW_CONDITION_CODE_MAP.getOrDefault(entryKey, entryKey);

            Object entryValue = entry.getValue();
            String objStr = JacksonUtils.toJsonString(entryValue);

            if (entry.getValue() instanceof List) {

                if (LEVEL_BUSINESS_IDS.contains(entryKey)) {
                    if (StringUtils.isNotBlank(objStr) && objStr.contains("level")) {
                        List<FvChannelReqDTO> fvChannelReqDTO = JacksonUtils.parseArray(objStr, FvChannelReqDTO.class);
                        List<Object> channelIds = fvChannelReqDTO.stream().map(e -> Integer.valueOf(e.getId())).collect(Collectors.toList());
                        finalMap.put(entryKey, channelIds);
                        continue;
                    }
                }

                if (BUSINESS_OBJECTS_NAME.equals(entryKey)) {
                    List<BusinessObjectDTO> dtoList = JacksonUtils.parseArray(objStr, BusinessObjectDTO.class);
                    if (CollectionUtils.isEmpty(dtoList)) {
                        continue;
                    }
                    List<Object> businessNameList = new ArrayList<>();
                    List<Object> endKeys = new ArrayList<>();
                    for (BusinessObjectDTO dto : dtoList) {
                        businessNameList.add(dto.getBizName());
                        if (CollectionUtils.isEmpty(dto.getEndKeys())) {
                            continue;
                        }
                        for (String endKey : dto.getEndKeys()) {
                            String endKeyValue = Constants.getBizEndKey(dto.getBizName(), endKey);
                            endKeys.add(endKeyValue);
                        }
                    }
                    finalMap.put("businessName", businessNameList);
                    finalMap.put("endKeys", endKeys);
                    continue;
                }

                if ("vipCardType".equals(entryKey)) {
                    List<String> objList = JacksonUtils.parseArray(objStr, String.class);
                    if (CollectionUtils.isEmpty(objList)) {
                        continue;
                    }
                    List<Object> newCodeList = new ArrayList<>();
                    for (String obj : objList) {
                        Arrays.stream(VipCardTypeEnum.values()).filter(e -> String.valueOf(e.getVipCardType()).equals(obj))
                            .findFirst().ifPresent(x -> newCodeList.add(x.getValue()));
                    }
                    if (CollectionUtils.isNotEmpty(newCodeList)) {
                        finalMap.put(entryKey, newCodeList);
                        continue;
                    }
                }

                if ("priceInsurances".equals(entryKey)) {
                    List<String> objList = JacksonUtils.parseArray(objStr, String.class);
                    if (CollectionUtils.isEmpty(objList)) {
                        continue;
                    }
                    List<Object> newCodeList = new ArrayList<>();
                    for (String obj : objList) {
                        Arrays.stream(PriceInsurancePeriodEnum.values()).filter(e -> String.valueOf(e.getId()).equals(obj))
                            .findFirst().ifPresent(x -> newCodeList.add(x.getDesc()));
                    }
                    if (CollectionUtils.isNotEmpty(newCodeList)) {
                        finalMap.put(entryKey, newCodeList);
                        continue;
                    }
                }

                if ("signTypes".equals(entryKey)) {
                    List<String> objList = JacksonUtils.parseArray(objStr, String.class);
                    if (CollectionUtils.isEmpty(objList)) {
                        continue;
                    }
                    List<Object> newCodeList = new ArrayList<>();
                    for (String obj : objList) {
                        Arrays.stream(SignTypeEnum.values()).filter(e -> String.valueOf(e.getId()).equals(obj))
                            .findFirst().ifPresent(x -> newCodeList.add(x.getDesc()));
                    }
                    if (CollectionUtils.isNotEmpty(newCodeList)) {
                        finalMap.put(entryKey, newCodeList);
                        continue;
                    }
                }

                if ("agreementTypes".equals(entryKey)) {
                    List<String> objList = JacksonUtils.parseArray(objStr, String.class);
                    if (CollectionUtils.isEmpty(objList)) {
                        continue;
                    }
                    List<Object> newCodeList = new ArrayList<>();
                    for (String obj : objList) {
                        Arrays.stream(AgreementTypeEnum.values()).filter(e -> String.valueOf(e.getId()).equals(obj))
                            .findFirst().ifPresent(x -> newCodeList.add(x.getDesc()));
                    }
                    if (CollectionUtils.isNotEmpty(newCodeList)) {
                        finalMap.put(entryKey, newCodeList);
                        continue;
                    }
                }

                finalMap.put(entryKey, (List) entry.getValue());
            } else {
                List<Object> singleValueList = new ArrayList<>();
                singleValueList.add(entryValue);
                finalMap.put(entryKey, singleValueList);
            }
        }

        List<Object> teamIds = finalMap.get("teamIds");
        if (CollectionUtils.isNotEmpty(teamIds)) {
             teamIds = teamIds.stream().map(e -> Integer.valueOf(String.valueOf(e))).collect(Collectors.toList());
            finalMap.put("teamIds", teamIds);
        }
        return finalMap;
    }


    private List<CodeDescPair> buildPayTypePairs(List<Integer> payTypes) {
        if (CollectionUtils.isEmpty(payTypes)) {
            return Collections.emptyList();
        }
        Map<Long, PaymentType> payTypeMap = paymentTypeRepository.selectAll()
            .stream()
            .collect(Collectors.toMap(PaymentType::getId, Function.identity(), (a, b) -> b));
        return payTypes.stream()
            .map(p -> payTypeMap.get(p.longValue()))
            .filter(Objects::nonNull)
            .map(paymentType -> new CodeDescPair(paymentType.getId().intValue(), String.format("%s_%s", paymentType.getName(), paymentType.getId())))
            .collect(Collectors.toList());
    }


    private AnalysisTargetVO getAnalysisTargetVO(String targetCode) {
        AnalysisTarget target = targetRepository.selectByCode(targetCode);
        if (target == null) {
            return null;
        }
        AnalysisTargetVO targetVO = new AnalysisTargetVO();
        BeanUtils.copyProperties(target, targetVO);
        return targetVO;
    }

    private AnalysisDimensionVO getAnalysisDimensionVO(String dimensionCode, Integer businessTypeId, Integer themeType) {
        AnalysisDimension dimension = dimensionRepository.selectByThemeType(dimensionCode, businessTypeId, themeType);
        if (dimension == null) {
            return null;
        }
        AnalysisDimensionVO dimensionVO = new AnalysisDimensionVO();
        BeanUtils.copyProperties(dimension, dimensionVO);
        return dimensionVO;
    }

}
