package com.iqiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.entity.AlbumMetaDataDO;
import com.iqiyi.vip.domain.repository.AlbumMetaDataRepository;
import com.iqiyi.vip.dto.base.ListResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO;
import com.iqiyi.vip.dto.remain.AlbumMetaDataVO;
import com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO;

/**
 * <AUTHOR>
 * @className AlbumMetaDataService
 * @description
 * @date 2024/9/27
 **/
@Component
@Slf4j
public class AlbumMetaDataService {

    @Resource
    private AlbumMetaDataRepository albumMetaDataRepository;

    public PageListResult<AlbumMetaDataVO> pageList(AlbumMetaDataPageQueryDTO query) {
        Integer count = albumMetaDataRepository.selectCount(query);
        List<AlbumMetaDataVO> dataList = null;
        if (count > 0) {
            dataList = albumMetaDataRepository.getPageQueryResult(query).stream().filter(Objects::nonNull).map(this::convertToVO).collect(Collectors.toList());
        }
        return PageListResult.createSuccess(dataList, query.getPageNo(), query.getPageSize(), count);
    }
    
    public ListResult<AlbumMetaDataVO> getByRemainTime(String startDate, String endDate) {
        List<AlbumMetaDataDO> byRemainTime = albumMetaDataRepository.getByRemainTime(startDate, endDate);
        return ListResult.createSuccess(byRemainTime.stream().filter(Objects::nonNull).map(this::convertToVO).collect(Collectors.toList()));
    }

    public int updateMetaData(UpdateAlbumMetaDataDTO updateDTO) {
        return albumMetaDataRepository.updateMetaData(updateDTO);
    }

    private AlbumMetaDataVO convertToVO(AlbumMetaDataDO albummetadatado) {
        //转换逻辑，根据实际情况进行实现，不要返回null
        if (albummetadatado == null) {
            return null;
        }

        return AlbumMetaDataVO
            .builder()
            .id(albummetadatado.getId())
            .qipuAlbumId(albummetadatado.getQipuAlbumId())
            .tvName(albummetadatado.getTvName())
            .channelId(albummetadatado.getChannelId())
            .channelName(albummetadatado.getChannelName())
            .firstEpisodeOnlineTime(albummetadatado.getFirstEpisodeOnlineTime())
            .vipEndTime(albummetadatado.getVipEndTime())
            .commonEndTime(albummetadatado.getCommonEndTime())
            .resourceRating(albummetadatado.getResourceRating())
            .remainRelated(albummetadatado.getRemainRelated())
            .createTime(albummetadatado.getCreateTime().getTime())
            .updateTime(albummetadatado.getUpdateTime().getTime())
            .finished(albummetadatado.getFinished())
            .build();
    }
}
