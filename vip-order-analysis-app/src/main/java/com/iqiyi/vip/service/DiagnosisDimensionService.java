package com.iqiyi.vip.service;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.target.DiagnosisQueryDTO;
import com.iqiyi.vip.dto.target.DownloadDiagnosisDTO;

/**
 * <AUTHOR>
 * @className DiagnosisDimensionServiceImpl
 * @description
 * @date 2023/10/26
 **/
public interface DiagnosisDimensionService {

    HashMap<String, DimensionLayerNode> getDiagnosisDimensionTree(BaseQry baseQry);

    DataResult<DiagnosisResultDTO> diagnosis(DiagnosisQueryDTO baseQry);

    /**
     * 根据groupCode获取维度层级树第一个节点
     */
    DimensionLayerNode getFirstLayerByGroupCoe(String targetCode, String groupCode);

    void download(HttpServletResponse response, DownloadDiagnosisDTO downloadDiagnosisDTO);

    List<DimensionLayerNode> getNextDimensionLayersByGroupCodeAndDimensionCode(String groupCode, String dimensionCode, DimensionLayerNode layerNodeRoot);

    DimensionLayerNode getDimensionLayersByGroupCodeAndDimensionCode(String groupCode, String dimensionCode, DimensionLayerNode layerNodeRoot);

}
