package com.iqiyi.vip.service;


import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.iqiyi.vip.constant.RemainConstants;
import com.iqiyi.vip.dto.remain.ConditionCombinationVO;
import com.iqiyi.vip.dto.remain.RemainConditionGroupVO;
import com.iqiyi.vip.dto.remain.RemainData;
import com.iqiyi.vip.dto.remain.RemainDataReqDTO;
import com.iqiyi.vip.dto.remain.RemainDataVO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.DimensionCombinationTypeEnum;
import com.iqiyi.vip.enums.RemainDataTypeEnum;
import com.iqiyi.vip.task.AnalysisTask;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.ConditionParamUtils;
import com.iqiyi.vip.utils.DateUtils;
import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.iqiyi.vip.constant.RemainConstants.CARD_TYPE;
import static com.iqiyi.vip.constant.RemainConstants.PRESENT_TYPE;
import static com.iqiyi.vip.constant.RemainConstants.REMAIN_ALL;
import static com.iqiyi.vip.constant.RemainConstants.RENEW_FLAG;
import static com.iqiyi.vip.constant.RemainConstants.SEX_AND_AGE;
import static com.iqiyi.vip.constant.RemainConstants.UID_LAYER;
import static com.iqiyi.vip.constant.RemainConstants.VIP_GROUP;
import static com.iqiyi.vip.constant.RemainConstants.VIP_TYPE;

/**
 * <AUTHOR>
 * @className RemainService
 * @description
 * @date 2024/6/25
 **/
@Service
public class RemainService {

    public static final String REMAIN_DATA_TYPE = "remainDataType";

    @Autowired
    private AnalysisTask analysisTask;

    public List<RemainConditionGroupVO> getRemainConditionGroupInfo() {
        return RemainConstants.initializeRemainConditionGroupList();
    }

    public List<ConditionCombinationVO> getConditionCombinationList() {
        ArrayList<ConditionCombinationVO> list = new ArrayList<>();
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(REMAIN_ALL))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(VIP_GROUP))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(VIP_TYPE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(PRESENT_TYPE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(CARD_TYPE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(RENEW_FLAG))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(UID_LAYER))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.SINGLE.getType())
            .combination(Lists.newArrayList(SEX_AND_AGE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, CARD_TYPE, RENEW_FLAG))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, UID_LAYER, SEX_AND_AGE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, CARD_TYPE))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, RENEW_FLAG))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, UID_LAYER))
            .build());
        list.add(ConditionCombinationVO.builder()
            .type(DimensionCombinationTypeEnum.MULTI.getType())
            .combination(Lists.newArrayList(VIP_GROUP, VIP_TYPE, PRESENT_TYPE, SEX_AND_AGE))
            .build());
        return list;
    }

    public List<RemainDataVO> getRemainData(RemainDataReqDTO reqDTO) throws ExecutionException, InterruptedException {
        validateParam(reqDTO);
        List<TargetAnalysisQueryDTO> analysisQueryDTOS = constructRemainDataQueryDTO(reqDTO);
        List<OriginalQueryResultDTO> queryResultDTOS = analysisTask.executeQuery(analysisQueryDTOS, null);
        if (remainDataByDimension(reqDTO)) {
            return constructRemainData(reqDTO, queryResultDTOS);
        }
        return constructOverviewData(reqDTO, queryResultDTOS);
    }


    public List<RemainDataVO> constructOverviewData(RemainDataReqDTO reqDTO, List<OriginalQueryResultDTO> queryResultDTOS) {
        ArrayList<RemainDataVO> result = new ArrayList<>(queryResultDTOS.size());
        for (OriginalQueryResultDTO resultDTO : queryResultDTOS) {
            String targetCode = resultDTO.getTargetCode();
            RemainDataTypeEnum remainDataTypeEnum = RemainDataTypeEnum.getByCode(Integer.parseInt(targetCode));
            if (remainDataTypeEnum == null) {
                continue;
            }
            List<RemainData> remainDataList = resultDTO.getDataList()
                .stream()
                .map(map -> {
                    RemainData remainData = new RemainData();
                    remainData.setDt((String) map.get("dt"));
                    remainData.setNum((Long) map.get("num"));
                    return remainData;
                })
                .collect(Collectors.toList());

            RemainDataVO remainDataVO = RemainDataVO.builder()
                .name(remainDataTypeEnum.getDesc())
                .data(remainDataList)
                .build();
            result.add(remainDataVO);
        }
        addMissingData(reqDTO, result);
        return result;
    }


    public List<RemainDataVO> constructRemainData(RemainDataReqDTO reqDTO, List<OriginalQueryResultDTO> queryResultDTOS) {
        //1、每个map都有dimensionCode、num、dt，根据dimensionCode分组，根据dt排序, 获得List<RemainDataVO>
        // 分组并排序
        String dimensionCode = reqDTO.getDimensionCode();
        Map<String, List<HashMap<String, Object>>> groupedSortedData = queryResultDTOS.get(0).getDataList().stream()
            .collect(Collectors.groupingBy(map -> (String) map.get(dimensionCode),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    ArrayList::new
                )
            ));
        ArrayList<RemainDataVO> result = new ArrayList<>(groupedSortedData.size());
        for (Entry<String, List<HashMap<String, Object>>> entry : groupedSortedData.entrySet()) {
            String dimensionName = entry.getKey().replace("[到期大于365天]后续费", "");
            if (needIgnoreValue(dimensionName)) {
                continue;
            }
            RemainDataVO remainDataVO = RemainDataVO.builder()
                .name(dimensionName)
                .data(entry.getValue()
                    .stream()
                    .map(map -> RemainData.builder().dt((String) map.get("dt")).num((Long) map.get("num")).build())
                    .collect(Collectors.toList()))
                .build();
            result.add(remainDataVO);
        }
        List<String> dimensionValue = RemainConstants.initializeRemainConditionGroupList()
            .stream()
            .filter(g -> g.getCode().equals(dimensionCode))
            .findFirst()
            .map(RemainConditionGroupVO::getGroupValue)
            .orElse(new ArrayList<String>());
        if (CollectionUtils.isEmpty(dimensionValue)) {
            return result;
        }
        addMissingData(reqDTO, result);
        return result.stream().sorted(Comparator.comparing(r -> dimensionValue.indexOf(r.getName()))).collect(Collectors.toList());
    }

    private void addMissingData(RemainDataReqDTO reqDTO, List<RemainDataVO> result) {
        String startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(reqDTO.getPayStartTime()), ZoneId.systemDefault())
            .format(DateTimeFormatter.ISO_LOCAL_DATE);
        String endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(reqDTO.getPayEndTime()), ZoneId.systemDefault())
            .format(DateTimeFormatter.ISO_LOCAL_DATE);
        List<String> dateStrBetweenDate = DateUtils.getDateStrBetweenDate(startTime, endTime, NORM_DATE_PATTERN, Calendar.DATE);
        for (RemainDataVO remainDataVO : result) {
            // 将 `RemainData` 列表转换为以 `dt` 为键的映射
            Map<String, RemainData> remainDataMap = remainDataVO.getData().stream()
                .collect(Collectors.toMap(RemainData::getDt, Function.identity(), (e1, e2) -> e1, LinkedHashMap::new));

            List<RemainData> sortedRemainData = dateStrBetweenDate.stream()
                .map(dateStr -> remainDataMap.getOrDefault(dateStr, new RemainData(dateStr, 0L)))
                .collect(Collectors.toList());
            remainDataVO.setData(sortedRemainData);  // 直接修改传入的 `result` 列表中的数据
        }
    }


    private boolean needIgnoreValue(String dimensionName) {
        return Lists.newArrayList("TV商用会员", "爱奇艺商用会员").contains(dimensionName);
    }


    private void validateParam(RemainDataReqDTO reqDTO) {
        boolean valid = ownRemainBoardPermission(reqDTO.getOperator());
        if (!valid) {
            throw new RuntimeException(CodeEnum.BASE_INIT_ERROR.getMessage());
        }
    }

    private List<TargetAnalysisQueryDTO> constructRemainDataQueryDTO(RemainDataReqDTO reqDTO) {
        Properties properties = new Properties();
        properties.setProperty("payStartDate", DateUtils.dateDatetimeFormat(reqDTO.getPayStartTime()));
        properties.setProperty("payEndDate", DateUtils.dateDatetimeFormat(reqDTO.getPayEndTime()));
        properties.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());
        boolean remainDataByDimension = remainDataByDimension(reqDTO);
        ArrayList<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String dimensionCode = reqDTO.getDimensionCode();
        if (!remainDataByDimension) {
            RemainDataTypeEnum[] values = RemainDataTypeEnum.values();
            for (RemainDataTypeEnum typeEnum : values) {
                reqDTO.setRemainDataType(typeEnum.getCode());
                constructRemainCondition(reqDTO, properties);
                constructRemainDimension(reqDTO, properties);
                queryDTOS.add(TargetAnalysisQueryDTO.builder()
                    .targetCode(String.valueOf(typeEnum.getCode()))
                    .targetName(typeEnum.getDesc())
                    .dataBase(properties.getProperty("dateBase"))
                    .querySql(analysisTask.parseSqlByProperties(properties, 900L))
                    .operator(reqDTO.getOperator())
                    .env(System.getProperty("spring.profiles.active")).build());
            }
            return queryDTOS;
        }
        constructRemainCondition(reqDTO, properties);
        constructRemainDimension(reqDTO, properties);
        queryDTOS.add(TargetAnalysisQueryDTO.builder()
            .targetCode(dimensionCode)
            .targetName(dimensionCode)
            .dimensionCode(dimensionCode)
            .dataBase(properties.getProperty("dateBase"))
            .querySql(analysisTask.parseSqlByProperties(properties, 900L))
            .operator(reqDTO.getOperator())
            .env(System.getProperty("spring.profiles.active")).build());
        return queryDTOS;
    }

    private boolean isRemainAll(String remainAll) {
        return StringUtils.isNotBlank(remainAll);
    }

    private void constructRemainCondition(RemainDataReqDTO reqDTO, Properties properties) {
        Map<String, List<Object>> conditionParamMap = reqDTO.getConditionParamMap();
        ArrayList<String> conditionSql = new ArrayList<>();
        Integer remainDataType = reqDTO.getRemainDataType();
        if (remainDataType != null) {
            conditionSql.add(RemainConstants.REMAIN_DATA_TYPE_COLUMN + " in (" + remainDataType + ")");
        }
        processCommonConditionParam(conditionParamMap, conditionSql);
        properties.setProperty("baseQueryCondition", String.join(" and ", conditionSql));
    }

    private void processCommonConditionParam(Map<String, List<Object>> conditionParamMap, ArrayList<String> conditionSql) {
        if (MapUtils.isEmpty(conditionParamMap)) {
            return;
        }
        for (Entry<String, List<Object>> entry : conditionParamMap.entrySet()) {
            String key = entry.getKey();
            List<Object> value = entry.getValue();
            if (StringUtils.isBlank(key) || CollectionUtils.isEmpty(value)) {
                continue;
            }
            if (needIgnore(key)) {
                continue;
            }

            List<String> listValue = ConditionParamUtils.getListValue(conditionParamMap, key, String.class);
            if (CollectionUtils.isEmpty(listValue)) {
                continue;
            }
            listValue = listValue.stream().map(String::trim).map(v -> v.replace("召回", "召回[到期大于365天]后续费")).collect(Collectors.toList());
            conditionSql.add(constructConditionSql(key, listValue));
        }
    }

    private void constructRemainDimension(RemainDataReqDTO reqDTO, Properties properties) {
        ArrayList<String> dimensionSql = Lists.newArrayList("dt");
        if (StringUtils.isNotBlank(reqDTO.getDimensionCode())) {
            dimensionSql.add(reqDTO.getDimensionCode());
        }
        properties.setProperty("dimensionCodes", String.join(",", dimensionSql));
    }

    private String constructConditionSql(String key, List<String> listValue) {
        return key + " in (" + listValue.stream().map(s -> "'" + s + "'").collect(Collectors.joining(",")) + ")";
    }

    private boolean needIgnore(String key) {
        return Lists.newArrayList("remainAll", "payStartTime", "payEndTime", "operator", "remainDataType").contains(key);
    }

    private boolean remainDataByDimension(RemainDataReqDTO reqDTO) {
        Integer remainDataType = reqDTO.getRemainDataType();
        String dimensionCode = reqDTO.getDimensionCode();
        return remainDataType != null && StringUtils.isNotBlank(dimensionCode);
    }

    public boolean ownRemainBoardPermission(String operator) {
        return CloudConfigUtils.ownRemainBoardAccess(operator);
    }

}
