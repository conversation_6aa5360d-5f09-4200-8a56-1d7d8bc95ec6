package com.iqiyi.vip.mail;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.uitls.mail.MailHelper;
import com.iqiyi.vip.uitls.model.MailHeader;
import com.iqiyi.vip.uitls.model.TableMailContent;

/**
 * @className MailManager
 * @description
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Component
@Slf4j
public class MailManager {

    @Resource
    private MailHelper mailHelper;

    public void sendMailWithTable(String mailTo, String title, List<TableMailContent> tableMailContentList) throws Exception {
        // 初始化mailHeader
        MailHeader header = new MailHeader();
        header.setTos(mailTo.split(","));
        header.setTitle(title);
        header.setNeedTitlePrefix(false);
        mailHelper.sendMail(header, tableMailContentList);
    }


    public void sendFailMail(String operator, String message) {
        MailHeader header = new MailHeader();
        String mailAddress = getSendResultMailAddress(operator);
        header.setTos(new String[]{mailAddress});
        header.setNeedTitlePrefix(false);
        header.setTitle("【天眼】查询失败通知");
        try {
            mailHelper.sendMail(header, message);
        } catch (Exception e) {
            log.error("sendFailMail fail! mailAddress:{}, message:{}", mailAddress, message);
        }
    }

    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public void sendExcelResultsMail(List<String> excelFileNames, String operator, String title, String content) throws Exception {
        MailHeader header = new MailHeader();
        if (CollectionUtils.isNotEmpty(excelFileNames)) {
            header.setAttachments(ArrayUtil.addAll(excelFileNames.toArray(new String[]{})));
        }
        String mailAddress = getSendResultMailAddress(operator);
        header.setTos(new String[]{getSendResultMailAddress(operator)});
        header.setTitle(title);
        header.setNeedTitlePrefix(false);
        try {
            mailHelper.sendMail(header, content);
        } catch (Exception e) {
            log.error("sendExcelResultsMail fail! excelFileNames:{}, mailAddress:{}, error:", excelFileNames, mailAddress, e);
            throw e;
        }
    }

    /**
     * Oa 审批通过，消息通知给负责人，去devops给用户配置权限
     * @param account
     * @param oaApprovedNoticeTos
     */
    public void oaApprovedNotice(String account, String oaApprovedNoticeTos) {
        MailHeader header = new MailHeader();
        String content = "数据权限申请已审批通过，请给用户：".concat(account).concat("添加devops项目（vip-order-analysis）普通用户权限");
        header.setTos(Constants.DATA_PERMISSION_COMMON_PATTERN.split(oaApprovedNoticeTos));
        header.setTitle("【天眼】".concat(content));
        header.setNeedTitlePrefix(false);
        try {
            mailHelper.sendMail(header, content);
        }catch (Exception e) {
            log.error("oaApprovedNotice fail! content:{}, tos:{}", content, Arrays.toString(header.getTos()));
        }
    }

    public String getSendResultMailAddress(String operator) {
        return operator + "@qiyi.com";
    }

}
