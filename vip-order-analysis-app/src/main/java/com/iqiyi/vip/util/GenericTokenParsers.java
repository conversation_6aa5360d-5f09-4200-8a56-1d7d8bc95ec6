package com.iqiyi.vip.util;

import org.apache.ibatis.parsing.GenericTokenParser;

import java.util.Properties;

import com.iqiyi.vip.handler.VariableTokenNewHandler;

/**
 * <AUTHOR>
 * @date 2022/10/19 21:01
 */
public class GenericTokenParsers {

    public static String parserSql(Properties properties, String sqlTemplate) {
        VariableTokenNewHandler handler = new VariableTokenNewHandler(properties);
        GenericTokenParser parser = new GenericTokenParser("${", "}", handler);
        return parser.parse(sqlTemplate); // 解析指标对应模板，解析@标识符替换和${}标识符已存在的变量
    }

    private GenericTokenParsers() {
        throw new RuntimeException();
    }

}
