package com.iqiyi.vip.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.dto.competitor.UserStatusType.ProductInfo.SaleInfo;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;

/**
 * 竞品价格工具类
 * <AUTHOR>
 * @date 2024/10/30
 */
public class CompetitorPriceUtil {

    /**
     * 从JSON字符串中获取会员产品信息
     * @param jsonConfig JSON格式的爱奇艺配置字符串
     * @param memberType 会员类型
     * @param userStatus 用户状态
     * @return 产品信息映射
     */
    public static Map<String, SaleInfo> getMemberProductInfo(String jsonConfig, String memberType, String userStatus) {
        if (StringUtils.isBlank(jsonConfig)) {
            return null;
        }
        
        try {
            // 解析JSON字符串为JSONObject
            JSONObject configObj = JSON.parseObject(jsonConfig);
            if (configObj == null || !configObj.containsKey(memberType)) {
                return null;
            }
            
            // 获取会员类型配置
            JSONObject memberConfig = configObj.getJSONObject(memberType);
            if (memberConfig == null || !memberConfig.containsKey(userStatus)) {
                return null;
            }
            
            // 获取用户状态配置
            JSONObject userStatusConfig = memberConfig.getJSONObject(userStatus);
            if (userStatusConfig == null || userStatusConfig.isEmpty()) {
                return null;
            }
            
            // 将产品配置转换为SaleInfo
            Map<String, SaleInfo> result = new HashMap<>();
            for (String productKey : userStatusConfig.keySet()) {
                JSONObject productValue = userStatusConfig.getJSONObject(productKey);
                
                if (productValue != null && productValue.containsKey("price")) {
                    SaleInfo saleInfo = new SaleInfo();
                    saleInfo.setPrice(String.valueOf(productValue.getString("price")));
                    if (productValue.containsKey("product")) {
                        saleInfo.setProduct(productValue.getString("product"));
                    }
                    result.put(productKey, saleInfo);
                }
            }
            
            return result;
        } catch (Exception e) {
            // 解析异常时返回null
            return null;
        }
    }
} 