package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;

import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className AvailableDateValidator
 * @description
 * @date 2024/2/26
 **/
@Component
@Slf4j
public class AvailableDateValidator implements Validator {

    @Resource
    private AvailableDateRepository availableDateRepository;

    @Value("${order.count.fee.querySql}")
    private String orderCountAndSumFeeSql;

    @Value("${order.count.fee.querySql.ck}")
    private String cKOrderCountAndSumFeeSql;

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit = 10;


    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -1)));
        AvailableDt dateByDt = availableDateRepository.getDateByDt(dt, ThemeTypeEnum.ORDER_THEME.getCode());
        if (dateByDt == null) {
            log.info("not find AvailableDt by dt:{}", dt);
            return;
        }
        Integer status = dateByDt.getStatus();
        if (status != null && status == 1) {
            log.info("status already set to 1, dt:{}", dt);
            return;
        }
        String targetCode = "CK_ORDER_COUNT_FEE_CHECK";
        TargetAnalysisQueryDTO biTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(orderCountAndSumFeeSql, BI_TABLE_NAME, dt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .dataBase(DataBaseTypeEnum.HIVE.getCode())
            .build();

        TargetAnalysisQueryDTO cKOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(cKOrderCountAndSumFeeSql, CK_TABLE_NAME))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .dataBase(DataBaseTypeEnum.CLICK_HOUSE.getCode())
            .build();
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        queryDTOS.add(biTableOrderCheck);
        queryDTOS.add(cKOrderCheck);

        try {
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
            if (CollectionUtils.isEmpty(resultDTOS) || resultDTOS.size() != 2) {
                return;
            }
            OriginalQueryResultDTO biTableQueryResultDTO = resultDTOS.get(0);
            Map<String, Object> biOrderResult = biTableQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            OriginalQueryResultDTO cKQueryResultDTO = resultDTOS.get(1);
            Map<String, Object> cKResult = cKQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            if (MapUtils.isEmpty(biOrderResult) || MapUtils.isEmpty(cKResult)) {
                return;
            }
            Long biOrderCount = MapUtils.getLong(biOrderResult, ORDER_COUNT, 0L);
            Long biOrderSumFee = MapUtils.getLong(biOrderResult, SUM_FEE, 0L);
            Long cKOrderCount = MapUtils.getLong(cKResult, ORDER_COUNT, 0L);
            Long cKOrderSumFee = MapUtils.getLong(cKResult, SUM_FEE, 0L);
            Boolean orderCountResult = Math.abs(cKOrderCount - biOrderCount) <= orderCountGapLimit ? Boolean.TRUE : Boolean.FALSE;
            Boolean orderFeeResult = Math.abs(cKOrderSumFee - biOrderSumFee) <= orderCountGapLimit * 2500 ? Boolean.TRUE : Boolean.FALSE;
            boolean same = orderCountResult && orderFeeResult;
            if (same) {
                availableDateRepository.updateStatusByDt(dt, ThemeTypeEnum.ORDER_THEME.getCode(), 1);
            }
            log.info("AvailableDateValidator end at:{}, dt:{}, same:{}, biOrderCount：{}, biOrderSumFee:{}, cKOrderCount:{}, cKOrderSumFee:{}", DateUtil.now(), dt, same, biOrderCount, biOrderSumFee, cKOrderCount, cKOrderSumFee);
        } catch (Exception e) {
            log.error("AvailableDateValidator error at: {}, dt :{} , e:", DateUtil.now(), dt, e);
            return;
        }
    }

}

