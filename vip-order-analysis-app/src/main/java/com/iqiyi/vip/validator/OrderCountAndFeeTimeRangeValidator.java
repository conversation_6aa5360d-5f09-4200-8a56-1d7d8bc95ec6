package com.iqiyi.vip.validator;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;
import com.iqiyi.vip.utils.DateUtils;
import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className OrderCountAndFeeTimeRangeValidator
 * @description
 * @date 2022/10/28
 **/
@Component
@Slf4j
public class OrderCountAndFeeTimeRangeValidator implements Validator {

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit = 10;

    @Value("${order.count.fee.querySql.with.range.hive}")
    private String orderCountAndSumFeeTimeRangeHiveSql;

    @Value("${order.count.fee.querySql.with.range.ck}")
    private String orderCountAndSumFeeTimeRangeCKSql;


    @Autowired
    private CommonAnalysisTask commonAnalysisTask;

    @Override
    public void validate(Object... args) {
        int hour = DateUtil.hour(DateUtil.date(), Boolean.TRUE);
        if (hour <= 8) {
            log.error("no need check clickhouse");
            return;
        }
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        String startDate = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -100)));
        String endDate = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -10)));

        String month_dts = DateUtils.getDateStrBetweenDate(startDate, endDate, DatePattern.NORM_MONTH_PATTERN, Calendar.MONTH).stream()
            .map(m -> String.format("'%s'", m))
            .collect(Collectors.joining(","));

        log.info("OrderCountAndFeeTimeRangeValidator start at:{}, dt:{}, taskId:{}", DateUtil.today(), dt, taskId);
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "ORDER_COUNT_FEE_CHECK_TIME_RANGE";
        TargetAnalysisQueryDTO hiveTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(orderCountAndSumFeeTimeRangeHiveSql, WIDE_TABLE_NAME, dt, startDate, endDate))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .dataBase(DataBaseTypeEnum.HIVE.getCode())
            .build();
        queryDTOS.add(hiveTableOrderCheck);

        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(orderCountAndSumFeeTimeRangeCKSql, "dist_".concat(WIDE_TABLE_NAME), month_dts, startDate, endDate))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .dataBase(DataBaseTypeEnum.CLICK_HOUSE.getCode())
            .build();
        queryDTOS.add(wideTableOrderCheck);

        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        try {
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
            if (CollectionUtils.isEmpty(resultDTOS) || resultDTOS.size() != 2) {
                checkResultMap.put(DataCheckEnum.ORDER_COUNT_CILCK_HOUSE.getId(), Boolean.FALSE);
                checkResultMap.put(DataCheckEnum.SUM_Fee_CILCK_HOUSE.getId(), Boolean.FALSE);
                return;
            }
            OriginalQueryResultDTO biTableQueryResultDTO = resultDTOS.get(0);
            Map<String, Object> biOrderResult = biTableQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            OriginalQueryResultDTO wideTableQueryResultDTO = resultDTOS.get(1);
            Map<String, Object> wideTableResult = wideTableQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            if (MapUtils.isEmpty(biOrderResult) || MapUtils.isEmpty(wideTableResult)) {
                checkResultMap.put(DataCheckEnum.ORDER_COUNT_CILCK_HOUSE.getId(), Boolean.FALSE);
                checkResultMap.put(DataCheckEnum.SUM_Fee_CILCK_HOUSE.getId(), Boolean.FALSE);
                return;
            }
            Long hiveOrderCount = MapUtils.getLong(biOrderResult, ORDER_COUNT, 0L);
            Long hiveOrderSumFee = MapUtils.getLong(biOrderResult, SUM_FEE, 0L);
            Long ckOrderCount = MapUtils.getLong(wideTableResult, ORDER_COUNT, 0L);
            Long ckOrderSumFee = MapUtils.getLong(wideTableResult, SUM_FEE, 0L);
            log.info("OrderCountAndFeeTimeRangeValidator end at: {}, dt :{} , hiveOrderCount：{}, hiveOrderSumFee:{}, ckOrderCount:{}, ckOrderSumFee:{}", DateUtil.now(), dt, hiveOrderCount, hiveOrderSumFee, ckOrderCount, ckOrderSumFee);
            checkResultMap.put(DataCheckEnum.ORDER_COUNT_CILCK_HOUSE.getId(),
                Math.abs(ckOrderCount - hiveOrderCount) <= orderCountGapLimit ? Boolean.TRUE : Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.SUM_Fee_CILCK_HOUSE.getId(),
                Math.abs(ckOrderSumFee - hiveOrderSumFee) <= orderCountGapLimit * 2200 ? Boolean.TRUE : Boolean.FALSE);
        } catch (Exception e) {
            log.error("OrderCountAndFeeTimeRangeValidator error :", e);
            checkResultMap.put(DataCheckEnum.ORDER_COUNT_CILCK_HOUSE.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.SUM_Fee_CILCK_HOUSE.getId(), Boolean.FALSE);
        }
    }
}
