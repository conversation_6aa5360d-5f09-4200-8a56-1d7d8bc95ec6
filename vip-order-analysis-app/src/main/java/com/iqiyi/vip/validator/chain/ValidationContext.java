package com.iqiyi.vip.validator.chain;

import lombok.Builder;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 校验上下文
 *
 * <AUTHOR>
 * @date 2024/12/14
 */
@Data
@Builder
public class ValidationContext {
    
    /**
     * 校验结果映射
     */
    private HashMap<Integer, Boolean> resultMap;

    /**
     * 日期
     */
    private String dt;

    /**
     * 任务ID
     */
    private long taskId;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 订单数量差异限制
     */
    private Integer orderCountGapLimit;

    /**
     * 订单数量和金额查询SQL
     */
    private String orderCountAndSumFeeSql;

    /**
     * 查询结果缓存，避免重复查询相同的表
     * key: 表名, value: 查询结果 Map<String, Object>
     */
    @Builder.Default
    private Map<String, Map<String, Object>> queryResultCache = new ConcurrentHashMap<>();
}
