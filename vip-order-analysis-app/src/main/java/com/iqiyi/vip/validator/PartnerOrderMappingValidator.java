package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;

import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className PartnerOrderMappingValidator
 * @description
 * @date 2022/10/28
 **/
@Component
@Slf4j
public class PartnerOrderMappingValidator implements Validator {

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit = 10;

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Value("${partner.order.mapping.querySql}")
    private String partnerOrderMappingQuerySql;


    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("PartnerOrderMappingValidator start at:{}, dt:{}, taskId:{}", DateUtil.today(), dt, taskId);
        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        log.info("PartnerOrderMappingValidator partner mapping error count:{}", 0);
        checkResultMap.put(DataCheckEnum.PARTNER_ORDER_MAPPING.getId(), Boolean.TRUE);
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = DataCheckEnum.PARTNER_ORDER_MAPPING.getDesc();
        int caseId = DataCheckEnum.PARTNER_ORDER_MAPPING.getId();
        TargetAnalysisQueryDTO fvMappingCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(partnerOrderMappingQuerySql, dt, dt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(fvMappingCheck);
        List<OriginalQueryResultDTO> resultDTOS = new ArrayList<>();
        try {
            resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        } catch (Exception e) {
            log.error("PartnerOrderMappingValidator error:", e);
            checkResultMap.put(caseId, Boolean.FALSE);
            return;
        }

        if (CollectionUtils.isEmpty(resultDTOS) || MapUtils.isEmpty(resultDTOS.get(0).getDataList().get(0))) {
            checkResultMap.put(caseId, Boolean.FALSE);
            return;
        }
        Integer count = MapUtils.getInteger(resultDTOS.get(0).getDataList().get(0), ORDER_COUNT, 0);
        checkResultMap.put(caseId, count > 0 ? Boolean.FALSE : Boolean.TRUE);
    }
}
