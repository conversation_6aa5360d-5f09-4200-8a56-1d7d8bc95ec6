package com.iqiyi.vip.validator.chain;

import org.springframework.stereotype.Component;
import java.lang.annotation.*;

/**
 * 校验处理器注解
 * 用于自动发现和注册校验处理器，并定义执行顺序
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ValidationProcessor {
    
    /**
     * 处理器名称
     */
    String name() default "";
    
    /**
     * 执行顺序，数字越小越先执行
     */
    int order() default 100;
    
    /**
     * 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 处理器描述
     */
    String description() default "";
}
