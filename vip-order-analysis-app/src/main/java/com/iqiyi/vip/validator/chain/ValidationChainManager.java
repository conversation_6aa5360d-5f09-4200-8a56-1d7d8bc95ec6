package com.iqiyi.vip.validator.chain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 校验责任链管理器 - 自动发现和组织校验器
 *
 * <AUTHOR>
 * @date 2024/12/14
 */
@Component
@Slf4j
public class ValidationChainManager {

    /**
     * Spring自动注入所有ValidationHandler实现类
     */
    private final List<ValidationHandler> allHandlers;

    @Autowired
    public ValidationChainManager(List<ValidationHandler> allHandlers) {
        this.allHandlers = allHandlers;
        log.info("自动发现校验处理器数量: {}", allHandlers.size());
        allHandlers.forEach(handler -> log.info("发现处理器: {} - 顺序: {} - 启用: {}", handler.getName(), handler.getOrder(), handler.isEnabled()));
    }

    /**
     * 自动构建校验责任链
     * 根据处理器的order自动排序并构建链
     *
     * @return 责任链的第一个处理器
     */
    private ValidationHandler buildChain() {
        // 过滤启用的处理器并按order排序
        List<ValidationHandler> enabledHandlers = allHandlers.stream()
            .filter(ValidationHandler::isEnabled)
            .sorted(Comparator.comparingInt(ValidationHandler::getOrder))
            .collect(Collectors.toList());

        if (enabledHandlers.isEmpty()) {
            log.warn("没有找到启用的校验处理器");
            return null;
        }

        log.info("构建校验责任链，处理器执行顺序:");
        for (int i = 0; i < enabledHandlers.size(); i++) {
            ValidationHandler current = enabledHandlers.get(i);
            log.info("  {}. {} (order: {})", i + 1, current.getName(), current.getOrder());
            // 设置下一个处理器
            if (i < enabledHandlers.size() - 1) {
                current.setNext(enabledHandlers.get(i + 1));
            }
        }
        return enabledHandlers.get(0);
    }

    /**
     * 执行完整的校验链
     *
     * @param context 校验上下文
     */
    public void executeValidationChain(ValidationContext context) {
        log.info("开始执行校验责任链, taskId: {}, dt: {}", context.getTaskId(), context.getDt());
        ValidationHandler chain = buildChain();
        if (chain != null) {
            chain.handle(context);
        } else {
            log.warn("责任链为空，跳过校验");
        }
        log.info("完成执行校验责任链, taskId: {}, dt: {}", context.getTaskId(), context.getDt());
    }
}
