package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.entity.DataCheckResult;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.service.DataCheckResultService;
import com.iqiyi.vip.task.CommonAnalysisTask;
import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;


@Component
@Slf4j
public class LtvTargetsValidator implements Validator {

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${data.check.begin.dt:2021-06-01}")
    private String dataCheckBeginDt;

    @Value("${data.check.end.dt:2021-06-02}")
    private String dataCheckEndDt;

    @Value("${lt.order.targets.fv.query.sql}")
    private String ltDataCheckQuerySql;

    @Value("${ltv.result.diff.gap}")
    private long ltvResultGapLimit;

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Resource
    private DataCheckResultService dataCheckResultService;

    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("LTTargetsValidator start at:{}, dt:{}", DateUtil.today(), dt);
        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        try {
            checkLTWithWideTable(dt, dataCheckBeginDt, dataCheckEndDt, checkResultMap);
        } catch (Exception e) {
            log.error("LTTargetsValidator error:", e);
            checkResultMap.put(DataCheckEnum.ORDER_LTV_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
        }

    }


    private void checkLTWithWideTable(String dt, String dataCheckBeginDt, String dataCheckEndDt, HashMap<Integer, Boolean> checkResultMap)
        throws ExecutionException, InterruptedException {
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "WIDE_TABLE_ORDER_LT_TARGETS_DIFF";
        DateTime endDateTime = DateUtil.endOfDay(DateUtil.offsetMonth(DateUtil.parse(dataCheckEndDt, "yyyy-MM-dd"), 1));
        String endTimeStr = DateUtil.format(endDateTime, "yyyy-MM-dd HH:mm:ss");
        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(ltDataCheckQuerySql, endTimeStr, endTimeStr, dt, dataCheckBeginDt, dataCheckEndDt, dt, dataCheckBeginDt, dataCheckEndDt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(wideTableOrderCheck);
        List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        if (CollectionUtils.isEmpty(resultDTOS)) {
            checkResultMap.put(DataCheckEnum.ORDER_LTV_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            return;
        }

        Map<String, HashMap<String, Object>> wideTableLtResult = resultDTOS.get(0).getDataList()
            .stream()
            .collect(Collectors.toMap(it -> MapUtils.getString(it, DIMENSION), it -> it));

        String diffDt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -(offset + 1))));
        Map<String, Map<String, Object>> checkResultToDiff = getCheckResultToDiff(targetCode, diffDt, dataCheckBeginDt, dataCheckEndDt, endTimeStr);
        boolean ltvSame = true;
        for (String dimension : wideTableLtResult.keySet()) {
            HashMap<String, Object> wideTableResultWithDimension = wideTableLtResult.get(dimension);
            Map<String, Object> checkResultToDiffWithDimension = checkResultToDiff.get(dimension);
            long ltvResult = MapUtils.getLong(wideTableResultWithDimension, LTV, 0L);
            long ltvResultDiffDt = MapUtils.getLong(checkResultToDiffWithDimension, LTV, 0L);
            if (Math.abs(ltvResult - ltvResultDiffDt) > ltvResultGapLimit) {
                ltvSame = false;
                break;
            }
        }
        checkResultMap.put(DataCheckEnum.ORDER_LTV_VIPTYPE_FV_DIFF.getId(), ltvSame);
    }

    private Map<String, Map<String, Object>> getCheckResultToDiff(String targetCode, String diffDt, String dataCheckBeginDt, String dataCheckEndDt, String endTimeStr)
        throws ExecutionException, InterruptedException {
        DataCheckResult dataCheckResultDO = DataCheckResult.builder()
            .caseIds(Lists.newArrayList(DataCheckEnum.ORDER_LTV_VIPTYPE_FV_DIFF.getId()))
            .dt(diffDt)
            .beginTime(dataCheckBeginDt)
            .endTime(dataCheckEndDt)
            .build();
        List<DataCheckResult> dataCheckResult = dataCheckResultService.getDataCheckResult(dataCheckResultDO);
        if (CollectionUtils.isEmpty(dataCheckResult)) {
            log.info("not find ltv check results dt:{}, caseIds：{}", diffDt, dataCheckResultDO.getCaseIds());
            List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
            TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
                .targetCode(targetCode)
                .querySql(String.format(ltDataCheckQuerySql, endTimeStr, endTimeStr, diffDt, dataCheckBeginDt, dataCheckEndDt, diffDt, dataCheckBeginDt, dataCheckEndDt))
                .operator(DATA_CHECK_OPERATOR)
                .dateYM(diffDt)
                .taskId(taskId)
                .build();
            queryDTOS.add(wideTableOrderCheck);
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
            List<LinkedHashMap<String, Object>> dataList = resultDTOS.get(0).getDataList();
            dataList.forEach(m -> {
                String dimension = MapUtils.getString(m, DIMENSION, (String) null);
                Double ltvResult = MapUtils.getDouble(m, LTV);
                DataCheckResult orderCountCheckResult = DataCheckResult.buildFrom(DataCheckEnum.ORDER_LTV_VIPTYPE_FV_DIFF.getId(), LTV, ltvResult, diffDt, dataCheckBeginDt, dataCheckEndDt, dimension, "wideTable");
                dataCheckResultService.saveDataCheckResult(orderCountCheckResult);
            });
            log.info("base check results dt:{} saved, caseIds:{}", diffDt, dataCheckResultDO.getCaseIds());
            return dataList
                .stream()
                .collect(Collectors.toMap(it -> MapUtils.getString(it, DIMENSION), it -> it));
        }
        return dataCheckResult.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.groupingBy(DataCheckResult::getDimension, Collectors.toMap(DataCheckResult::getKey, DataCheckResult::getValue)));
    }
}
