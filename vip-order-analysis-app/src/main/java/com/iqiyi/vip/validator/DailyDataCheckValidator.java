package com.iqiyi.vip.validator;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @className DailyDataCheckValidator
 * @description
 * @date 2022/11/3
 **/
@Component
public class DailyDataCheckValidator implements Validator {

    private final ConcurrentMap<String, Validator> registry = new ConcurrentHashMap<>();

   @Resource
   private OrderCountAndFeeValidator orderCountAndFeeValidator;

   @Resource
   private OldFvMappingValidator oldFvMappingValidator;

   @Resource
   private FvInfoMappingValidator fvInfoMappingValidator;

   @Resource
   private BizNameAndPlatformValidator bizNameAndPlatformValidator;

   @Resource
   private PartnerOrderMappingValidator partnerOrderMappingValidator;

   @Resource
   private BaseOrderTargetsValidator baseOrderTargetsValidator;

   @Resource
   private LtvTargetsValidator ltvTargetsValidator;


    @PostConstruct
    public void init() {
        registerValidator(orderCountAndFeeValidator);
        registerValidator(oldFvMappingValidator);
        registerValidator(fvInfoMappingValidator);
        registerValidator(bizNameAndPlatformValidator);
        registerValidator(partnerOrderMappingValidator);
        registerValidator(baseOrderTargetsValidator);
        registerValidator(ltvTargetsValidator);
    }

    public void registerValidator(Validator validator) {
        if (validator != null) {
            registry.put(validator.getClass().getSimpleName(), validator);
        }
    }

    @Override
    public void validate(Object... args) {
        for (Validator validator : registry.values()) {
            validator.validate(args);
        }
    }
}
