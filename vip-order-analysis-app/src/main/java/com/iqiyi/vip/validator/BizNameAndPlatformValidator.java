package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;

import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className BizNameAndPlatformValidator
 * @description
 * @date 2022/11/7
 **/
@Component
@Slf4j
public class BizNameAndPlatformValidator implements Validator {
    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit;

    @Value("${bizname.mapping.query.sql}")
    private String bizNameMappingQuerySql;

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("BizNameAndPlatformValidator start at:{}, dt:{}, taskId:{}", DateUtil.today(), dt, taskId);
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = DataCheckEnum.BIZ_MAPPING.getDesc();

        TargetAnalysisQueryDTO fvMappingCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(bizNameMappingQuerySql, WIDE_TABLE_NAME, dt, PLATFORM_TABLE_NAME))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(fvMappingCheck);
        List<OriginalQueryResultDTO> resultDTOS = new ArrayList<>();
        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        int bizMappingCaseId = DataCheckEnum.BIZ_MAPPING.getId();
        int endKeyMappingCaseId = DataCheckEnum.PLATFORM_END_KEY_MAPPING.getId();
        try {
            resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        } catch (Exception e) {
            log.error("BizNameAndPlatformValidator error:", e);
            checkResultMap.put(bizMappingCaseId, Boolean.FALSE);
            checkResultMap.put(endKeyMappingCaseId, Boolean.FALSE);
            return;
        }

        if (CollectionUtils.isEmpty(resultDTOS) || MapUtils.isEmpty(resultDTOS.get(0).getDataList().get(0))) {
            checkResultMap.put(bizMappingCaseId, Boolean.FALSE);
            checkResultMap.put(endKeyMappingCaseId, Boolean.FALSE);
            return;
        }
        Integer count = MapUtils.getInteger(resultDTOS.get(0).getDataList().get(0), ORDER_COUNT, 0);
        log.info("BizNameAndPlatformValidator biz_name mapping error count:{}", count);
        Boolean result = count > 0 ? Boolean.FALSE : Boolean.TRUE;
        checkResultMap.put(bizMappingCaseId, result);
        checkResultMap.put(endKeyMappingCaseId, result);
    }
}
