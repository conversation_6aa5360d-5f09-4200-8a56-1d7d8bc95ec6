package com.iqiyi.vip.validator.chain;


/**
 * 数据校验责任链处理器接口
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
public interface ValidationHandler {
    
    /**
     * 设置下一个处理器
     * 
     * @param nextHandler 下一个处理器
     * @return 下一个处理器
     */
    ValidationHandler setNext(ValidationHandler nextHandler);
    
    /**
     * 处理校验逻辑
     * 
     * @param context 校验上下文
     */
    void handle(ValidationContext context);

    /**
     * 获取处理器的执行顺序，数字越小越先执行
     *
     * @return 执行顺序
     */
    int getOrder();

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getName();

    /**
     * 是否启用此处理器
     *
     * @return true-启用，false-禁用
     */
    default boolean isEnabled() {
        return true;
    }
}
