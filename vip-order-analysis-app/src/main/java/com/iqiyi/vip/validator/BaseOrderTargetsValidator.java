package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.entity.DataCheckResult;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.service.DataCheckResultService;
import com.iqiyi.vip.task.CommonAnalysisTask;
import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className BaseOrderTargetsValidator
 * @description
 * @date 2022/10/31
 **/
@Component
@Slf4j
public class BaseOrderTargetsValidator implements Validator {

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit;

    @Value("${base.order.targets.query.sql}")
    private String baseOrderTargetsQuerySql;

    @Value("${base.order.targets.fv.query.sql}")
    private String baseOrderTargetsQuerySqlWithFv;

    @Value("${data.check.begin.dt:2021-06-01}")
    private String dataCheckBeginDt;

    @Value("${data.check.end.dt:2021-06-02}")
    private String dataCheckEndDt;

    @Autowired
    private CommonAnalysisTask commonAnalysisTask;

    @Resource
    private DataCheckResultService dataCheckResultService;

    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("BaseOrderTargetsValidator start at:{}, dt:{}", DateUtil.today(), dt);
        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        try {
            checkDiffBetweenBiAndWideTable(dt, checkResultMap);
            checkDiffWithWideTable(dt, checkResultMap);
        } catch (Exception e) {
            log.error("BaseOrderTargetsValidator e", e);
            checkResultMap.put(DataCheckEnum.ORDER_COUNT_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_DHSR_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
        }

    }

    private void checkDiffWithWideTable(String dt, HashMap<Integer, Boolean> checkResultMap) throws ExecutionException, InterruptedException {
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "BASE_WIDETABLE_ORDER_TARGETS";
        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(baseOrderTargetsQuerySqlWithFv, WIDE_TABLE_NAME, dt, dataCheckBeginDt, dataCheckEndDt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(wideTableOrderCheck);
        List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        if (CollectionUtils.isEmpty(resultDTOS)) {
            checkResultMap.put(DataCheckEnum.ORDER_COUNT_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_DHSR_VIPTYPE_FV_DIFF.getId(), Boolean.FALSE);
            return;
        }
        Map<String, HashMap<String, Object>> wideTableResult = resultDTOS.get(0).getDataList()
            .stream()
            .collect(Collectors.toMap(it -> MapUtils.getString(it, DIMENSION), it -> it));

        String diffDt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -(offset+1))));
        Map<String, Map<String, Object>> checkResultToDiff = getCheckResultToDiff(diffDt, dataCheckBeginDt, dataCheckEndDt);
        boolean orderCount = true;
        boolean orderUidCount = true;
        boolean dhsr = true;
        for (String dimension : wideTableResult.keySet()) {
            HashMap<String, Object> wideTableResultWithDimension = wideTableResult.get(dimension);
            Map<String, Object> checkResultToDiffWithDimension = checkResultToDiff.get(dimension);
            long wideTableOrderCount = MapUtils.getLong(wideTableResultWithDimension, ORDER_COUNT, 0L);
            long diffOrderCount = MapUtils.getLong(checkResultToDiffWithDimension, ORDER_COUNT, 0L);
            if (Math.abs(wideTableOrderCount - diffOrderCount) > orderCountGapLimit) {
                orderCount = false;
            }

            long wideTableOrderUidCount = MapUtils.getLong(wideTableResultWithDimension, UID_COUNT, 0L);
            long diffWideTableOrderUidCount = MapUtils.getLong(checkResultToDiffWithDimension, UID_COUNT, 0L);
            if (Math.abs(wideTableOrderUidCount - diffWideTableOrderUidCount) > orderCountGapLimit) {
                orderUidCount = false;
            }

            Double wideTableOrderDhsr = MapUtils.getDouble(wideTableResultWithDimension, DHSR, Double.NaN);
            Double diffWideTableOrderDhsr = MapUtils.getDouble(checkResultToDiffWithDimension, DHSR, Double.NaN);
            if (Math.abs(wideTableOrderDhsr - diffWideTableOrderDhsr) > orderCountGapLimit * 2200) {
                dhsr = false;
            }
        }
        checkResultMap.put(DataCheckEnum.ORDER_COUNT_VIPTYPE_FV_DIFF.getId(), orderCount);
        checkResultMap.put(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_FV_DIFF.getId(), orderUidCount);
        checkResultMap.put(DataCheckEnum.ORDER_DHSR_VIPTYPE_FV_DIFF.getId(), dhsr);
    }

    private Map<String, Map<String, Object>> getCheckResultToDiff(String diffDt, String dataCheckBeginDt, String dataCheckEndDt)
        throws ExecutionException, InterruptedException {
        DataCheckResult dataCheckResultDO = DataCheckResult.builder()
            .caseIds(Lists.newArrayList(DataCheckEnum.ORDER_COUNT_VIPTYPE_FV_DIFF.getId(), DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_FV_DIFF.getId(), DataCheckEnum.ORDER_DHSR_VIPTYPE_FV_DIFF.getId()))
            .dt(diffDt)
            .beginTime(dataCheckBeginDt)
            .endTime(dataCheckEndDt)
            .dataSource("wideTable")
            .build();
        List<DataCheckResult> dataCheckResult = dataCheckResultService.getDataCheckResult(dataCheckResultDO);
        if (CollectionUtils.isEmpty(dataCheckResult)) {
            log.info("not find order base check results dt:{}, caseIds：{}", diffDt, dataCheckResultDO.getCaseIds());
            List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
            TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
                .targetCode("base_widetable_order_targets")
                .querySql(String.format(baseOrderTargetsQuerySqlWithFv, WIDE_TABLE_NAME, diffDt, dataCheckBeginDt, dataCheckEndDt))
                .operator(DATA_CHECK_OPERATOR)
                .dateYM(diffDt)
                .taskId(taskId)
                .build();
            queryDTOS.add(wideTableOrderCheck);
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
            List<LinkedHashMap<String, Object>> dataList = resultDTOS.get(0).getDataList();
            dataList.forEach(m ->{
                String dimension = MapUtils.getString(m, DIMENSION, (String) null);
                Double orderCount = MapUtils.getDouble(m, ORDER_COUNT);
                Double uidCount = MapUtils.getDouble(m, UID_COUNT);
                Double dhsr = MapUtils.getDouble(m, DHSR);
                DataCheckResult orderCountCheckResult = DataCheckResult.buildFrom(DataCheckEnum.ORDER_COUNT_VIPTYPE_FV_DIFF.getId(), ORDER_COUNT, orderCount, diffDt, dataCheckBeginDt, dataCheckEndDt, dimension, "wideTable");
                dataCheckResultService.saveDataCheckResult(orderCountCheckResult);
                DataCheckResult uidCountCheckResult = DataCheckResult.buildFrom(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_FV_DIFF.getId(), UID_COUNT, uidCount, diffDt, dataCheckBeginDt, dataCheckEndDt, dimension, "wideTable");
                dataCheckResultService.saveDataCheckResult(uidCountCheckResult);
                DataCheckResult dhsrDataCheckResult = DataCheckResult.buildFrom(DataCheckEnum.ORDER_DHSR_VIPTYPE_FV_DIFF.getId(), DHSR, dhsr, diffDt, dataCheckBeginDt, dataCheckEndDt, dimension, "wideTable");
                dataCheckResultService.saveDataCheckResult(dhsrDataCheckResult);
            });
            log.info("base check results dt:{} saved, caseIds:{}", diffDt, dataCheckResultDO.getCaseIds());
            return dataList
                .stream()
                .collect(Collectors.toMap(it -> MapUtils.getString(it, DIMENSION), it -> it));
        }
        Map<String, Map<String, Object>> collect = dataCheckResult.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.groupingBy(DataCheckResult::getDimension, Collectors.toMap(DataCheckResult::getKey, DataCheckResult::getValue)));
        return collect;
    }


    private void checkDiffBetweenBiAndWideTable(String dt, HashMap<Integer, Boolean> checkResultMap) throws ExecutionException, InterruptedException {
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "BASE_BI_ORDER_TARGETS";
        TargetAnalysisQueryDTO biOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(baseOrderTargetsQuerySql, BI_TABLE_NAME, dt, dataCheckBeginDt, dataCheckEndDt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(biOrderCheck);

        targetCode = "BASE_WIDETABLE_ORDER_TARGETS";
        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(baseOrderTargetsQuerySql, WIDE_TABLE_NAME, dt, dataCheckBeginDt, dataCheckEndDt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(wideTableOrderCheck);
        List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        if (CollectionUtils.isEmpty(resultDTOS) || resultDTOS.size() != 2) {
            checkResultMap.put(DataCheckEnum.ORDER_COUNT_VIPTYPE_TABLES_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_TABLES_DIFF.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_DHSR_VIPTYPE_TABLES_DIFF.getId(), Boolean.FALSE);
            return;
        }
        Map<Integer, HashMap<String, Object>> biResultByBizType = resultDTOS.get(0)
            .getDataList()
            .stream()
            .collect(Collectors.toMap(it -> MapUtils.getInteger(it, VIP_TYPE), it -> it));

        java.util.Map<Integer, HashMap<String, Object>> wideTableResultByBizType = resultDTOS.get(1)
            .getDataList()
            .stream()
            .collect(Collectors.toMap(it -> MapUtils.getInteger(it, VIP_TYPE), it -> it));
        boolean orderCount = true;
        boolean orderUidCount = true;
        boolean dhsr = true;
        for (Integer vipType : biResultByBizType.keySet()) {
            long biOrderCount = MapUtils.getLong(biResultByBizType.get(vipType), ORDER_COUNT, 0L);
            long wideTableOrderCount = MapUtils.getLong(wideTableResultByBizType.get(vipType), ORDER_COUNT, 0L);
            if (Math.abs(wideTableOrderCount - biOrderCount) > orderCountGapLimit) {
                orderCount = false;
            }

            long biOrderUidCount = MapUtils.getLong(biResultByBizType.get(vipType), UID_COUNT, 0L);
            long wideTableOrderUidCount = MapUtils.getLong(wideTableResultByBizType.get(vipType), UID_COUNT, 0L);
            if (Math.abs(wideTableOrderUidCount - biOrderUidCount) > orderCountGapLimit) {
                orderUidCount = false;
            }

            long biOrderDhsr = MapUtils.getLong(biResultByBizType.get(vipType), DHSR, 0L);
            long wideTableOrderDhsr = MapUtils.getLong(wideTableResultByBizType.get(vipType), DHSR, 0L);
            if (Math.abs(wideTableOrderDhsr - biOrderDhsr) > orderCountGapLimit * 2200) {
                dhsr = false;
            }
        }
        checkResultMap.put(DataCheckEnum.ORDER_COUNT_VIPTYPE_TABLES_DIFF.getId(), orderCount);
        checkResultMap.put(DataCheckEnum.ORDER_UID_COUNT_VIPTYPE_TABLES_DIFF.getId(), orderUidCount);
        checkResultMap.put(DataCheckEnum.ORDER_DHSR_VIPTYPE_TABLES_DIFF.getId(), dhsr);
    }

}
