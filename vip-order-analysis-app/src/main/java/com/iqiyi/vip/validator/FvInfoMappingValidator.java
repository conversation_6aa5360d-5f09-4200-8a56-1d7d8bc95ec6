package com.iqiyi.vip.validator;


import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;

import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className FvInfoMappingValidator
 * @description
 * @date 2022/10/28
 **/
@Component
@Slf4j
public class FvInfoMappingValidator implements Validator {

    @Resource
    private CommonAnalysisTask commonAnalysisTask;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit;

    @Value("${fv.info.mapping.query.sql}")
    private String fvInfoMappingQuerySql;

    @Value("${dt.offset:2}")
    private Integer offset;

    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("FvInfoMappingValidator start at:{}, dt:{}, taskId:{}", DateUtil.today(), dt, taskId);
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "WIDE_TABLE_ORDER_LT_TARGETS";
        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(fvInfoMappingQuerySql, WIDE_TABLE_NAME, dt, FV_DIM_TABLE_NAME))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(wideTableOrderCheck);
        List<OriginalQueryResultDTO> resultDTOS = new ArrayList<>();
        try {
            resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
        } catch (Exception e) {
            log.error("FvInfoMappingValidator error: ", e);
            checkResultMap.put(DataCheckEnum.FV_MAPPING.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.TEAM_MAPPING.getId(), Boolean.FALSE);
            return;
        }

        if (CollectionUtils.isEmpty(resultDTOS)) {
            checkResultMap.put(DataCheckEnum.FV_MAPPING.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.TEAM_MAPPING.getId(), Boolean.FALSE);
            return;
        }

        List<LinkedHashMap<String, Object>> dataList = resultDTOS.get(0).getDataList();
        if (CollectionUtils.isEmpty(dataList) || MapUtils.isEmpty(dataList.get(0))) {
            checkResultMap.put(DataCheckEnum.FV_MAPPING.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.TEAM_MAPPING.getId(), Boolean.FALSE);
            return;
        }
        HashMap<String, Object> resultMap = dataList.get(0);
        Integer count = MapUtils.getInteger(resultMap, ORDER_COUNT);
        Boolean result = Math.abs(count) > orderCountGapLimit ? Boolean.FALSE : Boolean.TRUE;
        log.info("FvInfoMappingValidator fv mapping error count:{}", count);
        checkResultMap.put(DataCheckEnum.FV_MAPPING.getId(), result);
        checkResultMap.put(DataCheckEnum.TEAM_MAPPING.getId(), result);
    }
}
