package com.iqiyi.vip.validator.chain;

import cn.hutool.core.date.DateUtil;
import com.iqiyi.vip.enums.DataCheckEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.iqiyi.vip.validator.Validator.*;

/**
 * Hive与Clickhouse比较处理器 - 两个Hive表结果的和与Clickhouse表的结果比较
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@Component
@Slf4j
public class HiveClickhouseComparisonHandler extends AbstractValidationHandler {
    
    @Autowired
    private QueryService queryService;
    
    @Override
    public int getOrder() {
        return 20; // 优先级20，在OriginalValidationHandler之后执行
    }
    
    @Override
    public String getName() {
        return "Hive表与Clickhouse表比较校验";
    }
    
    @Override
    protected void doValidate(ValidationContext context) {
        log.info("HiveClickhouseComparisonHandler start at:{}, dt:{}, taskId:{}", DateUtil.today(), context.getDt(), context.getTaskId());
        
        // 使用QueryService查询数据，避免重复查询WIDE表
        Map<String, Object> wideTableResult = queryService.queryWideTable(context);
        Map<String, Object> popWideTableResult = queryService.queryPopWideTable(context);
        Map<String, Object> ckTableResult = queryService.queryClickhouseTable(context);
        
        if (MapUtils.isEmpty(wideTableResult) || MapUtils.isEmpty(popWideTableResult) || MapUtils.isEmpty(ckTableResult)) {
            context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT.getId(), Boolean.FALSE);
            context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE.getId(), Boolean.FALSE);
            return;
        }
        
        // 计算两个Hive表的和
        Long wideTableOrderCount = MapUtils.getLong(wideTableResult, ORDER_COUNT, 0L);
        Long wideTableOrderSumFee = MapUtils.getLong(wideTableResult, SUM_FEE, 0L);
        Long popWideTableOrderCount = MapUtils.getLong(popWideTableResult, ORDER_COUNT, 0L);
        Long popWideTableOrderSumFee = MapUtils.getLong(popWideTableResult, SUM_FEE, 0L);
        
        Long hiveTotalOrderCount = wideTableOrderCount + popWideTableOrderCount;
        Long hiveTotalSumFee = wideTableOrderSumFee + popWideTableOrderSumFee;
        
        // Clickhouse表结果
        Long ckOrderCount = MapUtils.getLong(ckTableResult, ORDER_COUNT, 0L);
        Long ckOrderSumFee = MapUtils.getLong(ckTableResult, SUM_FEE, 0L);
        
        log.info("HiveClickhouseComparisonHandler end at: {}, dt :{} , hiveTotalOrderCount：{}, hiveTotalSumFee:{}, ckOrderCount:{}, ckOrderSumFee:{}", 
                DateUtil.now(), context.getDt(), hiveTotalOrderCount, hiveTotalSumFee, ckOrderCount, ckOrderSumFee);
        
        // 比较结果
        context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT.getId(),
            Math.abs(hiveTotalOrderCount - ckOrderCount) <= context.getOrderCountGapLimit() ? Boolean.TRUE : Boolean.FALSE);
        context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE.getId(),
            Math.abs(hiveTotalSumFee - ckOrderSumFee) <= context.getOrderCountGapLimit() * 2500 ? Boolean.TRUE : Boolean.FALSE);
    }
    
    @Override
    protected void handleException(ValidationContext context, Exception e) {
        log.error("HiveClickhouseComparisonHandler error :", e);
        context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT.getId(), Boolean.FALSE);
        context.getResultMap().put(DataCheckEnum.HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE.getId(), Boolean.FALSE);
    }
}
