package com.iqiyi.vip.validator.chain;

import cn.hutool.core.date.DateUtil;
import com.iqiyi.vip.enums.DataCheckEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Map;
import static com.iqiyi.vip.validator.Validator.*;

/**
 * 原始校验处理器 - BI表与WIDE表的比较校验
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@Component
@Slf4j
public class OriginalValidationHandler extends AbstractValidationHandler {
    
    @Autowired
    private QueryService queryService;
    
    @Override
    public int getOrder() {
        return 10; // 优先级10，较早执行
    }
    
    @Override
    public String getName() {
        return "BI表与WIDE表比较校验";
    }
    
    @Override
    protected void doValidate(ValidationContext context) {
        log.info("OriginalValidationHandler start at:{}, dt:{}, taskId:{}", DateUtil.today(), context.getDt(), context.getTaskId());
        
        // 查询BI表和WIDE表数据
        Map<String, Object> biOrderResult = queryService.queryBiTable(context);
        Map<String, Object> wideTableResult = queryService.queryWideTable(context);
        
        if (MapUtils.isEmpty(biOrderResult) || MapUtils.isEmpty(wideTableResult)) {
            context.getResultMap().put(DataCheckEnum.ORDER_COUNT.getId(), Boolean.FALSE);
            context.getResultMap().put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Boolean.FALSE);
            return;
        }
        
        Long biOrderCount = MapUtils.getLong(biOrderResult, ORDER_COUNT, 0L);
        Long biOrderSumFee = MapUtils.getLong(biOrderResult, SUM_FEE, 0L);
        Long wideTableOrderCount = MapUtils.getLong(wideTableResult, ORDER_COUNT, 0L);
        Long wideTableOrderSumFee = MapUtils.getLong(wideTableResult, SUM_FEE, 0L);
        
        log.info("OriginalValidationHandler end at: {}, dt :{} , biOrderCount：{}, biOrderSumFee:{}, wideTableOrderCount:{}, wideTableOrderSumFee:{}", DateUtil.now(), context.getDt(), biOrderCount, biOrderSumFee, wideTableOrderCount, wideTableOrderSumFee);
        
        // 比较BI表和WIDE表的结果
        context.getResultMap().put(DataCheckEnum.ORDER_COUNT.getId(), Math.abs(wideTableOrderCount - biOrderCount) <= context.getOrderCountGapLimit() ? Boolean.TRUE : Boolean.FALSE);
        context.getResultMap().put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Math.abs(wideTableOrderSumFee - biOrderSumFee) <= context.getOrderCountGapLimit() * 2500 ? Boolean.TRUE : Boolean.FALSE);
    }
    
    @Override
    protected void handleException(ValidationContext context, Exception e) {
        log.error("OriginalValidationHandler error :", e);
        context.getResultMap().put(DataCheckEnum.ORDER_COUNT.getId(), Boolean.FALSE);
        context.getResultMap().put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Boolean.FALSE);
    }
}
