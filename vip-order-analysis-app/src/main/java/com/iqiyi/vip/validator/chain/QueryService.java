package com.iqiyi.vip.validator.chain;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;
import static com.iqiyi.vip.validator.Validator.*;

/**
 * 查询服务 - 统一管理数据查询逻辑，避免重复查询
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@Service
@Slf4j
public class QueryService {
    
    @Autowired
    private CommonAnalysisTask commonAnalysisTask;
    
    @Value("${order.count.fee.querySql.ck:SELECT count(*) as orderCount, sum(real_fee) as sumFee FROM %s WHERE dt = '%s'}")
    private String cKOrderCountAndSumFeeSql;
    
    /**
     * 查询BI表数据
     */
    public Map<String, Object> queryBiTable(ValidationContext context) {
        String cacheKey = "BI_TABLE";
        if (context.getQueryResultCache().containsKey(cacheKey)) {
            log.info("从缓存获取BI表查询结果");
            return context.getQueryResultCache().get(cacheKey);
        }
        
        log.info("查询BI表数据: {}", BI_TABLE_NAME);
        TargetAnalysisQueryDTO queryDTO = TargetAnalysisQueryDTO.builder()
            .targetCode("BI_TABLE_QUERY")
            .querySql(String.format(context.getOrderCountAndSumFeeSql(), BI_TABLE_NAME, context.getDt()))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(context.getDt())
            .taskId(context.getTaskId())
            .dataBase(DataBaseTypeEnum.HIVE.getCode())
            .build();
        
        Map<String, Object> result = executeQuery(queryDTO);
        context.getQueryResultCache().put(cacheKey, result);
        return result;
    }
    
    /**
     * 查询WIDE表数据
     */
    public Map<String, Object> queryWideTable(ValidationContext context) {
        String cacheKey = "WIDE_TABLE";
        if (context.getQueryResultCache().containsKey(cacheKey)) {
            log.info("从缓存获取WIDE表查询结果");
            return context.getQueryResultCache().get(cacheKey);
        }
        
        log.info("查询WIDE表数据: {}", WIDE_TABLE_NAME);
        TargetAnalysisQueryDTO queryDTO = TargetAnalysisQueryDTO.builder()
            .targetCode("WIDE_TABLE_QUERY")
            .querySql(String.format(context.getOrderCountAndSumFeeSql(), WIDE_TABLE_NAME, context.getDt()))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(context.getDt())
            .taskId(context.getTaskId())
            .dataBase(DataBaseTypeEnum.HIVE.getCode())
            .build();
        
        Map<String, Object> result = executeQuery(queryDTO);
        context.getQueryResultCache().put(cacheKey, result);
        return result;
    }
    
    /**
     * 查询POP_WIDE表数据
     */
    public Map<String, Object> queryPopWideTable(ValidationContext context) {
        String cacheKey = "POP_WIDE_TABLE";
        if (context.getQueryResultCache().containsKey(cacheKey)) {
            log.info("从缓存获取POP_WIDE表查询结果");
            return context.getQueryResultCache().get(cacheKey);
        }
        
        log.info("查询POP_WIDE表数据: {}", POP_WIDE_TABLE_NAME);
        TargetAnalysisQueryDTO queryDTO = TargetAnalysisQueryDTO.builder()
            .targetCode("POP_WIDE_TABLE_QUERY")
            .querySql(String.format(context.getOrderCountAndSumFeeSql(), POP_WIDE_TABLE_NAME, context.getDt()))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(context.getDt())
            .taskId(context.getTaskId())
            .dataBase(DataBaseTypeEnum.HIVE.getCode())
            .build();
        
        Map<String, Object> result = executeQuery(queryDTO);
        context.getQueryResultCache().put(cacheKey, result);
        return result;
    }
    
    /**
     * 查询Clickhouse表数据
     */
    public Map<String, Object> queryClickhouseTable(ValidationContext context) {
        String cacheKey = "CLICKHOUSE_TABLE";
        if (context.getQueryResultCache().containsKey(cacheKey)) {
            log.info("从缓存获取Clickhouse表查询结果");
            return context.getQueryResultCache().get(cacheKey);
        }
        
        log.info("查询Clickhouse表数据: {}", CK_TABLE_NAME);
        TargetAnalysisQueryDTO queryDTO = TargetAnalysisQueryDTO.builder()
            .targetCode("CLICKHOUSE_TABLE_QUERY")
            .querySql(String.format(cKOrderCountAndSumFeeSql, CK_TABLE_NAME, context.getDt()))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(context.getDt())
            .taskId(context.getTaskId())
            .dataBase(DataBaseTypeEnum.CLICK_HOUSE.getCode())
            .build();
        
        Map<String, Object> result = executeQuery(queryDTO);
        context.getQueryResultCache().put(cacheKey, result);
        return result;
    }
    
    /**
     * 执行单个查询
     */
    private Map<String, Object> executeQuery(TargetAnalysisQueryDTO queryDTO) {
        try {
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(Collections.singletonList(queryDTO), queryDTO.getTaskId());
            
            if (CollectionUtils.isEmpty(resultDTOS)) {
                log.warn("查询结果为空: {}", queryDTO.getTargetCode());
                return null;
            }
            
            OriginalQueryResultDTO resultDTO = resultDTOS.get(0);
            Map<String, Object> result = resultDTO.getDataList().stream().findFirst().orElse(null);
            
            if (result == null) {
                log.warn("查询结果数据为空: {}", queryDTO.getTargetCode());
            }
            
            return result;
        } catch (Exception e) {
            log.error("查询执行异常: {}", queryDTO.getTargetCode(), e);
            return null;
        }
    }
}
