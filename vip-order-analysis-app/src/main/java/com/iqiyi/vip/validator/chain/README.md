# 数据校验责任链模式实现

## 概述

本模块使用责任链模式重构了`countAndFeeCheckJobHandler`的数据校验逻辑，将原本单一的校验方法拆分为多个独立的校验处理器，提高了代码的可维护性和扩展性。

## 架构设计

### 核心组件

1. **ValidationHandler** - 责任链处理器接口
2. **AbstractValidationHandler** - 抽象处理器基类
3. **ValidationContext** - 校验上下文，包含校验所需的所有数据
4. **ValidationChainManager** - 责任链管理器

### 具体处理器

1. **OriginalValidationHandler** - 原始校验处理器
   - 包装现有的`countAndFeeValidator.validate(resultMap)`逻辑
   - 比较BI表和WIDE表的订单数量和金额
   - 校验项：`ORDER_COUNT`、`ORDER_REALFEE_SUM`

2. **HiveClickhouseComparisonHandler** - Hive与Clickhouse比较处理器
   - 新增的校验项：使用2个hive表结果的和与Clickhouse表的结果比较
   - 查询表：`WIDE_TABLE_NAME`、`POP_WIDE_TABLE_NAME`、`CK_TABLE_NAME`
   - 校验项：`HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT`、`HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE`

## 使用方式

### 在Job中使用

```java
@Job("countAndFeeCheckJobHandler")
public void countAndFeeCheckJobHandler() throws Exception {
    HashMap<Integer, Boolean> resultMap = new HashMap<>();
    
    // 构建校验上下文
    ValidationContext context = ValidationContext.builder()
        .resultMap(resultMap)
        .dt(dt)
        .taskId(-1L)
        .offset(offset)
        .orderCountGapLimit(orderCountGapLimit)
        .orderCountAndSumFeeSql(orderCountAndSumFeeSql)
        .build();
    
    // 执行责任链校验
    validationChainManager.executeValidationChain(context);
    
    // 处理校验结果...
}
```

### 扩展新的校验处理器

1. 继承`AbstractValidationHandler`
2. 实现`doValidate(ValidationContext context)`方法
3. 实现`handleException(ValidationContext context, Exception e)`方法
4. 在`ValidationChainManager`中添加到责任链

```java
@Component
public class NewValidationHandler extends AbstractValidationHandler {
    
    @Override
    protected void doValidate(ValidationContext context) {
        // 实现具体的校验逻辑
        // 将校验结果放入context.getResultMap()
    }
    
    @Override
    protected void handleException(ValidationContext context, Exception e) {
        // 处理异常，设置校验失败状态
    }
}
```

## 配置要求

需要在配置文件中配置以下属性：

- `order.count.fee.querySql` - Hive查询SQL模板
- `order.count.fee.querySql.ck` - Clickhouse查询SQL模板
- `order.count.gap.limit` - 订单数量差异限制
- `dt.offset` - 日期偏移量

## 数据校验枚举

新增了以下校验枚举项：

- `HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT(18, "订单总数（两个Hive表之和对比Clickhouse）")`
- `HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE(19, "订单总金额（两个Hive表之和对比Clickhouse）")`

## 优势

1. **单一职责**：每个处理器只负责一种类型的校验
2. **易于扩展**：新增校验逻辑只需添加新的处理器
3. **易于维护**：各校验逻辑相互独立，互不影响
4. **异常隔离**：单个处理器异常不会影响其他处理器的执行
5. **日志清晰**：每个处理器都有独立的日志记录

## 测试

提供了`ValidationChainTest`测试类来验证责任链的正确性。
