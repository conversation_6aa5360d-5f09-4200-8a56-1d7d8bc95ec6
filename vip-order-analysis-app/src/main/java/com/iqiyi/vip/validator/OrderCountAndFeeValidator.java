package com.iqiyi.vip.validator;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.DataCheckEnum;
import com.iqiyi.vip.task.CommonAnalysisTask;

import static com.iqiyi.vip.constant.Constants.DATA_CHECK_OPERATOR;

/**
 * <AUTHOR>
 * @className OrderCountAndFeeValidator
 * @description
 * @date 2022/10/28
 **/
@Component
@Slf4j
public class OrderCountAndFeeValidator implements Validator {

    @Value("${dt.offset:2}")
    private Integer offset;

    @Value("${order.count.gap.limit:10}")
    private Integer orderCountGapLimit = 10;

    @Value("${order.count.fee.querySql}")
    private String orderCountAndSumFeeSql;

    @Autowired
    private CommonAnalysisTask commonAnalysisTask;

    @Override
    public void validate(Object... args) {
        String dt = DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
        log.info("OrderCountAndFeeValidator start at:{}, dt:{}, taskId:{}", DateUtil.today(), dt, taskId);
        List<TargetAnalysisQueryDTO> queryDTOS = new ArrayList<>();
        String targetCode = "ORDER_COUNT_FEE_CHECK";

        TargetAnalysisQueryDTO biTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(orderCountAndSumFeeSql, BI_TABLE_NAME, dt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(biTableOrderCheck);
        TargetAnalysisQueryDTO wideTableOrderCheck = TargetAnalysisQueryDTO.builder()
            .targetCode(targetCode)
            .querySql(String.format(orderCountAndSumFeeSql, WIDE_TABLE_NAME, dt))
            .operator(DATA_CHECK_OPERATOR)
            .dateYM(dt)
            .taskId(taskId)
            .build();
        queryDTOS.add(wideTableOrderCheck);

        HashMap<Integer, Boolean> checkResultMap = (HashMap) args[0];
        try {
            List<OriginalQueryResultDTO> resultDTOS = commonAnalysisTask.executeQuery(queryDTOS, taskId);
            if (CollectionUtils.isEmpty(resultDTOS) || resultDTOS.size() != 2) {
                checkResultMap.put(DataCheckEnum.ORDER_COUNT.getId(), Boolean.FALSE);
                checkResultMap.put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Boolean.FALSE);
                return;
            }
            OriginalQueryResultDTO biTableQueryResultDTO = resultDTOS.get(0);
            Map<String, Object> biOrderResult = biTableQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            OriginalQueryResultDTO wideTableQueryResultDTO = resultDTOS.get(1);
            Map<String, Object> wideTableResult = wideTableQueryResultDTO.getDataList().stream().findFirst().orElse(null);
            if (MapUtils.isEmpty(biOrderResult) || MapUtils.isEmpty(wideTableResult)) {
                checkResultMap.put(DataCheckEnum.ORDER_COUNT.getId(), Boolean.FALSE);
                checkResultMap.put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Boolean.FALSE);
                return;
            }
            Long biOrderCount = MapUtils.getLong(biOrderResult, ORDER_COUNT, 0L);
            Long biOrderSumFee = MapUtils.getLong(biOrderResult, SUM_FEE, 0L);
            Long wideTableOrderCount = MapUtils.getLong(wideTableResult, ORDER_COUNT, 0L);
            Long wideTableOrderSumFee = MapUtils.getLong(wideTableResult, SUM_FEE, 0L);
            log.info("OrderCountAndFeeValidator end at: {}, dt :{} , biOrderCount：{}, biOrderSumFee:{}, wideTableOrderCount:{}, wideTableOrderSumFee:{}", DateUtil.now(), dt, biOrderCount, biOrderSumFee, wideTableOrderCount, wideTableOrderSumFee);
            checkResultMap.put(DataCheckEnum.ORDER_COUNT.getId(),
                Math.abs(wideTableOrderCount - biOrderCount) <= orderCountGapLimit ? Boolean.TRUE : Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_REALFEE_SUM.getId(),
                Math.abs(wideTableOrderSumFee - biOrderSumFee) <= orderCountGapLimit * 2500 ? Boolean.TRUE : Boolean.FALSE);
        } catch (Exception e) {
            log.error("OrderCountAndFeeValidator error :", e);
            checkResultMap.put(DataCheckEnum.ORDER_COUNT.getId(), Boolean.FALSE);
            checkResultMap.put(DataCheckEnum.ORDER_REALFEE_SUM.getId(), Boolean.FALSE);
        }
    }
}
