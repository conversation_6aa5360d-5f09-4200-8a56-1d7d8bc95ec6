package com.iqiyi.vip.validator;

/**
 * <AUTHOR>
 * @className Validator
 * @description
 * @date 2022/10/28
 **/
public interface Validator {

    String BI_TABLE_NAME = "bi_business_dwd.dwd_da_shuffle_hive_qiyue_order_widetable";

    String CK_TABLE_NAME = "dist_hive_trade_qiyue_order_widetable_order_analysis";

    String WIDE_TABLE_NAME = "boss.hive_trade_qiyue_order_widetable_order_analysis";

    String POP_WIDE_TABLE_NAME = "boss.hive_trade_qiyue_order_widetable_order_analysis_pop";

    String FV_DIM_TABLE_NAME = "boss.hive_dim_fv_channel";

    String PLATFORM_TABLE_NAME = "boss.hive_dim_qiyue_platform_end_key";

    String ORDER_COUNT = "orderCount";

    String UID_COUNT = "uidCount";

    String DHSR = "dhsr";

    String DIMENSION = "dimension";

    String VIP_TYPE = "vip_biz_type";

    String SUM_FEE = "sumFee";

    String LTV = "LTV";

    long taskId = -1;

    void validate(Object... args);
}
