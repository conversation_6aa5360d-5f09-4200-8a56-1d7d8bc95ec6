package com.iqiyi.vip.validator.chain;

import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.ExecutionException;

/**
 * 抽象校验处理器
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@Slf4j
public abstract class AbstractValidationHandler implements ValidationHandler {
    
    private ValidationHandler nextHandler;
    
    @Override
    public ValidationHandler setNext(ValidationHandler nextHandler) {
        this.nextHandler = nextHandler;
        return nextHandler;
    }
    
    @Override
    public void handle(ValidationContext context) {
        try {
            log.info("开始执行校验处理器: {}", this.getClass().getSimpleName());
            doValidate(context);
            log.info("完成执行校验处理器: {}", this.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("校验处理器执行异常: {}", this.getClass().getSimpleName(), e);
            handleException(context, e);
        }
        
        // 继续执行下一个处理器
        if (nextHandler != null) {
            nextHandler.handle(context);
        }
    }
    
    /**
     * 具体的校验逻辑
     * 
     * @param context 校验上下文
     */
    protected abstract void doValidate(ValidationContext context) throws ExecutionException, InterruptedException;
    
    /**
     * 异常处理
     * 
     * @param context 校验上下文
     * @param e 异常
     */
    protected abstract void handleException(ValidationContext context, Exception e);

    /**
     * 默认实现，子类可以重写
     */
    @Override
    public int getOrder() {
        return 100; // 默认优先级
    }

    /**
     * 默认实现，子类可以重写
     */
    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
