package com.iqiyi.vip.handler.base;

import com.iqiyi.vip.api.DevOpsPermissionApi;
import com.iqiyi.vip.component.util.VipApplicationContextUtil;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.domain.repository.DataPermissionRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.dto.permission.LayeredDataPermissionDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.handler.FvConditionDataPermissionHandler;
import com.iqiyi.vip.handler.VipTypeConditionDataPermissionHandler;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.utils.DataPermissionNodeTrans;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/11 15:59
 */
public abstract class ConditionDataPermissionHandler {

    public static ConditionDataPermissionHandler conditionDataPermissionHandler = new FvConditionDataPermissionHandler();

    public static ConditionDataPermissionHandler getConditionDataPermissionHandler(ConditionEnum conditionEnum) {
        return conditionEnum == ConditionEnum.VIP_TYPE ? new VipTypeConditionDataPermissionHandler() : new FvConditionDataPermissionHandler();
    }

    public static ConditionDataPermissionHandler getConditionDataPermissionHandler(BaseQry baseQry) {
        boolean isFvDataPermission = DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode()
            .equals(baseQry.getDataPermissionType());

        ConditionEnum targetCondition = isFvDataPermission
            ? ConditionEnum.BUSINESS_LEVEL
            : ConditionEnum.VIP_TYPE;

        if (targetCondition == ConditionEnum.VIP_TYPE) {
            return conditionDataPermissionHandler.getSpringApplicationBean(VipTypeConditionDataPermissionHandler.class);
        } else {
            return conditionDataPermissionHandler.getSpringApplicationBean(FvConditionDataPermissionHandler.class);
        }
    }

    /**
     * 是否使用数据权限，例如：会员类型权限场景下，渠道、产品端平台就不需要权限限制了。相反也一样
     * @param baseQry
     * @return true 限制权限 false 不限制权限
     */
    public abstract boolean determineUseDataPermission(BaseQry baseQry);

    public List<DataPermissionDTO> nextOwnedDataPermissions(BaseQry baseQry, ConditionEnum conditionEnum, LabelEnum labelEnum, List<String> curDataPermissionIds, List<String> teamIds) {
        if (this.determineUseDataPermission(baseQry)) { // 产品端平台权限
            // DevOps权限
            // 数据权限
            // 特殊逻辑：分类的第一层级数据，需要特殊处理，因为会没有后两个参数
            final boolean highLevelUser = this.isHighestDataPermission(true, baseQry); // DevOps权限
            final DataPermission dataPermission = highLevelUser ? null : this.getSpringApplicationBean(DataPermissionRepository.class)
                .getDataPermission(baseQry.getOperator(), baseQry.getThemeType(), baseQry.getDataPermissionType()); // 用户已申请权限
            final LayeredDataPermissionDTO layeredDataPermissionDTO =
                highLevelUser ? null : LayeredDataPermissionDTO.getJson2LayeredDataPermissionDTO(dataPermission.getLayeredPermission());
            return this.getSpringApplicationBean(DataPermissionService.class)
                .nextOwnedDataPermissions(conditionEnum, labelEnum, curDataPermissionIds, teamIds,
                    sub -> {
                        if (highLevelUser) { // DevOps权限
                            return true;
                        }
                        DataPermissionNode cur = ((DataPermissionNode) sub);
                        if (conditionEnum == ConditionEnum.BUSINESS_LEVEL && cur.getCurLabelLevel() > LabelEnum.datePermissionCtrlLabel()
                            .getLevel()) {
                            return true;
                        }
                        if (conditionEnum == ConditionEnum.BUSINESS_LEVEL && curDataPermissionIds.contains(Constants.INTERNAL_CHANNEL_ID)) {
                            return true;
                        }
                        return layeredDataPermissionDTO.containsDataPermissions4LayeredOwned(conditionEnum.getFieldPrefix(), cur.getLabel(), DataPermissionNodeTrans.copyDataPermissionNode(cur)); // 数据权限
                    });
        }
        // 用户选择的权限或者只有会员类型权限的时候，对产品端平台不限制权限
        return  this.getSpringApplicationBean(DataPermissionService.class).nextOwnedDataPermissions(conditionEnum, labelEnum, curDataPermissionIds, teamIds, null);
    }

    /**
     * 是否是高等级权限
     * @param useDataPermissionType 是否增加数据类型综合判断用户权限等级。高等级 = 高等级 或者 不限制权限
     * @param baseQry
     * @return
     */
    public boolean isHighestDataPermission(boolean useDataPermissionType, BaseQry baseQry) {
        // 1. 先判断是否为高级用户（直接拥有最高权限）
        boolean isHighLevelUser = getSpringApplicationBean(DevOpsPermissionApi.class)
            .vipOrderAnalysisHighLevelUser(baseQry.getOperator());

        // 2. 若已是高级用户，直接返回true；否则根据权限配置判断
        if (isHighLevelUser) {
            return true;
        }

        // 3. 非高级用户时，需同时满足"启用权限控制"且"无需应用数据权限"才拥有最高权限
        return useDataPermissionType && !determineUseDataPermission(baseQry);
    }


    /**
     * 交集，保证用户提交的权限都合法，与已申请权限取交集 （包含用户的身份-高等级用户或者不需要权限）
     * @param conditionDataPermissions 用户前端提交的查询条件
     * @param conditionEnum
     * @param labelEnum
     * @param baseQry
     * @return
     */
    public List<String> intersectionWithOwnedDataPermissions(BaseQry baseQry, List<String> conditionDataPermissions, ConditionEnum conditionEnum, LabelEnum labelEnum) {
        // 判断是否为高等级权限（高等级权限无需过滤）
        boolean isHighestPermission = this.isHighestDataPermission(true, baseQry);
        if (isHighestPermission) {
            return conditionDataPermissions;
        }

        // 非高等级权限，调用服务计算权限交集
        DataPermissionService dataPermissionService = getSpringApplicationBean(DataPermissionService.class);
        return dataPermissionService.intersectionWithOwnedDataPermissions(
            conditionDataPermissions,
            conditionEnum,
            labelEnum,
            baseQry
        );
    }

    protected <T> T getSpringApplicationBean(Class<T> clazz) {
        return VipApplicationContextUtil.getBean(clazz);
    }

}
