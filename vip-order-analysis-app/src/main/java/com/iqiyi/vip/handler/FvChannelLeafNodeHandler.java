package com.iqiyi.vip.handler;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.enums.LeafNodeProcessTypeEnum;
import com.iqiyi.vip.service.FvChannelLeafNodeProcessor;

/**
 * <AUTHOR>
 * @className FvChannelLeafNodeHanlder
 * @description
 * @date 2022/10/10
 **/
@Component
@Slf4j
public class FvChannelLeafNodeHandler implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<LeafNodeProcessTypeEnum, FvChannelLeafNodeProcessor> agreementTypeToHandlerMap = Maps.newEnumMap(LeafNodeProcessTypeEnum.class);

    @Override
    public void afterPropertiesSet() {
        log.info("FvChannelLeafNodeHandler-init start!");
        applicationContext.getBeansOfType(FvChannelLeafNodeProcessor.class).values()
            .forEach(handler -> {
                log.info("FvChannelLeafNodeHandler-init:register handler {}  for {} agreement",
                    handler.getClass().getSimpleName(), handler.getProcessType());
                agreementTypeToHandlerMap.put(handler.getProcessType(), handler);
            });
        log.info("FvChannelLeafNodeHandler-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public FvChannelLeafNodeProcessor getHandler(LeafNodeProcessTypeEnum processTypeEnum) {
        return agreementTypeToHandlerMap.get(processTypeEnum);
    }
}
