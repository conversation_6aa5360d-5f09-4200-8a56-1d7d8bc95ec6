package com.iqiyi.vip.handler;

import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/11 16:00
 */
@Component
public class VipTypeConditionDataPermissionHandler extends ConditionDataPermissionHandler {
    @Override
    public boolean determineUseDataPermission(BaseQry baseQry) {
        // 2 表示用户选择了会员类型权限
        return DataPermissionTypeEnum.VIP_TYPE_DATA_PERMISSION.getCode().equals(baseQry.getDataPermissionType());
    }
}
