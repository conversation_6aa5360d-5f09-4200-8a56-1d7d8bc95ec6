package com.iqiyi.vip.handler;

import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/11 16:00
 */
@Component
public class FvConditionDataPermissionHandler extends ConditionDataPermissionHandler {
    @Override
    public boolean determineUseDataPermission(BaseQry baseQry) {
        // 1 表示用户选择了渠道、产品端平台权限
        return DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode().equals(baseQry.getDataPermissionType());
    }
}
