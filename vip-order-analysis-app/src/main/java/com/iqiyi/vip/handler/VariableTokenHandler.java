package com.iqiyi.vip.handler;

import org.apache.ibatis.parsing.TokenHandler;

import java.util.Properties;

/**
 * @className VariableTokenHandler
 * @description
 * <AUTHOR>
 * @date 2022/5/17
 **/
public class VariableTokenHandler implements TokenHandler {
    private final Properties variables;
    private final boolean enableDefaultValue;
    private final String defaultValueSeparator;

    public VariableTokenHandler(Properties variables) {
        this.variables = variables;
        this.enableDefaultValue = Boolean.parseBoolean(this.getPropertyValue("org.apache.ibatis.parsing.PropertyParser.enable-default-value", "true"));
        this.defaultValueSeparator = this.getPropertyValue("org.apache.ibatis.parsing.PropertyParser.default-value-separator", ":");
    }

    private String getPropertyValue(String key, String defaultValue) {
        return this.variables == null ? defaultValue : this.variables.getProperty(key, defaultValue);
    }

    public String handleToken(String content) {
        if (this.variables != null) {
            String key = content;
            if (this.enableDefaultValue) {
                int separatorIndex = content.indexOf(this.defaultValueSeparator);
                String defaultValue = null;
                if (separatorIndex >= 0) {
                    key = content.substring(0, separatorIndex);
                    defaultValue = content.substring(separatorIndex + this.defaultValueSeparator.length());
                }

                if (defaultValue != null) {
                    return this.variables.getProperty(key, defaultValue);
                }
            }

            if (this.variables.containsKey(key)) {
                return this.variables.getProperty(key);
            }
        }

        return "${" + content + "}";
    }
}
