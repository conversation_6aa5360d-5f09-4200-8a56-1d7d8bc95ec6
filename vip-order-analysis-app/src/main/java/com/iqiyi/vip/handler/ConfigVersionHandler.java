package com.iqiyi.vip.handler;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.service.ConfigVersionProcessor;

/**
 * <AUTHOR>
 * @className ConfigVersionHandler
 * @description
 * @date 2022/11/25
 **/
@Component
@Slf4j
public class ConfigVersionHandler implements InitializingBean, ApplicationContextAware {


    private static final Map<Integer, ConfigVersionProcessor> configTypeToHandlerMap = Maps.newHashMap();

    private ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("ConfigVersionHandler-init start!");
        applicationContext.getBeansOfType(ConfigVersionProcessor.class).values()
            .forEach(handler -> {
                Integer processType = handler.getProcessType();
                log.info("ConfigVersionProcessor-init:register handler {}  for {} agreement", handler.getClass().getSimpleName(), processType);
                configTypeToHandlerMap.put(processType, handler);
            });
        log.info("ConfigVersionHandler-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public ConfigVersionProcessor getProcessor(Integer configType) {
        return configTypeToHandlerMap.get(configType);
    }
}
