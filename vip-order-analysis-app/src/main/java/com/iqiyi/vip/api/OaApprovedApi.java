package com.iqiyi.vip.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.oa.OaApprovedDetail;
import com.iqiyi.vip.dto.oa.OaApprovedResponse;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.UrlAssembleUtils;

/**
 * <AUTHOR>
 * @date 2022/8/19 17:00
 */
@Component
@Slf4j
public final class OaApprovedApi extends BaseApi {

    @Resource
    private RestTemplate restTemplate;

    @Value("${oa.approved.url:http://devops.vip.online.qiyi.qae/vip-devops/api/v1/workOrder/add}")
    private String oaApprovedUrl;
    @Value("${oa.approved.type:common}")
    private String defaultOaApprovedType;
    @ConfigJsonValue("${themeType.to.oa.approved.type.map:{\"6\":\"trade-order2\"}}")
    private Map<String, String> themeTypeToOaApprovedTypeMap;
    @Value("${oa.approved.callback.url:http://order.analysis.online.qiyi.qae/order-analysis/permission/approval-callback}")
    private String oaApprovedCallbackUrl;

    public DataResult<OaApprovedResponse> submitApproved(String account, OaApprovedDetail oaApprovedDetail, Integer themeType) {
        String oaApprovedType = MapUtils.getString(themeTypeToOaApprovedTypeMap, themeType.toString(), defaultOaApprovedType);
        Map<String, Object> params = new HashMap<>();
        params.put("messageId", oaApprovedDetail.getMessageId()); // voa( vip-order-analysis )
        params.put("description", "天眼平台数据权限");
        params.put("applicant", account);
        params.put("type", oaApprovedType);
        params.put("details", oaApprovedDetail.oaDetails());
        params.put("sensitived", false);
        params.put("audit", true);
        params.put("systemCode", "vip-order-analysis");
        params.put("woCallbackUrl", oaApprovedCallbackUrl);
        Map<String, Object> response = null;
        try {
            response = doPostByJson(restTemplate, oaApprovedUrl.concat("?token=kouri43uriuj4k3o5uifyfioq3iui&sysCode=vip-order-analysis"), params, "oa-approved-".concat(account), new ParameterizedTypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            try {
                log.error("[order-analysis][oa-approved] fail. ", e);
                response = doPostByJson(restTemplate, oaApprovedUrl.concat("?token=kouri43uriuj4k3o5uifyfioq3iui&sysCode=vip-order-analysis"), params, "oa-approved-".concat(account), new ParameterizedTypeReference<Map<String, Object>>() {
                });
            } catch (Exception e1) {
                log.error("[order-analysis][oa-approved] fail 2.", e1);
                throw new BizRuntimeException(CodeEnum.OA_APPROVED_ERROR);
            }
        }

        if (response != null && CodeEnum.SUCCESS.getCode().equalsIgnoreCase((String) response.get("code"))) {
            final Map<String, Object> data = (Map<String, Object>) response.get("data");
            if (data != null && Boolean.TRUE.equals(data.get("result"))) {
                final String acId = (String) data.get("acId");
                final String messageId = (String) data.get("messageId");
                return DataResult.success(OaApprovedResponse.builder().acId(acId).messageId(messageId).build());
            }
        }
        throw new BizRuntimeException(CodeEnum.OA_APPROVED_ERROR);
    }

    public String queryMessageId(String acId) {
        Map<String, Object> params = new HashMap<>();
        params.put("acId", acId);
        params.put("token", "kouri43uriuj4k3o5uifyfioq3iui");
        params.put("sysCode", "vip-order-analysis");
        URI uri = null;
        BaseResponse<String> response = null;
        try {
            uri = new URI(UrlAssembleUtils.make("http://devops.vip.online.qiyi.qae/vip-devops/api/v1/workOrder/queryMessageId", params));
            response = doGet(restTemplate, uri, "queryMessageIdByAcId-".concat(acId), new ParameterizedTypeReference<BaseResponse<String>>() {
            });
        } catch (Exception e) {
            try {
                log.error("queryMessageIdByAcId fail. ", e);
                response = doGet(restTemplate, uri, "queryMessageIdByAcId-".concat(acId), new ParameterizedTypeReference<BaseResponse<String>>() {
                });
            } catch (Exception e1) {
                log.error("queryMessageIdByAcId fail 2.", e1);
                throw new BizRuntimeException(CodeEnum.OA_APPROVED_ERROR);
            }
        }

        if (response != null && CodeEnum.SUCCESS.getCode().equalsIgnoreCase(response.getCode())) {
            String data = response.getData();
            if (data != null) {
                return data;
            }
        }
        throw new BizRuntimeException(CodeEnum.OA_APPROVED_ERROR);
    }

}
