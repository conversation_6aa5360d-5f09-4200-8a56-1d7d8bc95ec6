package com.iqiyi.vip.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.iqiyi.job.core.biz.client.AdminBizClient;
import com.iqiyi.job.core.biz.model.JobInfoParam;
import com.iqiyi.job.core.biz.model.JobPartParam;
import com.iqiyi.job.core.biz.model.ReturnT;
import com.iqiyi.job.core.enums.AlarmTypeEnum;
import com.iqiyi.job.core.glue.GlueTypeEnum;
import com.iqiyi.vip.dto.realmonitor.TimeIntervalPair;
import com.iqiyi.vip.enums.OffsetTimeUnit;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/3/5 23:42
 */
@Slf4j
@Component
public class QiyiJobApi {

    private static final String FIVE_MINUTE_INTERVAL_SCHEDULE_CONF = "0 0/5 * * * ?";

    public static final String BIZ_TARGET_MONITOR_DATA_PRODUCT_JOB_HANDLER = "bizTargetMonitorDataProduceHandler";

    @Resource(name = "qiyiJobClient")
    private AdminBizClient qiyiJobClient;

    @Value("${vip.job.executor.appname}")
    private String executorAppName;

    /**
     * 创建定时任务
     * @param jobDesc
     * @param jobCreator
     * @param execFrequency
     * @param executorParam
     * @param alertReceivers
     */
    public Integer createJob(String jobDesc, String jobCreator, TimeIntervalPair execFrequency, String executorParam, List<String> alertReceivers) {
        String alertReceiverStr = CollectionUtils.isNotEmpty(alertReceivers)
            ? StringUtils.join(alertReceivers, ",")
            : jobCreator;
        String cronExpression = getCronExpression(execFrequency);
        JobInfoParam jobInfoParam = new JobInfoParam();
        jobInfoParam.setExecutorAppName(executorAppName);
        jobInfoParam.setJobDesc(jobDesc);
        jobInfoParam.setAuthor(jobCreator);
        jobInfoParam.setExecutorParam(executorParam);
        jobInfoParam.setAlarmEmail(alertReceiverStr);
        jobInfoParam.setScheduleType("CRON");
        jobInfoParam.setScheduleConf(cronExpression != null ? cronExpression : FIVE_MINUTE_INTERVAL_SCHEDULE_CONF);
        jobInfoParam.setGlueType(GlueTypeEnum.BEAN.name());
        jobInfoParam.setExecutorHandler(QiyiJobApi.BIZ_TARGET_MONITOR_DATA_PRODUCT_JOB_HANDLER);
        jobInfoParam.setAlarmType(AlarmTypeEnum.HOT_CHAT.getValue());
        jobInfoParam.setExecutorFailRetryCount(2);
        ReturnT<Map<String, String>> createResp = qiyiJobClient.createJobInfo(jobInfoParam);
        if (createResp.isFail()) {
            log.error("create job failed, param:{}, resp:{}", JacksonUtils.toJsonString(jobInfoParam), createResp);
            return null;
        }
        return MapUtils.getInteger(createResp.getContent(), "JobId");
    }

    /**
     * 创建并启动定时任务
     */
    public Integer createAndResumeJob(String jobDesc, String jobCreator, TimeIntervalPair execFrequency, String executorParam, List<String> alertReceivers) {
        Integer jobId = createJob(jobDesc, jobCreator, execFrequency, executorParam, alertReceivers);
        if (jobId == null) {
            return null;
        }
        resumeJob(jobId);
        return jobId;
    }

    /**
     * 获取定时任务的cron表达式
     * @param execFrequency
     */
    private String getCronExpression(TimeIntervalPair execFrequency) {
        if (execFrequency == null) {
            return null;
        }
        int interval = execFrequency.getTimeValue();
        OffsetTimeUnit timeUnit = execFrequency.getTimeUnit();
        switch (timeUnit) {
            case s:
                if (interval < 1) interval = 1;
                if (interval >= 60) {
                    int minutes = interval / 60;
                    return String.format("0 0/%d * * * ?", minutes);
                } else {
                    return String.format("0/%d * * * * ?", interval);
                }
            case m:
                return String.format("0 0/%d * * * ?", interval);
            case h:
                return String.format("0 0 0/%d * * ?", interval);
            case d:
                // 每天的特定时间执行，这里设置为每天0点执行
                return String.format("0 30 3 1/%d * ?", interval);
            case w:
                // 每周的特定时间执行，这里设置为每周日0点执行
                return String.format("0 30 5 ? * 1/%d", interval);
            default:
                log.error("未知的时间单位: {}", timeUnit);
                return FIVE_MINUTE_INTERVAL_SCHEDULE_CONF; // 默认5分钟执行一次
        }
    }

    /**
     * 更新定时任务
     */
    public boolean updateJob(Integer jobId, TimeIntervalPair execFrequency, String executorParam) {
        JobPartParam jobPartParam = new JobPartParam();
        jobPartParam.setId(jobId);
        if (execFrequency != null) {
            jobPartParam.setJobCron(getCronExpression(execFrequency));
        }
        if (executorParam != null) {
            jobPartParam.setExecutorParam(executorParam);
        }
        jobPartParam.setExecutorAppName(executorAppName);
        ReturnT<String> updateResp = qiyiJobClient.updateJobInfoPart(jobPartParam);
        if (updateResp.isFail()) {
            log.error("update job failed, param:{}, resp:{}", JacksonUtils.toJsonString(jobPartParam), updateResp);
            return false;
        }
        return true;
    }

    /**
     * 删除定时任务
     */
    public boolean deleteJob(Integer jobId) {
        JobPartParam jobPartParam = new JobPartParam();
        jobPartParam.setId(jobId);
        jobPartParam.setExecutorAppName(executorAppName);
        ReturnT<String> deleteResp = qiyiJobClient.deleteJob(jobPartParam);
        if (deleteResp.isFail()) {
            log.error("delete job failed, param:{}, resp:{}", JacksonUtils.toJsonString(jobPartParam), deleteResp);
            return false;
        }
        return true;
    }

    /**
     * 启动定时任务
     */
    public boolean resumeJob(Integer jobId) {
        JobPartParam jobPartParam = new JobPartParam();
        jobPartParam.setId(jobId);
        jobPartParam.setExecutorAppName(executorAppName);
        ReturnT<String> resumeResp = qiyiJobClient.resume(jobPartParam);
        if (resumeResp.isFail()) {
            log.error("resume job failed, param:{}, resp:{}", JacksonUtils.toJsonString(jobPartParam), resumeResp);
            return false;
        }
        return true;
    }

    /**
     * 停止定时任务
     */
    public boolean pauseJob(Integer jobId) {
        JobPartParam jobPartParam = new JobPartParam();
        jobPartParam.setId(jobId);
        jobPartParam.setExecutorAppName(executorAppName);
        ReturnT<String> pauseResp = qiyiJobClient.pause(jobPartParam);
        if (pauseResp.isFail()) {
            log.error("pause job failed, param:{}, resp:{}", JacksonUtils.toJsonString(jobPartParam), pauseResp);
            return false;
        }
        return true;
    }

}
