package com.iqiyi.vip.api;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListeners;
import com.google.common.collect.Lists;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.DataResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.UrlAssembleUtils;

/**
 * <AUTHOR>
 * @date 2022/8/19 17:00
 */
@Component
@Slf4j
public class DevOpsPermissionApi extends BaseApi {

    @Resource
    private RestTemplate restTemplate;

    // http://wiki.qiyi.domain/pages/viewpage.action?pageId=1290671386#id-%E6%9D%83%E9%99%90%E6%8E%A7%E5%88%B6%E5%BC%80%E6%94%BEAPI-3.%E7%94%A8%E6%88%B7%E8%A7%92%E8%89%B2%EF%BC%8C%E8%8F%9C%E5%8D%95%E5%8F%8A%E9%A1%B5%E9%9D%A2%E5%85%83%E7%B4%A0%E5%90%88%E5%B9%B6%E6%9F%A5%E8%AF%A2%E6%8E%A5%E5%8F%A3
    @Value("${devops.permission.url:http://devops.vip.online.qiyi.qae/vip-devops/api/v1/user/userRoleAndResource}")
    private String devOpsPermissionUrl;

    // http://atlas.qiyi.domain/system/interface/detail?interfaceId=3fad1bb9eccf42fbafa8bb7d49e290d3&branch=master&version=currentVersion&path=undefined
    @Value("${devops.add.user.role.url:http://devops.vip.online.qiyi.qae/vip-devops/api/v1/auth/user/add}")
    private String devopsAddUserRoleUrl;

    private LoadingCache<String, Boolean> loadingCache = CacheBuilder.newBuilder()
        //设置并发级别为8，并发级别是指可以同时写缓存的线程数
        .concurrencyLevel(Runtime.getRuntime().availableProcessors())
        //设置缓存容器的初始容量为10
        .initialCapacity(2)
        //设置缓存最大容量为100，超过100之后就会按照LRU最近虽少使用算法来移除缓存项
        .maximumSize(8)
        //是否需要统计缓存情况,该操作消耗一定的性能,生产环境应该去除
        .recordStats()
        //设置写缓存后n秒钟过期
        .expireAfterWrite(60, TimeUnit.SECONDS)
        //设置读写缓存后n秒钟过期,实际很少用到,类似于expireAfterWrite
        //.expireAfterAccess(17, TimeUnit.SECONDS)
        //只阻塞当前数据加载线程，其他线程返回旧值
        //.refreshAfterWrite(13, TimeUnit.SECONDS)
        //设置缓存的移除通知
        .removalListener(RemovalListeners.asynchronous(notification -> log.warn(
                notification.getKey() + " " + notification.getValue() + " 被移除,原因:" + notification.getCause()),
            new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors(), 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(1 << 10))))
        //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存z
        .build(new CacheLoader<String, Boolean>() {
            @Override
            public Boolean load(String account) throws Exception {
                return DevOpsPermissionApi.this.init(account);
            }
        });

    /**
     * 会员订单分析平台高级用户（拥有全权限）
     * @param account
     * @return
     */
    public boolean vipOrderAnalysisHighLevelUser(String account) {
        try {
            return loadingCache.get(account);
        } catch (ExecutionException e) {
            log.error("[order-analysis] [cache error] fail. ", e);
        }
        return false;
    }

    public boolean init(String account) {
        Map<String, Object> params = new HashMap<>();
        params.put("oaAccount", account);
        params.put("sysCode", "vip-order-analysis");
        params.put("token", "kouri43uriuj4k3o5uifyfioq3iui");
        BaseResponse<Map<String, Object>> response = null;
        URI uri = null;
        try {
            uri = new URI(UrlAssembleUtils.make(devOpsPermissionUrl, params));
            response = doGet(restTemplate, uri, "devops-permission-".concat(account), new ParameterizedTypeReference<BaseResponse<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            try {
                log.error("[order-analysis][devops-permission] fail. ", e);
                response = doGet(restTemplate, uri, "devops-permission-".concat(account), new ParameterizedTypeReference<BaseResponse<Map<String, Object>>>() {
                });
            } catch (Exception e1) {
                log.error("[order-analysis][devops-permission] fail 2.", e1);
                throw new BizRuntimeException(CodeEnum.DEVOPS_PERMISSION_REQUEST_ERROR);
            }
        }

        if (response != null && CodeEnum.SUCCESS.getCode().equalsIgnoreCase(response.getCode()) && response.getData() != null) {
            final Map<String, Object> data = response.getData();
            final List<Map<String, Object>> roleList = (List<Map<String, Object>>) data.get("roleList");
            if (roleList != null && !roleList.isEmpty()) {
                return roleList.stream().anyMatch(v -> v.getOrDefault("roleCode", "").equals("order_analysis_developer")
                    || v.getOrDefault("roleCode", "").equals("order_analysis_manager"));
            }
        }
        return false;
    }

    public CommonResult submitUserRole(String account) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("messageId", "voa-oa-approved-" + UUID.randomUUID()); // voa( vip-order-analysis )
        params.put("description", "天眼平台数据权限");
        params.put("oaAccount", account);
        params.put("roleList", Lists.newArrayList("order_analysis_normaluser"));
        params.put("status", 1);
        params.put("systemCode", "vip-order-analysis");
        Map<String, Object> response = null;
        try {
            response = doPostByJson(restTemplate, devopsAddUserRoleUrl.concat("?token=kouri43uriuj4k3o5uifyfioq3iui&sysCode=vip-order-analysis"), params, "oa-approved-".concat(account), new ParameterizedTypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            try {
                log.error("[order-analysis][oa-approved] fail. ", e);
                response = doPostByJson(restTemplate, devopsAddUserRoleUrl.concat("?token=kouri43uriuj4k3o5uifyfioq3iui&sysCode=vip-order-analysis"), params, "oa-approved-".concat(account), new ParameterizedTypeReference<Map<String, Object>>() {
                });
            } catch (Exception e1) {
                log.error("[order-analysis][oa-approved] fail 2.", e1);
                throw e1;
            }
        }

        if (response != null && CodeEnum.SUCCESS.getCode().equalsIgnoreCase((String) response.get("code"))) {
            final Integer data = (Integer) response.get("data");
            if (data != null && data.equals(1)) {
                return DataResult.success();
            }
        }

        return DataResult.create(CodeEnum.ERROR_SYSTEM);
    }

}
