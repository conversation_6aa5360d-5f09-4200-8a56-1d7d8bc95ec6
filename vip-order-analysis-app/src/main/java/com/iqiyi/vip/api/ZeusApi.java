package com.iqiyi.vip.api;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.zeus.ZeusMonitorSaveParam;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * @AUTHOR: GUOJING
 * @DATE: 2025/3/6 23:37
 */
@Slf4j
@Component
public class ZeusApi extends BaseApi {

    private static final String CREATE_MONITOR_URL = "/zeus/monitor/tianyan/create";
    private static final String UPDATE_MONITOR_URL = "/zeus/monitor/tianyan/update";
    private static final String DELETE_MONITOR_URL = "/zeus/monitor/tianyan/delete";
    private static final String GET_MONITOR_URL = "/zeus/monitor/tianyan/getDetailById";
    private static final String EXIST_MONITOR_BY_RULE_NAME_URL = "/zeus/monitor/tianyan/existMonitorByRuleName";

    @Value("${zeus.domain:http://devops.vip.online.qiyi.qae}")
    private String zeusDomain;

    @Resource(name = "zeusClient")
    private RestTemplate restTemplate;

    public Integer createMonitor(String operator, ZeusMonitorSaveParam createParam) {
        String requestUrl = zeusDomain + CREATE_MONITOR_URL + "?oaAccount=" + operator;
        BaseResponse<Integer> response;
        try {
            response = doPostByJson(restTemplate, requestUrl, createParam, "请求宙斯创建监控", new ParameterizedTypeReference<BaseResponse<Integer>>() {
            });
        } catch (Exception e) {
            try {
                log.error("[order-analysis][ZeusApi-createMonitor] fail, requestUrl:{}", requestUrl, e);
                response = doPostByJson(restTemplate, requestUrl, createParam, "重试请求宙斯创建监控", new ParameterizedTypeReference<BaseResponse<Integer>>() {});
            } catch (Exception e1) {
                log.error("[order-analysis][ZeusApi-createMonitor] retry fail, requestUrl:{}", requestUrl, e);
                throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_SAVE_ERROR);
            }
        }
        if (response.isFailure()) {
            log.error("[order-analysis][ZeusApi-createMonitor] fail, requestUrl:{}, respCode:{}, respMsg:{}", requestUrl, response.getCode(), response.getMsg());
            throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_SAVE_ERROR);
        }
        return response.getData();
    }

    public boolean updateMonitor(String operator, ZeusMonitorSaveParam updateParam) {
        String requestUrl = zeusDomain + UPDATE_MONITOR_URL + "?oaAccount=" + operator;
        BaseResponse<Boolean> response;
        try {
            response = doPostByJson(restTemplate, requestUrl, updateParam, "请求宙斯更新监控", new ParameterizedTypeReference<BaseResponse<Boolean>>() {
            });
        } catch (Exception e) {
            try {
                log.error("[order-analysis][ZeusApi-updateMonitor] fail, requestUrl:{}", requestUrl, e);
                response = doPostByJson(restTemplate, requestUrl, updateParam, "重试请求宙斯更新监控", new ParameterizedTypeReference<BaseResponse<Boolean>>() {});
            } catch (Exception e1) {
                log.error("[order-analysis][ZeusApi-updateMonitor] retry fail, requestUrl:{}", requestUrl, e);
                throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_UPDATE_ERROR);
            }
        }
        if (response.isFailure()) {
            log.error("[order-analysis][ZeusApi-updateMonitor] fail, requestUrl:{}, respCode:{}, respMsg:{}", requestUrl, response.getCode(), response.getMsg());
            throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_UPDATE_ERROR);
        }
        return response.getData();
    }

    public boolean deleteMonitor(String operator, Integer monitorId) {
        if (monitorId == null) {
            return false;
        }
        Map<String, String> params = new HashMap<>();
        params.put("id", monitorId.toString());
        String requestUrl = zeusDomain + DELETE_MONITOR_URL + "?oaAccount=" + operator;
        BaseResponse<Boolean> response;
        try {
            response = doPostByFormData(restTemplate, requestUrl, params, "请求宙斯删除监控", new ParameterizedTypeReference<BaseResponse<Boolean>>() {});
        } catch (Exception e) {
            try {
                log.error("[order-analysis][ZeusApi-deleteMonitor] fail, requestUrl:{}", requestUrl, e);
                response = doPostByFormData(restTemplate, requestUrl, params, "重试请求宙斯删除监控", new ParameterizedTypeReference<BaseResponse<Boolean>>() {});
            } catch (Exception e1) {
                log.error("[order-analysis][ZeusApi-deleteMonitor] retry fail, requestUrl:{}", requestUrl, e);
                throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_DELETE_ERROR);
            }
        }
        if (response.isFailure()) {
            log.error("[order-analysis][ZeusApi-deleteMonitor] fail, requestUrl:{}, respCode:{}, respMsg:{}", requestUrl, response.getCode(), response.getMsg());
            throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_DELETE_ERROR);
        }
        return response.getData();
    }

    public String getByMonitorId(String operator, Integer monitorId) {
        if (monitorId == null) {
            return null;
        }
        String params = String.format("oaAccount=%s&monitorId=%d", operator, monitorId);
        String requestUrl = zeusDomain + GET_MONITOR_URL + "?" + params;
        URI uri = URI.create(requestUrl);
        BaseResponse<Boolean> response;
        try {
            response = doGet(restTemplate, uri, "请求宙斯查询监控详情", new ParameterizedTypeReference<BaseResponse<Boolean>>() {});
        } catch (Exception e) {
            try {
                log.error("[order-analysis][ZeusApi-getByMonitorId] fail, requestUrl:{}", requestUrl, e);
                response = doGet(restTemplate, uri, "重试请求宙斯删除监控", new ParameterizedTypeReference<BaseResponse<Boolean>>() {});
            } catch (Exception e1) {
                log.error("[order-analysis][ZeusApi-getByMonitorId] retry fail, requestUrl:{}", requestUrl, e);
                throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_SELECT_ERROR);
            }
        }
        return null;
    }

    public Integer existMonitorByRuleName(String operator, String ruleName) {
        if (StringUtils.isBlank(ruleName)) {
            return null;
        }
        String params = String.format("oaAccount=%s&smartAlertRuleName=%s", operator, ruleName);
        String requestUrl = zeusDomain + EXIST_MONITOR_BY_RULE_NAME_URL + "?" + params;
        URI uri = URI.create(requestUrl);
        BaseResponse<Integer> response;
        try {
            response = doGet(restTemplate, uri, "请求宙斯校验智能告警规则名称判断监控是否存在", new ParameterizedTypeReference<BaseResponse<Integer>>() {});
        } catch (Exception e) {
            try {
                log.error("[order-analysis][ZeusApi-existMonitorByRuleName] fail, requestUrl:{}", requestUrl, e);
                response = doGet(restTemplate, uri, "请求宙斯校验智能告警规则名称判断监控是否存在", new ParameterizedTypeReference<BaseResponse<Integer>>() {});
            } catch (Exception e1) {
                log.error("[order-analysis][ZeusApi-existMonitorByRuleName] retry fail, requestUrl:{}", requestUrl, e);
                throw new BizRuntimeException(CodeEnum.ZEUS_MONITOR_SELECT_ERROR);
            }
        }
        return response.getData();
    }

}
