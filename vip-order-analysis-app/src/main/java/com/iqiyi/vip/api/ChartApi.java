package com.iqiyi.vip.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.imageio.stream.FileImageOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/29 下午 01:46
 */
@Component
@Slf4j
public class ChartApi extends BaseApi{

    @Value("${chart.url:http://vfb.vip.qiyi.domain/chartsImg/create}")
    private String chartUrl;

    @Resource(name = "chartClient")
    private RestTemplate restTemplate;

    public void downloadCharts(Map<String, Object> paramsMap, String logHeader, String fileName) {
        try {
            byte[] bytes = doPostByJson(restTemplate, chartUrl, paramsMap, logHeader, new ParameterizedTypeReference<byte[]>() {});
            byte2image(bytes, fileName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void byte2image(byte[] data,String path){
        if (data.length < 3 || StringUtils.isBlank(path)) {
            return;
        }
        try{
            FileImageOutputStream imageOutput = new FileImageOutputStream(new File(path));
            imageOutput.write(data, 0, data.length);
            imageOutput.close();
            System.out.println("Make Picture success,Please find image in " + path);
        } catch(Exception ex) {
            System.out.println("Exception: " + ex);
            ex.printStackTrace();
        }
    }


}
