package com.iqiyi.vip.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class HttpClient {

    public Map getRemoteResponse(String url, Map<String, Object> params, RestTemplate restTemplate) {
        Map result = new HashMap();
        try {
            log.info("[HTTP-get][url:{}][params:{}][request:{}]", url, params, result);
            //创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map> entity = new HttpEntity<Map>(params, headers);
            ResponseEntity<HashMap> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity, HashMap.class);
            result = responseEntity.getBody();
            log.info("[HTTP-get][url:{}][params:{}][result:{}]", url, params, result);
            return result;
        } catch (Exception e) {
            log.error("[HTTP-get][Exception][url:{}][params:{}]", url, params, e);
            result.put("cause", e.getCause());
        }
        result.put("code", "ERROR_TIME_OUT");
        result.put("msg", "TIMEOUT");
        return result;
    }
}
