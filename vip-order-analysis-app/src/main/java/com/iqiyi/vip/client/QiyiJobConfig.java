package com.iqiyi.vip.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.job.core.biz.client.AdminBizClient;

/**
 * @author: guojing
 * @date: 2025/3/5 23:38
 */
@Slf4j
@Configuration
public class QiyiJobConfig {

    @Value("${vip.job.accessToken}")
    private String accessToken;
    @Value("${vip.job.admin.addresses}")
    private String qiyiJobAdminAddress;

    @Bean(name = "qiyiJobClient")
    public AdminBizClient qiyiJobClient() {
        return new AdminBizClient(qiyiJobAdminAddress, accessToken);
    }

}
