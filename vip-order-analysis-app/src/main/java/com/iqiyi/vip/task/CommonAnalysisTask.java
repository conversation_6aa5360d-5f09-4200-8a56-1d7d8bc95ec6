package com.iqiyi.vip.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.IdUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Supplier;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.File;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.ConditionPayEndTimeDTO;
import com.iqiyi.vip.dto.condition.FvChannelReqDTO;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.GroupTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;
import com.iqiyi.vip.service.AnalysisResultStorageService;
import com.iqiyi.vip.service.TaskQueryInfoService;
import com.iqiyi.vip.util.DynamicEasyExcelExportUtils;
import com.iqiyi.vip.util.GenericTokenParsers;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtils;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static com.iqiyi.vip.constant.Constants.CLICKHOUSE_WIDE_TABLE_NAME;
import static com.iqiyi.vip.constant.Constants.CONTACT_INFO;
import static com.iqiyi.vip.constant.Constants.HIVE_WIDE_TABLE_NAME;
import static com.iqiyi.vip.constant.Constants.STARROCKS_REAL_ORDER_TABLE_NAME;
import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

/**
 * <AUTHOR>
 * @className CommonAnalysisAsyncTask
 * @description
 * @date 2022/6/6
 **/
@Component
@Slf4j
public class CommonAnalysisTask extends AnalysisTask {

    public static final int EXCEL_MAX_LINE = 1048570;

    @Resource
    protected AnalysisDimensionRepository analysisDimensionRepository;

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;
    @Resource
    private AnalysisResultStorageService storageService;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    @Value("${user.group.ck.table:dist_vip_user_group_order_analysis}")
    private String userGroupCKTable;

    @Resource
    private CloudConfig cloudConfig;

    @Value("${excel.width.adjust:0}")
    private Integer excelWidthAdjust;

    @Resource
    protected TaskQueryInfoService taskQueryInfoService;

    @Resource
    private AnalysisConditionRepository conditionRepository;

    @Resource
    private AvailableDateRepository availableDateRepository;

    /**
     * 设置任务正在处理
     */
    public void setTaskRunning(AnalysisTaskExecuteDTO executeDTO) {
        Long taskId = executeDTO.getTaskId();
        String operator = executeDTO.getOperator();
        AnalysisTaskDO analysisTaskDO = analysisTaskRepository.getTaskByIdAndOperator(taskId, operator);
        String taskResult = analysisTaskDO.getResult();
        if (StringUtils.isNotBlank(taskResult)) {
            storageService.deleteResult(taskResult);
        }
        analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.RUNNING.getStatus(), null, null);
    }

    public List<TargetAnalysisQueryDTO> constructTargetAnalysisQueryDTO(AnalysisTaskExecuteDTO executeDTO, List<AnalysisTarget> analysisTargets, boolean sync) {
        List<String> dimensionCodes = executeDTO.getDimensionCodes();
        // 维度
        Integer businessTypeId = executeDTO.getBusinessTypeId();
        Integer themeType = executeDTO.getThemeType();
        List<AnalysisDimension> analysisDimensions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dimensionCodes)) {
            analysisDimensions = analysisDimensionRepository.selectByCodes(dimensionCodes, businessTypeId, themeType);
        }
        // 构造查询条件
        List<TargetAnalysisQueryDTO> list = constructQueryDTO(analysisTargets, analysisDimensions, executeDTO);
        Constants.BUSINESS_LOG.info("AnalysisTaskExecuteDTO:{}, sync:{}, queryInfo:{}", executeDTO, sync, list);
        return list;
    }


    public void processAsync(AnalysisTaskExecuteDTO executeDTO) throws Exception {
        StopWatch stopWatch = new StopWatch(getStopWatchName(executeDTO));
        stopWatch.start();
        TimeInterval asyncTimeInterval = DateUtil.timer();
        setTaskRunning(executeDTO);
        String operator = executeDTO.getOperator();
        Long taskId = executeDTO.getTaskId();
        String taskName = executeDTO.getTaskName();
        if (StringUtils.isNotBlank(taskName)) {
            taskName =  taskName.replace(".", "-").replace(" ", "");
            executeDTO.setTaskName(taskName);
        }
        List<AnalysisTarget> analysisTargets = executeDTO.getTargetCodes().stream()
            .map(analysisTargetRepository::selectByCode)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        String title = generateMailTitle(executeDTO, analysisTargets);
        try {
            // 1、大数据查询 sql 处理
            List<TargetAnalysisQueryDTO> analysisQueryDTOS = constructTargetAnalysisQueryDTO(executeDTO, analysisTargets, false);
            if (CollectionUtils.isEmpty(analysisQueryDTOS)) {
                setTaskFailedWithMail(taskId, operator, "未配置SQL模板，请联系维护人员查看情况");
                return;
            }
            TaskQueryInfo taskQueryInfo = TaskQueryInfo.buildFrom(taskId, JacksonUtils.toJsonString(analysisQueryDTOS));
            taskQueryInfoService.saveOrUpdate(taskQueryInfo);

            List<OriginalQueryResultDTO> queryResultDTOS = executeQuery(analysisQueryDTOS, taskId);
            // 此次是指标失败
            if (CollectionUtils.isEmpty(queryResultDTOS) || queryResultDTOS.stream().noneMatch(OriginalQueryResultDTO::getSuccess)) {
                stopWatch.stop();
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
                return;
            }

            List<ExcelSheetDataDTO> excelSheetDataDTOS = queryResultDTOS.stream()
                .filter(OriginalQueryResultDTO::getSuccess)
                .filter(OriginalQueryResultDTO::commonTargetType)
                .map(ExcelSheetDataDTO::from)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(excelSheetDataDTOS)) {
                log.info("no need generate excel");
                stopWatch.stop();
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
                return;
            }
            if (exceededExcelLineLimit(excelSheetDataDTOS)) {
                stopWatch.stop();
                alterService.sendHotChat(String.format("指标分析超出Excel行数限制！任务id：%s, 操作人：%s, 运行环境：%s", taskId, operator, System.getProperty("spring.profiles.active")));
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
                return;
            }

            // 3、Excel 表头更换成中文名称
            for (ExcelSheetDataDTO excelSheetDataDTO : excelSheetDataDTOS) {
                LinkedHashMap<String, String> changeNameHashMap = new LinkedHashMap<>();
                LinkedHashMap<String, String> headColumnMap = excelSheetDataDTO.getHeadColumnMap();
                for (Entry<String, String> entry : headColumnMap.entrySet()) {
                    Integer businessTypeId = executeDTO.getBusinessTypeId();
                    Integer themeType = executeDTO.getThemeType();
                    String targetName = TargetDimensionHandler.getTargetName(businessTypeId, analysisTargetRepository, entry.getKey(), null);
                    String dimensionName = TargetDimensionHandler
                            .getDimensionName(businessTypeId, themeType, analysisDimensionRepository, entry.getKey(), entry.getValue());
                    String name = targetName == null ? dimensionName : targetName;
                    changeNameHashMap.put(entry.getKey(), name);
                }
                excelSheetDataDTO.setHeadColumnMap(changeNameHashMap);
            }

            // 4、Excel 导出，存储到机器上
            String excelFileName = DynamicEasyExcelExportUtils.generateResultExcelFile2(excelSheetDataDTOS, operator, localFilePath
                    , code -> analysisTargetRepository.selectByCode(code)
                    , code -> {
                        final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
                        if (analysisTarget != null && analysisTarget.getGroup() != null) {
                            return analysisTargetGroupRepository.get(analysisTarget.getGroup());
                        }
                        return null;
                    }, taskId, executeDTO, excelWidthAdjust);

            if (StringUtils.isBlank(excelFileName)) {
                stopWatch.stop();
                alterService.sendHotChat(String.format("指标分析结果为空！任务id：%s, 操作人：%s, 运行环境：%s", taskId, operator, System.getProperty("spring.profiles.active")));
                analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
                return;
            }

            String absoluteResultFilePath = localFilePath + "/" + excelFileName;
            ArrayList<String> fileAttachments = Lists.newArrayList(absoluteResultFilePath);
            // 上传 Excel 到 OSS
            storageService.uploadResultFile(new File(absoluteResultFilePath), excelFileName);
            stopWatch.stop();
            analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), excelFileName, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            log.info("executeQuery end cost:{}, executeDTO:{}, result:{}", asyncTimeInterval.intervalMinute(), executeDTO, excelFileName);

            String tempFilePath = null;
            if (cloudConfig.getProperty("need.send.query.info.person", "").contains(operator)) {
                tempFilePath = localFilePath + "/" + IdUtil.simpleUUID() + "_sql.txt";
                FileUtil.touch(tempFilePath);
                FileWriter writer = new FileWriter(tempFilePath);
                for (TargetAnalysisQueryDTO analysisQueryDTO : analysisQueryDTOS) {
                    writer.write(analysisQueryDTO.getTargetCode(), true);
                    writer.write("\n", true);
                    writer.write(analysisQueryDTO.getQuerySql(), true);
                    writer.write("\n", true);
                }
                fileAttachments.add(tempFilePath);
            }
            mailManager.sendExcelResultsMail(fileAttachments, operator, title, getContactInfo(queryResultDTOS));
            // 5、清除机器上导出的 Excel 文件
            cleanLocalFile(absoluteResultFilePath);
            if (StringUtils.isNotBlank(tempFilePath)) {
                cleanLocalFile(tempFilePath);
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("processAsync error ! taskId:{}, operator:{}", taskId, operator, e);
            analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            alterService.sendHotChat(String.format("指标分析失败！任务id：%s, 操作人：%s, 运行环境：%s", taskId, operator, System.getProperty("spring.profiles.active")));
            mailManager.sendExcelResultsMail(null, operator, title, getContactInfo(OriginalQueryResultDTO.buildFailedResults(executeDTO.getTargetCodes())));
        }
    }

    private String getContactInfo(List<OriginalQueryResultDTO> queryResultDTOS) {
        List<String> failedTargetCodes = queryResultDTOS.stream()
            .filter(r -> !r.getSuccess())
            .map(OriginalQueryResultDTO::getTargetCode)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failedTargetCodes)) {
            return CONTACT_INFO;
        }
        String failedTargetNames = analysisTargetRepository.selectByCodes(failedTargetCodes)
            .stream()
            .map(AnalysisTarget::getName)
            .collect(Collectors.joining("、"));
        return failedTargetNames + "指标查询失败，其余指标" + CONTACT_INFO;
    }

    public static String getStopWatchName(AnalysisTaskExecuteDTO executeDTO) {
        return "order-".concat(ObjectUtils.isEmpty(executeDTO.getTaskName()) ? "" : executeDTO.getTaskName()).concat(executeDTO.getOperator());
    }

    public String generateMailTitle(AnalysisTaskExecuteDTO executeDTO, List<AnalysisTarget> analysisTargets) {
        // 指标
        String fileNamePattern = "【天眼指标分析结果】%s：%s";
        String analysisTargetNames = analysisTargets.stream().map(AnalysisTarget::getName).collect(Collectors.joining("、"));
        String detailTitle = StringUtils.isBlank(executeDTO.getTaskName()) ?
            analysisTargetNames.substring(0, Math.min(analysisTargetNames.length(), 20)).concat("……")
            : executeDTO.getTaskName();
        return String.format(fileNamePattern, executeDTO.getTaskId(), detailTitle);
    }

    private boolean exceededExcelLineLimit(List<ExcelSheetDataDTO> excelSheetDataDTOS) {
        return excelSheetDataDTOS.stream().anyMatch(c -> c.getDataList() != null && c.getDataList().size() >= EXCEL_MAX_LINE);
    }

    private List<TargetAnalysisQueryDTO> constructQueryDTO(List<AnalysisTarget> analysisTargets, List<AnalysisDimension> analysisDimensions, AnalysisTaskExecuteDTO executeDTO) {
        Integer businessTypeId = executeDTO.getBusinessTypeId();
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        String operator = executeDTO.getOperator();
        Long taskId = executeDTO.getTaskId();

        ArrayList<TargetAnalysisQueryDTO> targetAnalysisQueryDTOS = new ArrayList<>();
        List<AnalysisTargetGroup> analysisTargetGroups = analysisTargetGroupRepository
                .selectByBusinessType(businessTypeId == null ? 1 : businessTypeId, executeDTO.getThemeType());

        Map<Integer, AnalysisTargetGroup> toMap = analysisTargetGroups.stream()
                .collect(Collectors.toMap(AnalysisTargetGroup::getId, v -> v, (v1, v2) -> v1));

        Map<Boolean, List<AnalysisTarget>> collect = analysisTargets.stream()
                .collect(Collectors.partitioningBy(v -> {
                    Integer type = toMap.get(v.getGroup()).getType();
                    return type != null && type.equals(2) || CloudConfigUtils.transUserGroup(v.getCode());
                })); // Boolean.TRUE 是父指标，Boolean.FALSE 是子指标。只有转移分析会使用子指标

        for (AnalysisTarget target : collect.get(Boolean.TRUE)) {
            // 循环指标，构造指标查询条件
            Properties properties = constructProperties(executeDTO, analysisDimensions, target,
                collect.get(Boolean.FALSE), taskId, businessTypeId);
            // 特殊逻辑，ARPU指标需要批量查询返回。例如分析时间区间为 2022-05-01 - 2022-10-31，则需要迭代成05、06、07、08、09、10的时间区间，6个sql分析查询模板。
            if (needSpecialProcess(target)) {
                Long payStartTimeLong = conditionParamContext.getPayStartTime();
                Long payEndTimeLong = conditionParamContext.getPayEndTime();
                final LocalDateTime payStartTime = DateUtils.retBeginningOfMonth(payStartTimeLong);// 订单支付开始时间
                final LocalDateTime payEndTime = DateUtils.retEndOfTheMonth(payEndTimeLong); // 订单支付结束时间
                final long dateDistance = DateUtils.dateDistance(payStartTime, payEndTime);
                for (long idx = 0; idx <= dateDistance; idx++) {
                    final LocalDateTime nowMonth = payStartTime.plusMonths(idx);
                    this.arpuDeadlineWriteToParroperties(properties, ConditionPayEndTimeDTO
                        .of(DateUtils.toMilliseconds(payStartTime.plusMonths(idx + 1))));
                    if (isRemainRelated(target)) {
                        reWriteRemainRelatedProperties(properties, conditionParamContext, idx);
                    }
                    // 依据指标解析成对应的完整sql
                    TargetAnalysisQueryDTO targetAnalysisQueryDTO = this.constructQueryDTO(properties, target, operator);
                    if (targetAnalysisQueryDTO != null) {
                        targetAnalysisQueryDTO.setDateYM(DateUtils.ymDataTimeFormat(nowMonth));
                        targetAnalysisQueryDTOS.add(targetAnalysisQueryDTO); // 解析后的指标sql集合录入
                    }
                }
            } else {
                // 依据指标解析成对应的完整sql
                final TargetAnalysisQueryDTO targetAnalysisQueryDTO = this.constructQueryDTO(properties, target, operator);
                if (targetAnalysisQueryDTO != null) {
                    targetAnalysisQueryDTOS.add(targetAnalysisQueryDTO); // 解析后的指标sql集合录入
                }
            }
        }
        return targetAnalysisQueryDTOS;
    }

    private static boolean needSpecialProcess(AnalysisTarget target) {
        if (StringUtils.isBlank(target.getCode())) {
            return false;
        }
        return target.getCode().toUpperCase(Locale.ROOT).contains(Constants.TARGET_ANALYSIS_ARPU_CHARACTERISTIC)
            || (isRemainRelated(target) && CloudConfigUtils.enableAnalysisRemainByMonth());
    }

    private static boolean isRemainRelated(AnalysisTarget target) {
        return target.getCode().toLowerCase(Locale.ROOT).contains(Constants.TARGET_ANALYSIS_REAMIN_CHARACTERISTIC);
    }

    private void reWriteRemainRelatedProperties(Properties properties, ConditionParamContext context, long idx) {
        Long payStartTime = context.getPayStartTime();
        Long payEndTime = context.getPayEndTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将long类型时间戳转换为LocalDateTime
        LocalDateTime payStartTimeLocal = LocalDateTime.ofInstant(
            Instant.ofEpochMilli(payStartTime),
            ZoneId.systemDefault()
        );

        LocalDateTime payEndTimeLocal = LocalDateTime.ofInstant(
            Instant.ofEpochMilli(payEndTime),
            ZoneId.systemDefault()
        );

        if (idx == 0) {
            properties.setProperty("payStartDate", payStartTimeLocal.format(ISO_LOCAL_DATE));
            properties.setProperty("beforePayStartDate", payStartTimeLocal.minusDays(1).format(ISO_LOCAL_DATE));

            LocalDateTime endOfCurrentMonth = getEndOfCurrentMonth(payStartTimeLocal);
            long endOfCurrentMonthLong = DateUtils.toMilliseconds(endOfCurrentMonth);
            LocalDateTime conditionEndDate = payEndTime <= endOfCurrentMonthLong ? payEndTimeLocal : endOfCurrentMonth;
            properties.setProperty("payEndDate", conditionEndDate.format(ISO_LOCAL_DATE));
            properties.setProperty("beforePayEndDate", conditionEndDate.minusDays(1).format(ISO_LOCAL_DATE));
            properties.setProperty("payEndTime", conditionEndDate.format(formatter));
            return;
        }

        final LocalDateTime payStartTimeMonthBegin = DateUtils.retBeginningOfMonth(payStartTime);
        // 订单支付开始时间的月初 + 月份偏移量
        final LocalDateTime nowMonth = payStartTimeMonthBegin.plusMonths(idx);

        properties.setProperty("payStartDate", nowMonth.format(ISO_LOCAL_DATE));
        properties.setProperty("beforePayStartDate", nowMonth.minusDays(1).format(ISO_LOCAL_DATE));

        LocalDateTime endOfCurrentMonth = getEndOfCurrentMonth(nowMonth);
        long endOfCurrentMonthLong = DateUtils.toMilliseconds(endOfCurrentMonth);
        LocalDateTime conditionEndDate = payEndTime <= endOfCurrentMonthLong ? payEndTimeLocal : endOfCurrentMonth;
        properties.setProperty("payEndDate", conditionEndDate.format(ISO_LOCAL_DATE));
        properties.setProperty("payEndTime", conditionEndDate.format(formatter));
        properties.setProperty("beforePayEndDate", conditionEndDate.minusDays(1).format(ISO_LOCAL_DATE));
    }

    private static LocalDateTime getEndOfCurrentMonth(LocalDateTime payStartTimeLocal) {
        //获取Long payStartTime对应的当月最后时间，而不是最初时间
        return payStartTimeLocal.with(TemporalAdjusters.lastDayOfMonth())
            .withHour(23)
            .withMinute(59)
            .withSecond(59)
            .withNano(999999999);
    }

    /**
     * sql 解析
     */
    private TargetAnalysisQueryDTO constructQueryDTO(Properties properties, AnalysisTarget target, String operator) {
        // 依据指标解析成对应的完整sql
        String sql = parseSqlByProperties(properties, target.getSqlTemplateId());
        return StringUtils.isNotBlank(sql) ? TargetAnalysisQueryDTO.builder()
            .operator(operator)
            .querySql(sql)
            .targetCode(target.getCode()).targetName(target.getName())
            .businessTypeName(target.getBusinessTypeName())
            .dataBase(properties.getProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode()))
            .build() : null;
    }


    /**
     * @param unionAnalysisTargets 联合指标，是analysisTarget指标的子指标，也需要落到 sql 中。只有转移分析会用到
     */
    private Properties constructProperties(AnalysisTaskExecuteDTO executeDTO, List<AnalysisDimension> analysisDimensions,
                                           AnalysisTarget analysisTarget, List<AnalysisTarget> unionAnalysisTargets,
                                           Long taskId, Integer businessTypeId) {
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        Long endTime = conditionParamContext.getPayEndTime();
        DateTime payEndTime = DateUtil.date(endTime);
        String payEndTimeStr = DateUtil.format(DateUtil.date(payEndTime), NORM_DATETIME_PATTERN);

        Long startTime = conditionParamContext.getPayStartTime();
        DateTime payStartTime = DateUtil.date(startTime);
        String payStartTimeStr = DateUtil.format(payStartTime, NORM_DATETIME_PATTERN);
        LocalDate payStartDate = LocalDate.parse(payStartTimeStr.substring(0, 10));
        LocalDate payEndDate = LocalDate.parse(payEndTimeStr.substring(0, 10));

        Properties variables = new Properties();
        variables.setProperty("payStartTime", payStartTimeStr);
        variables.setProperty("payStartDate", payStartDate.toString());
        variables.setProperty("beforePayStartDate", payStartDate.minusDays(1).toString());
        variables.setProperty("payEndTime", payEndTimeStr);
        variables.setProperty("payEndDate", payEndDate.toString());
        variables.setProperty("beforePayEndDate", payEndDate.minusDays(1).toString());

        Integer ltPeriod = conditionParamContext.getLtPeriod();
        if (ltPeriod != null) {
            variables.setProperty("ltPeriod", String.valueOf(ltPeriod));
            DateTime ltPeriodEndTime = DateUtil.offsetMonth(payEndTime, ltPeriod);
            String ltPeriodEndTimeStr = DateUtil.format(ltPeriodEndTime, NORM_DATETIME_PATTERN);
            long ltPeriodDuration = DateUtil.between(payEndTime, ltPeriodEndTime, DateUnit.DAY, false);
            variables.setProperty("ltPeriodEndTime", ltPeriodEndTimeStr);
            variables.setProperty("ltPeriodDuration", String.valueOf(ltPeriodDuration));
        }
        Integer userGroupId = conditionParamContext.getUserGroup();
        if (Objects.nonNull(userGroupId)) {
            UserGroupDO userGroupDO = userGroupRepository.queryById(userGroupId);
            if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroupDO.getGroupType())) {
                String str = " inner join (select * from %s as ug where user_group_id = '%s') userGroup\n"
                    + "    on base.passport_id = userGroup.passport_id";
                variables.setProperty("userGroupUid", String.format(str, userGroupCKTable, userGroupId));
            } else {
                String str = " inner join (select * from %s as ug where user_group_id = '%s') userGroup\n"
                    + "    on base.passport_id = userGroup.passport_id and base.order_code=userGroup.order_code";
                variables.setProperty("userGroupOrderCode", String.format(str, userGroupCKTable, userGroupId));
            }
        }

        Integer refundPeriod = conditionParamContext.getRefundPeriod();
        if (refundPeriod != null) {
            variables.setProperty("refundPeriod", String.valueOf(refundPeriod));
        }

        Integer minusLtPeriod = conditionParamContext.getMinusLtPeriod();
        if (minusLtPeriod != null) {
            DateTime minusLtPeriodStartTime = DateUtil.offsetMonth(payStartTime, -minusLtPeriod);
            String minusLtPeriodStartTimeStr = DateUtil.format(minusLtPeriodStartTime, NORM_DATETIME_PATTERN);
            long minusLtPeriodDuration = DateUtil.between(minusLtPeriodStartTime, payStartTime, DateUnit.DAY, false);
            variables.setProperty("minusLtPeriodStartTime", minusLtPeriodStartTimeStr);
            variables.setProperty("minusLtPeriodDuration", String.valueOf(minusLtPeriodDuration));
        }

        Integer autoRenewLtPeriodMonth = conditionParamContext.getAutoRenewLtPeriodMonth();
        if (autoRenewLtPeriodMonth != null) {
            variables.setProperty("autoRenewLtPeriodMonth", String.valueOf(autoRenewLtPeriodMonth));
            DateTime autoRenewLtPeriodMonthEndTime = DateUtil.offsetMonth(payEndTime, autoRenewLtPeriodMonth);
            String autoRenewLtPeriodMonthStr = DateUtil.format(autoRenewLtPeriodMonthEndTime, NORM_DATETIME_PATTERN);
            long autoRenewLtPeriodDuration = DateUtil.between(payEndTime, autoRenewLtPeriodMonthEndTime, DateUnit.DAY, false);
            variables.setProperty("autoRenewLtPeriodMonthEndTime", autoRenewLtPeriodMonthStr);
            variables.setProperty("autoRenewLtPeriodDuration", String.valueOf(autoRenewLtPeriodDuration));
        }

        AnalysisTaskSourceEnum taskSource = executeDTO.getTaskSource();
        String tableDT = getOrderTableDT();
        Integer themeType = executeDTO.getThemeType();
        String maxDt = availableDateRepository.maxDate(themeType).getTableDt();
        String targetValue = analysisTarget.getValue();
        String targetCode = analysisTarget.getCode();
        if (taskSource == AnalysisTaskSourceEnum.SCHEDULED) {
            log.info("targetCode: {}, use StarRocks", targetCode);
            variables.setProperty("tableName", STARROCKS_REAL_ORDER_TABLE_NAME);
            variables.setProperty("dateBase", DataBaseTypeEnum.STAR_ROCKS.getCode());
            targetValue = this.ckSqlHandler(targetCode, targetValue);
        } else {
            boolean useClickhouse = couldUseClickhouse(targetCode);
            executeDTO.setUseCK(useClickhouse);
            if (useClickhouse) {
                log.info("targetCode: {}, couldUseClickhouse", targetCode);
                variables.setProperty("tableName", CLICKHOUSE_WIDE_TABLE_NAME);
                variables.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());
                targetValue = this.ckSqlHandler(targetCode, targetValue);
            } else {
                log.info("targetCode: {}, could not UseClickhouse", targetCode);

                variables.setProperty("tableName", HIVE_WIDE_TABLE_NAME);
                variables.setProperty("hiveDt", tableDT);
                variables.setProperty("dt", tableDT);
                variables.setProperty("maxDt", maxDt);
                variables.setProperty("dateBase", DataBaseTypeEnum.HIVE.getCode());
            }
        }

        Long compareStartTime = conditionParamContext.getCompareStartTime();
        Long compareEndTime = conditionParamContext.getCompareEndTime();
        if (compareStartTime != null && compareEndTime != null) {
            variables.setProperty("compareStartTime", DateUtil.format(DateUtil.date(compareStartTime), NORM_DATETIME_PATTERN));
            variables.setProperty("compareStartDate", DateUtil.format(DateUtil.date(compareStartTime), NORM_DATE_FORMAT));
            variables.setProperty("compareEndTime", DateUtil.format(DateUtil.date(compareEndTime), NORM_DATETIME_PATTERN));
            variables.setProperty("compareEndDate", DateUtil.format(DateUtil.date(compareEndTime), NORM_DATE_FORMAT));
        }

        this.arpuDeadlineWriteToParroperties(variables, ConditionPayEndTimeDTO.of(conditionParamContext.getPayEndTime()));

        ConditionDataPermissionHandler conditionDataPermissionHandler = ConditionDataPermissionHandler.getConditionDataPermissionHandler(executeDTO);
        List<Integer> vipTypes = conditionParamContext.getVipTypes();
        if (CollectionUtils.isNotEmpty(vipTypes)) {
            List<String> vipTypes2Str = conditionDataPermissionHandler.intersectionWithOwnedDataPermissions(executeDTO, vipTypes.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList()),
                ConditionEnum.VIP_TYPE, LabelEnum.VT2); // 与已申请权限取交集 -- 会员类型
            variables.setProperty("vipTypes", Joiner.on(",").join(vipTypes2Str));
        }
        List<Integer> vipGroupIds = conditionParamContext.getGroupIds();
        if (CollectionUtils.isNotEmpty(vipGroupIds)) {
            List<String> vipGroupIds2Str = conditionDataPermissionHandler
                    .intersectionWithOwnedDataPermissions(executeDTO, vipGroupIds.stream().map(String::valueOf).collect(Collectors.toList()),
                            ConditionEnum.VIP_TYPE, LabelEnum.VT1); // 与已申请权限取交集 -- 会员分组
            variables.setProperty("vipGroup", Joiner.on(",").join(vipGroupIds2Str));
        }
        String ugType = conditionParamContext.getUgType();
        if (StringUtils.isNotBlank(ugType)) {
            fillUgType(variables, ugType);
        }


        Map<String, List<Object>> conditionParamMap = conditionParamContext.getParamMap();
        for (Map.Entry<String, List<Object>> entry : conditionParamMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            String code = entry.getKey();
            if (variables.get(code) != null) {
                continue;
            }
            AnalysisCondition condition = conditionRepository.selectByCode(code);
            if (condition == null) {
                continue;
            }
            String propValue = constructCommonConditionPropValue(code, conditionParamMap, condition);
            if (StringUtils.isNotBlank(propValue)) {
                variables.setProperty(code, propValue);
            }
        }

        if (CollectionUtils.isNotEmpty(analysisDimensions)) {
            String dimensionCodes = TargetDimensionHandler.retDimensionCodes(analysisDimensions, unionAnalysisTargets, taskId, businessTypeId);
            String dimensionValues = this
                    .ckSqlHandler(targetCode, TargetDimensionHandler.retDimensionValues(analysisDimensions, unionAnalysisTargets, businessTypeId));
            variables.setProperty("dimensionCodes", dimensionCodes);
            variables.setProperty("dimensionValues", GenericTokenParsers.parserSql(variables, dimensionValues));

            String dimensionCodesWithOutTimeType = TargetDimensionHandler
                    .retDimensionCodesWithOutTimeType(analysisDimensions, taskId, businessTypeId);
            String dimensionValuesWithOutTimeType = TargetDimensionHandler.retDimensionValuesWithOutTimeType(analysisDimensions, businessTypeId);
            if (StringUtils.isNotBlank(dimensionCodesWithOutTimeType)) {
                variables.setProperty("dimensionCodesWithOutTimeType", dimensionCodesWithOutTimeType);
                variables.setProperty("dimensionValuesWithOutTimeType", GenericTokenParsers.parserSql(variables, dimensionValuesWithOutTimeType));
            }

            String multiColumnJoinStr = Joiner.on(" and ")
                    .join(analysisDimensions.stream().map(AnalysisDimension::getCode)
                            .map(c -> String.format("COALESCE(m.%s,'NULL') = COALESCE(n.%s, 'NULL')", c, c))
                            .collect(Collectors.toList()));
            if (StringUtils.isNotBlank(multiColumnJoinStr)) {
                variables.setProperty("multiColumnJoinStr", multiColumnJoinStr);
            }

            String multiColumnJoinStrWithOutTimeType = Joiner.on(" and ").join(
                    analysisDimensions.stream()
                            .filter(d -> d.getGroup() != null && d.getGroup() != 3)
                            .map(AnalysisDimension::getCode)
                            .map(c -> String.format("COALESCE(m.%s,'NULL') = COALESCE(n.%s, 'NULL')", c, c))
                            .collect(Collectors.toList())
            );
            if (StringUtils.isNotBlank(multiColumnJoinStrWithOutTimeType)) {
                variables.setProperty("multiColumnJoinStrWithOutTimeType", multiColumnJoinStrWithOutTimeType);
            }
        }
        this.baseDataPermissionsCtrl(variables, executeDTO);

        this.fillBaseQueryCondition(variables, executeDTO);

        this.fillThemeBaseQueryCondition(variables, executeDTO);

        fillStoreVipAbTest(variables, executeDTO);

        fillAbExperimentCondition(variables, executeDTO);

        variables.setProperty("targetValues", GenericTokenParsers.parserSql(variables, targetValue));

        log.info("constructProperties result:{}", variables);
        return variables;
    }

    /**
     * 处理UG归因类型
     *
     * @param variables
     * @param ugType
     */
    private void fillUgType(Properties variables, String ugType) {
        if (ugType.equals("none")) {
            return;
        }
        if (ugType.equals("t0")) {
            variables.setProperty("ugType", "( ug_click_dt=dt )");
        } else if (ugType.equals("t7")) {
            variables.setProperty("ugType", "( ug_click_dt>formatDateTime(toDate(dt)-7, '%Y-%m-%d') and ug_click_dt<=dt )");
        } else if (ugType.equals("t30")) {
            variables.setProperty("ugType", "( ug_click_dt>formatDateTime(toDate(dt)-30, '%Y-%m-%d') and ug_click_dt<=dt )");
        } else if (ugType.equals("season")) {
            variables.setProperty("ugType", "( ug_click_dt>=formatDateTime(toStartOfQuarter(toDate(dt)), '%Y-%m-%d') and ug_click_dt<=dt )");
        } else if (ugType.equals("all")) {
            variables.setProperty("ugType", "( coalesce(ug_click_dt,'') not in ('UNKNOWN', '') )");
        }
    }

    public static Map<LabelEnum, Supplier<List<FvChannelReqDTO>>> initUserPermissionParamsGetFuncMap(EnumMap<LabelEnum, Supplier<List<FvChannelReqDTO>>> labelEnumSupplierMap, ConditionParamContext conditionParamContext) {
        if (labelEnumSupplierMap == null) {
            labelEnumSupplierMap = Maps.newEnumMap(LabelEnum.class);
        }
        List<String> teamIds = conditionParamContext.getTeamIds();
        final List<FvChannelReqDTO> fvChannelReqDTOS =
                CollectionUtils.isEmpty(teamIds) ? null : teamIds.stream() // 团队
                        .map(v -> FvChannelReqDTO.builder()
                                .level(LabelEnum.T.getLevel())
                                .id(v)
                                .teamId(Integer.valueOf(v))
                                .build()
                        ).collect(Collectors.toList());
        // TODO 临时注释
        Integer teamId;

        if (CollectionUtils.isNotEmpty(teamIds)) {
            teamId = Integer.valueOf(teamIds.get(0));
        } else {
            teamId = null;
        }
        labelEnumSupplierMap.put(LabelEnum.L2, () -> conditionParamContext.getLevel2BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L2.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.T, () -> fvChannelReqDTOS);
        labelEnumSupplierMap.put(LabelEnum.L3, () -> conditionParamContext.getLevel3BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L3.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.L4, () -> conditionParamContext.getLevel4BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L4.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.L5, () -> conditionParamContext.getLevel5BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L5.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.L6, () -> conditionParamContext.getLevel6BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L6.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.L7, () -> conditionParamContext.getLevel7BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L7.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        labelEnumSupplierMap.put(LabelEnum.L8, () -> conditionParamContext.getLevel8BusinessIds()
                .stream()
                .filter(Objects::nonNull)
                .map(id -> new FvChannelReqDTO(LabelEnum.L8.getLevel(), id.toString(), teamId))
                .collect(Collectors.toList()));
        return labelEnumSupplierMap;
    }
}
