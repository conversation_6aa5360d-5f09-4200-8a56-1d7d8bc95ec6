package com.iqiyi.vip.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.db.sql.SqlExecutor;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisSqlTemplateRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.OrderRepository;
import com.iqiyi.vip.domain.repository.UserGroupRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.ConditionPayEndTimeDTO;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.FiledTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.LeafNodeProcessTypeEnum;
import com.iqiyi.vip.enums.TargetGroupOptionEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.handler.FvChannelLeafNodeHandler;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.mail.MailManager;
import com.iqiyi.vip.service.AlterService;
import com.iqiyi.vip.service.DataPermissionService;
import com.iqiyi.vip.service.FvChannelLeafNodeProcessor;
import com.iqiyi.vip.util.GenericTokenParsers;
import com.iqiyi.vip.utils.ConditionParamUtils;
import com.iqiyi.vip.utils.DateUtils;

import static com.iqiyi.vip.constant.Constants.TARGET_ANALYSIS_REAMIN_CHARACTERISTIC;

/**
 * <AUTHOR>
 * @className AnalysisTask
 * @description
 * @date 2023/10/31
 **/
@Component
@Slf4j
public class AnalysisTask {

    @Resource
    private CloudConfig cloudConfig;
    @Resource
    protected AnalysisConditionRepository conditionRepository;
    @Resource
    protected DataPermissionService dataPermissionService;
    @Resource
    private FvChannelLeafNodeHandler fvChannelLeafNodeHandler;
    @Resource(name = "analysisSqlTemplateRepositoryImpl")
    protected AnalysisSqlTemplateRepository analysisSqlTemplateRepository;
    @Resource(name = "analysisSqlTemplateCKRepositoryImpl")
    protected AnalysisSqlTemplateRepository analysisSqlTemplateCKRepository;
    @Resource
    protected AnalysisTaskRepository analysisTaskRepository;
    @Resource
    protected MailManager mailManager;
    @Resource(name = "fastThreadPoolExecutor")
    private ExecutorService fastThreadPoolExecutor;
    @Resource(name = "slowThreadPoolExecutor")
    private ExecutorService slowThreadPoolExecutor;
    @Resource(name = "checkThreadPoolExecutor")
    private ExecutorService checkThreadPoolExecutor;

    @Resource(name = "diagnosisThreadPoolExecutor")
    private ExecutorService diagnosisThreadPoolExecutor;
    @Resource(name = "scheduleTaskThreadPoolExecutor")
    private ExecutorService scheduleTaskThreadPoolExecutor;
    @Resource
    protected AlterService alterService;
    @Resource
    private OrderRepository orderRepository;

    @Resource
    protected UserGroupRepository userGroupRepository;

    @Resource
    protected AnalysisTargetRepository analysisTargetRepository;

    @Value("${dt.offset:2}")
    private Integer offset;
    @Value("${scheduledTask.feiShu.receiver:zhouguojing}")
    private String scheduledTaskFeiShuReceiver;

    public boolean couldUseClickhouse(String targetCode) {
        return enableClickhouseEngine() && clickhouseEngineWhiteList().contains(targetCode);
    }

    private boolean enableClickhouseEngine() {
        return cloudConfig.getBooleanProperty("enable.clickhouse.engine", true);
    }

    private List<String> clickhouseEngineWhiteList() {
        return Splitter.on(",").splitToList(cloudConfig.getProperty("ck.targets.whiteList", ""))
            .stream().map(String::trim)
            .collect(Collectors.toList());
    }

    public String getOrderTableDT() {
        return DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.date(), -offset)));
    }

    public String ckSqlHandler(String targetCode, String targetSql) {
        return couldUseClickhouse(targetCode) ? targetSql.replace("format_number", "round").replace("FORMAT_NUMBER", "round")
            : targetSql;
    }

    public void arpuDeadlineWriteToParroperties(Properties variables, ConditionPayEndTimeDTO payEndTime) {
        variables.setProperty("arpuDeadline", DateUtils.dateDatetimeFormat(payEndTime.getPayEndTime()));
    }

    /**
     * 构建通用的条件sql
     */
    public String constructCommonConditionPropValue(String code, Map<String, List<Object>> conditionParamMap, AnalysisCondition condition) {
        if (condition == null) {
            return "";
        }
        if (Constants.CLAUSE_OP_LIKE.equals(condition.getClauseOp())) {
            String value = ConditionParamUtils.getSingleValue(conditionParamMap, code, String.class);
            if (StringUtils.isBlank(value)) {
                return "";
            }
            return "'%" + value + "%'";
        }
        if (FiledTypeEnum.isString(condition.getFieldType())) {
            List<String> listValue = ConditionParamUtils.getListValue(conditionParamMap, code, String.class)
                .stream()
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(listValue)) {
                log.error("Get string list value is null. code:{}", code);
                return "";
            }
            return listValue.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
        }
        if (FiledTypeEnum.isInteger(condition.getFieldType())) {
            List<Long> listValue = ConditionParamUtils.getListValue(conditionParamMap, code, Long.class)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(listValue)) {
                log.error("Get integer list value is null. code:{}", code);
                return "";
            }
            return Joiner.on(",").join(listValue);
        }
        log.error("Condition fileType error. code:{}", code);
        return "";
    }


    public void fillBaseQueryCondition(Properties variables, AnalysisTaskExecuteDTO executeDTO) {
        List<String> conditionCodeList = getBaseQueryConditionCodeList();
        if (CollectionUtils.isEmpty(conditionCodeList)) {
            return;
        }
        StringBuilder baseQuerySQL = new StringBuilder(" ( ");
        Map<String, List<Object>> paramMap = executeDTO.getConditionParamContext().getParamMap();
        List<String> conditionSqlList = new ArrayList<>();
        constructBaseConditionSqlList(executeDTO, conditionCodeList, paramMap, conditionSqlList, executeDTO.isUseCK());
        if (CollectionUtils.isNotEmpty(conditionSqlList)) {
            String sql = Joiner.on(" and ").join(conditionSqlList);
            variables.setProperty("baseQueryCondition", baseQuerySQL.append(sql).append(" )").toString());
        }
    }

    public void fillThemeBaseQueryCondition(Properties variables, AnalysisTaskExecuteDTO executeDTO) {
        if (executeDTO.getThemeType().equals(ThemeTypeEnum.STORE.getCode())) {
            List<String> storeConditionCodeList = getStoreThemeStoreBaseQueryConditionCodeList();
            if (CollectionUtils.isNotEmpty(storeConditionCodeList)) {
                StringBuilder baseQuerySQL = new StringBuilder(" ( ");
                Map<String, List<Object>> paramMap = executeDTO.getConditionParamContext().getParamMap();
                List<String> conditionSqlList = new ArrayList<>();
                constructBaseConditionSqlList(executeDTO, storeConditionCodeList, paramMap, conditionSqlList, executeDTO.isUseCK());
                if (CollectionUtils.isNotEmpty(conditionSqlList)) {
                    String sql = Joiner.on(" and ").join(conditionSqlList);
                    variables.setProperty("storeThemeStoreBaseQueryCondition", baseQuerySQL.append(sql).append(" )").toString());
                }
            }
            List<String> orderConditionCodeList = getStoreThemeOrderBaseQueryConditionCodeList();
            if (CollectionUtils.isNotEmpty(orderConditionCodeList)) {
                StringBuilder baseQuerySQL = new StringBuilder(" ( ");
                Map<String, List<Object>> paramMap = executeDTO.getConditionParamContext().getParamMap();
                List<String> conditionSqlList = new ArrayList<>();
                constructBaseConditionSqlList(executeDTO, orderConditionCodeList, paramMap, conditionSqlList,executeDTO.isUseCK());
                if (CollectionUtils.isNotEmpty(conditionSqlList)) {
                    String sql = Joiner.on(" and ").join(conditionSqlList);
                    variables.setProperty("storeThemeOrderBaseQueryCondition", baseQuerySQL.append(sql).append(" )").toString());
                }
            }
        }
    }

    public void fillAbExperimentCondition(Properties variables, AnalysisTaskExecuteDTO executeDTO) {
        Integer themeType = executeDTO.getThemeType();
        if (!ObjectUtils.equals(themeType, ThemeTypeEnum.ORDER_THEME.getCode()) && !ObjectUtils.equals(themeType, ThemeTypeEnum.AUTO_RENEW_THEME.getCode())) {
            return;
        }
        boolean experimentDimension = executeDTO.getDimensionCodes().stream().anyMatch(c -> StringUtils.isNotBlank(c) && c.contains("abExperiment"));
        if (experimentDimension) {
            variables.setProperty("experimentDimension", "LATERAL VIEW explode(ab_config) exploded_table AS abExperiment, abExperimentGroup");
            variables.setProperty("experimentDimensionCK", " ARRAY JOIN JSONExtractKeysAndValues(ab_config, 'String') AS ab_config_pair");
            variables.setProperty("abConfigPair", " , ab_config_pair");
        }
        boolean useCK = executeDTO.isUseCK();
        String dimensionValues = variables.getProperty("dimensionValues");
        if (useCK && StringUtils.isNotBlank(dimensionValues)) {
            String newDimensionValues = dimensionValues
                .replaceAll("\\babExperimentGroup\\b", "ab_config_pair.2 as abExperimentGroup")
                .replaceAll("\\babExperiment\\b", "ab_config_pair.1 as abExperiment");
            variables.setProperty("dimensionValues", newDimensionValues);
        }
        ConditionParamContext paramContext = executeDTO.getConditionParamContext();
        List<String> abExperiments = paramContext.getAbExperiments();
        if (CollectionUtils.isEmpty(abExperiments)) {
            return;
        }
        List<String> abConditions = abExperiments.stream()
            .filter(StringUtils::isNotBlank)
            .map(String::trim)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abConditions)) {
            return;
        }
        String sql;
        String tempCondition = " ( " + abConditions.stream().map(c -> "'" + c + "'").collect(Collectors.joining(",")) + " ) ";
        if (useCK) {
            if (experimentDimension) {
                sql = String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(ab_config, 'String'))) AND ab_config_pair.1 IN %s", tempCondition, tempCondition);
            } else {
                sql = String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(ab_config, 'String')))", tempCondition);
            }
        } else {
            if (experimentDimension) {
               sql = String.format(" abExperiment in %s ", tempCondition);
            } else {
                List<String> hiveAbConditions = abConditions.stream()
                    .map(d -> String.format("array_contains(map_keys(ab_config), '%s')", d))
                    .collect(Collectors.toList());
                sql = Joiner.on(" or ").join(hiveAbConditions);
            }
        }

        String baseQueryCondition = variables.getProperty("baseQueryCondition", null);

        if (StringUtils.isNotBlank(baseQueryCondition)) {
            variables.setProperty("baseQueryCondition", baseQueryCondition + " and ( " + sql + " )");
        } else {
            variables.setProperty("baseQueryCondition", " ( " + sql + " )");
        }
    }

    public void fillStoreVipAbTest(Properties variables, AnalysisTaskExecuteDTO executeDTO) {
        Integer themeType = executeDTO.getThemeType();
        if (!Objects.equals(themeType, ThemeTypeEnum.STORE.getCode())) {
            return;
        }
        boolean useCK = executeDTO.isUseCK();
        boolean experimentDimension = executeDTO.getDimensionCodes().stream().anyMatch(c -> StringUtils.isNotBlank(c) && c.contains("abExperiment"));
        if (experimentDimension) {
            variables.setProperty("experimentDimension", " LATERAL VIEW explode(vip_abtest_config) exploded_table AS abExperiment, abExperimentGroup");
            variables.setProperty("experimentDimensionCK", " ARRAY JOIN JSONExtractKeysAndValues(vip_abtest_config, 'String') AS ab_config_pair");
            variables.setProperty("abConfigPair", " , ab_config_pair");
        }
        if (useCK) {
            String dimensionValues = variables.getProperty("dimensionValues");
            String newDimensionValues = dimensionValues
                .replaceAll("\\babExperimentGroup\\b", "ab_config_pair.2 as abExperimentGroup")
                .replaceAll("\\babExperiment\\b", "ab_config_pair.1 as abExperiment");
            variables.setProperty("dimensionValues", newDimensionValues);
        }
        ConditionParamContext paramContext = executeDTO.getConditionParamContext();
        List<String> vipAbTest = paramContext.getVipAbTest();
        if (CollectionUtils.isEmpty(vipAbTest)) {
            return;
        }
        String tempCondition = " ( " + vipAbTest.stream().map(c -> "'" + c + "'").collect(Collectors.joining(",")) + " ) ";
        if (useCK) {
            if (experimentDimension) {
                variables.setProperty("storeAbTest", String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(vip_abtest_config, 'String'))) AND ab_config_pair.1 IN %s", tempCondition, tempCondition));
                variables.setProperty("orderAbTest", String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(order_vip_abtest_config, 'String'))) AND ab_config_pair.1 IN %s", tempCondition, tempCondition));
            } else {
                variables.setProperty("storeAbTest", String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(vip_abtest_config, 'String'))) ", tempCondition));
                variables.setProperty("orderAbTest", String.format(" arrayExists(k -> k IN %s, arrayMap(pair -> pair.1, JSONExtractKeysAndValues(order_vip_abtest_config, 'String'))) ", tempCondition));
            }
        } else {
            List<String> hiveAbConditions = vipAbTest.stream()
                .map(d -> String.format(" array_contains(map_keys(vip_abtest_config), '%s')", d))
                .collect(Collectors.toList());
            String sql = "(" + Joiner.on(" or ").join(hiveAbConditions) + " )";
            variables.setProperty("storeAbTest", sql);
            variables.setProperty("orderAbTest", sql);
        }
    }

    private void constructBaseConditionSqlList(AnalysisTaskExecuteDTO executeDTO, List<String> conditionCodeList, Map<String, List<Object>> paramMap, List<String> conditionSqlList, boolean useCK) {
        for (String code : conditionCodeList) {
            if (CollectionUtils.isEmpty(paramMap.get(code))) {
                continue;
            }
            AnalysisCondition condition = conditionRepository.selectByCode(code);
            if (condition == null) {
                continue;
            }
            String propValue = constructCommonConditionPropValue(code, paramMap, condition).trim();
            if (StringUtils.isBlank(propValue)) {
                continue;
            }
            boolean ruleType = Constants.CLAUSE_OP_RULE.equals(condition.getClauseOp());
            String name = condition.getThemeBizFieldName(executeDTO.getThemeType());
            if (!ruleType && StringUtils.isBlank(name)) {
                log.error("Biz filed name is blank. condition code:{}", condition.getCode());
                continue;
            }

            //SQL的条件拼接
            if (Constants.CLAUSE_OP_LIKE.equals(condition.getClauseOp())) {
                String tmpSql = name + " like " + propValue;
                conditionSqlList.add(tmpSql);
                continue;
            }
            if (Constants.CLAUSE_OP_IN.equals(condition.getClauseOp())) {
                String tmpSql = name + " in (" + propValue + ")";
                conditionSqlList.add(tmpSql);
                continue;
            }
            
            // 处理RULE类型的条件
            if (ruleType) {
                Map<String, String> bizFieldNameMap = condition.getBizFieldName();
                if (MapUtils.isEmpty(bizFieldNameMap)) {
                    log.error("Biz field name map is empty. condition code:{}", condition.getCode());
                    continue;
                }
                
                List<String> sqlParts = new ArrayList<>();
                
                // 处理情况2：'值1','值2'
                if (propValue.contains("'")) {
                    // 去掉单引号并分割
                    String[] values = propValue.replace("'", "").split(",");
                    for (String value : values) {
                        String sqlPart = bizFieldNameMap.get(value);
                        if (StringUtils.isNotBlank(sqlPart)) {
                            sqlParts.add(sqlPart);
                        }
                    }
                } 
                // 处理情况3：整数1,整数2
                else {
                    String[] values = propValue.split(",");
                    for (String value : values) {
                        String sqlPart = bizFieldNameMap.get(value);
                        if (StringUtils.isNotBlank(sqlPart)) {
                            sqlParts.add(sqlPart);
                        }
                    }
                }
                
                if (!sqlParts.isEmpty()) {
                    // 使用OR连接所有SQL片段，并确保每个片段都被括号包围
                    String combinedSql = "(" + sqlParts.stream()
                        .map(part -> "(" + part + ")")
                        .collect(Collectors.joining(" OR ")) + ")";
                    conditionSqlList.add(combinedSql);
                }
                continue;
            }

            if ("'fromCasherType'".equals(code)) {
                boolean b = useCK ? conditionSqlList.add(" coalesce(JSONExtractString(ext, 'FromCasher'),  JSONExtractString(ext, 'fromCasher')) in (" + propValue + ") ") : conditionCodeList.add("  coalesce(get_json_object(ext,\"$.FromCasher\"),  get_json_object(ext,\"$.fromCasher\"), 'null')  in (" + propValue + ") ");
                continue;
            }

            if ("'vip'".equals(propValue)) {
                conditionSqlList.add("custom_income_type = 'vip' and spu_id != 'spu_gift_card' and spu_id != 'spu_cloud_seat' and spu_id != 'spu_universal_voucher'  and spu_id != 'spu_cloud_public'");
                continue;
            }
            if ("'advanced_package'".equals(propValue)) {
                conditionSqlList.add("custom_income_type = 'advanced_package'");
                continue;
            }
            if ("'spu_cloud_seat_gift_card'".equals(propValue)) {
                conditionSqlList.add("spu_id = 'spu_gift_card' and JSONExtractString(sku_attr_json, 'giftCardType') = '2'");
                continue;
            }
            if ("'spu_cloud_seat_gift_card_original'".equals(propValue)) {
                conditionSqlList.add("spu_id = 'spu_gift_card' and JSONExtractString(sku_attr_json, 'giftCardType') = '3'");
                continue;
            }
            if ("'spu_cloud_seat'".equals(propValue)) {
                conditionSqlList.add("spu_id = 'spu_cloud_seat' ");
                continue;
            }
            if ("'spu_universal_voucher'".equals(propValue)) {
                conditionSqlList.add("spu_id = 'spu_universal_voucher' ");
                continue;
            }
            if ("'spu_cloud_public'".equals(propValue)) {
                conditionSqlList.add("spu_id = 'spu_cloud_public' ");
                continue;
            }

            if ("'fromCasher'".equals(propValue)) {
                conditionSqlList.add(" ext like '%fromCasher%' ");
                continue;
            }

            log.error("Clause op without handler. condition code:{}, op:{}", condition.getCode(), condition.getClauseOp());
        }
    }


    private List<String> getBaseQueryConditionCodeList() {
        return Splitter.on(",").splitToList(cloudConfig.getProperty("base.query.condition.codes", ""))
            .stream().map(String::trim)
            .collect(Collectors.toList());
    }

    private List<String> getStoreThemeStoreBaseQueryConditionCodeList() {
        return Splitter.on(",").splitToList(cloudConfig.getProperty("storeTheme.store.base.query.condition.codes", ""))
            .stream().map(String::trim)
            .collect(Collectors.toList());
    }

    private List<String> getStoreThemeOrderBaseQueryConditionCodeList() {
        return Splitter.on(",").splitToList(cloudConfig.getProperty("storeTheme.order.base.query.condition.codes", ""))
            .stream().map(String::trim)
            .collect(Collectors.toList());
    }

    /**
     * 订单基础数据，权限控制
     */
    public void baseDataPermissionsCtrl(Properties variables, AnalysisTaskExecuteDTO executeDTO) {
        String account = executeDTO.getOperator();
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        boolean existDataPermission4o;
        boolean existDataPermission4i;
        Queue<DataPermissionNode> queryLimits = new LinkedList<>(); // 没什么用
        List<DataPermissionNode> leafNodes = new ArrayList<>();
        StringBuilder mainStringBuilder = new StringBuilder(); // --- 主拼Sql流程 ---
        // 高等级权限 = 高等级用户 或者 不需要权限
        final boolean ownedHighLevelUser = ConditionDataPermissionHandler.getConditionDataPermissionHandler(executeDTO)
            .isHighestDataPermission(true, executeDTO);

        // 含有筛选条件及非高等级用户，都需要拼上数据权限
        // 产品端平台只有在选择站内渠道（二级渠道）的时候，才有意义，否则即使选择了产品端平台也不加入到sql权限条件中
        // 查找叶节点
        //未选到3级渠道，必须判断权限，此时需要使用复杂的叶子节点处理器
        List<String> level3BusinessIds = conditionParamContext.getLevel3BusinessIds()
            .stream()
            .filter(Objects::nonNull)
            .map(String::valueOf)
            .distinct()
            .collect(Collectors.toList());

        boolean isProcessAllFvLevel = CollectionUtils.isEmpty(level3BusinessIds)
            || (level3BusinessIds.size() == 1 && level3BusinessIds.get(0) == null);

        LeafNodeProcessTypeEnum processTypeEnum = isProcessAllFvLevel
            ? LeafNodeProcessTypeEnum.ALL_FV_LEVEl
            : LeafNodeProcessTypeEnum.ONLY_FINAL_FV;

        FvChannelLeafNodeProcessor handler = fvChannelLeafNodeHandler.getHandler(processTypeEnum);
        final boolean[] dataPermission4ios = handler.findLeafNodes(executeDTO, queryLimits, leafNodes, ownedHighLevelUser, account);
        // && !leafNodes.isEmpty()  是因为如果用户拥有某个节点下面的全部权限，则该节点不算是叶节点，所以可能存在有二级内外节点但是没有一个"叶节点"，所以需要增加该判断
        existDataPermission4o = dataPermission4ios[0] && !leafNodes.isEmpty();
        existDataPermission4i = dataPermission4ios[1] && !leafNodes.isEmpty();
        // 有二级渠道叶子节点
        Set<DataPermissionNode> dataPermissionNodes4o2Fv = new HashSet<>(); // 站外2级渠道集合
        Set<DataPermissionNode> dataPermissionNodes4o3Fv = new HashSet<>(); // 站外3级渠道集合
        Set<DataPermissionNode> dataPermissionNodes4i2Fv = new HashSet<>(); // 站内2级渠道集合
        Set<DataPermissionNode> dataPermissionNodes4i3Fv = new HashSet<>(); // 站内3级渠道集合
        Set<DataPermissionNode> dataPermissionNodes4Team = new HashSet<>(); // 团队集合
        Map<LabelEnum, Set<DataPermissionNode>> dataPermissionNodesMap = new EnumMap<>(LabelEnum.class); // 除上面剩余集合
        // 数据权限节点分拣
        dataPermissionNodeSorting(leafNodes, dataPermissionNodes4o2Fv, dataPermissionNodes4o3Fv, dataPermissionNodes4i2Fv, dataPermissionNodes4i3Fv, dataPermissionNodes4Team, dataPermissionNodesMap);
        // 公共部分 - 内外渠道
        StringBuilder stringBuilder4common = new StringBuilder(); // 公共部分   例子：团队以下叶子节点or表达式
        builder4commonSql(dataPermissionNodesMap, stringBuilder4common);

        // Team 筛选
        StringBuilder stringBuilder4team = new StringBuilder(); // Team
        builder4TeamSql(dataPermissionNodes4Team, stringBuilder4team);

        // 站外渠道
        StringBuilder stringBuilder4o = new StringBuilder(); // 站外拼Sql
        if (existDataPermission4o) {
            stringBuilder4o = builder4oSql(dataPermissionNodes4o2Fv, dataPermissionNodes4o3Fv, stringBuilder4o, stringBuilder4team);
        }
        // 处理产品、端平台
        StringBuilder bizEndKeyStringBuilder = new StringBuilder(); // 站内拼sql
        boolean useProDatePermission = DataPermissionTypeEnum.FV_DATA_PERMISSION.getCode().equals(executeDTO.getDataPermissionType());
        bizEndKeyStringBuilder = this.createBizEndKey4pro(bizEndKeyStringBuilder, conditionParamContext, ownedHighLevelUser, existDataPermission4i, executeDTO, useProDatePermission);

        // 站内渠道
        StringBuilder stringBuilder4i = new StringBuilder(); // 站内拼sql
        if (existDataPermission4i) {
            stringBuilder4i = builder4iSql(dataPermissionNodes4i2Fv, dataPermissionNodes4i3Fv, stringBuilder4i, bizEndKeyStringBuilder);
        }

        // 最后对组装sql整理
        StringBuilder fvBuilder = new StringBuilder();
        ArrayList<StringBuilder> tempBuilder = new ArrayList<>();
        if (StringUtils.isNotBlank(stringBuilder4o) && StringUtils.isNotBlank(stringBuilder4i)) {
            fvBuilder
                .append(" ( ")
                .append(stringBuilder4o)
                .append(" or ")
                .append(stringBuilder4i)
                .append(" ) ");
        } else if (StringUtils.isNotBlank(stringBuilder4i)) {
            fvBuilder.append(stringBuilder4i);
        } else if (StringUtils.isNotBlank(stringBuilder4o)) {
            fvBuilder.append(stringBuilder4o);
        }

        if (StringUtils.isNotBlank(fvBuilder)) {
            tempBuilder.add(fvBuilder);
        }

        if (StringUtils.isNotBlank(stringBuilder4common)) {
            tempBuilder.add(stringBuilder4common);
        }

        if (StringUtils.isNotBlank(bizEndKeyStringBuilder) && CollectionUtils.isNotEmpty(conditionParamContext.getBizNames())) {
            tempBuilder.add(bizEndKeyStringBuilder);
        }

        if (CollectionUtils.isNotEmpty(tempBuilder)) {
            String result = tempBuilder.stream()
                .map(
                    builder -> builder.toString()
                    .replace("(产品)", "")
                    .replace("(端平台)", "")
                    .replace("(业务团队)", "")
                    .replace("(三级渠道)", "")
                )
                .collect(Collectors.joining(" and "));
            mainStringBuilder.append(result);
            variables.setProperty("dataPermissionLimit", mainStringBuilder.toString());
        }
    }

    /**
     * 数据权限节点分拣
     *
     * @param leafNodes 叶节点集合  --  分拣源
     * @param dataPermissionNodes4o2Fv 站外Fv二级渠道权限节点集合
     * @param dataPermissionNodes4i2Fv 站外Fv二级渠道权限节点集合
     * @param dataPermissionNodes4Team 团队筛选权限节点集合
     * @param dataPermissionNodesMap 公共权限节点集合
     */
    private void dataPermissionNodeSorting(List<DataPermissionNode> leafNodes, Set<DataPermissionNode> dataPermissionNodes4o2Fv, Set<DataPermissionNode> dataPermissionNodes4o3Fv,
        Set<DataPermissionNode> dataPermissionNodes4i2Fv, Set<DataPermissionNode> dataPermissionNodes4i3Fv
        , Set<DataPermissionNode> dataPermissionNodes4Team, Map<LabelEnum, Set<DataPermissionNode>> dataPermissionNodesMap) {
        for (DataPermissionNode dataPermissionNode : leafNodes) {
            if (dataPermissionNode.getLabel() == LabelEnum.L2) {
                if (Constants.unMainStation(dataPermissionNode.getId())) {
                    dataPermissionNodes4o2Fv.add(dataPermissionNode);
                    continue;
                } else {
                    dataPermissionNodes4i2Fv.add(dataPermissionNode);
                    continue;
                }
            }
            if (dataPermissionNode.getLabel() == LabelEnum.L3) {
                if (Constants.unMainStation(dataPermissionNode.getParent().getParent().getId())) {
                    dataPermissionNodes4o3Fv.add(dataPermissionNode);
                    continue;
                } else {
//                    dataPermissionNodes4i3Fv.add(dataPermissionNode);
                    continue;
                }
            }
            if (dataPermissionNode.getLabel() == LabelEnum.T) {
                dataPermissionNodes4Team.add(dataPermissionNode);
            } else {
                dataPermissionNodesMap.computeIfAbsent(dataPermissionNode.getLabel(), le -> new HashSet<>()).add(dataPermissionNode);
            }
        }
    }


    /**
     * 构建站内部分Sql -- Part
     */
    private StringBuilder builder4iSql(Set<DataPermissionNode> dataPermissionNodes4i2Fv, Set<DataPermissionNode> dataPermissionNodes4i3Fv,
        StringBuilder stringBuilder4i, StringBuilder bizEndKeyStringBuilder) {

        StringBuilder level2forFv = new StringBuilder();
        if (CollectionUtils.isNotEmpty(dataPermissionNodes4i2Fv)) {
            level2forFv
                .append(String.format(Constants.DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT, dataPermissionNodes4i2Fv.iterator().next()
                    .getCurLabelLevel()))
                .append(" in ( ")
                .append(dataPermissionNodes4i2Fv.stream()
                    .map(DataPermissionNode::getId)
                    .distinct()
                    .collect(Collectors.joining(",")))
                .append(" ) ");
        }

        StringBuilder level3forFv = new StringBuilder();
        if (CollectionUtils.isNotEmpty(dataPermissionNodes4i3Fv)) {
            level3forFv
                .append(String.format(Constants.DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT, dataPermissionNodes4i3Fv.iterator().next()
                    .getCurLabelLevel()))
                .append(" in ( ")
                .append(dataPermissionNodes4i3Fv.stream()
                    .map(DataPermissionNode::getId)
                    .distinct()
                    .collect(Collectors.joining(",")))
                .append(" ) ");
        }

        if (StringUtils.isNotBlank(level2forFv) && StringUtils.isNotBlank(level3forFv)) {
            stringBuilder4i
                .append(" ( ")
                .append(level2forFv)
                .append(" or ")
                .append(level3forFv)
                .append(" ) ");
        } else if (StringUtils.isNotBlank(level2forFv) || StringUtils.isNotBlank(level3forFv)) {
            stringBuilder4i
                .append(" ( ")
                .append(level2forFv)
                .append(" ")
                .append(level3forFv)
                .append(" ) ");
            ;
        }

        if (StringUtils.isNotBlank(stringBuilder4i) && StringUtils.isNotBlank(bizEndKeyStringBuilder)) {
            return new StringBuilder(" ( ")
                .append(stringBuilder4i)
                .append(" and ")
                .append(bizEndKeyStringBuilder)
                .append(" ) ");
        } else if (StringUtils.isNotBlank(bizEndKeyStringBuilder)) {
            return new StringBuilder(" ( ")
                .append(bizEndKeyStringBuilder)
                .append(" ) ");
        } else if (StringUtils.isNotBlank(stringBuilder4i)) {
            return new StringBuilder(" ( ")
                .append(stringBuilder4i)
                .append(" ) ");
        }
        return stringBuilder4i;
    }


    /**
     * 构建站外部分Sql -- Part
     */
    private StringBuilder builder4oSql(Set<DataPermissionNode> dataPermissionNodes4o2Fv, Set<DataPermissionNode> dataPermissionNodes4o3Fv,
        StringBuilder stringBuilder4o, StringBuilder stringBuilder4team) {
        StringBuilder level2forFv = new StringBuilder();
        if (CollectionUtils.isNotEmpty(dataPermissionNodes4o2Fv)) {
            level2forFv.append(String.format(Constants.DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT, dataPermissionNodes4o2Fv.iterator().next()
                .getCurLabelLevel()))
                .append(" in ( ")
                .append(dataPermissionNodes4o2Fv.stream()
                    .map(DataPermissionNode::getId)
                    .distinct()
                    .collect(Collectors.joining(",")))
                .append(" ) ");
        }

        StringBuilder level3forFv = new StringBuilder();
        if (CollectionUtils.isNotEmpty(dataPermissionNodes4o3Fv)) {
            level3forFv
                .append(String.format(Constants.DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT, dataPermissionNodes4o3Fv.iterator().next()
                    .getCurLabelLevel()))
                .append(" in ( ")
                .append(dataPermissionNodes4o3Fv.stream()
                    .map(DataPermissionNode::getId)
                    .distinct()
                    .collect(Collectors.joining(",")))
                .append(" ) ");
        }

        if (StringUtils.isNotBlank(level2forFv) && StringUtils.isNotBlank(level3forFv)) {
            stringBuilder4o
                .append(level2forFv)
                .append(" or ")
                .append(level3forFv);

        } else if (StringUtils.isNotBlank(level2forFv) || StringUtils.isNotBlank(level3forFv)) {
            stringBuilder4o
                .append(level2forFv)
                .append(" ")
                .append(level3forFv);
        }

        if (stringBuilder4team.length() > 0 && stringBuilder4o.length() > 0) {
             stringBuilder4o = new StringBuilder(" ( ( ")
                .append(stringBuilder4o)
                .append(" )  and ")
                .append(stringBuilder4team)
                .append(" ) ");
        } else if (stringBuilder4team.length() > 0) {
             stringBuilder4o = new StringBuilder(" ( ")
                .append(stringBuilder4team)
                .append(" ) ");
        }
        return stringBuilder4o;
    }


    /**
     * 构建团队筛选部分Sql -- Part
     */
    private void builder4TeamSql(Set<DataPermissionNode> dataPermissionNodes4Team, StringBuilder stringBuilder4team) {
        if (CollectionUtils.isNotEmpty(dataPermissionNodes4Team)) {
            stringBuilder4team
                .append(" team_id ")
                .append(" in ( ")
                .append(dataPermissionNodes4Team.stream()
                    .map(DataPermissionNode::getId)
                    .distinct()
                    .collect(Collectors.joining(",")))
                .append(" ) ");
        }
    }


    /**
     * 构建公共部分Sql -- Part
     */
    private StringBuilder builder4commonSql(Map<LabelEnum, Set<DataPermissionNode>> dataPermissionNodesMap, StringBuilder stringBuilder4common) {
        if (!dataPermissionNodesMap.isEmpty()) {
            for (Map.Entry<LabelEnum, Set<DataPermissionNode>> entry : dataPermissionNodesMap.entrySet()) {
                final Set<DataPermissionNode> dataPermissionNodes4Tmp = entry.getValue();
                stringBuilder4common
                    .append(String.format(Constants.DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT, dataPermissionNodes4Tmp.iterator().next()
                        .getCurLabelLevel()))
                    .append(" in ( ")
                    .append(dataPermissionNodes4Tmp.stream()
                        .map(DataPermissionNode::getId)
                        .distinct()
                        .collect(Collectors.joining(",")))
                    .append(" ) ");
            }
        }
        return stringBuilder4common;
    }


    /**
     * 产品端平台管理
     */
    private StringBuilder createBizEndKey4pro(StringBuilder bizEndKeyStringBuilder, ConditionParamContext conditionParamContext, boolean ownedHighLevelUser, boolean existDataPermission4i, BaseQry baseQry, boolean useProDatePermission) {
        String sqlPart = Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES + Constants.DATA_PERMISSION_COMMON_SEPARATOR
            + Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES;
        List<String> bizNames = conditionParamContext.getBizNames().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> endKeys = conditionParamContext.getBizEndKeys().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 首先根据权限类型判断，非fv权限
        if (!useProDatePermission) {
            if (CollectionUtils.isEmpty(bizNames)) {
                return bizEndKeyStringBuilder;
            }
            if (CollectionUtils.isNotEmpty(bizNames) && CollectionUtils.isEmpty(endKeys)) {
                bizEndKeyStringBuilder
                    .append(" biz_name in ( ")
                    .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(Joiner.on(sqlPart).join(bizNames))
                    .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(" ) "); // Joiner.on(sqlPart).join(businessNames)
                return bizEndKeyStringBuilder;
            }
            if (CollectionUtils.isNotEmpty(endKeys)) {
                bizEndKeyStringBuilder
                    .append(" biz_end_key in ( ")
                    .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(Joiner.on(sqlPart).join(endKeys))
                    .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(" ) "); // Joiner.on(sqlPart).join(businessNames)
            }
            return bizEndKeyStringBuilder;
        }

        // 处理fv权限模式下产品端平台处理逻辑
        if (CollectionUtils.isEmpty(bizNames)) {
            // 高等级不需要处理权限问题
            // 使用会员类型权限无需处理端平台权限
            if (!ownedHighLevelUser) {
                final List<DataPermissionDTO> ownedDataPermissions = dataPermissionService.getOwnedDataPermission(ConditionEnum.PRO, baseQry);
                //非高级权限下，两种情况也无需考虑端平台
                // 1、用户没有产品、端平台权限
                // 2、用户没有选择站内渠道
                if (CollectionUtils.isEmpty(ownedDataPermissions) || !existDataPermission4i) {
                    return bizEndKeyStringBuilder;
                }
                List<String> ownedBizName = ownedDataPermissions.stream()
                    .map(DataPermissionDTO::getName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
                ArrayList<String> ownedEndKey = new ArrayList<>();
                for (DataPermissionDTO dataPermissionDTO : ownedDataPermissions) {
                    List<String> bizEndKey = dataPermissionDTO.getDataPermissionDTOList()
                        .stream()
                        .map(e -> Constants.getBizEndKey(dataPermissionDTO.getName(), e.getId()))
                        .collect(Collectors.toList());
                    ownedEndKey.addAll(bizEndKey);
                }
                bizEndKeyStringBuilder.append(this.builderBizEndKeyPartSql(ownedHighLevelUser, sqlPart, ownedBizName, ownedEndKey, baseQry));
            }
        } else {
            // 指定了产品且需要判断产品端平台权限时，根据情况判断
            bizEndKeyStringBuilder.append(this.builderBizEndKeyPartSql(ownedHighLevelUser, sqlPart, bizNames, endKeys, baseQry));
        }
        return bizEndKeyStringBuilder;
    }

    private StringBuilder builderBizEndKeyPartSql(boolean ownedHighLevelUser, String sqlPart, List<String> bizName, List<String> endKeys, BaseQry baseQry) {
        StringBuilder stringBuilder4proPart = new StringBuilder();
        stringBuilder4proPart.append(" biz_name in ( ")
            .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
            .append(Joiner.on(sqlPart).join(bizName))
            .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
            .append(" ) "); // Joiner.on(sqlPart).join(businessNames)

        if (CollectionUtils.isNotEmpty(endKeys)) {
            stringBuilder4proPart
                .append(" and biz_end_key in ( ")
                .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                .append(Joiner.on(sqlPart).join(endKeys))
                .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                .append(" ) ");
        }
        if (CollectionUtils.isEmpty(endKeys)) {

            if (!ownedHighLevelUser) { // 高等级不需要处理权限问题
                List<DataPermissionDTO> bizPermissions = dataPermissionService.getOwnedDataPermissions(ConditionEnum.PRO, LabelEnum.P1, baseQry)
                    .stream()
                    .filter(d -> bizName.contains(d.getName())).collect(Collectors.toList());

                ArrayList<String> ownedBizEndKey = new ArrayList<>();
                for (DataPermissionDTO bizPermission : bizPermissions) {
                    List<String> bizEndKeyByBizName = bizPermission.getDataPermissionDTOList().stream()
                        .map(e -> Constants.getBizEndKey(bizPermission.getName(), e.getId()))
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                    ownedBizEndKey.addAll(bizEndKeyByBizName);
                }
                if (CollectionUtils.isNotEmpty(ownedBizEndKey)) {
                    stringBuilder4proPart.append(" and biz_end_key in ( ")
                        .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                        .append(Joiner.on(sqlPart).join(ownedBizEndKey))
                        .append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                        .append(" ) ");
                }
            }
        }
        return stringBuilder4proPart;
    }

    /**
     * 构建产品、端平台部分sql
     */
    public StringBuilder builder4proPartSql(boolean ownedHighLevelUser, String account, String sqlPart, String bizName, List<String> endKeys, BaseQry baseQry) {
        StringBuilder stringBuilder4proPart = new StringBuilder(" ( ");
        stringBuilder4proPart.append(" biz_name = ").append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
            .append(bizName).append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES); // Joiner.on(sqlPart).join(businessNames)
        if (CollectionUtils.isEmpty(endKeys)) {
            if (!ownedHighLevelUser) { // 高等级不需要处理权限问题
                final List<String> dataPermissionIds = dataPermissionService.getOwnedDataPermissionIds(ConditionEnum.PRO, LabelEnum.P2, baseQry);
                stringBuilder4proPart.append(" and platform_end_key in ( ").append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(Joiner.on(sqlPart).join(dataPermissionIds)).append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                    .append(" ) ");
            }
        } else {
            stringBuilder4proPart.append(" and platform_end_key in ( ").append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                .append(Joiner.on(sqlPart).join(endKeys)).append(Constants.DATA_PERMISSION_COMMON_SINGLE_QUOTES)
                .append(" ) ");
        }
        return stringBuilder4proPart.append(" ) ");
    }

    /**
     * 解析指标对应模板，解析@标识符替换和${}标识符已存在的变量
     */
    public String parseSqlByProperties(Properties properties, Long sqlTemplateId) {
        String dateBase = MapUtils.getString(properties, "dateBase", DataBaseTypeEnum.HIVE.getCode());
        String sqlFramework = getTargetAnalysisSqlFramework(dateBase, sqlTemplateId);
        return GenericTokenParsers.parserSql(properties, sqlFramework); // 解析指标对应模板，解析@标识符替换和${}标识符已存在的变量
    }

    private String getTargetAnalysisSqlFramework(String dataBase, Long sqlTemplateId) {
        if (DataBaseTypeEnum.CLICK_HOUSE.getCode().equals(dataBase) || DataBaseTypeEnum.STAR_ROCKS.getCode().equals(dataBase)) {
            return analysisSqlTemplateCKRepository.selectById(sqlTemplateId).getValue();
        }
        return analysisSqlTemplateRepository.selectById(sqlTemplateId).getValue();
    }


    public void setTaskFailedWithMail(Long taskId, String operator, String message) {
        analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, null);
        mailManager.sendFailMail(operator, message);
    }

    public List<OriginalQueryResultDTO> executeQuery(List<TargetAnalysisQueryDTO> analysisQueryDTOS, Long taskId)
        throws ExecutionException, InterruptedException {
        int analysisQueryDTOSize = analysisQueryDTOS.size();
        String operator = analysisQueryDTOS.get(0).getOperator();
        CountDownLatch countDownLatch = new CountDownLatch(analysisQueryDTOSize);
        LinkedHashMap<TargetAnalysisQueryDTO, Future<OriginalQueryResultDTO>> map = new LinkedHashMap<>();
        ExecutorService executorService = getExecutorService(analysisQueryDTOS);
        String activeFile = System.getProperty("spring.profiles.active");

        for (TargetAnalysisQueryDTO queryDTO : analysisQueryDTOS) {
            queryDTO.setOperator(queryDTO.getOperator());
            queryDTO.setTaskId(taskId);
            queryDTO.setEnv(activeFile);
            AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(queryDTO.getTargetCode());
            queryDTO.setTargetType(isAdvancedTarget(analysisTarget) ? TargetGroupOptionEnum.ADVANCED_INDICATORS.getCode()
                : TargetGroupOptionEnum.ORDINARY_INDICATORS.getCode());
            map.put(queryDTO, executorService.submit(new SqlExecutor(countDownLatch, queryDTO)));
        }
        ArrayList<OriginalQueryResultDTO> queryResultDTOS = new ArrayList<>();
        try {
            boolean waitResult = countDownLatch.await(
                cloudConfig.getLongProperty("query.execute.timeout", 90L) * analysisQueryDTOSize, TimeUnit.MINUTES);
            if (!waitResult) {
                log.error("指标分析计算-等待结果超时! param:{}", analysisQueryDTOS);
                alterService.sendHotChat(String.format("指标分析计算-等待结果超时！任务id：%s,  操作人：%s, 运行环境：%s", taskId, operator, activeFile));
            }
            for (Map.Entry<TargetAnalysisQueryDTO, Future<OriginalQueryResultDTO>> resultSetPairFuture : map.entrySet()) {
                OriginalQueryResultDTO queryResult = resultSetPairFuture.getValue().get();
                queryResult.setDateYM(resultSetPairFuture.getKey().getDateYM());
                queryResultDTOS.add(queryResult);
            }
        } catch (Exception e) {
            log.error("指标分析计算主线程被中断!,param:{}", analysisQueryDTOS);
            throw e;
        }
        return queryResultDTOS;
    }

    private static boolean isAdvancedTarget(AnalysisTarget analysisTarget) {
        return analysisTarget != null
            && TargetGroupOptionEnum.ADVANCED_INDICATORS.getCode().equals(analysisTarget.getAdvancedOption());
    }


    private ExecutorService getExecutorService(List<TargetAnalysisQueryDTO> analysisQueryDTOS) {
        String dataBase = analysisQueryDTOS.get(0).getDataBase();
        if (Objects.equals(DataBaseTypeEnum.STAR_ROCKS.getCode(), dataBase)) {
            return scheduleTaskThreadPoolExecutor;
        }
        boolean anyMatch = analysisQueryDTOS.stream().anyMatch(d -> d.getTaskId() != null && d.getTaskId().intValue() < 0);
        if (anyMatch) {
            return checkThreadPoolExecutor;
        }
        boolean isDiagnosis = analysisQueryDTOS.stream().anyMatch(d -> StringUtils.isNotBlank(d.getGroupCode()));
        if (isDiagnosis) {
            return diagnosisThreadPoolExecutor;
        }

        List<TargetAnalysisQueryDTO> queryDTOS = analysisQueryDTOS.stream()
            .filter(d -> d.getTargetCode().contains(Constants.TARGET_ANALYSIS_ARPU_CHARACTERISTIC) || d.getTargetCode()
                .contains(TARGET_ANALYSIS_REAMIN_CHARACTERISTIC))
            .collect(Collectors.toList());
        if (queryDTOS.size() >= Constants.DEFAULT_COUNT) {
            return slowThreadPoolExecutor;
        }
        return fastThreadPoolExecutor;
    }


    @TraceCrossThread
    class SqlExecutor implements Callable<OriginalQueryResultDTO> {

        private final CountDownLatch countDownLatch;
        private final TargetAnalysisQueryDTO queryDTO;

        SqlExecutor(TargetAnalysisQueryDTO queryDTO) {
            this.countDownLatch = null;
            this.queryDTO = queryDTO;
        }

        SqlExecutor(CountDownLatch countDownLatch, TargetAnalysisQueryDTO queryDTO) {
            this.countDownLatch = countDownLatch;
            this.queryDTO = queryDTO;
        }

        @Override
        public OriginalQueryResultDTO call() {
            String targetCode = queryDTO.getTargetCode();
            String targetName = queryDTO.getTargetName();
            String businessTypeName = queryDTO.getBusinessTypeName();
            String dateYM = queryDTO.getDateYM();
            String operator = queryDTO.getOperator();
            Long taskId = queryDTO.getTaskId();
            String env = queryDTO.getEnv();
            String groupCode = queryDTO.getGroupCode();
            String dimensionCode = queryDTO.getDimensionCode();
            Integer targetType = queryDTO.getTargetType();
            try {
                if (AnalysisTaskSourceEnum.SCHEDULED != queryDTO.getTaskSource()) {
                    String currentThreadName = Thread.currentThread().getName();
                    Thread.currentThread().setName((String.format("%s-%s-%s-%s", currentThreadName, taskId, targetCode, operator)));
                }
                log.info("begin target analysis, targetAnalysisQueryDTO:{}", queryDTO);
                AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(targetCode);
                return doQueryTargetResult(queryDTO, analysisTarget);
            } catch (Exception e) {
                log.error("指标查询子任务异常! 任务id：{}, 指标标识：{}, error:", taskId, targetCode, e);
                alterService.sendHotChat(String
                    .format("指标分析查询异常！任务id：%s,  业务：%s, 指标标识：%s(%s), 操作人：%s, 运行环境：%s", taskId, businessTypeName, targetCode, targetName, operator, env));
                return new OriginalQueryResultDTO(targetCode, dateYM, groupCode, dimensionCode, new LinkedHashMap<>(), new ArrayList<>(), targetType, Boolean.FALSE);
            } finally {
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }

        private OriginalQueryResultDTO doQueryTargetResult(TargetAnalysisQueryDTO queryDTO, AnalysisTarget analysisTarget)
            throws SQLException {
            Long taskId = queryDTO.getTaskId();
            String targetCode = queryDTO.getTargetCode();
            String targetName = queryDTO.getTargetName();
            String businessTypeName = queryDTO.getBusinessTypeName();
            String operator = queryDTO.getOperator();
            String env = queryDTO.getEnv();
            Integer targetType = queryDTO.getTargetType();
            if (isAdvancedTarget(analysisTarget)) {
                userGroupRepository.generateUserGroup(queryDTO, analysisTarget);
                return new OriginalQueryResultDTO(targetCode, null, null, null, new LinkedHashMap<>(), new ArrayList<>(), targetType, Boolean.TRUE);
            } else {
                OriginalQueryResultDTO queryResultDTO;
                if (queryDTO.getTaskSource() == AnalysisTaskSourceEnum.SCHEDULED) {
                    queryResultDTO = orderRepository.queryTargetResultAfter3SecRetry(queryDTO);
                } else {
                    queryResultDTO = orderRepository.queryTargetResult(queryDTO);
                }
                if (queryResultEmpty(queryResultDTO)) {
                    log.info("queryResult finally find no data!, 任务id：{}, 业务：{}, 指标标识：{}({}), 操作人：{}", taskId, businessTypeName, targetCode, targetName, operator);
                    String content = String.format("指标分析结果为空！任务id：%s, 业务：%s, 指标标识：%s(%s), 操作人：%s, 运行环境：%s",
                        taskId, businessTypeName, targetCode, targetName, operator, env);
                    boolean scheduledTask = AnalysisTaskSourceEnum.SCHEDULED.getValue().equals(analysisTarget.getSource());
                    if (scheduledTask) {
                        alterService.sendHotChat(AnalysisTaskSourceEnum.SCHEDULED.getDesc() + content, scheduledTaskFeiShuReceiver);
                    } else {
                        alterService.sendHotChat(content);
                    }
                }
                return queryResultDTO;
            }
        }
    }


    private boolean queryResultEmpty(OriginalQueryResultDTO queryResultDTO) {
        return queryResultDTO == null
            || CollectionUtils.isEmpty(queryResultDTO.getDataList())
            || MapUtils.isEmpty(queryResultDTO.getHeadColumnMap());
    }

    public void cleanLocalFile(String absoluteFilePath) {
        File file = new File(absoluteFilePath);
        if (!file.exists() || !file.isFile() || !file.delete()) {
            log.warn("[order-analysis][delete file: {}] fail. ", absoluteFilePath);
        }
    }
}
