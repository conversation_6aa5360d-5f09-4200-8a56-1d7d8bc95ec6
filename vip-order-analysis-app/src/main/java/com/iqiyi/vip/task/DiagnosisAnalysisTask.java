package com.iqiyi.vip.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.UserGroupRepository;
import com.iqiyi.vip.dto.base.DataResult;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.condition.ConditionPayEndTimeDTO;
import com.iqiyi.vip.dto.diagnosis.DiagnosisConditionDTO;
import com.iqiyi.vip.dto.diagnosis.DiagnosisConditionPairDTO;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResult;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.diagnosis.NextDiagnosisDimensionInfo;
import com.iqiyi.vip.dto.target.DiagnosisQueryDTO;
import com.iqiyi.vip.dto.target.DownloadDiagnosisDTO;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.GroupTypeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.handler.base.ConditionDataPermissionHandler;
import com.iqiyi.vip.repository.DiagnosisRepositoryImpl;
import com.iqiyi.vip.service.AlterService;
import com.iqiyi.vip.service.AnalysisResultStorageService;
import com.iqiyi.vip.service.DiagnosisDimensionService;
import com.iqiyi.vip.service.TaskQueryInfoService;
import com.iqiyi.vip.util.DynamicEasyExcelExportUtils;
import com.iqiyi.vip.util.GenericTokenParsers;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.JacksonUtils;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static com.iqiyi.vip.constant.Constants.CLICKHOUSE_WIDE_TABLE_NAME;
import static com.iqiyi.vip.constant.Constants.CURRENT_PERIOD;
import static com.iqiyi.vip.constant.Constants.DIFF_PERIOD;
import static com.iqiyi.vip.constant.Constants.DIMENSION_ITEM;
import static com.iqiyi.vip.constant.Constants.EXCEEDING_AVERAGE_CONTRIBUTION;
import static com.iqiyi.vip.constant.Constants.HIVE_WIDE_TABLE_NAME;
import static com.iqiyi.vip.constant.Constants.INCREMENT;
import static com.iqiyi.vip.constant.Constants.INCREMENTAL_CONTRIBUTION_RATIO;
import static com.iqiyi.vip.constant.Constants.INCREMENTAL_RATIO;
import static com.iqiyi.vip.constant.Constants.SUMMATION;

/**
 * <AUTHOR>
 * @className DiagnosisAnalysisTask
 * @description
 * @date 2023/10/31
 **/
@Component
@Slf4j
public class DiagnosisAnalysisTask extends AnalysisTask {

    @Resource
    private TaskQueryInfoService taskQueryInfoService;
    @Resource
    private AnalysisTaskRepository taskRepository;
    @Resource
    private AnalysisTargetRepository targetRepository;
    @Resource
    private AnalysisTargetGroupRepository targetGroupRepository;
    @Resource
    private AnalysisConditionRepository conditionRepository;
    @Resource
    private AnalysisDimensionRepository dimensionRepository;
    @Resource
    private DiagnosisDimensionService diagnosisDimensionService;

    @Resource
    private DiagnosisRepositoryImpl diagnosisRepository;

    @Resource
    private AnalysisTargetRepository analysisTargetRepository;

    @Resource
    private AnalysisTargetGroupRepository analysisTargetGroupRepository;

    @Resource
    private AlterService alterService;
    @Resource
    private AnalysisResultStorageService storageService;

    @Resource
    private AnalysisResultStorageService analysisResultStorageService;

    @Resource
    private AnalysisTaskRepository analysisTaskRepository;

    @Resource
    private UserGroupRepository userGroupRepository;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    @Value("${user.group.ck.table:dist_vip_user_group_order_analysis}")
    private String userGroupCKTable;

    @Resource
    private CloudConfig cloudConfig;

    private static final DecimalFormat df = new DecimalFormat("0.00%");

    private static final ThreadLocal<DecimalFormat> dfHolder = ThreadLocal.withInitial(() -> new DecimalFormat("0.00%"));
    private static final ThreadLocal<NumberFormat> numberHolder = ThreadLocal.withInitial(NumberFormat::getInstance);


    //帮我重构processDiagnosis方法
    public DataResult<DiagnosisResultDTO> processDiagnosis(DiagnosisQueryDTO queryDTO) {
        String uniqueId = queryDTO.getUniqueId();
        // 第一次提交任务才生成uniqueId
        Long taskId;
        if (StringUtils.isBlank(uniqueId)) {
            uniqueId = IdUtil.simpleUUID();
            queryDTO.setUniqueId(uniqueId);
            AnalysisTaskDO analysisTaskDO = initializeTaskDO(queryDTO);
            taskRepository.addAnalysisTask(analysisTaskDO);
            taskId = analysisTaskDO.getId();
        } else {
            AnalysisTaskDO task = analysisTaskRepository.getTaskByUniqueIdentification(uniqueId);
            taskId = task.getId();
        }

        AnalysisTaskExecuteDTO executeDTO = AnalysisTaskExecuteDTO.buildFrom(queryDTO);
        StopWatch stopWatch = new StopWatch("order-".concat(ObjectUtils.isEmpty(executeDTO.getTaskName()) ? "" : executeDTO.getTaskName())
            .concat(executeDTO.getOperator()));
        stopWatch.start();
        String operator = executeDTO.getOperator();
        DiagnosisResultDTO diagnosisResultDTO = new DiagnosisResultDTO();
        try {
            // 1、大数据查询sql处理
            List<TargetAnalysisQueryDTO> analysisQueryDTOS = constructDiagnosisAnalysisQueryDTO(queryDTO, executeDTO);
            if (CollectionUtils.isEmpty(analysisQueryDTOS)) {
                setTaskFailedWithMail(taskId, operator, "未配置SQL模板，请联系维护人员查看情况");
                return DataResult.success(null);
            }

            TaskQueryInfo taskQueryInfo = TaskQueryInfo.buildFrom(taskId, JacksonUtils.toJsonString(analysisQueryDTOS));
            taskQueryInfoService.saveOrUpdate(taskQueryInfo);

            diagnosisResultDTO.setTaskId(taskId);
            diagnosisResultDTO.setUniqueId(queryDTO.getUniqueId());
            String fileName = DynamicEasyExcelExportUtils.getFileName(queryDTO.getOperator(), taskId
                , executeDTO, code -> {
                    final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
                    if (analysisTarget != null && analysisTarget.getGroup() != null) {
                        return analysisTargetGroupRepository.get(analysisTarget.getGroup());
                    }
                    return null;
                });
            diagnosisResultDTO.setResult(fileName);

            LinkedHashMap<String, List<DiagnosisResult>> diagnosisResults = new LinkedHashMap<>();
            LinkedHashMap<String, List<Float>> calculateData = new LinkedHashMap<>();
            // 2、使用拼接好的 sql，调用Pilot查询
            List<OriginalQueryResultDTO> queryResultDTOS = executeQuery(analysisQueryDTOS, taskId);

            //3、 遍历每个sql查询结果
            extractResult(queryDTO, diagnosisResultDTO, diagnosisResults, calculateData, queryResultDTOS);
            LinkedHashMap<String, List<Float>> sortResult = sortLinkedHashMapByListValues(queryDTO, calculateData);
            LinkedHashMap<String, List<DiagnosisResult>> sortedResult = buildResult(sortResult, diagnosisResults);

            //4、excel快照中保存全部数据
            diagnosisResultDTO.setDiagnosisResults(sortedResult);
            diagnosisRepository.addDiagnosisResult(diagnosisResultDTO, queryDTO.getDiagnosisPath());

            //针对sortedResult的每一个value,只取该value的top n个元素给到前端
            diagnosisResultDTO.setDiagnosisResults(limitTopN(sortedResult, cloudConfig.getIntProperty("limit.show.count", 10)));

            stopWatch.stop();
            double totalTimeSeconds = stopWatch.getTotalTimeSeconds();
            log.info("processed {} query, cost:{}", queryResultDTOS.size(), totalTimeSeconds);
            analysisTaskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), fileName, (int) totalTimeSeconds);
            return DataResult.success(diagnosisResultDTO);
        } catch (Exception e) {
            stopWatch.stop();
            log.error("processAsync error ! taskId:{}, operator:{}", taskId, operator, e);
            taskRepository.reSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
            alterService.sendHotChat(String.format("指标分析失败！任务id：%s, 操作人：%s, 运行环境：%s", taskId, operator, System.getProperty("spring.profiles.active")));
            return new DataResult<>(CodeEnum.ERROR_SYSTEM.getCode(), CodeEnum.ERROR_SYSTEM.getMessage());
        }
    }

    private void extractResult(DiagnosisQueryDTO queryDTO, DiagnosisResultDTO diagnosisResultDTO, LinkedHashMap<String, List<DiagnosisResult>> diagnosisResults, LinkedHashMap<String, List<Float>> calculateData, List<OriginalQueryResultDTO> queryResultDTOS) {
        DimensionLayerNode root = CloudConfigUtils.getDimensionLayerPair(queryDTO.getTargetCodes().get(0));
        for (OriginalQueryResultDTO queryResult : queryResultDTOS) {
            String groupCode = queryResult.getGroupCode();
            String dimensionCode = queryResult.getDimensionCode();
            List<LinkedHashMap<String, Object>> dataList = queryResult.getDataList();

            // 构造表头
            LinkedHashMap<String, String> headColumnMap = queryResult.getHeadColumnMap();
            if (MapUtils.isEmpty(diagnosisResultDTO.getColumnNames())) {
                LinkedHashMap<String, String> columnNameMap = new LinkedHashMap<>();
                setDiagnosisResultColumnName(headColumnMap, columnNameMap);
                diagnosisResultDTO.setColumnNames(columnNameMap);
            }

            // 遍历每行数据
            List<DiagnosisResult> diagnosisResultList = new ArrayList<>();
            List<Float> calculateDataList = new ArrayList<>();

            for (HashMap<String, Object> dataObjectHashMap : dataList) {
                // 构造每行数据
                DiagnosisResult diagnosisResult = new DiagnosisResult();
                diagnosisResult.setDimensionGroupCode(groupCode);
                DimensionLayerNode dimensionLayerNode = diagnosisDimensionService.getDimensionLayersByGroupCodeAndDimensionCode(groupCode, dimensionCode, root);
                if (dimensionLayerNode == null) {
                    continue;
                }
                String dimensionColumnName = getColumnNameByKeyWord(headColumnMap, DIMENSION_ITEM, false);
                String dimensionName = MapUtils.getString(dataObjectHashMap, dimensionColumnName);

                if (CollectionUtils.isEmpty(dimensionLayerNode.getChildren()) || SUMMATION.equals(dimensionName)
                    || StringUtils.isBlank(dimensionName)) {
                    diagnosisResult.setCouldDrillDown(Boolean.FALSE);
                    diagnosisResult.setNextDiagnosisDimensionInfos(new ArrayList<>());
                } else {
                    diagnosisResult.setCouldDrillDown(Boolean.TRUE);
                    List<NextDiagnosisDimensionInfo> nextDiagnosisDimensionInfo = dimensionLayerNode.getChildren()
                        .stream()
                        .map(d -> new NextDiagnosisDimensionInfo(d.getDimensionCode(), d.getGroupCode()))
                        .collect(Collectors.toList());
                    diagnosisResult.setNextDiagnosisDimensionInfos(nextDiagnosisDimensionInfo);
                }

                diagnosisResult.setDimensionLayerCode(dimensionCode);
                diagnosisResult.setDimensionLayerDesc(dimensionLayerNode.getDesc());

                String conditionKey = dimensionLayerNode.getConditionKey();
                diagnosisResult.setConditionCode(conditionKey);
                diagnosisResult.setConditionValue(MapUtils.getString(dataObjectHashMap, conditionKey.concat("_support")));

                diagnosisResult.setDimensionName(dimensionName);

                // 设置结果中数据部分
                setDiagnosisResultNumericalData(diagnosisResult, dataObjectHashMap);
                //不能包含合计
                if (!dimensionName.contains(SUMMATION)) {
                    //准备组间排序依赖的数据
                    buildCalculateData(dataObjectHashMap, calculateDataList, queryDTO);
                }

                //此次是为了把总计行放上面搞正确
                if (dimensionName.contains(SUMMATION)) {
                    diagnosisResult.setExceedingAverageContribution("100%");
                    diagnosisResultList.add(0, diagnosisResult);
                    continue;
                }
                diagnosisResultList.add(diagnosisResult);
            }
            diagnosisResults.put(dimensionCode, diagnosisResultList);
            calculateData.put(dimensionCode, calculateDataList);
        }
    }

    /**
     * @param inputMap 原始结果
     * @param n top n
     */
    public static LinkedHashMap<String, List<DiagnosisResult>> limitTopN(LinkedHashMap<String, List<DiagnosisResult>> inputMap, int n) {
        LinkedHashMap<String, List<DiagnosisResult>> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<DiagnosisResult>> entry : inputMap.entrySet()) {
            List<DiagnosisResult> originalList = entry.getValue();
            List<DiagnosisResult> limitedList = originalList.subList(0, Math.min(n, originalList.size()));
            result.put(entry.getKey(), limitedList);
        }
        return result;
    }

    /**
     * 整合组间排序的结果
     *
     * @param calculateData    通过计算方差后的排序结果
     * @param diagnosisResults 原始结果
     */
    public static LinkedHashMap<String, List<DiagnosisResult>> buildResult(LinkedHashMap<String, List<Float>> calculateData,
                                                                           LinkedHashMap<String, List<DiagnosisResult>> diagnosisResults) {
        LinkedHashMap<String, List<DiagnosisResult>> diagnosisResultsFinally = new LinkedHashMap<>();
        for (String key : calculateData.keySet()) {
            diagnosisResultsFinally.put(key, diagnosisResults.get(key));
        }
        return diagnosisResultsFinally;
    }

    public static LinkedHashMap<String, List<Float>> sortLinkedHashMapByListValues(DiagnosisQueryDTO queryDTO, LinkedHashMap<String, List<Float>> inputMap) {
        Integer sortType = queryDTO.getConditionParamContext().getSortType();
        // Create a list of map entries
        List<Map.Entry<String, List<Float>>> entryList = new ArrayList<>(inputMap.entrySet());

        // Create a custom comparator to sort the entries based on the sum of List<Float> values
        Comparator<Entry<String, List<Float>>> comparator = (entry1, entry2) -> {
            float result1 = calculateVariance(entry1.getValue(), calculateMean(sortType, entry1.getValue()));
            log.info(entry1.getKey() + " : " + result1);
            float result2 = calculateVariance(entry2.getValue(), calculateMean(sortType, entry2.getValue()));
            log.info(entry2.getKey() + " : " + result2);
            return Float.compare(result2, result1);
        };

        // Sort the entryList using the custom comparator
        entryList.sort(comparator);

        // Create a new LinkedHashMap to store the sorted entries
        LinkedHashMap<String, List<Float>> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<Float>> entry : entryList) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        return sortedMap;
    }

    public static float calculateMean(Integer sortType, List<Float> data) {
        float sum = 0.0f;
        if (Objects.nonNull(sortType) && 1 == sortType) {
            for (float value : data) {
                sum += value;
            }
        } else if (Objects.nonNull(sortType) && 2 == sortType) {
            for (float value : data) {
                sum += Math.abs(value);
            }
        }
        return sum / data.size();
    }

    public static float calculateVariance(List<Float> data, float mean) {
        float sumSquaredDifferences = 0.0f;
        for (float value : data) {
            float difference = Math.abs((Math.abs(value) - mean));
            sumSquaredDifferences += difference * difference;
        }
        return sumSquaredDifferences / data.size();
    }


    private void setDiagnosisResultColumnName(LinkedHashMap<String, String> headColumnMap, LinkedHashMap<String, String> columnNameMap) {
        if (MapUtils.isEmpty(headColumnMap)) {
            return;
        }
        columnNameMap.put("维度", "dimensionLayerDesc");
        String dimensionName = getColumnNameByKeyWord(headColumnMap, DIMENSION_ITEM, false);
        if (StringUtils.isNotBlank(dimensionName)) {
            columnNameMap.put(dimensionName, "dimensionName");
        }
        String currentPeriodName = getColumnNameByKeyWord(headColumnMap, CURRENT_PERIOD, false);
        if (StringUtils.isNotBlank(currentPeriodName)) {
            columnNameMap.put(currentPeriodName, "targetValueCurrentPeriod");
        }

        String diffPeriodName = getColumnNameByKeyWord(headColumnMap, DIFF_PERIOD, false);
        if (StringUtils.isNotBlank(diffPeriodName)) {
            columnNameMap.put(diffPeriodName, "targetValueDiffPeriod");
        }

        String incrementName = getColumnNameByKeyWord(headColumnMap, INCREMENT, true);
        if (StringUtils.isNotBlank(incrementName)) {
            columnNameMap.put(incrementName, "increment");
        }

        String incrementalContributionRatio = getColumnNameByKeyWord(headColumnMap, INCREMENTAL_CONTRIBUTION_RATIO, true);
        if (StringUtils.isNotBlank(incrementalContributionRatio)) {
            columnNameMap.put(incrementalContributionRatio, "incrementalContributionRatio");
        }

        String incrementalRatio = getColumnNameByKeyWord(headColumnMap, INCREMENTAL_RATIO, false);
        if (StringUtils.isNotBlank(incrementalRatio)) {
            columnNameMap.put(incrementalRatio, "incrementalRatio");
        }

        String exceedingAverageContribution = getColumnNameByKeyWord(headColumnMap, EXCEEDING_AVERAGE_CONTRIBUTION, false);
        if (StringUtils.isNotBlank(exceedingAverageContribution)) {
            columnNameMap.put(exceedingAverageContribution, "exceedingAverageContribution");
        }
    }

    private <T> String getColumnNameByKeyWord(Map<String, T> headColumnMap, String keyWord, boolean strict) {
        if (strict) {
            return headColumnMap.keySet()
                    .stream()
                    .filter(k -> StringUtils.isNotBlank(k) && k.equals(keyWord))
                    .findFirst()
                    .orElse(null);
        }
        return headColumnMap.keySet()
                .stream()
                .filter(k -> StringUtils.isNotBlank(k) && k.contains(keyWord))
                .findFirst()
                .orElse(null);
    }

    private void setDiagnosisResultNumericalData(DiagnosisResult result, HashMap<String, Object> dataObjectHashMap) {

        result.setTargetValueCurrentPeriod(formatLong(MapUtils.getLongValue(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, CURRENT_PERIOD, false))));

        result.setTargetValueDiffPeriod(formatLong(MapUtils.getLongValue(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, DIFF_PERIOD, false))));

        result.setIncrement(formatLong(MapUtils.getLongValue(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, INCREMENT, true))));

        Float incrementalContributionRatio = MapUtils.getFloat(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, INCREMENTAL_CONTRIBUTION_RATIO, false));
        String incrementalContributionRatioStr = formatFloat(Float.isNaN(incrementalContributionRatio) ? 0f : incrementalContributionRatio);
        result.setIncrementalContributionRatio(
            "0.00%".equals(incrementalContributionRatioStr) ? "0" : incrementalContributionRatioStr.replaceAll("\\.00", ""));

        Float incrementalRatio = MapUtils.getFloat(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, INCREMENTAL_RATIO, true));
        String incrementalRatioStr = formatFloat(Float.isNaN(incrementalRatio) ? 0f : incrementalRatio);
        result.setIncrementalRatio("0.00%".equals(incrementalRatioStr) ? "0" : incrementalRatioStr.replaceAll("\\.00", ""));

        Float exceedingAverageContribution = MapUtils.getFloat(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, EXCEEDING_AVERAGE_CONTRIBUTION, true));
        String exceedingAverageContributionStr = formatFloat(Float.isNaN(exceedingAverageContribution) ? 0f : exceedingAverageContribution);
        result.setExceedingAverageContribution(
            "0.00%".equals(exceedingAverageContributionStr) ? "0" : exceedingAverageContributionStr.replaceAll("\\.00", ""));
    }


    public static String formatLong(Long number) {
        return numberHolder.get().format(number);
    }

    public static String formatFloat(Float number) {
        return dfHolder.get().format(number);
    }


    /**
     * @param dataObjectHashMap 每行数据
     * @param calculateDataList 需要计算的数据
     * @param queryDTO 排序依据
     */
    private void buildCalculateData(HashMap<String, Object> dataObjectHashMap, List<Float> calculateDataList, DiagnosisQueryDTO queryDTO) {
        Integer sortType = queryDTO.getConditionParamContext().getSortType();
        Float data;
        if (Objects.nonNull(sortType) && 1 == sortType) {
            data = MapUtils.getFloat(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, INCREMENTAL_CONTRIBUTION_RATIO, true));
            calculateDataList.add(Objects.nonNull(data) ? data : 0f);
        } else if (Objects.nonNull(sortType) && 2 == sortType) {
            data = MapUtils.getFloat(dataObjectHashMap, getColumnNameByKeyWord(dataObjectHashMap, EXCEEDING_AVERAGE_CONTRIBUTION, true));
            calculateDataList.add(Objects.nonNull(data) ? data : 0f);
        }
    }

    /**
     * 1、生成excel
     * 2、上传excel到oss
     * 3、下载excel
     */
    public void generateExcel(HttpServletResponse response, List<ExcelSheetDataDTO> excelSheetDataDtoS,
                              DownloadDiagnosisDTO downloadDiagnosisDTO) {
        try {
            AnalysisTaskExecuteDTO taskExecuteDTO = getAnalysisTaskExecuteDTO(downloadDiagnosisDTO);

            // Excel 导出，存储到机器上
            String excelFileName = DynamicEasyExcelExportUtils.generateResultExcelFile2(excelSheetDataDtoS, downloadDiagnosisDTO.getOperator(), localFilePath
                , code -> analysisTargetRepository.selectByCode(code)
                , code -> {
                    final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
                    if (analysisTarget != null && analysisTarget.getGroup() != null) {
                        return analysisTargetGroupRepository.get(analysisTarget.getGroup());
                    }
                    return null;
                }, taskExecuteDTO.getTaskId(), taskExecuteDTO, null);
            //上传到OSS
            String absoluteResultFilePath = localFilePath + "/" + excelFileName;
            storageService.uploadResultFile(new File(absoluteResultFilePath), excelFileName); // Excel 上传到 OSS。


//            taskRepository
            //下载,todo优先本地，再从oss查询
            analysisResultStorageService.downloadResult(excelFileName, response);
            // 清除机器上导出的 Excel 文件
            cleanLocalFile(absoluteResultFilePath);
        } catch (Exception e) {
            log.error("generateExcel error !", e);
        }
    }

    public List<TargetAnalysisQueryDTO> constructDiagnosisAnalysisQueryDTO(DiagnosisQueryDTO queryDTO, AnalysisTaskExecuteDTO executeDTO) {
        List<TargetAnalysisQueryDTO> list = constructDiagnosisQueryDTO(queryDTO, executeDTO);
        Constants.BUSINESS_LOG.info("AnalysisTaskExecuteDTO:{}, queryInfo:{}", executeDTO, list);
        return list;
    }

    /**
     * @param downloadDiagnosisDTO 下载诊断结果参数
     * @return AnalysisTaskExecuteDTO
     */
    private AnalysisTaskExecuteDTO getAnalysisTaskExecuteDTO(DownloadDiagnosisDTO downloadDiagnosisDTO) throws JsonProcessingException {
        AnalysisTaskDO task;
        if (Objects.nonNull(downloadDiagnosisDTO.getTaskId())) {
            task = analysisTaskRepository.getTaskByIdAndOperator(downloadDiagnosisDTO.getTaskId(), downloadDiagnosisDTO.getOperator());
        } else {
            task = analysisTaskRepository.getTaskByUniqueIdentification(downloadDiagnosisDTO.getUniqueId());
        }
        Map<String, List<Object>> conditionParamMap = JacksonUtils.getMapObject(task.getCondition());
        ConditionParamContext conditionParamContext = ConditionParamContext.builder().paramMap(conditionParamMap).build();
        // 查询条件、维度、指标mq发放
        return AnalysisTaskExecuteDTO.builder()
                .taskId(task.getId())
                .taskName(task.getTaskName())
                .conditionParamContext(conditionParamContext)
                .dimensionCodes(StringUtils.isNotBlank(task.getDimensions()) ? Splitter.on(",").splitToList(task.getDimensions()) : null)
                .targetCodes(Splitter.on(",").splitToList(task.getTargets()))
                .operator(task.getOperator())
                .taskMD5(task.getUniqueIdentification())
                .businessTypeId(task.getBusinessTypeId())
                .dataPermissionType(task.getDataPermissionType())
                .themeType(task.getThemeType())
                .build();
    }

    private List<TargetAnalysisQueryDTO> constructDiagnosisQueryDTO(DiagnosisQueryDTO queryDTO, AnalysisTaskExecuteDTO executeDTO) {
        List<String> targetCodes = executeDTO.getTargetCodes();
        ConditionParamContext paramContext = queryDTO.getConditionParamContext();
        Integer sortType = paramContext.getSortType();
        // 指标
        List<AnalysisTarget> analysisTargets = new ArrayList<>();
        for (String targetCode : targetCodes) {
            analysisTargets.add(targetRepository.selectByCode(targetCode));
        }

        Integer businessTypeId = executeDTO.getBusinessTypeId();
        Integer themeType = executeDTO.getThemeType();
        String operator = executeDTO.getOperator();

        List<AnalysisTargetGroup> analysisTargetGroups = targetGroupRepository.selectByBusinessType(
                businessTypeId == null ? 1 : businessTypeId, themeType);

        // Boolean.TRUE 是父指标，Boolean.FALSE 是子指标。只有转移分析会使用子指标
        Map<Integer, AnalysisTargetGroup> toMap = analysisTargetGroups.stream()
                .collect(Collectors.toMap(AnalysisTargetGroup::getId, v -> v, (v1, v2) -> v1));

        Map<Boolean, List<AnalysisTarget>> collect = analysisTargets.stream()
                .collect(Collectors.partitioningBy(v -> {
                    Integer type = toMap.get(v.getGroup()).getType();
                    return type != null && type.equals(2) || CloudConfigUtils.transUserGroup(v.getCode());
                }));

        List<DiagnosisConditionDTO> diagnosisConditions = executeDTO.getDiagnosisConditions();
        ArrayList<TargetAnalysisQueryDTO> targetAnalysisQueryDTOS = new ArrayList<>(diagnosisConditions.size());
        DimensionLayerNode root = CloudConfigUtils.getDimensionLayerPair(targetCodes.get(0));
        for (AnalysisTarget target : collect.get(Boolean.TRUE)) {
            // 循环指标，构造指标查询条件
            Properties properties = constructDiagnosisProperties(queryDTO, executeDTO, target);
            // 循环分组，构造不同维度分组
            for (DiagnosisConditionDTO diagnosisCondition : diagnosisConditions) {
                String dimensionCode = diagnosisCondition.getDimensionCode();
                String groupCode = diagnosisCondition.getGroupCode();
                String conditionKey = diagnosisCondition.getConditionKey();
                // 只有首次分析前端只传groupCode， 不传dimensionCode
                if (StringUtils.isNotBlank(groupCode) && StringUtils.isBlank(dimensionCode)) {
                    DimensionLayerNode dimensionLayerNode = getFirstDimensionByGroup(target.getCode(), groupCode);
                    if (dimensionLayerNode == null) {
                        continue;
                    }
                    dimensionCode = dimensionLayerNode.getDimensionCode();
                    conditionKey = dimensionLayerNode.getConditionKey();
                }

                AnalysisDimension analysisDimension = dimensionRepository.selectByThemeType(dimensionCode, businessTypeId, themeType);
                String supportDimensionKey;
                String supportDimensionValue;
                // 首层
                if (StringUtils.isNotBlank(groupCode) && StringUtils.isBlank(dimensionCode)) {
                    supportDimensionKey = conditionKey + "_support";
                    supportDimensionValue = conditionKey + " as " + supportDimensionKey;

                } else {
                    // 此时需要找父节点，比如展开团队时，此时conditionKey是level2_business_name，但是需要把team_name_support也展示出来
                    DimensionLayerNode dimensionLayerNode = diagnosisDimensionService.getDimensionLayersByGroupCodeAndDimensionCode(groupCode, dimensionCode, root);
                    supportDimensionKey = dimensionLayerNode.getConditionKey() + "_support";
                    supportDimensionValue = dimensionLayerNode.getConditionKey() + " as " + supportDimensionKey;
                }

                if (analysisDimension != null) {
                    properties.setProperty("dimensionValue", analysisDimension.getValue());
                    properties.setProperty("dimensionValues", analysisDimension.getValue().concat(",").concat(supportDimensionValue));
                } else {
                    properties.setProperty("dimensionValues", dimensionCode.concat(",").concat(supportDimensionValue));
                    properties.setProperty("dimensionValue", dimensionCode);
                }
                properties.setProperty("dimensionCodes", dimensionCode.concat(",").concat(supportDimensionKey));
                properties.setProperty("dimensionCode", dimensionCode);
                properties.setProperty("supportDimensionCode", supportDimensionKey);
                properties.setProperty("targetValues", GenericTokenParsers.parserSql(properties, target.getValue()));

                properties.setProperty("orderBy", sortType.equals(1) ? "abs(gap_prop)" : "abs(over_avg)");

                // 依据指标解析成对应的完整sql
                final TargetAnalysisQueryDTO targetAnalysisQueryDTO = this.constructQueryDTO(properties, target, operator, groupCode, dimensionCode);
                if (targetAnalysisQueryDTO != null) {
                    targetAnalysisQueryDTOS.add(targetAnalysisQueryDTO); // 解析后的指标sql集合录入
                }
            }

        }
        return targetAnalysisQueryDTOS;
    }


    private DimensionLayerNode getFirstDimensionByGroup(String targetCode, String groupCode) {
        return diagnosisDimensionService.getFirstLayerByGroupCoe(targetCode, groupCode);
    }

    /**
     * sql 解析
     */
    private TargetAnalysisQueryDTO constructQueryDTO(Properties properties, AnalysisTarget target, String operator, String groupCode, String dimensionCode) {
        // 依据指标解析成对应的完整sql
        String sql = parseSqlByProperties(properties, target.getSqlTemplateId());
        return StringUtils.isNotBlank(sql) ? TargetAnalysisQueryDTO.builder()
                .operator(operator)
                .querySql(sql)
                .targetCode(target.getCode())
                .targetName(target.getName())
                .businessTypeName(target.getBusinessTypeName())
                .dataBase(properties.getProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode()))
                .groupCode(groupCode)
                .dimensionCode(dimensionCode)
                .build() : null;
    }

    private Properties constructDiagnosisProperties(DiagnosisQueryDTO queryDTO, AnalysisTaskExecuteDTO executeDTO, AnalysisTarget analysisTarget) {
        ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
        Long endTime = conditionParamContext.getPayEndTime();
        DateTime payEndTime = DateUtil.date(endTime);
        String payEndTimeStr = DateUtil.format(DateUtil.date(payEndTime), NORM_DATETIME_PATTERN);

        Long startTime = conditionParamContext.getPayStartTime();
        DateTime payStartTime = DateUtil.date(startTime);
        String payStartTimeStr = DateUtil.format(payStartTime, NORM_DATETIME_PATTERN);

        Properties variables = new Properties();
        variables.setProperty("payStartTime", payStartTimeStr);
        variables.setProperty("payStartDate", payStartTimeStr.substring(0, 10));
        variables.setProperty("payEndTime", payEndTimeStr);
        variables.setProperty("payEndDate", payEndTimeStr.substring(0, 10));

        Integer ltPeriod = conditionParamContext.getLtPeriod();
        if (ltPeriod != null) {
            variables.setProperty("ltPeriod", String.valueOf(ltPeriod));
            DateTime ltPeriodEndTime = DateUtil.offsetMonth(payEndTime, ltPeriod);
            String ltPeriodEndTimeStr = DateUtil.format(ltPeriodEndTime, NORM_DATETIME_PATTERN);
            long ltPeriodDuration = DateUtil.between(payEndTime, ltPeriodEndTime, DateUnit.DAY, false);
            variables.setProperty("ltPeriodEndTime", ltPeriodEndTimeStr);
            variables.setProperty("ltPeriodDuration", String.valueOf(ltPeriodDuration));
        }

        Integer userGroupId = conditionParamContext.getUserGroup();
        if (Objects.nonNull(userGroupId)) {
            UserGroupDO userGroupDO = userGroupRepository.queryById(userGroupId);
            if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroupDO.getGroupType())) {
                String str = " inner join (select * from %s as ug where user_group_id = '%s') userGroup\n"
                    + "    on base.passport_id = userGroup.passport_id";
                variables.setProperty("userGroupUid", String.format(str, userGroupCKTable, userGroupId));
            } else {
                String str = " inner join (select * from %s as ug where user_group_id = '%s') userGroup\n"
                    + "    on base.passport_id = userGroup.passport_id and base.order_code=userGroup.order_code";
                variables.setProperty("userGroupOrderCode", String.format(str, userGroupCKTable, userGroupId));
            }
        }

        Integer minusLtPeriod = conditionParamContext.getMinusLtPeriod();
        if (minusLtPeriod != null) {
            DateTime minusLtPeriodStartTime = DateUtil.offsetMonth(payStartTime, -minusLtPeriod);
            String minusLtPeriodStartTimeStr = DateUtil.format(minusLtPeriodStartTime, NORM_DATETIME_PATTERN);
            long minusLtPeriodDuration = DateUtil.between(minusLtPeriodStartTime, payStartTime, DateUnit.DAY, false);
            variables.setProperty("minusLtPeriodStartTime", minusLtPeriodStartTimeStr);
            variables.setProperty("minusLtPeriodDuration", String.valueOf(minusLtPeriodDuration));
        }

        Integer autoRenewLtPeriodMonth = conditionParamContext.getAutoRenewLtPeriodMonth();
        if (autoRenewLtPeriodMonth != null) {
            variables.setProperty("autoRenewLtPeriodMonth", String.valueOf(autoRenewLtPeriodMonth));
            DateTime autoRenewLtPeriodMonthEndTime = DateUtil.offsetMonth(payEndTime, autoRenewLtPeriodMonth);
            String autoRenewLtPeriodMonthStr = DateUtil.format(autoRenewLtPeriodMonthEndTime, NORM_DATETIME_PATTERN);
            long autoRenewLtPeriodDuration = DateUtil.between(payEndTime, autoRenewLtPeriodMonthEndTime, DateUnit.DAY, false);
            variables.setProperty("autoRenewLtPeriodMonthEndTime", autoRenewLtPeriodMonthStr);
            variables.setProperty("autoRenewLtPeriodDuration", String.valueOf(autoRenewLtPeriodDuration));
        }

        String tableDT = getOrderTableDT();

        String targetValue = analysisTarget.getValue();
        String targetCode = analysisTarget.getCode();
        if (couldUseClickhouse(targetCode)) {
            variables.setProperty("tableName", CLICKHOUSE_WIDE_TABLE_NAME);
            variables.setProperty("dateBase", DataBaseTypeEnum.CLICK_HOUSE.getCode());
            targetValue = this.ckSqlHandler(targetCode, targetValue);
        } else {
            variables.setProperty("tableName", HIVE_WIDE_TABLE_NAME);
            variables.setProperty("hiveDt", tableDT);
            variables.setProperty("dt", tableDT);
            variables.setProperty("dateBase", DataBaseTypeEnum.HIVE.getCode());
        }

        Long compareStartTime = conditionParamContext.getCompareStartTime();
        Long compareEndTime = conditionParamContext.getCompareEndTime();
        if (compareStartTime != null && compareEndTime != null) {
            variables.setProperty("compareStartTime", DateUtil.format(DateUtil.date(compareStartTime), NORM_DATETIME_PATTERN));
            variables.setProperty("compareStartDate", DateUtil.format(DateUtil.date(compareStartTime), NORM_DATE_FORMAT));
            variables.setProperty("compareEndTime", DateUtil.format(DateUtil.date(compareEndTime), NORM_DATETIME_PATTERN));
            variables.setProperty("compareEndDate", DateUtil.format(DateUtil.date(compareEndTime), NORM_DATE_FORMAT));
            long minTime = Math.min(compareStartTime, startTime);
            long maxTime = Math.max(compareEndTime, endTime);
            variables.setProperty("minimumDate", DateUtil.format(DateUtil.date(minTime), NORM_DATE_FORMAT));
            variables.setProperty("minimumTime", DateUtil.format(DateUtil.date(minTime), NORM_DATETIME_PATTERN));
            variables.setProperty("maximumDate", DateUtil.format(DateUtil.date(maxTime), NORM_DATE_FORMAT));
            variables.setProperty("maximumTime", DateUtil.format(DateUtil.date(maxTime), NORM_DATETIME_PATTERN));
        }

        this.arpuDeadlineWriteToParroperties(variables, ConditionPayEndTimeDTO.of(conditionParamContext.getPayEndTime()));

        ConditionDataPermissionHandler conditionDataPermissionHandler = ConditionDataPermissionHandler.getConditionDataPermissionHandler(executeDTO);
        List<Integer> vipTypes = conditionParamContext.getVipTypes();
        List<Integer> vipGroupIds = conditionParamContext.getGroupIds();
        if (CollectionUtils.isNotEmpty(vipGroupIds)) {
            List<String> vipGroupIds2Str = conditionDataPermissionHandler
                    .intersectionWithOwnedDataPermissions(executeDTO, vipGroupIds.stream().map(String::valueOf).collect(Collectors.toList()),
                            ConditionEnum.VIP_TYPE, LabelEnum.VT1); // 与已申请权限取交集 -- 会员分组
            variables.setProperty("vipGroup", Joiner.on(",").join(vipGroupIds2Str));
        }
        if (CollectionUtils.isNotEmpty(vipTypes)) {
            List<String> vipTypes2Str = conditionDataPermissionHandler
                    .intersectionWithOwnedDataPermissions(executeDTO, vipTypes.stream().map(String::valueOf).collect(Collectors.toList()),
                            ConditionEnum.VIP_TYPE, LabelEnum.VT2); // 与已申请权限取交集 -- 会员类型
            variables.setProperty("vipTypes", Joiner.on(",").join(vipTypes2Str));
        }

        Map<String, List<Object>> conditionParamMap = conditionParamContext.getParamMap();

        //处理通用条件部分
        for (Map.Entry<String, List<Object>> entry : conditionParamMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            String code = entry.getKey();
            if (variables.get(code) != null) {
                continue;
            }
            AnalysisCondition condition = conditionRepository.selectByCode(code);
            if (condition == null) {
                continue;
            }
            String propValue = constructCommonConditionPropValue(code, conditionParamMap, condition);
            if (StringUtils.isNotBlank(propValue)) {
                variables.setProperty(code, propValue);
            }
        }

        //处理诊断分析条件部分，此时每个分组的条件其实也是全局条件
        List<DiagnosisConditionDTO> diagnosisConditions = executeDTO.getDiagnosisConditions();
        HashMap<String, String> conditionKeyAndValueMap = new HashMap<>(diagnosisConditions.size());
        for (DiagnosisConditionDTO diagnosisCondition : diagnosisConditions) {
            String conditionKey = diagnosisCondition.getConditionKey();
            Object conditionValue = diagnosisCondition.getConditionValue();
            if (StringUtils.isBlank(conditionKey)) {
                continue;
            }
            AnalysisCondition condition = conditionRepository.selectByCode(conditionKey);
            if (condition != null) {
                ArrayList<Object> objectValue = Lists.newArrayList(conditionValue);
                HashMap<String, List<Object>> hashMap = new HashMap<String, List<Object>>() {{
                    put(conditionKey, objectValue);
                }};
                String propValue = constructCommonConditionPropValue(conditionKey, hashMap, condition);
                if (StringUtils.isNotBlank(propValue)) {
                    conditionKeyAndValueMap.put(conditionKey, propValue);
                }
            } else {
                conditionKeyAndValueMap.put(conditionKey, "'" + conditionValue + "'");
            }
        }
        // 增加诊断分析过程中下转对应的每个条件
        if (MapUtils.isNotEmpty(conditionKeyAndValueMap)) {
            variables.setProperty("diagnosisCondition", Joiner.on(" and ").withKeyValueSeparator(" in ")
                    .join(Maps.transformValues(conditionKeyAndValueMap, value -> " ( " + value + " ) ")));
        }

        //处理因维度分组转换导致丢失的条件， 这些条件其实是下转路径上的条件
        Map<String, List<Object>> pathConditionParamMap = queryDTO.getDiagnosisPathConditionParamMap();
        HashMap<String, String> pathConditionKeyAndValueMap = new HashMap<>();
        if (MapUtils.isNotEmpty(pathConditionParamMap)) {
            for (Entry<String, List<Object>> entry : pathConditionParamMap.entrySet()) {
                String code = entry.getKey();
                if (variables.get(code) != null) {
                    continue;
                }
                AnalysisCondition condition = conditionRepository.selectByCode(code);
                String propValue;
                if (condition != null) {
                    propValue = constructCommonConditionPropValue(code, pathConditionParamMap, condition);
                } else {
                    propValue = entry.getValue().stream()
                            .map(value -> "'" + value + "'")
                            .collect(Collectors.joining(","));
                }
                if (StringUtils.isNotBlank(propValue)) {
                    pathConditionKeyAndValueMap.put(code, propValue);
                }
            }
        }
        if (MapUtils.isNotEmpty(pathConditionKeyAndValueMap)) {
            variables.setProperty("diagnosisPathCondition", Joiner.on(" and ").withKeyValueSeparator(" in ")
                    .join(Maps.transformValues(pathConditionKeyAndValueMap, value -> " ( " + value + " ) ")));
        }
        LinkedHashMap<String, String> diagnosisConditionsMap = new LinkedHashMap<>();
        diagnosisConditionsMap.putAll(conditionKeyAndValueMap);
        diagnosisConditionsMap.putAll(pathConditionKeyAndValueMap);
        if (MapUtils.isNotEmpty(diagnosisConditionsMap)) {
            variables.setProperty("diagnosisConditions", Joiner.on(" and ").withKeyValueSeparator(" in ")
                    .join(Maps.transformValues(diagnosisConditionsMap, value -> " ( " + value + " ) ")));
        }

        variables.setProperty("targetValues", GenericTokenParsers.parserSql(variables, targetValue));
        this.baseDataPermissionsCtrl(variables, executeDTO); // 权限校验
        this.fillBaseQueryCondition(variables, executeDTO);

        Constants.BUSINESS_LOG.info("constructProperties result:{}", variables);
        log.info("constructProperties result:{}", variables);
        return variables;
    }


    private AnalysisTaskDO initializeTaskDO(DiagnosisQueryDTO baseQueryDTO) {
        List<String> targetCodes = baseQueryDTO.getTargetCodes();
        List<DiagnosisConditionDTO> diagnosisConditions = baseQueryDTO.getDiagnosisConditions();
        Map<String, List<Object>> pathConditionParamMap = baseQueryDTO.getDiagnosisPathConditionParamMap();
        DiagnosisConditionPairDTO conditionPairDTO = new DiagnosisConditionPairDTO(diagnosisConditions, pathConditionParamMap);
        ConditionParamContext conditionParamContext = baseQueryDTO.getConditionParamContext();
        String operator = baseQueryDTO.getOperator();
        return AnalysisTaskDO.builder()
                .taskName(baseQueryDTO.getTaskName())
                .status(TaskStatusEnum.RUNNING.getStatus())
                .targets(Joiner.on(",").join(targetCodes)) // 指标
                .diagnosisCondition(JacksonUtils.toJsonString(conditionPairDTO)) // 维度c
                .condition(JacksonUtils.toJsonString(conditionParamContext.getParamMap())) // 条件 - 条件需要作为基础数据的条件[产品、渠道]
                .createTime(DateUtil.date())
                .updateTime(DateUtil.date())
                .operator(operator)
                .uniqueIdentification(baseQueryDTO.getUniqueId())
                .businessTypeId(baseQueryDTO.getBusinessTypeId())
                .dataPermissionType(baseQueryDTO.getDataPermissionType())
                .themeType(baseQueryDTO.getThemeType())
                .build();
    }
}
