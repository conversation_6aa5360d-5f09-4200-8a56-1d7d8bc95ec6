package com.iqiyi.vip.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.MetricTypeEnum;
import com.iqiyi.vip.enums.TargetGroupOptionEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.util.EagleMeterReporter;
import com.iqiyi.vip.utils.JacksonUtils;
import com.iqiyi.vip.utils.MonitorUtils;

/**
 * @author: guojing
 * @date: 2025/3/7 18:29
 */
@Slf4j
@Component
public class ScheduleAnalysisTask extends CommonAnalysisTask {

    @ConfigJsonValue("${target.to.metric.type.map:{\"orderCount_real\":\"Counter\",\"kedanjia_real\":\"Gauge\"}}")
    private Map<String, String> targetToMetricTypeMap;

    public void setTaskRunning(AnalysisTaskExecuteDTO executeDTO) {
        Long taskId = executeDTO.getTaskId();
        analysisTaskRepository.reScheduledSetTaskStatus(taskId, TaskStatusEnum.RUNNING.getStatus(), null, null);
    }

    /**
     * 处理监控分析任务
     * 将查询结果上报到Prometheus
     *
     * @param executeDTO 任务执行DTO
     * @throws Exception 执行异常
     */
    public void processAsync(AnalysisTaskExecuteDTO executeDTO) throws Exception {
        StopWatch stopWatch = new StopWatch("scheduledTask-" + getStopWatchName(executeDTO));
        stopWatch.start();
        TimeInterval timeInterval = DateUtil.timer();
        setTaskRunning(executeDTO);
        String operator = executeDTO.getOperator();
        Long taskId = executeDTO.getTaskId();
        log.info("ScheduleAnalysisTask start, taskId:{}, taskName:{}", taskId, executeDTO.getTaskName());
        try {
            // 获取分析目标
            List<AnalysisTarget> analysisTargets = executeDTO.getTargetCodes().stream()
                .map(analysisTargetRepository::selectByCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            Map<String, AnalysisTarget> targetCodeToTargetMap = analysisTargets.stream()
                .collect(Collectors.toMap(AnalysisTarget::getCode, t -> t));


            //大数据查询 sql 处理
            List<TargetAnalysisQueryDTO> analysisQueryDTOS = constructTargetAnalysisQueryDTO(executeDTO, analysisTargets, false);
            if (CollectionUtils.isEmpty(analysisQueryDTOS)) {
                log.error("ScheduleAnalysisTask failed: 未配置SQL模板, taskId:{}", taskId);
                analysisTaskRepository.reScheduledSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null, null);
                String alertContent = String.format("定时任务查询失败！任务id：%s, 任务名称：%s, 运行环境：%s", taskId, executeDTO.getTaskName(), System.getProperty("spring.profiles.active"));
                alterService.sendHotChat(alertContent);
                return;
            }
            
            // 保存查询信息
            TaskQueryInfo taskQueryInfo = TaskQueryInfo.buildFrom(taskId, JacksonUtils.toJsonString(analysisQueryDTOS));
            taskQueryInfoService.saveOrUpdateScheduledQueryInfo(taskQueryInfo);
            
            // 执行查询
            List<OriginalQueryResultDTO> queryResultDTOS = executeQuery(analysisQueryDTOS, taskId);
            if (CollectionUtils.isEmpty(queryResultDTOS) || queryResultDTOS.stream().noneMatch(OriginalQueryResultDTO::getSuccess)) {
                log.error("ScheduleAnalysisTask failed: 查询执行失败, taskId:{}", taskId);
                analysisTaskRepository.reScheduledSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null,
                        (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getTotalTimeMillis()));
                return;
            }
            
            // 处理查询结果并上报到Prometheus
            reportToPrometheus(queryResultDTOS, executeDTO, targetCodeToTargetMap);
            
            stopWatch.stop();
            analysisTaskRepository.reScheduledSetTaskStatus(taskId, TaskStatusEnum.FINISHED.getStatus(), null,
                    (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getTotalTimeMillis()));
            log.info("ScheduleAnalysisTask finished，耗时:{}分钟，executeDTO:{}",
                    timeInterval.intervalMinute(), executeDTO);
            
        } catch (Exception e) {
            stopWatch.stop();
            log.error("ScheduleAnalysisTask has exception! taskId:{}, operator:{}", taskId, operator, e);
            analysisTaskRepository.reScheduledSetTaskStatus(taskId, TaskStatusEnum.FAILED.getStatus(), null,
                    (int) TimeUnit.MILLISECONDS.toSeconds(stopWatch.getTotalTimeMillis()));
            alterService.sendHotChat(String.format("ScheduleAnalysisTask failed！任务id：%s, 操作人：%s, 运行环境：%s",
                    taskId, operator, System.getProperty("spring.profiles.active")));
        }
    }

    public List<OriginalQueryResultDTO> executeQuery(List<TargetAnalysisQueryDTO> analysisQueryDTOS, Long taskId)
        throws ExecutionException, InterruptedException {
        String activeFile = System.getProperty("spring.profiles.active");
        ArrayList<OriginalQueryResultDTO> queryResultDTOS = new ArrayList<>();
        for (TargetAnalysisQueryDTO queryDTO : analysisQueryDTOS) {
            queryDTO.setOperator(queryDTO.getOperator());
            queryDTO.setTaskId(taskId);
            queryDTO.setEnv(activeFile);
            queryDTO.setTargetType(TargetGroupOptionEnum.ORDINARY_INDICATORS.getCode());
            queryDTO.setTaskSource(AnalysisTaskSourceEnum.SCHEDULED);
            OriginalQueryResultDTO queryResultDTO = new SqlExecutor(queryDTO).call();
            queryResultDTO.setDateYM(queryDTO.getDateYM());
            queryResultDTOS.add(queryResultDTO);
        }
        return queryResultDTOS;
    }

    /**
     * 将查询结果上报到Prometheus
     *
     * @param queryResultDTOS 查询结果列表
     * @param executeDTO 任务执行DTO
     */
    private void reportToPrometheus(List<OriginalQueryResultDTO> queryResultDTOS, AnalysisTaskExecuteDTO executeDTO, Map<String, AnalysisTarget> targetCodeToTargetMap) {
        Integer themeType = executeDTO.getThemeType();
        ThemeTypeEnum themeTypeEnum = ThemeTypeEnum.findEnumByThemeType(themeType);
        String dimensionCodePart = CollectionUtils.isNotEmpty(executeDTO.getDimensionCodes())
            ? String.join("_", executeDTO.getDimensionCodes())
            : null;

        for (OriginalQueryResultDTO resultDTO : queryResultDTOS) {
            if (!resultDTO.getSuccess()) {
                continue;
            }
            String targetCode = resultDTO.getTargetCode();
            List<LinkedHashMap<String, Object>> dataList = resultDTO.getDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                log.info("目标[{}]无数据，跳过上报", targetCode);
                continue;
            }
            // 获取指标名称，用于Prometheus标签
            AnalysisTarget target = MapUtils.getObject(targetCodeToTargetMap, targetCode, analysisTargetRepository.selectByCode(targetCode));
            if (target == null) {
                log.error("未找到目标[{}]的信息，跳过上报", targetCode);
                continue;
            }

            String metricTypeStr = MapUtils.getString(targetToMetricTypeMap, targetCode);
            MetricTypeEnum metricTypeEnum = MetricTypeEnum.findByValue(metricTypeStr);
            if (StringUtils.isBlank(metricTypeStr)) {
                log.error("target metric type invalid, targetCode:{}, metricType:{}", targetCode, metricTypeStr);
                continue;
            }
            String valueHeadColumn = resultDTO.getHeadColumnMap().values().stream().reduce((first, second) -> second).orElse(null);
            String metricName = MonitorUtils.genMetricName(themeTypeEnum.name(), targetCode, dimensionCodePart);
            for (Map<String, Object> data : dataList) {
                Map<String, String> metricLabels = new HashMap<>();
                if (CollectionUtils.isNotEmpty(executeDTO.getDimensionCodes())) {
                    for (String dimensionCode : executeDTO.getDimensionCodes()) {
                        String labelValue = MapUtils.getString(data, dimensionCode, "unknown");
                        metricLabels.put(dimensionCode, labelValue);
                    }
                }

                double metricValue = MapUtils.getDoubleValue(data, valueHeadColumn);
                log.info("------metricName：{}, metricLabels:{}, metricValue:{}", metricName, metricLabels, metricValue);
                EagleMeterReporter.reportValue(metricTypeEnum, metricName, metricLabels, metricValue);
            }
        }
    }
    
}
