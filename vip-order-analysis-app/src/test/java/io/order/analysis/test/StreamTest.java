package io.order.analysis.test;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/15 11:38
 */
public class StreamTest {

    public static void main(String[] args) {

        final ArrayList<String> strings = Lists.newArrayList("1", "2", "234", "432", "5", "6", "432", "432", "432", "432", "234", "234", "9", "10");
        final List<String> collect = strings.stream().distinct().collect(Collectors.toList());
        System.out.println(strings);
        System.out.println(collect);

    }


}
