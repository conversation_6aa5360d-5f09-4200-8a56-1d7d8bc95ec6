package com.iqiyi.vip.validator.chain;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 校验责任链测试
 * 
 * <AUTHOR>
 * @date 2024/12/14
 */
@SpringBootTest
@ActiveProfiles("test")
public class ValidationChainTest {
    
    @Resource
    private ValidationChainManager validationChainManager;
    
    @Test
    public void testValidationChain() {
        // 构建测试上下文
        HashMap<Integer, Boolean> resultMap = new HashMap<>();
        ValidationContext context = ValidationContext.builder()
            .resultMap(resultMap)
            .dt("2024-12-14")
            .taskId(1L)
            .offset(2)
            .orderCountGapLimit(10)
            .orderCountAndSumFeeSql("SELECT count(*) as orderCount, sum(real_fee) as sumFee FROM %s WHERE dt = '%s'")
            .build();
        
        // 执行责任链
        validationChainManager.executeValidationChain(context);
        
        // 验证结果
        assertNotNull(resultMap);
        assertTrue(resultMap.size() > 0);
        
        System.out.println("校验结果: " + resultMap);
    }
}
