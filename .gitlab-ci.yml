image: docker-registry.qiyi.virtual/library/ci-env-jdk8-maven:3.6.0-iqiyi-2

push_atlas_job:
  stage: test
  only:
    - pushes
    - master

  script:
    - echo "enter push_atlas_job"
    - |
      SYSTEM_ID="627c84cd48f8a6001023eafd"  # 填写atlas系统systemId
      ATLAS_API_KEY="0d79ff2fdf82f16ad175373c5e5b7bce" # 填写组内分配的key
      SYSTEM_NAME="vip-order-analysis-admin" # 填写配置生成的json文件名
      
      
      FILE_NAME="target/generated-swagger-api/$SYSTEM_NAME.json"
      json_string=$(printf '{"from":"swaggerJson","branch":"%s","conflictStrategy":"cover","systemId":"%s"}' $CI_COMMIT_REF_NAME $SYSTEM_ID)
      yum install -y perl
      encoded_json_string=$(echo -n "$json_string" | perl -pe 's/([^-_.~A-Za-z0-9])/sprintf("%%%02X", ord($1))/seg')
      echo "请求链接：http://api.atlas.qiyi.domain/atlas-sysview/api/interface/interface_import_from_file?contentParam=$encoded_json_string"
      mvn clean install -Dmaven.test.skip=true
      response=$(curl --location --request POST "http://api.atlas.qiyi.domain/atlas-sysview/api/interface/interface_import_from_file?contentParam=$encoded_json_string" \
      --header "X-atlas-api-key: $ATLAS_API_KEY" \
      --form "file=@$FILE_NAME")
      echo "HTTP响应结果是: $response"
      rm -f "$FILE_NAME"