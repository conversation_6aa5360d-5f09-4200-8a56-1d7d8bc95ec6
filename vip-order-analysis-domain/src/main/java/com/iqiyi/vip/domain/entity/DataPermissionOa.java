package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/18 15:38
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DataPermissionOa implements java.io.Serializable {

    private Integer id;
    private String oaIdentifier;
    private String account;
    private String permissionReason;
    private String permission;
    private Integer permissionStatus;
    private String rejectReason;
    private Integer themeType;
    private Integer type;
    private String operators;
    private Date createTime;
    private Date updateTime;
    private String messageId;

}
