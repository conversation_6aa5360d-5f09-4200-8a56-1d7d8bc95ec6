package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.condition.FvChannelPair;

/**
 * <AUTHOR>
 * @className FvStructureRepository
 * @description
 * @date 2022/6/13
 **/
public interface FvStructureRepository {

    List<FvChannelPair> getFvChannelsByLevel(Integer level);

    /**
     * 获取当前等级所有渠道
     */
    List<FvChannelPair> fvChannels(Integer preChannelId, int curLevel, int nextLevel, Integer teamId);

    /**
     * g根据级别与团队查询
     */
    List<FvChannelPair> getFvByLevelAndTeamId(Integer level, Integer teamId);

}
