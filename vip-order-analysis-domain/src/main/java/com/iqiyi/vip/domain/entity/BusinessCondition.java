package com.iqiyi.vip.domain.entity;

import com.iqiyi.vip.dto.condition.BusinessConditionUpdateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分析类型下的条件管理实体
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2023/08/11
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCondition {
    private Long id;
    private Long themeId;
    private Long businessId;
    private Long conditionId;
    private Integer sort;
    private String operator;
    private Date createTime;
    private Date updateTime;

    public BusinessCondition(Long themeId, Long businessId, String operator, BusinessConditionUpdateDTO.ConditionSort conditionSort) {
        this.themeId = themeId;
        this.businessId = businessId;
        this.operator = operator;
        this.conditionId = conditionSort.getId();
        this.sort = conditionSort.getSort();
        this.createTime = new Date();
        this.updateTime = this.createTime;
    }
}
