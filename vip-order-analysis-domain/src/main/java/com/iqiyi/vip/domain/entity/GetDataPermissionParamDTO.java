package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;

/**
 * <AUTHOR>
 * @date 2022/8/17 15:45
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetDataPermissionParamDTO implements java.io.Serializable {

    private ConditionEnum conditionEnum;
    private LabelEnum label;
    private Integer teamId;
    private List<Integer> permissionIds;
}
