package com.iqiyi.vip.domain.factory;

import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.dto.dimension.DimensionUpdateDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.StatusEnum;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

import static com.iqiyi.vip.constant.Constants.DEFAULT_OPERATOR;
import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public class AnalysisDimensionFactory {

    public static AnalysisDimension getSaveDimension(DimensionUpdateDTO updateDTO) {
        AnalysisDimension dimension = new AnalysisDimension();
        dimension.setCode(updateDTO.getCode());
        dimension.setName(updateDTO.getName());
        dimension.setValue(processValue(updateDTO.getValue(), updateDTO.getCode()));
        dimension.setGroup(updateDTO.getGroup());
        dimension.setOrder(updateDTO.getOrder());
        dimension.setCreateTime(new Date());
        dimension.setUpdateTime(new Date());
        dimension.setCreateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        dimension.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        dimension.setStatus(StatusEnum.EFFECTIV.getCode());
        dimension.setCommitNote(updateDTO.getCommitNote());
        dimension.setVersion(DEFAULT_VERSION);
        dimension.setDocumentation(updateDTO.getDocumentation());
        return dimension;
    }

    public static void updateExist(AnalysisDimension exist, DimensionUpdateDTO updateDTO, Integer newVersion) {
        exist.setCode(updateDTO.getCode());
        exist.setName(updateDTO.getName());
        exist.setValue(processValue(updateDTO.getValue(), updateDTO.getCode()));
        exist.setGroup(updateDTO.getGroup());
        exist.setOrder(updateDTO.getOrder());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        exist.setCommitNote(updateDTO.getCommitNote());
        exist.setVersion(newVersion);
        exist.setDocumentation(updateDTO.getDocumentation());
    }

    private static String regexp = "^(CASE|case)[\\s\\S]*?(END|end)$";

    /**
     * 处理维值别名
     *
     * @param value
     * @return
     */
    private static String processValue(String value, String code) {
        if (value.matches(regexp)) {
            return value + " as " + code;
        }
        return value;
    }

    public static void versionSwitch(AnalysisDimension exist, ConfigChangeLog log, VersionSwitchReqDTO reqDTO) {
        exist.setCode(log.getConfigCode());
        exist.setName(log.getConfigName());
        exist.setValue(log.getConfigValue());
        exist.setGroup(log.getConfigGroupId());
        exist.setDocumentation(log.getConfigDesc());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(reqDTO.getOperator()) ? DEFAULT_OPERATOR : reqDTO.getOperator());
        exist.setVersion(log.getVersion());
        exist.setCommitNote(log.getCommitNote());
    }
}
