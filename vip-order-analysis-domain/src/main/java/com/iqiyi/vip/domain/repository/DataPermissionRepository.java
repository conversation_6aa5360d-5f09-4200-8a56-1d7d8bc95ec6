package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.domain.entity.DataPermissionOa;
import com.iqiyi.vip.dto.oa.OaApprovedResponse;
import com.iqiyi.vip.dto.permission.DataPermissionApplyDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApprovalCallbackDTO;
import com.iqiyi.vip.enums.DataPermissionOaStatusEnum;

/**
 * <AUTHOR>
 * @date 2022/8/17 17:59
 */
public interface DataPermissionRepository {

    /**
     * 根据Oa唯一标识获取审批记录
     * @param oaIdentifier
     * @return
     */
    List<DataPermissionOa> getDataPermissionOa(String oaIdentifier, String messageId);

    int updateDataPermissionOaAcIdByMessageId(String oaIdentifier, String messageId);

    int saveDataPermissionOa(DataPermissionApplyDTO dataPermissionApplyDTO, OaApprovedResponse oaApprovedResponse);

    int existApprovingDataPermission(String account);

    /**
     * 更新审批状态
     * @param dataPermissionApprovalCallbackDTO
     * @param dataPermissionOaStatus
     * @return
     */
    int updateDataPermissionOaApprovalStatus(DataPermissionApprovalCallbackDTO dataPermissionApprovalCallbackDTO, DataPermissionOaStatusEnum dataPermissionOaStatus);

    int saveDataPermission(DataPermission dataPermission);

    int updateDataPermission(DataPermission dataPermission);

    List<DataPermission> getDataPermissions(String account);

    List<DataPermission> getDataPermissionsByThemeType(String account, Integer themeType);

    DataPermission getDataPermission(String account, Integer themeType, Integer dataPermissionType);

    int ownedDataPermission(String account);

}
