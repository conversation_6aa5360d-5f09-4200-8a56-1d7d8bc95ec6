package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Data
@ApiModel(value = "天眼业务信息")
public class AnalysisBusinessType {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "业务名称")
    private String name;
    @ApiModelProperty(value = "指标排序", required = true)
    private Integer order;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty(value = "状态")
    private Integer status;
}
