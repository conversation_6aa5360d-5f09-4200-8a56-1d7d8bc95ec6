package com.iqiyi.vip.domain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.enums.ConfigTypeEnum;

/**
 * config_change_log
 *
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigChangeLog implements Serializable {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 配置类型：1：指标模板 2：指标 3：维度
     */
    private Integer configType;

    /**
     * 原配置id
     */
    private Long configId;

    /**
     * 原配置code
     */
    private String configCode;

    /**
     * 原配置名称
     */
    private String configName;
    /**
     * 原配置描述
     */
    private String configDesc;

    /**
     * 原配置值
     */
    private String configValue;

    /**
     * 原配置提交备注
     */
    private String commitNote;

    /**
     * 配置分组
     */
    private Integer configGroupId;
    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public static ConfigChangeLog buildFrom(AnalysisSqlTemplate template, Integer version) {
        return ConfigChangeLog.builder()
            .configType(ConfigTypeEnum.SQL_TEMPLATE.getType())
            .configId(template.getId())
            .commitNote(template.getCommitNote())
            .configName(template.getName())
            .configValue(template.getValue())
            .operator(template.getUpdateOpr())
            .version(version)
            .configDesc(template.getDescription())
            .configCode(template.getCode())
            .build();
    }

    public static ConfigChangeLog buildFrom(AnalysisTarget target, Integer version) {
        return ConfigChangeLog.builder()
            .configType(ConfigTypeEnum.TARGET.getType())
            .configId(target.getId())
            .commitNote(target.getCommitNote())
            .configName(target.getName())
            .configValue(target.getValue())
            .operator(target.getUpdateOpr())
            .configDesc(target.getDocumentation())
            .configCode(target.getCode())
            .version(version)
            .templateId(target.getSqlTemplateId())
            .configGroupId(target.getGroup())
            .build();
    }

    public static ConfigChangeLog buildFrom(AnalysisDimension dimension, Integer version) {
        return ConfigChangeLog.builder()
            .configType(ConfigTypeEnum.DIMENSION.getType())
            .configId(dimension.getId())
            .commitNote(dimension.getCommitNote())
            .configName(dimension.getName())
            .configValue(dimension.getValue())
            .operator(dimension.getUpdateOpr())
            .version(version)
            .configDesc(dimension.getDocumentation())
            .configCode(dimension.getCode())
            .configGroupId(dimension.getGroup())
            .build();
    }

    private static final long serialVersionUID = 1L;
}