package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * available_dt
 *
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AvailableDt implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    private String tableDt;

    private Date createTime;

    private Date updateTime;

    private Integer status;

    private Integer themeType;

    private Integer themeSubType;

    private static final long serialVersionUID = 1L;
}