package com.iqiyi.vip.domain.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * data_check_result
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(Include.NON_NULL)
public class DataCheckResult implements Serializable {
    private Integer id;

    /**
     * 校验case的id
     */
    private Integer caseId;

    private List<Integer> caseIds;

    /**
     * 结果对应的维度
     */
    private String key;

    /**
     * 数据查询结果
     */
    private Double value;

    /**
     * 查询dt
     */
    private String dt;

    /**
     * 数据校验范围开始时间
     */
    private String beginTime;

    /**
     * 数据校验范围结束时间
     */
    private String endTime;

    /**
     * 数据来源表
     */
    private String dataSource;

    private Date createTime;

    private Date updateTime;

    private String dimension;

    private static final long serialVersionUID = 1L;

    public static DataCheckResult buildFrom(Integer caseId, String key, double value, String dt, String beginTime, String endTime, String dimension, String dataSource) {
        return DataCheckResult.builder()
            .caseId(caseId)
            .key(key)
            .value(value)
            .dt(dt)
            .beginTime(beginTime)
            .endTime(endTime)
            .dimension(dimension)
            .dataSource(dataSource)
            .createTime(new DateTime())
            .updateTime(new DateTime())
            .build();
    }
}