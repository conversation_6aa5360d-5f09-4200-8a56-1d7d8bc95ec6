package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.ConditionCascadePageQryDTO;

import java.util.List;

/**
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2023/08/07
 */
public interface ConditionCascadeRepository {

    Long save(ConditionCascade cascade);

    void update(ConditionCascade cascade);

    void deleteByThemeAndBusiness(Integer themeId, Integer businessId);

    void deleteByConditionId(Long conditionId);

    ConditionCascade selectByConditions(Integer themeId, Integer businessId, Integer currentConditionId, Integer nextConditionId);

    List<ConditionCascade> list(Integer themeId, Integer businessId);

    PageListResult<ConditionCascade> selectPageList(ConditionCascadePageQryDTO pageQryDTO);
}
