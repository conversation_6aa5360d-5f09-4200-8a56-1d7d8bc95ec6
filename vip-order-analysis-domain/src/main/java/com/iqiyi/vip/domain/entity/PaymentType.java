package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@Data
@ApiModel(value = "支付方式")
public class PaymentType {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "状态 1：可用")
    private Integer status;
    @ApiModelProperty(value = "关联支付渠道")
    private Long payChannel;
    @ApiModelProperty(value = "暂时无用")
    private Long subPayChannel;
}
