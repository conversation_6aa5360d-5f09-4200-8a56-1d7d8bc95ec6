package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;

/**
 * @author: guojing
 * @date: 2024/10/30 15:12
 */
public interface CompetitorPriceRepository {

    List<String> brandsList();

    List<String> userStatusList(String brand, String date);

    CompetitorDateRangeDTO dateRange();

    List<CompetitorPriceDTO> search(CompetitorPriceSearchParam param);

    void save(List<CompetitorPriceDTO> competitorPriceDTOS);

}
