package com.iqiyi.vip.domain.repository;

import java.util.List;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;

/**
 * <AUTHOR>
 * @className QueryInfoRepository
 * @description
 * @date 2023/1/31
 **/
public interface TaskQueryInfoRepository {

    void updateByTaskId(TaskQueryInfo queryInfo);

    void add(TaskQueryInfo queryInfo);

    TaskQueryInfo searchByTaskId(long taskId);

    List<TaskQueryInfo> searchByTaskIds(List<Long> taskIds);


    void updateByScheduledTaskId(TaskQueryInfo queryInfo);

    void addScheduled(TaskQueryInfo queryInfo);

    TaskQueryInfo searchByScheduledTaskId(long taskId);

}
