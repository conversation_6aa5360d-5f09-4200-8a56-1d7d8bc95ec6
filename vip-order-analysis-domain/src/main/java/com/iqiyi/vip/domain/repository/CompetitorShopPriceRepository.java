package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSearchParam;

/**
 * 竞品店铺价格仓库
 */
public interface CompetitorShopPriceRepository {

    /**
     * 查询所有平台
     *
     * @return 平台列表
     */
    List<String> platformList();

    /**
     * 查询品牌列表
     *
     * @param platform 平台
     * @param date 日期
     * @return 品牌列表
     */
    List<String> brandsList(String platform, String date);

    /**
     * 查询人群列表
     *
     * @param platform 平台
     * @param brand 品牌
     * @param date 日期
     * @return 人群列表
     */
    List<String> userGroupList(String platform, String brand, String date);

    /**
     * 查询日期范围
     *
     * @return 日期范围
     */
    CompetitorDateRangeDTO dateRange();

    /**
     * 保存价格信息
     *
     * @param param 保存参数
     */
    void save(CompetitorShopPriceSaveDTO param);

    /**
     * 根据条件查询
     *
     * @param param 查询参数
     * @return 价格信息列表
     */
    List<CompetitorShopPriceDTO> search(CompetitorShopPriceSearchParam param);
    
    /**
     * 根据条件和日期查询
     *
     * @param param 查询参数
     * @param date 日期字符串
     * @return 价格信息列表
     */
    List<CompetitorShopPriceDTO> search(CompetitorShopPriceSearchParam param, String date);
    
    /**
     * 根据日期范围查询
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param storeType 店铺类型
     * @param platform 平台（可选）
     * @param brand 品牌（可选）
     * @return 价格信息列表
     */
    List<CompetitorShopPriceDTO> searchByDateRange(String startDate, String endDate, Integer storeType, String platform, String brand);

} 