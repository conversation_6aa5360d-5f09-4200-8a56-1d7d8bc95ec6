package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.ConfigChangeLog;

/**
 * <AUTHOR>
 * @className ConfigChangeLogRepository
 * @description
 * @date 2022/11/23
 **/
public interface ConfigChangeLogRepository {

    void save(ConfigChangeLog configChangeLog);

    Integer getMaxVersion(Integer configType, Long configId);

    List<ConfigChangeLog> getConfigChangeLogs(Integer configType, Long configId, Integer version);

    int delete(Integer configType, Long configId);
}
