package com.iqiyi.vip.domain.repository;

import java.sql.SQLException;

import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;

/**
 * <AUTHOR>
 * @date 2022/3/22 下午 08:17
 */
public interface OrderRepository {

    OriginalQueryResultDTO queryTargetResult(TargetAnalysisQueryDTO targetAnalysisQueryDTO) throws SQLException;

    OriginalQueryResultDTO queryTargetResultAfter3SecRetry(TargetAnalysisQueryDTO targetAnalysisQueryDTO) throws SQLException;

}
