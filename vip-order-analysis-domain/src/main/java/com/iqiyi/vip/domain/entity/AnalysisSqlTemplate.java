package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "sql模板信息")
public class AnalysisSqlTemplate {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板sql值")
    private String value;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createOpr;

    @ApiModelProperty(value = "更新人")
    private String updateOpr;

    @ApiModelProperty(value = "状态 0：无效 1：有效")
    private Integer status;

    @ApiModelProperty(value = "更改记录")
    private String commitNote;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "业务主题ID")
    private Integer themeType;
}
