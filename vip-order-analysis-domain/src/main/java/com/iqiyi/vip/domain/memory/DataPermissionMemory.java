package com.iqiyi.vip.domain.memory;

import java.util.List;

import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;

/**
 * <AUTHOR>
 * @date 2022/8/17 18:01
 */
public interface DataPermissionMemory {

    /**
     * 获取Fv数据权限树形结构基础数据
     * @param labelEnum
     * @return
     */
    DataPermissionDTO getFvBaseDataPermission(LabelEnum labelEnum);

    /**
     * 获取Pro数据权限树形结构基础数据
     * @return
     */
    DataPermissionDTO getProBaseDataPermission();

    DataPermissionDTO getVipTypeBaseDataPermission();

    DataPermissionDTO getCompetitorMonitorBaseDataPermission();

    /**
     * 获取分层基础数据
     *
     * @param conditionEnum 分类
     * @param labelEnum 层级
     */
    List<DataPermissionNode> getBaseDataLayeredDataPermission(ConditionEnum conditionEnum, LabelEnum labelEnum);

    DataPermissionNode getBaseDataLayeredDataPermission4One(ConditionEnum conditionEnum, LabelEnum labelEnum, String permissionId);
}
