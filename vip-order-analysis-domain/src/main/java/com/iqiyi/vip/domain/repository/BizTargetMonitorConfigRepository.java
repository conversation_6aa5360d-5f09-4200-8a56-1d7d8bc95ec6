package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.BizTargetMonitorConfigDO;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigPageQryDTO;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigSaveDTO;

/**
 * @author: guojing
 * @date: 2025/3/1 11:32
 */
public interface BizTargetMonitorConfigRepository {

    int create(BizTargetMonitorConfigSaveDTO createParam);

    boolean update(BizTargetMonitorConfigSaveDTO updateParam);

    boolean updateJobIdById(Integer id, Integer jobId);

    boolean updateMonitorIdById(Integer id, Integer monitorId);

    boolean delete(Integer id);

    BizTargetMonitorConfigDO getDetailById(Integer id);

    boolean existByName(String name);

    PageListResult<BizTargetMonitorConfigDO> search(BizTargetMonitorConfigPageQryDTO param);

}
