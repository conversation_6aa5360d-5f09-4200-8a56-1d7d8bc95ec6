package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public interface AnalysisTargetGroupRepository {

    List<AnalysisTargetGroup> selectAll();
    List<AnalysisTargetGroup> selectByBusinessType(Integer businessTypeId, Integer themeType);
    List<AnalysisTargetGroup> selectByBusinessType(Integer businessTypeId, Integer type, Integer themeType);

    AnalysisTargetGroup get(int id);
}
