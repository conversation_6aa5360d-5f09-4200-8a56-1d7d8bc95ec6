package com.iqiyi.vip.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * album_meta_data
 */
@Data
public class AlbumMetaDataDO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 专辑id
     */
    private String qipuAlbumId;

    /**
     * 内容名称
     */
    private String tvName;

    /**
     * 频道id
     */
    private Integer channelId;

    /**
     * 上线时间
     */
    private String firstEpisodeOnlineTime;

    /**
     * 会员完结时间
     */
    private String vipEndTime;

    /**
     * 非会员完结时间
     */
    private String commonEndTime;

    /**
     * 播后定级
     */
    private String resourceRating;

    /**
     * 关联留存展示
     */
    private Integer remainRelated;

    /**
     * 频道名称
     */
    private String channelName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否完结
     */
    private Integer finished;

    private static final long serialVersionUID = 1L;
}