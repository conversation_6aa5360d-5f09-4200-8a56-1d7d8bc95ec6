package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionPageQryDTO;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public interface AnalysisDimensionRepository {

    Long save(AnalysisDimension dimension);

    void update(AnalysisDimension dimension);

    void delete(String code);

    void deleteById(Long id);

//    AnalysisDimension selectByCode(String code);

    AnalysisDimension selectTransferDimension(String code, Integer businessTypeId, Integer themeType);


    AnalysisDimension selectById(Long id);

    PageListResult<AnalysisDimension> selectPageList(AnalysisDimensionPageQryDTO pageQryDTO);

    List<AnalysisDimension> selectByCodes(List<String> codes, Integer businessTypeId, Integer themeType);

    List<AnalysisDimension> selectAll(BaseQry baseQry);

    AnalysisDimension selectByThemeType(String code, Integer businessTypeId, Integer themeType);
}
