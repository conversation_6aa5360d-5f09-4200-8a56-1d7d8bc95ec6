package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.QiyuePlatform;
import com.iqiyi.vip.dto.condition.QiyuePlatformPair;
import com.iqiyi.vip.enums.LabelEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public interface QiyuePlatformRepository {

    List<QiyuePlatform> selectAll();

    List<QiyuePlatform> selectByCodes(List<String> platformCodes);

    List<QiyuePlatformPair> getQiyuePlatforms(String preId, LabelEnum labelEnum);
}
