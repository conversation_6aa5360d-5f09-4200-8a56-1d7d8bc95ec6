package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@Data
@ApiModel(value = "分组维度数据")
public class GroupDimensionDTO {

    @ApiModelProperty(value = "分组id")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "是否支持多选")
    private Boolean supportMultiple;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "主题类型ID")
    private Integer themeType;

    @ApiModelProperty(value = "分组维度列表")
    private List<AnalysisDimension> dimensions;
}
