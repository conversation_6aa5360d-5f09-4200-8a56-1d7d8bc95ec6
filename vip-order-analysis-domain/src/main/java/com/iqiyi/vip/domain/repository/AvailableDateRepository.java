package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.AvailableDt;

/**
 * <AUTHOR>
 * @className AvailableDateRepository
 * @description
 * @date 2023/5/6
 **/
public interface AvailableDateRepository {

    int insert(AvailableDt record);

    AvailableDt maxDate(Integer themeType);

    AvailableDt maxDateByThemeTypeAndThemeSubType(Integer themeType, Integer themeSubType);

    List<AvailableDt> dateRangeByThemeTypeAndThemeSubType(Integer themeType, Integer themeSubType);

    AvailableDt getDateByDt(String dt, Integer themeType);

    AvailableDt getDateByDtAndThemeTypeAndThemeSubType(String dt, Integer themeType, Integer themeSubType);

    int updateStatusByDt(String dt, Integer themeType, Integer status);

}
