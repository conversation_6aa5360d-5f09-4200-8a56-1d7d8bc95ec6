package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.target.AnalysisTargetPageQryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public interface AnalysisTargetRepository {

    Long save(AnalysisTarget target);

    void update(AnalysisTarget target);

    void delete(String code);

    void deleteById(Long id);

    AnalysisTarget selectByCode(String code);

    AnalysisTarget selectByCodeAndBusinessTypeId(String code, Integer businessTypeId);

    AnalysisTarget selectById(Long id);

    PageListResult<AnalysisTarget> selectPageList(AnalysisTargetPageQryDTO pageQryDTO);

    List<AnalysisTarget> selectByCodes(List<String> codes);

    List<AnalysisTarget> selectAll(BaseQry baseQry);
}
