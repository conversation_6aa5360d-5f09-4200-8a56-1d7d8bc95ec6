package com.iqiyi.vip.domain.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * fv_structure
 * <AUTHOR>
@Data
public class FvStructureDO implements Serializable {
    /**
     * 一级渠道
     */
    private Integer level1Id;

    /**
     * 二级渠道
     */
    private Integer level2Id;

    /**
     * 三级渠道
     */
    private Integer level3Id;

    /**
     * 四级渠道
     */
    private Integer level4Id;

    /**
     * 五级渠道
     */
    private Integer level5Id;

    /**
     * 六级渠道
     */
    private Integer level6Id;

    /**
     * 七级渠道

     */
    private Integer level7Id;

    /**
     * 八级渠道

     */
    private Integer level8Id;

    /**
     * 一级渠道名称
     */
    private String level1Name;

    /**
     * 二级渠道名称
     */
    private String level2Name;

    /**
     * 三级渠道名称

     */
    private String level3Name;

    /**
     * 四级渠道名称

     */
    private String level4Name;

    /**
     * 五级渠道名称

     */
    private String level5Name;

    /**
     * 六级渠道名称

     */
    private String level6Name;

    /**
     * 七级渠道名称

     */
    private String level7Name;

    /**
     * 八级渠道名称

     */
    private String level8Name;

    private static final long serialVersionUID = 1L;
}