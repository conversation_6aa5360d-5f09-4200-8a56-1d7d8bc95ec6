package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSearchParam;

/**
 * @author: guojing
 * @date: 2024/10/30 15:12
 */
public interface CompetitorUiRepository {

    List<String> clientVersionList();

    List<String> brandsList();

    CompetitorDateRangeDTO dateRange();

    List<String> pageList(String brand, String date);

    List<String> userStatusList(String brand, String date);

    void save(CompetitorUiSaveDTO param);

    List<CompetitorUiDTO> search(CompetitorUiSearchParam param);

}
