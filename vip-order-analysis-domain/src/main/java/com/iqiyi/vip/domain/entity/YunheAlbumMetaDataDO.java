package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YunheAlbumMetaDataDO {
    private Long albumId;
    private String albumName;
    private String tags;
    private String episodeNum;
    private String totalEpisodeNum;
    private String channels;
    private String releaseTime;
    private String worksType;
    private String dayPlatform;
    private String notVipDowntime;
    private Double dbRating;
    private String yunheLevel;
    private String offlineTime;
    private String dayPlatformStart;
    private Date createTime;
    private Date updateTime;
} 