package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.BusinessCondition;

import java.util.List;

/**
 * @author: linpe<PERSON><PERSON>
 * @createTime: 2023/08/07
 */
public interface BusinessConditionRepository {

    void batchInsert(List<BusinessCondition> businessConditionList);

    int deleteByThemeAndBusiness(Long themeId, Long businessId);

    int deleteByConditionId(Long conditionId);

    List<BusinessCondition> selectByThemeAndBusiness(Long themeId, Long businessId);
}
