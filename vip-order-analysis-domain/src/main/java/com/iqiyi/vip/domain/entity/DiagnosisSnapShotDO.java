package com.iqiyi.vip.domain.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @className DiagnosisSnapShotDO
 * @description
 * @date 2023/11/1
 **/
@Data
public class DiagnosisSnapShotDO {
    /**
     * 表格头
     */
    private LinkedHashMap<String, String> headColumnMap;

    /**
     * 表格数据
     */
    private List<HashMap<String, Object>> dataList;


    private String desc;

    /**
     * 分组code
     */
    private String groupCode;
}
