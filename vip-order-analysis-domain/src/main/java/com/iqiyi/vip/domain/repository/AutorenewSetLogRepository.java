package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.pie.AutoRenewCancelSceneDTO;

/**
 * <AUTHOR>
 * @date 2022/3/28 下午 03:49
 */
public interface AutorenewSetLogRepository {

    /** 查询在指定的到期时间的自动续费用户在给定时间段内的取消数目
     * @param operateTime
     * @param deadlineStartTime
     * @param deadlineEndTime
     * @return
     */
    int getCancelNumByRange(String dt,String operateTime, String deadlineStartTime, String deadlineEndTime);

    /** 查询一段时间内的自动续费取消场景统计数据
     * @param dt
     * @param beginTime
     * @param endTime
     * @return
     */
    List<AutoRenewCancelSceneDTO> getCancelSceneData(String dt, String beginTime, String endTime);

}
