package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.task.AnalysisTaskPageQryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskResDTO;

/**
 * @className AnalysisTaskRepository
 * @description
 * <AUTHOR>
 * @date 2022/6/1
 **/
public interface AnalysisTaskRepository {

    AnalysisTaskDO getTaskByUniqueIdentification(String uniqueIdentification);

    PageListResult<AnalysisTaskResDTO> getPageListResult(AnalysisTaskPageQryDTO pageQryDTO, boolean isHighLevelUser);

    long addAnalysisTask(AnalysisTaskDO analysisTaskDO);

    AnalysisTaskDO getTaskByIdAndOperator(Long taskId, String operator);

    AnalysisTaskDO getTaskById(Long taskId);

    int reSetTaskStatus(Long taskId, Integer status, String result, Integer executeTime);

    int reTaskName(Long taskId, String taskName, String operator);

    int deleteTaskRecord(Long taskId);

    int saveQueryInfo(Long taskId, String querySql);


    long addScheduledAnalysisTask(AnalysisTaskDO analysisTaskDO);

    AnalysisTaskDO getScheduledTaskByUniqueIdentification(String uniqueIdentification);

    int reScheduledSetTaskStatus(Long taskId, Integer status, String result, Integer executeTime);

}
