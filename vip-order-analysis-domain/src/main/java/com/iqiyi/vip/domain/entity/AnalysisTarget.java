package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "指标信息")
public class AnalysisTarget {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "指标名称")
    private String name;

    @ApiModelProperty(value = "指标code")
    private String code;

    @ApiModelProperty(value = "指标值")
    private String value;

    @ApiModelProperty(value = "指标管理的sql模板id")
    private Long sqlTemplateId;

    @ApiModelProperty(value = "指标分组id")
    private Integer group;

    @ApiModelProperty(value = "指标分组名称")
    private String groupName;

    @ApiModelProperty(value = "说明文档", required = true)
    private String documentation;

    @ApiModelProperty(value = "指标Excel导出表头说明")
    private String descs;

    @ApiModelProperty(value = "指标Excel导出表头分区，只允许两个分区，例如：a,b")
    private String partitions;

    @ApiModelProperty(value = "指标排序", required = true)
    private Integer order;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createOpr;

    @ApiModelProperty(value = "更新人")
    private String updateOpr;

    @ApiModelProperty(value = "状态 0：无效 1：有效")
    private Integer status;

    @ApiModelProperty(value = "更改记录")
    private String commitNote;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "主题类型ID")
    private Integer themeType;

    @ApiModelProperty(value = "主题类型名称")
    private String themeTypeName;

    @ApiModelProperty(value = "高级选项")
    private Integer advancedOption;

    @ApiModelProperty(value = "来源，1:天眼; 2:定时任务")
    private Integer source;
}
