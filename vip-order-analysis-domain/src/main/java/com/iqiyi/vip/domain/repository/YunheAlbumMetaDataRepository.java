package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.dto.competitor.AlbumContentInfoDTO;
import java.util.List;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;

/**
 * 云合专辑元数据仓储接口
 */
public interface YunheAlbumMetaDataRepository {
    
    /**
     * 根据搜索参数查询专辑内容信息
     * @param param 搜索参数
     * @return 专辑内容信息列表
     */
    List<AlbumContentInfoDTO> search(CompetitorPriceSearchParam param);
} 