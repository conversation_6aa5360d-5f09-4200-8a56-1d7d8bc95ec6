package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.user.group.UpdateUserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO;
import com.iqiyi.vip.dto.user.group.UserGroupQueryDTO;

/**
 * <AUTHOR>
 */
public interface UserGroupRepository {

    PageListResult<UserGroupDO> selectPageList(UserGroupPageQueryDTO pageQryDTO);

    List<UserGroupDO> query(UserGroupQueryDTO userGroupQueryDTO);

    int deleteUserGroup(Long userGroupId, String operator);

    void addUserGroup(UserGroupDTO userGroupDTO);

    void updateUserGroup(UpdateUserGroupDTO updateUserGroupDTO);

    void generateUserGroup(TargetAnalysisQueryDTO queryDTO, AnalysisTarget analysisTarget);

    UserGroupDO queryById(Integer userGroupId);
}
