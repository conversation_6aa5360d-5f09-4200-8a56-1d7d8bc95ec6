package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/16/22
 * @apiNote
 */
@Data
@ApiModel(value = "分组指标数据")
public class GroupTargetDTO {

    @ApiModelProperty(value = "分组id")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "关联父分组，当权益转移分析的场景，除了父分组都会关联一个父分组")
    private Integer parent;

    @ApiModelProperty(value = "分组类型：1 普通分组 2 分析类别")
    private Integer type;

    @ApiModelProperty(value = "高级选项")
    private Integer advancedOption;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "分组维度列表")
    private List<AnalysisTarget> targets;

}
