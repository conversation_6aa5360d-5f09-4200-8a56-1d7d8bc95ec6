package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataCheckResult;

/**
 * <AUTHOR>
 * @className DataCheckResultRepository
 * @description
 * @date 2022/10/31
 **/
public interface DataCheckResultRepository {

    List<DataCheckResult> getDataCheckResult(DataCheckResult dataCheckResultDO);

    void saveDataCheckResult(DataCheckResult dataCheckResultDO);
}
