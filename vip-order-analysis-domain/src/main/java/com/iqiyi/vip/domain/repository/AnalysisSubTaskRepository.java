package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;

/**
 * <AUTHOR>
 * @date 2024/1/4 20:26
 */
public interface AnalysisSubTaskRepository {
    int addAnalysisSubTask(AnalysisSubTaskDO analysisSubTaskDO);

    AnalysisSubTaskDO getSubTaskByTaskIdOrUserGroupId(Long taskId, Integer userGroupId);

    int updateByTaskId(AnalysisSubTaskDO analysisSubTaskDO);

    int deleteByTaskId(Long taskId);
    int deleteByUserGroupId(Integer userGroupId);
}
