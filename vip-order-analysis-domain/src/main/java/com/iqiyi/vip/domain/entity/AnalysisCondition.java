package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.dto.condition.ConditionAlias;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.dto.condition.ConditionUpdateDTO;
import com.iqiyi.vip.enums.StatusEnum;

/**
 * 条件实体
 *
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "天眼条件")
public class AnalysisCondition {

    @ApiModelProperty(value = "条件id")
    private Long id;
    @ApiModelProperty(value = "条件code")
    private String code;
    @ApiModelProperty(value = "条件名称")
    private String name;
    @ApiModelProperty(value = "字段类型")
    private Integer type;
    @ApiModelProperty(value = "是否必填。 0：非必填  1：必填")
    private Boolean required;
    @ApiModelProperty(value = "默认提示词")
    private String defaultPrompt;
    @ApiModelProperty(value = "数据加载地址")
    private String dataLoadAddr;
    @ApiModelProperty(value = "是否可全选")
    private Boolean selectAll;
    @ApiModelProperty(value = "是否有提示icon")
    private Boolean hasTipsIcon;
    @ApiModelProperty(value = "提示文案")
    private String tips;
    @ApiModelProperty(value = "字段类型。1：String 2:Integer")
    private Integer fieldType;
    @ApiModelProperty(value = "状态。 0：无效 1：有效")
    private Integer status;
    @ApiModelProperty(value = "是否多选。 0：单选 1：多选")
    private Integer multiChoice;
    @ApiModelProperty(value = "该条件对应的业务表中的字段名")
    private Map<String, String> bizFieldName;
    @ApiModelProperty(value = "字段在不同主题下的别名")
    private List<ConditionAlias> aliases;
    @ApiModelProperty(value = "条件枚举值")
    private List<ConditionPair> enums;
    @ApiModelProperty(value = "创建人")
    private String createOpr;
    @ApiModelProperty(value = "更新人")
    private String updateOpr;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "条件默认值")
    private String defaultValue;
    /**
     * sql where条件的运算符
     */
    @ApiModelProperty(value = "sql where条件的运算符")
    private String clauseOp;

    public AnalysisCondition(ConditionUpdateDTO updateDTO) {
        this.code = updateDTO.getCode();
        this.name = updateDTO.getName();
        this.type = updateDTO.getType();
        this.required = updateDTO.getRequired();
        this.defaultPrompt = updateDTO.getDefaultPrompt();
        this.dataLoadAddr = updateDTO.getDataLoadAddr();
        this.selectAll = updateDTO.getSelectAll();
        this.hasTipsIcon = updateDTO.getHasTipsIcon();
        this.tips = updateDTO.getTips();
        this.fieldType = updateDTO.getFieldType();
        this.status = StatusEnum.EFFECTIV.getCode();
        this.multiChoice = updateDTO.getMultiChoice();
        this.createOpr = updateDTO.getOperator();
        this.updateOpr = updateDTO.getOperator();
        this.createTime = new Date();
        this.updateTime = this.createTime;
        this.aliases = updateDTO.getAliases();
    }

    public void modify(ConditionUpdateDTO updateDTO) {
        this.code= updateDTO.getCode();
        this.name= updateDTO.getName();
        this.type= updateDTO.getType();
        this.required= updateDTO.getRequired();
        this.defaultPrompt= updateDTO.getDefaultPrompt();
        this.dataLoadAddr= updateDTO.getDataLoadAddr();
        this.selectAll= updateDTO.getSelectAll();
        this.hasTipsIcon= updateDTO.getHasTipsIcon();
        this.tips= updateDTO.getTips();
        this.fieldType = updateDTO.getFieldType();
        this.aliases = updateDTO.getAliases();
        this.multiChoice = updateDTO.getMultiChoice();
        this.updateOpr= updateDTO.getOperator();
    }

    /**
     * 获取指定主题下对应的 业务表字段名
     * @param themeType
     * @return
     */
    public String getThemeBizFieldName(Integer themeType) {
        if (MapUtils.isEmpty(this.bizFieldName)) {
            return "";
        }
        String name = this.bizFieldName.get(String.valueOf(themeType));
        return StringUtils.isNotBlank(name) ? name : this.bizFieldName.get("default");
    }
}
