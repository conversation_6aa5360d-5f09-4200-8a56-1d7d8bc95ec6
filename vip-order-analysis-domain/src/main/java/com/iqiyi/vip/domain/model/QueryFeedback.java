package com.iqiyi.vip.domain.model;

import lombok.Data;
import java.util.Date;

@Data
public class QueryFeedback {
    private Long id;
    private Long taskId;
    private String uniqueId; // 新增唯一标识字段
    private Integer status; // 1-满意，0-不满意
    private String feedback;
    private String query; // 查询语句
    private String originalQuery; // 原始查询语句
    private String pipelineId; // 流水线ID
    private String answer; // 回答内容
    private String operator; // 操作人
    private Date createTime;
    private Date updateTime;
}