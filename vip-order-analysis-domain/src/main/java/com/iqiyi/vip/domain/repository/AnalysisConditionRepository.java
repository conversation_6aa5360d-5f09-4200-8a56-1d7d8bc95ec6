package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.AnalysisConditionPageQryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
public interface AnalysisConditionRepository {

    Long save(AnalysisCondition condition);

    void update(AnalysisCondition condition);

    void delete(String code, String operator);

    AnalysisCondition selectById(Long id);

    List<AnalysisCondition> selectByIdList(List<Long> idList);

    AnalysisCondition selectByCode(String code);

    List<AnalysisCondition> batchSelectByCode(List<String> codes);

    AnalysisCondition selectByName(String name);

    PageListResult<AnalysisCondition> selectPageList(AnalysisConditionPageQryDTO pageQryDTO);

    List<AnalysisCondition> selectAll();
}
