package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/4 20:27
 */
@Data
public class AnalysisSubTaskDO {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 父任务id
     */
    @ApiModelProperty(value = "父任务id")
    private Long parentTaskId;

    /**
     * 用户分群id
     */
    @ApiModelProperty(value = "用户分群id")
    private Integer userGroupId;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private Integer status;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;

    /**
     * 上传北斗的人群包名称
     */
    @ApiModelProperty(value = "上传北斗的人群包名称")
    private String userPackageName;

    /**
     * 业务线id
     */
    @ApiModelProperty(value = "业务线id, 使用逗号分隔")
    private String businessIds;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "子任务创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "子任务更新时间")
    private Date updateTime;
}
