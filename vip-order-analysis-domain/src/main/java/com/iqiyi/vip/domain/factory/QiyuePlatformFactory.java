package com.iqiyi.vip.domain.factory;

import com.iqiyi.vip.domain.entity.QiyuePlatform;
import com.iqiyi.vip.dto.platform.QiyuePlate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
public class QiyuePlatformFactory {

    public static List<QiyuePlate> convertToView(List<QiyuePlatform> platforms) {
        List<QiyuePlate> list = new ArrayList<>();
        for (QiyuePlatform platform : platforms) {
            list.add(new QiyuePlate(platform.getName(), platform.getCode(), platform.getDescription()));
        }
        return list;
    }
}
