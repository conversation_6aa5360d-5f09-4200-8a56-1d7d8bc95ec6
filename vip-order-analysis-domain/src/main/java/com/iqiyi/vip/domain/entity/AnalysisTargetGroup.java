package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Data
@ApiModel(value = "指标分组信息")
public class AnalysisTargetGroup {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "分组名称")
    private String name;

    @ApiModelProperty(value = "指标分组排序", required = true)
    private Integer order;

    @ApiModelProperty(value = "分组类型：1 普通分组 2 分析类别", required = true)
    private Integer type;

    @ApiModelProperty(value = "关联父分组，当权益转移分析的场景，除了父分组都会关联一个父分组", required = true)
    private Integer parent;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "主题类型ID")
    private Integer themeType;

    @ApiModelProperty(value = "高级选项")
    private Integer advancedOption;
}
