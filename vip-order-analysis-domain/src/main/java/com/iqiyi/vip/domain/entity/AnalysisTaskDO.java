package com.iqiyi.vip.domain.entity;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.google.common.base.Joiner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.target.BaseQueryDTO;
import com.iqiyi.vip.dto.target.SimpleQueryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.JacksonUtils;
import static com.iqiyi.vip.dto.target.SimpleQueryDTO.getParamMap;

/**
 * analysis_task
 * <AUTHOR>
@Data
@ApiModel(value = "任务信息")
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class AnalysisTaskDO extends BaseQry implements Serializable {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 指标code
     */
    @ApiModelProperty(value = "指标名称")
    private String targets;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 筛选条件
     */
    @ApiModelProperty(value = "条件")
    private String condition;

    /**
     * 诊断分析下钻
     */
    @ApiModelProperty(value = "诊断分析下钻条件与维度信息")
    private String diagnosisCondition;

    /**
     * 维度code
     */
    @ApiModelProperty(value = "维度名称")
    private String dimensions;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private Integer status;

    /**
     * 指标结果链接
     */
    @ApiModelProperty(value = "指标结果链接")
    private String result;


    /**
     * 任务执行周期
     */
    @ApiModelProperty(value = "任务执行周期")
    private String executeCycle;

    /**
     * 最近一次任务执行时间
     */
    @ApiModelProperty(value = "最近一次任务执行时间")
    private Integer executeTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "任务更新时间")
    private Date updateTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "任务操作人")
    private String operator;

    /**
     * 任务参数的唯一标识
     */
    @ApiModelProperty(value = "任务参数的唯一标识")
    private String uniqueIdentification;

    @ApiModelProperty(value = "任务查询信息")
    private String queryInfo;

    @ApiModelProperty(value = "删除状态，未删除为0，删除为1")
    private Integer deleteStatus;
    @ApiModelProperty(value = "任务来源,1:天眼页面; 2:定时任务")
    private Integer source;

    public enum DeleteStatusEnum {
        EXIST(0, "未删除"),
        DELETED(1, "已删除"),
        ;

        private Integer status;

        private String desc;

        DeleteStatusEnum(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public Integer getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }
    }


    public boolean taskExist() {
        return DeleteStatusEnum.EXIST.getStatus().equals(this.deleteStatus);
    }

    public static AnalysisTaskDO initializeTaskDO(BaseQueryDTO baseQueryDTO, String taskMD5, AnalysisTaskSourceEnum taskSourceEnum) {
        List<String> targetCodes = baseQueryDTO.getTargetCodes();
        List<String> dimensionCodes = baseQueryDTO.getDimensionCodes();
        String dimensions = CollectionUtils.isNotEmpty(dimensionCodes) ? Joiner.on(",").join(dimensionCodes) : null;
        String operator = baseQueryDTO.getOperator();
        return AnalysisTaskDO.builder()
            .taskName(baseQueryDTO.getTaskName())
            .status(TaskStatusEnum.READY.getStatus())
            .targets(Joiner.on(",").join(targetCodes)) // 指标
            .dimensions(dimensions) // 维度
            .condition(JacksonUtils.toJsonString(baseQueryDTO.getConditionParamMap())) // 条件 - 条件需要作为基础数据的条件[产品、渠道]
            .createTime(DateUtil.date())
            .updateTime(DateUtil.date())
            .operator(operator)
            .uniqueIdentification(taskMD5)
            .businessTypeId(baseQueryDTO.getBusinessTypeId())
            .dataPermissionType(baseQueryDTO.getDataPermissionType())
            .themeType(baseQueryDTO.getThemeType())
            .source(taskSourceEnum != null ? taskSourceEnum.getValue() : AnalysisTaskSourceEnum.UI_MANUAL.getValue())
            .build();
    }

    public static AnalysisTaskDO initializeTaskDO(SimpleQueryDTO queryDTO, ConditionParamContext paramContext) throws UnsupportedEncodingException {

        List<String> targetCodes = paramContext.getTargetCodes();
        List<String> operators = paramContext.getSimpleQueryOperator();
        if (CollectionUtils.isEmpty(targetCodes)) {
            log.error("targetCodes or dimensionCodes is empty");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "指标为空");
        }
        if (CollectionUtils.isEmpty(operators)) {
            log.error("operator is empty");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "未获取到用户账号");
        }

        Long payStartTime = paramContext.getPayStartTime();
        Long payEndTime = paramContext.getPayEndTime();
        if (payStartTime == null || payEndTime == null) {
            log.error("payStartTime or payEndTime is null");
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), "未获取到支付时间");
        }
        String customIncomeType = paramContext.getCustomIncomeType();
        Map<String, List<Object>> paramMap = paramContext.getParamMap();
        if (StringUtils.isBlank(customIncomeType)) {
            paramMap.put("customIncomeType", Collections.singletonList("vip"));
        }
        return AnalysisTaskDO.builder()
            .taskName(paramContext.getUniqueId())
            .uniqueIdentification(paramContext.getUniqueId())
            .status(TaskStatusEnum.RUNNING.getStatus())
            .targets(Joiner.on(",").join(targetCodes.subList(0, 1))) // 指标
            .condition(JacksonUtils.toJsonString(paramMap)) // 条件 - 条件需要作为基础数据的条件[产品、渠道]
            .createTime(DateUtil.date())
            .updateTime(DateUtil.date())
            .operator(operators.get(0))
            .businessTypeId(queryDTO.getBusinessTypeId())
            .dataPermissionType(queryDTO.getDataPermissionType())
            .themeType(queryDTO.getThemeType())
            .dimensions(Joiner.on(",").join(paramContext.getDimensionCodes()))
            .source(AnalysisTaskSourceEnum.AGENT.getValue())
            .build();
    }

}