package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/18 15:38
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DataPermission implements java.io.Serializable {

    private Integer id;
    private String account;
    // DataPermissionComposeDTO 映射JSON格式
    private String permission;
    // LayeredDataPermissionDTO 映射JSON格式
    private String layeredPermission;
    private Integer themeType;
    private Integer type;
    private Integer status;
    private Date createTime;
    private Date updateTime;

}
