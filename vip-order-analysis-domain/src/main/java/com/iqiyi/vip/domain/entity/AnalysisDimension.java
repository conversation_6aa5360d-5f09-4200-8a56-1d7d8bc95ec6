package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "维度信息")
public class AnalysisDimension {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "维度名称")
    private String name;

    @ApiModelProperty(value = "维度code")
    private String code;

    @ApiModelProperty(value = "维度值")
    private String value;

    @ApiModelProperty(value = "维度分组")
    private Integer group;

    @ApiModelProperty(value = "维度分组描述")
    private String groupName;

    @ApiModelProperty(value = "指标排序", required = true)
    private Integer order;

    @ApiModelProperty(value = "说明文档", required = true)
    private String documentation;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createOpr;

    @ApiModelProperty(value = "更新人")
    private String updateOpr;

    @ApiModelProperty(value = "状态 0：无效 1：有效")
    private Integer status;

    @ApiModelProperty(value = "更改记录")
    private String commitNote;

    @ApiModelProperty(value = "版本")
    private Integer version;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "主题类型ID")
    private Integer themeType;

    @ApiModelProperty(value = "主题类型名称")
    private String themeTypeName;
}
