package com.iqiyi.vip.domain.repository;

import java.util.List;
import com.iqiyi.vip.domain.entity.AlbumMetaDataDO;
import com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO;
import com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO;

/**
 * <AUTHOR>
 * @className AlbumMetaDataRepository
 * @description
 * @date 2024/9/28
 **/
public interface AlbumMetaDataRepository {

    List<AlbumMetaDataDO> getPageQueryResult(AlbumMetaDataPageQueryDTO query);

    Integer selectCount(AlbumMetaDataPageQueryDTO albumQuery);

    List<AlbumMetaDataDO> getByRemainTime(String startDate, String endDate);

    int updateMetaData(UpdateAlbumMetaDataDTO updateAlbumMetaDataDTO);

}
