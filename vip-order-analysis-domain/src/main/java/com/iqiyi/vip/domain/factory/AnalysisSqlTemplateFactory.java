package com.iqiyi.vip.domain.factory;

import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.dto.sqltemplate.SqlTemplateUpdateDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.StatusEnum;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

import static com.iqiyi.vip.constant.Constants.DEFAULT_OPERATOR;
import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public class AnalysisSqlTemplateFactory {

    public static AnalysisSqlTemplate getSaveTemplate(SqlTemplateUpdateDTO updateDTO) throws Exception {
        AnalysisSqlTemplate target = new AnalysisSqlTemplate();
        target.setName(updateDTO.getName());
        target.setValue(updateDTO.getValue());
        target.setDescription(updateDTO.getDescription());
        target.setCreateTime(new Date());
        target.setUpdateTime(new Date());
        target.setCreateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        target.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        target.setStatus(StatusEnum.EFFECTIV.getCode());
        target.setVersion(DEFAULT_VERSION);
        target.setCommitNote(updateDTO.getCommitNote());
        target.setBusinessTypeId(updateDTO.getBusinessTypeId());
        target.setThemeType(updateDTO.getThemeType());
        return target;
    }

    public static void updateExist(AnalysisSqlTemplate exist, SqlTemplateUpdateDTO updateDTO, Integer newVersion) throws Exception {
        exist.setName(updateDTO.getName());
        exist.setValue(updateDTO.getValue());
        exist.setDescription(updateDTO.getDescription());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        exist.setCommitNote(updateDTO.getCommitNote());
        exist.setVersion(newVersion);
    }

    public static void versionSwitch(AnalysisSqlTemplate exist, ConfigChangeLog destinationChangeLog, VersionSwitchReqDTO reqDTO) {
        exist.setCode(destinationChangeLog.getConfigCode());
        exist.setName(destinationChangeLog.getConfigName());
        exist.setValue(destinationChangeLog.getConfigValue());
        exist.setDescription(destinationChangeLog.getConfigDesc());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(reqDTO.getOperator()) ? DEFAULT_OPERATOR : reqDTO.getOperator());
        exist.setVersion(destinationChangeLog.getVersion());
        exist.setCommitNote(destinationChangeLog.getCommitNote());
    }
}
