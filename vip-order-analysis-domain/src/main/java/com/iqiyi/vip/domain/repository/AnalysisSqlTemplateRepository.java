package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.sqltemplate.AnalysisSqlTemplatePageQryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public interface AnalysisSqlTemplateRepository {

    Long save(AnalysisSqlTemplate template) throws Exception;

    void update(AnalysisSqlTemplate template) throws Exception;

    void delete(Long id);

    AnalysisSqlTemplate selectById(Long id);

    PageListResult<AnalysisSqlTemplate> selectPageList(AnalysisSqlTemplatePageQryDTO pageQryDTO);

    List<AnalysisSqlTemplate> selectAll(BaseQry baseQry);
}
