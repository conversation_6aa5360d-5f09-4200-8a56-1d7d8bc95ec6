package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigCondition;

/**
 * @author: guojing
 * @date: 2025/3/1 11:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "业务指标监控配置Model")
public class BizTargetMonitorConfigDO {

    /**
     * 主键di
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private Integer themeType;
    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String themeTypeName;
    /**
     * 条件
     */
    @ApiModelProperty(value = "条件")
    private List<BizTargetMonitorConfigCondition> conditions;
    /**
     * 业务指标
     */
    @ApiModelProperty(value = "业务指标")
    private String target;
    /**
     * 维度
     */
    private List<String> dimensions;
    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String targetName;
    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频率，格式：\"(number)(unit)\", 比如：\"1m\". 合法的单位: m, h, d")
    private String execFrequency;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createOpr;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateOpr;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 状态,1:有效,2:无效
     */
    @ApiModelProperty(value = "状态，1:有效,2:无效")
    private Integer status;

    /**
     * 定时任务id
     */
    @ApiModelProperty(value = "定时任务id")
    private Integer jobId;

    /**
     * 定时任务id对应的URL
     */
    @ApiModelProperty(value = "定时任务id对应的URL")
    private String jobConfigUrl;

    /**
     * 宙斯服务的监控id
     */
    @ApiModelProperty(value = "宙斯服务的监控id")
    private Integer monitorId;

    /**
     * 宙斯服务的监控URL
     */
    @ApiModelProperty(value = "宙斯服务的监控URL")
    private String zeusMonitorPageListUrl;

}
