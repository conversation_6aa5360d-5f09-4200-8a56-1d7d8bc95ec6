package com.iqiyi.vip.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @className TaskQueryInfo
 * @description
 * @date 2023/1/11
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskQueryInfo implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务查询信息
     */
    private String queryInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public static TaskQueryInfo buildFrom(long taskId, String queryInfo) {
        return TaskQueryInfo.builder()
            .taskId(taskId)
            .queryInfo(queryInfo)
            .build();
    }
}