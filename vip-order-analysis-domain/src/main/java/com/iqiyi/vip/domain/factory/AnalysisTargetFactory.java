package com.iqiyi.vip.domain.factory;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.dto.target.TargetUpdateDTO;
import com.iqiyi.vip.dto.version.VersionSwitchReqDTO;
import com.iqiyi.vip.enums.StatusEnum;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

import static com.iqiyi.vip.constant.Constants.DEFAULT_VERSION;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
public class AnalysisTargetFactory {

    private static String DEFAULT_OPERATOR = "backend";

    public static AnalysisTarget getSaveTarget(TargetUpdateDTO updateDTO) {

        AnalysisTarget target = new AnalysisTarget();
        target.setCode(updateDTO.getCode());
        target.setName(updateDTO.getName());
        target.setValue(updateDTO.getValue());
        target.setSqlTemplateId(updateDTO.getSqlTemplateId());
        target.setGroup(updateDTO.getGroup());
        target.setOrder(updateDTO.getOrder());
        target.setDocumentation(updateDTO.getDocumentation());
        target.setCreateTime(new Date());
        target.setUpdateTime(new Date());
        target.setCreateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        target.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        target.setStatus(StatusEnum.EFFECTIV.getCode());
        target.setCommitNote(updateDTO.getCommitNote());
        target.setVersion(DEFAULT_VERSION);
        return target;
    }

    public static void updateExist(AnalysisTarget exist, TargetUpdateDTO updateDTO, Integer newVersion) {
        exist.setCode(updateDTO.getCode());
        exist.setName(updateDTO.getName());
        exist.setValue(updateDTO.getValue());
        exist.setSqlTemplateId(updateDTO.getSqlTemplateId());
        exist.setGroup(updateDTO.getGroup());
        exist.setOrder(updateDTO.getOrder());
        exist.setDocumentation(updateDTO.getDocumentation());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(updateDTO.getOperator()) ? DEFAULT_OPERATOR : updateDTO.getOperator());
        exist.setCommitNote(updateDTO.getCommitNote());
        exist.setVersion(newVersion);
    }

    public static void versionSwitch(AnalysisTarget exist, ConfigChangeLog log, VersionSwitchReqDTO reqDTO) {
        exist.setCode(log.getConfigCode());
        exist.setName(log.getConfigName());
        exist.setValue(log.getConfigValue());
        exist.setSqlTemplateId(log.getTemplateId());
        exist.setGroup(log.getConfigGroupId());
        exist.setDocumentation(log.getConfigDesc());
        exist.setUpdateTime(new Date());
        exist.setUpdateOpr(StringUtils.isBlank(reqDTO.getOperator()) ? DEFAULT_OPERATOR : reqDTO.getOperator());
        exist.setVersion(log.getVersion());
        exist.setCommitNote(log.getCommitNote());
    }
}
