package com.iqiyi.vip.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 支付渠道
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@Data
@ApiModel(value = "支付渠道")
public class PaymentChannel {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "渠道编号")
    private String code;
    @ApiModelProperty(value = "父子维护关系，父为0，子为父的code")
    private String categoryCode;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "协议名称")
    private String dutAgreementName;
    @ApiModelProperty(value = "协议地址")
    private String dutAgreementUrl;
    @ApiModelProperty(value = "文案")
    private String promotionText;
    @ApiModelProperty(value = "图标地址")
    private String iconUrl;
    @ApiModelProperty(value = "关联的支付方式")
    private List<PaymentType> paymentTypes;
}
