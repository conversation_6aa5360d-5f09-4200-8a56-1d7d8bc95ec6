package com.iqiyi.vip.domain.entity;

import com.iqiyi.vip.dto.condition.ConditionCascadeReq;
import com.iqiyi.vip.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 查询条件的级联关系
 * @author: linpeih<PERSON>
 * @createTime: 2023/08/08
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ConditionCascade {
    private Long id;
    private Integer themeId;
    private Integer businessId;
    private Long currentConditionId;
    private Long nextConditionId;
    private Integer status;
    private String createOpr;
    private String updateOpr;
    private Date createTime;
    private Date updateTime;

    public ConditionCascade(ConditionCascadeReq updateDTO, ConditionCascadeReq.ConditionCascade cascade) {
        this.themeId = updateDTO.getThemeId();
        this.businessId= updateDTO.getBusinessId();
        this.currentConditionId = cascade.getCurrentId();
        this.nextConditionId = cascade.getNextId();
        this.status = StatusEnum.EFFECTIV.getCode();
        this.createOpr = updateDTO.getOperator();
        this.updateOpr = updateDTO.getOperator();
        this.createTime = new Date();
        this.updateTime = this.createTime;
    }
}
