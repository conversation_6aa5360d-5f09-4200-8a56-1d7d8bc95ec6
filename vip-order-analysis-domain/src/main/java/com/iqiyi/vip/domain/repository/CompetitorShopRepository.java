package com.iqiyi.vip.domain.repository;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSearchParam;

/**
 * @author: guojing
 * @date: 2024/12/25 10:30
 */
public interface CompetitorShopRepository {

    List<String> platformList();

    List<String> brandsList(String platform, String date);

    CompetitorDateRangeDTO dateRange();

    void save(CompetitorShopSaveDTO param);

    List<CompetitorShopDTO> search(CompetitorShopSearchParam param);
    
    /**
     * 根据参数和日期查询
     *
     * @param param 查询参数
     * @param date 日期字符串
     * @return 店铺监控数据列表
     */
    List<CompetitorShopDTO> search(CompetitorShopSearchParam param, String date);
} 