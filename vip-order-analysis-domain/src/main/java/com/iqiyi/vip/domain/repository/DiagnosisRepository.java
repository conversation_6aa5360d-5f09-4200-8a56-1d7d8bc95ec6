package com.iqiyi.vip.domain.repository;

import com.iqiyi.vip.dto.diagnosis.DiagnosisResult;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface DiagnosisRepository {
    //添加诊断结果
    int addDiagnosisResult(DiagnosisResultDTO diagnosisResultDTO,String diagnosisPath);

    List<ExcelSheetDataDTO> generateDiagoisisExcel(Long taskId);
    List<ExcelSheetDataDTO> generateDiagoisisExcel(String uniqueId);

}
