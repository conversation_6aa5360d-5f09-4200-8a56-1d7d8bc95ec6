<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <packaging>pom</packaging>
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.example</groupId>
    <artifactId>vip-order-analysis</artifactId>
    <version>1.8.77</version>
    <name>vip-order-analysis</name>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <com.alibaba.csp-sentinel>1.8.0-iqiyi-4</com.alibaba.csp-sentinel>
        <sentinel.version>1.7.1-iqiyi-7</sentinel.version>
        <qiyi-job.version>2.4.16-RELEASE</qiyi-job.version>
        <springfox-swagger.version>3.0.0</springfox-swagger.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>spring-boot-starter-qiyi-job</artifactId>
                <version>${qiyi-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>qiyi-job-core</artifactId>
                <version>${qiyi-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.bigdata</groupId>
                <artifactId>pilot-client</artifactId>
                <version>2.6.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>config-client</artifactId>
                        <groupId>com.iqiyi.config</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-boot-starter-eagle</artifactId>
                <version>0.2.22.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.db</groupId>
                <artifactId>mysql-dal-spring-boot-starter</artifactId>
                <version>1.3.4</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.iqiyi.db</groupId>-->
<!--                <artifactId>spring-boot-starter-qdbm-redis2</artifactId>-->
<!--                <version>1.0.5</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>com.iqiyi.config</groupId>-->
<!--                        <artifactId>config-client</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.4.0-jre</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!--mybatis-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.5.0-M2</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <!--vip component-->
        <dependency>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>vip-component</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-apollo</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iqiyi.config</groupId>
                    <artifactId>config-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-web-servlet</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-annotation-aspectj</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-parameter-flow-control</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-extension</artifactId>
            <version>${com.alibaba.csp-sentinel}</version>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>sentinel-extension-iqiyi</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sentinel-datasource-apollo</artifactId>
                    <groupId>com.iqiyi.config</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.config</groupId>
            <artifactId>sentinel-datasource-apollo</artifactId>
            <version>3.11.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-datasource-extension</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>config-client</artifactId>
                    <groupId>com.iqiyi.config</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.hubble</groupId>
            <artifactId>hubble-sdk</artifactId>
            <version>0.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.hystrix</groupId>
                    <artifactId>hystrix-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--sentinel end-->

        <!--cache-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
            <version>2.7.3</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.22</version>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.config</groupId>
            <artifactId>config-client</artifactId>
            <version>3.15.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>vip-utils</artifactId>
            <version>0.1.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.22.Final</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!-- oss实体存储 -->
        <dependency>
            <groupId>com.iqiyi.oss</groupId>
            <artifactId>iqiyi-oss-java-sdk</artifactId>
            <version>0.1.8</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.6.1-iqiyi-qdbm-1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.iqiyi.config</groupId>
                    <artifactId>config-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.lego</groupId>
            <artifactId>lego-spring-rocketmq</artifactId>
            <version>0.0.16</version>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${springfox-swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.iqiyi.vip</groupId>
            <artifactId>vip-threadpool-core</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>6.1.0-iqiyi-7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>6.1.0-iqiyi-8</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>libs-release</id>
            <name>libs-release</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
        </repository>
        <repository>
            <snapshots/>
            <id>libs-snapshot</id>
            <name>libs-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
        </repository>
        <!-- cloudservice -->
        <repository>
            <snapshots/>
            <id>iqiyi-maven-middleware</id>
            <name>iqiyi-maven-middleware</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
        </repository>
    </repositories>

    <modules>
        <module>vip-order-analysis-admin</module>
        <module>vip-order-analysis-task</module>
        <module>vip-order-analysis-job</module>
        <module>vip-order-analysis-app</module>
        <module>vip-order-analysis-domain</module>
        <module>vip-order-analysis-infrastructure</module>
        <module>vip-order-analysis-common</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <artifactId>lombok</artifactId>
                            <groupId>org.projectlombok</groupId>
                            <version>1.18.16</version>
                        </path>
                        <!-- additional annotation processor required as of Lombok 1.18.16 -->
                        <path>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <groupId>org.projectlombok</groupId>
                            <!-- 如果是0.1.0 有可能出现生成了maptruct的实现类，但该类只创建了对象，没有进行赋值 -->
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
