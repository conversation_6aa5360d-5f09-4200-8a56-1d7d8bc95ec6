package com.iqiyi.vip.constants;

import com.iqiyi.lego.rocketmq.serializer.Jackson2JsonRocketMQSerializer;
import com.iqiyi.vip.dto.oa.OaApprovedNoticeMsgDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;

/**
 * @author: guojing
 * @date: 2025/3/20 22:22
 */
public class MQSerializerConstants {

    public static final Jackson2JsonRocketMQSerializer<AnalysisTaskExecuteDTO> ASYNC_TASK_SERIALIZER = new Jackson2JsonRocketMQSerializer(AnalysisTaskExecuteDTO.class);

    public static final Jackson2JsonRocketMQSerializer<OaApprovedNoticeMsgDTO> OA_APPROVED_SERIALIZER = new Jackson2JsonRocketMQSerializer(OaApprovedNoticeMsgDTO.class);

}
