package com.iqiyi.vip.handler.mybatis;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.dto.condition.ConditionAlias;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: linpeihui
 * @createTime: 2023/09/18
 */
public class ConditionAliasesTypeHandler extends BaseTypeHandler<List<ConditionAlias>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<ConditionAlias> conditionAliases, JdbcType jdbcType)
        throws SQLException {
        String aliasesStr = JacksonUtils.toJsonString(conditionAliases);
        preparedStatement.setString(i, aliasesStr);
    }

    @Override
    public List<ConditionAlias> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String aliasesStr = resultSet.getString(s);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(aliasesStr, ConditionAlias.class);
    }

    @Override
    public List<ConditionAlias> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String aliasesStr = resultSet.getString(i);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(aliasesStr, ConditionAlias.class);
    }

    @Override
    public List<ConditionAlias> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String aliasesStr = callableStatement.getString(i);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(aliasesStr, ConditionAlias.class);
    }

}
