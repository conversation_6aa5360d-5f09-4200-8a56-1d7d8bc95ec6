package com.iqiyi.vip.handler.base;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.IntStream;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.handler.DefaultExcelGenerateNameHandler;
import com.iqiyi.vip.handler.TransferExcelGenerateNameHandler;
import com.iqiyi.vip.handler.base.TargetDimensionHandler.Extra;
import com.iqiyi.vip.utils.CloudConfigUtils;

/**
 * 导出Excel生产的Excel名称、Sheet名称
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/8 11:43
 */
public abstract class ExcelGenerateNameHandler {
    static String DATE_FORMAT = "yyyy.M.dd";
    // 占位符
    public static final String PLACEHOLDER = "-";


    /**
     * 生成Excel名称
     * @param businessTypeId
     * @param excelSheetDataDTOS
     * @param targetGroupFunction
     * @return
     */
    public static String getExcelName(Integer businessTypeId, List<ExcelSheetDataDTO> excelSheetDataDTOS, Function<String, AnalysisTargetGroup> targetGroupFunction) {
        return getInstance(businessTypeId).getExcelName(excelSheetDataDTOS, targetGroupFunction);
    }

    /**
     * 生成Sheet名称
     * @param payStartTime
     * @param payEndTime
     * @param businessTypeId
     * @param targetFunction
     * @param excelSheetDataDTO
     * @return
     */
    public static String getSheetName(Long payStartTime, Long payEndTime, Integer businessTypeId, Function<String, AnalysisTarget> targetFunction, ExcelSheetDataDTO excelSheetDataDTO) {
        String sheetNamePrefix = "";
        if (payStartTime != null && payEndTime != null) {
            sheetNamePrefix = getSheetNamePrefix(payStartTime, payEndTime);
        }
        AnalysisTarget analysisTarget = targetFunction.apply(excelSheetDataDTO.getSheetName());
        String sheetName = getInstance(businessTypeId).getSheetName(excelSheetDataDTO, analysisTarget, sheetNamePrefix);
        if (StringUtils.isNotBlank(sheetName) && sheetName.endsWith("_")) {
            sheetName = sheetName.substring(0, sheetName.length() - 1);
        }
        return sheetName;
    }

    /**
     * 导出Excel
     * @param path 导出路径
     */
    public static void exportExcel(Long taskId, Integer businessTypeId, String path, List<ExcelSheetDataDTO> toExportDatas, AnalysisTaskExecuteDTO executeDTO,
                                   Function<String, AnalysisTarget> targetFunction, Integer excelWidthAdjust) {
        ExcelGenerateNameHandler instance = getInstance(businessTypeId);
        WriteWorkbook writeWorkbook = new WriteWorkbook();
        writeWorkbook.setFile(new File(path));
        ExcelWriter excelWriter = new ExcelWriter(writeWorkbook);
        int idx = 0;
        for (ExcelSheetDataDTO excelSheetDataDTO : toExportDatas) {
            AnalysisTarget analysisTarget = StringUtils.isNotBlank(excelSheetDataDTO.getSheetName()) ? targetFunction.apply(excelSheetDataDTO.getSheetName()) : null; // 指标查询

            List<List<Object>> descs = Lists.newArrayList();
            if (analysisTarget != null && StringUtils.isNotBlank(analysisTarget.getDescs())) {
                descs.add(Lists.newArrayList(analysisTarget.getDescs()));
            }
            //下钻路径
            if(StringUtils.isNotBlank(excelSheetDataDTO.getDiagnosisPath())){
                descs.add(Lists.newArrayList(excelSheetDataDTO.getDiagnosisPath()));
            }

            List<List<String>> heads = Lists.newArrayList();
            int cs = 0; // excel 列数
            LinkedHashMap<String, String> headColumnMap = excelSheetDataDTO.getHeadColumnMap();
            if (MapUtils.isNotEmpty(headColumnMap)) {

                // 获取选定维度
                Extra extra = TargetDimensionHandler.getExtra(taskId, businessTypeId);
                int[] dimensions = extra.getDimensions();

                // 表头
                // key为匹配符，value为列名，如果多级列名用逗号隔开
                List<String> heads_part = Lists.newArrayList();
                int dc = dimensions == null ? 0 : dimensions[0];
                for (Entry<String, String> entry : headColumnMap.entrySet()) {
                    String value = entry.getValue();
                    if (instance instanceof TransferExcelGenerateNameHandler) {
                        String name = analysisTarget.getName();
                        if (dc == 0) {
                            heads_part.add(name.length() > 2 ? name.substring(0, 2).concat(value) : value);
                        } else {
                            dc--;
                            heads_part.add(value);
                        }
                    } else {
                        heads_part.add(value);
                    }
                }
                heads.add(heads_part);
                cs = heads_part.size();

                // 分区
                List<List<Object>> partitions = Lists.newArrayList();
                String partitionsStr = Objects.nonNull(analysisTarget) ? analysisTarget.getPartitions() : null;
                if (StringUtils.isNotBlank(partitionsStr)) {
                    String[] split = partitionsStr.split(",");
                    List<Object> partitions_part = Lists.newArrayList();
                    if (dimensions[0] > 0) {
                        partitions_part.add(split[0]);
                    }

                    int at = 0;
                    if (dimensions != null && dimensions.length > 0) {
                        at = cs - dimensions[0];
                        IntStream.range(1, dimensions[0]).forEach(v -> partitions_part.add(PLACEHOLDER));
                        partitions_part.add(split[1]); // 分区名
                        IntStream.range(1, at).forEach(v -> partitions_part.add(PLACEHOLDER));

                        partitions.add(partitions_part);
                    }
                }

                // 表数据
                List<List<Object>> datas = Lists.newArrayList();
                List<LinkedHashMap<String, Object>> dataList = excelSheetDataDTO.getDataList();
                if (MapUtils.isNotEmpty(headColumnMap) && CollectionUtils.isNotEmpty(dataList)) {
                    for (Map<String, Object> dataMap : dataList) {
                        List<Object> rows = new ArrayList<>();
                        headColumnMap.entrySet().forEach(headColumnEntry -> {
                            if (dataMap.containsKey(headColumnEntry.getKey())) {
                                Object data = dataMap.get(headColumnEntry.getKey());
                                //定制逻辑，对于用户id，不转换数字
                                if (data instanceof String && NumberUtils.isCreatable(String.valueOf(data))
                                    && !CloudConfigUtils.noNeedToConvertNumbers(headColumnEntry.getValue())) {
                                    rows.add(new BigDecimal(String.valueOf(data)));
                                } else {
                                    rows.add(data);
                                }
                            }
                        });
                        datas.add(rows);
                    }
                }
                ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
                Long payStartTime = conditionParamContext.getPayStartTime();
                Long payEndTime = conditionParamContext.getPayEndTime();
                // sheet名
                String sn = getSheetName(payStartTime, payEndTime, executeDTO.getBusinessTypeId(), targetFunction, excelSheetDataDTO);

                // 导出Excel
                instance.export(excelWriter, cs, path, sn, idx, descs, partitions, heads, datas, excelWidthAdjust);
                idx++;
            }
        }
        excelWriter.finish();
        TargetDimensionHandler.removeExtra(taskId, businessTypeId); // 清空缓存
    }

    protected static void addWriteHandler(List<WriteHandler> writeHandlers, WriteHandler writeHandler, boolean addHandler) {
        if (addHandler) {
            writeHandlers.add(writeHandler);
        }
    }

    private static ExcelGenerateNameHandler getInstance(Integer businessTypeId) {
        boolean isTransfer = businessTypeId != null && businessTypeId.intValue() == 2;
        return isTransfer ? new TransferExcelGenerateNameHandler() : new DefaultExcelGenerateNameHandler();
    }

    protected abstract String getExcelName(List<ExcelSheetDataDTO> excelSheetDataDTOS, Function<String, AnalysisTargetGroup> targetGroupFunction);
    protected abstract String getSheetName(ExcelSheetDataDTO excelSheetDataDTO, AnalysisTarget analysisTarget, String sheetNamePrefix);

    protected abstract void export(ExcelWriter excelWriter, int cs, String path, String sn, int no,
                                   List<List<Object>> descs, List<List<Object>> partitions, List<List<String>> heads, List<List<Object>> datas, Integer excelWidthAdjust);

    public static String getSheetNamePrefix(Long payStartTime, Long payEndTime) {
        return DateUtil.format(DateUtil.date(payStartTime), DATE_FORMAT) +
                "-" +
                DateUtil.format(DateUtil.date(payEndTime), DATE_FORMAT);
    }

}
