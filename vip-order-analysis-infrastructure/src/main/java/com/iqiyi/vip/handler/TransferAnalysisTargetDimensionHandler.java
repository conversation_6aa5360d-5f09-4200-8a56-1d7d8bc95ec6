package com.iqiyi.vip.handler;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import shade.com.google.common.base.Strings;

import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/3 17:33
 */
public class TransferAnalysisTargetDimensionHandler extends TargetDimensionHandler {

    static Pattern pattern = Pattern.compile(",");
    @Override
    protected String handler4codes(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets, String uk) {
        String separate = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE;
        analysisDimensions.sort(Comparator.comparing(v -> v.getCode().indexOf(separate) > 0 ? 2 : 1));

        // 维度
        // 转移
        String dpart1 = analysisDimensions.stream()
                .filter(v -> v.getCode().indexOf(separate) < 0)
                .map(v -> v.getCode())
                .collect(Collectors.joining(","));
        // 选定
        String dpart2 = analysisDimensions.stream()
                .filter(v -> v.getCode().indexOf(separate) > 0)
                .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getCode()).length == 2)
                .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getCode())[0])
                .collect(Collectors.joining(","));
        // 转移
        String dpart3 = analysisDimensions.stream()
                .filter(v -> v.getCode().indexOf(separate) > 0)
            .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getCode()).length == 2)
            .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getCode())[1])
            .collect(Collectors.joining(","));

        TargetDimensionHandler.Extra extra = getExtra(uk);
        int dsize = analysisDimensions.size();
        int tsize = analysisTargets.size();
        extra.setDimensions(new int[]{(Strings.isNullOrEmpty(dpart1) ? dsize : dsize - pattern.split(dpart1).length) + tsize, dsize + tsize});

        return Lists.newArrayList(dpart2, dpart1, dpart3).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
    }

    @Override
    protected String handler4codesWithOutTimeType(List<AnalysisDimension> analysisDimensions, String uk) {
        return "";
    }

    @Override
    protected String handler4values(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets) {
        String separate = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE;
        analysisDimensions.sort(Comparator.comparing(v -> v.getCode().indexOf(separate) > 0 ? 2 : 1));

        // 维度
        // 转移
        String dpart1 = analysisDimensions.stream()
            .filter(v -> v.getValue().indexOf(separate) < 0)
            .map(v -> v.getValue())
                .collect(Collectors.joining(","));
        // 选定
        String dpart2 = analysisDimensions.stream()
                .filter(v -> v.getValue().indexOf(separate) > 0)
                .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue()).length == 2)
                .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue())[0])
            .collect(Collectors.joining(","));
        // 转移
        String dpart3 = analysisDimensions.stream()
            .filter(v -> v.getValue().indexOf(separate) > 0)
            .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue()).length == 2)
            .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue())[1])
            .collect(Collectors.joining(","));

        // 指标
        // 选定
        String tpart1 = analysisTargets.stream()
                .filter(v -> v.getValue().indexOf(separate) > 0)
                .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue()).length == 2)
                .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue())[0])
                .collect(Collectors.joining(","));
        // 转移
        String tpart2 = analysisTargets.stream()
                .filter(v -> v.getValue().indexOf(separate) > 0)
                .filter(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue()).length == 2)
                .map(v -> Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(v.getValue())[1])
                .collect(Collectors.joining(","));

        return Lists.newArrayList(dpart2, tpart1, dpart1, dpart3, tpart2).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
    }

    @Override
    protected String handler4valuesWithOutTimeType(List<AnalysisDimension> analysisDimensions) {
        return "";
    }

    @Override
    protected String doGetDimensionName(Integer businessTypeId, Integer themeType, AnalysisDimensionRepository analysisDimensionRepository, String code, String defaultVal) {
        AnalysisDimension dimension = analysisDimensionRepository.selectTransferDimension(code, businessTypeId, themeType);
        String name = dimension == null ? defaultVal : TargetDimensionHandler.retDimensionName(dimension, code);
        return name;
    }

    @Override
    protected String doGetTargetName(Integer businessTypeId, AnalysisTargetRepository analysisTargetRepository, String code, String defaultVal) {
        AnalysisTarget target = analysisTargetRepository.selectByCodeAndBusinessTypeId(code, businessTypeId);
        String name = target == null ? defaultVal : TargetDimensionHandler.retTargetName(target, code);
        return name;
    }
}
