package com.iqiyi.vip.handler.mybatis;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: linpeihui
 * @createTime: 2023/09/18
 */
public class BizFieldNameTypeHandler extends BaseTypeHandler<Map<String, String>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Map<String, String> bizFieldName, JdbcType jdbcType)
        throws SQLException {
        String aliasesStr = JacksonUtils.toJsonString(bizFieldName);
        preparedStatement.setString(i, aliasesStr);
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String aliasesStr = resultSet.getString(s);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new HashMap<>();
        }
        return JacksonUtils.parseObject(aliasesStr, new TypeReference<Map<String, String>>() {});
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String aliasesStr = resultSet.getString(i);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new HashMap<>();
        }
        return JacksonUtils.parseObject(aliasesStr, new TypeReference<Map<String, String>>() {});
    }

    @Override
    public Map<String, String> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String aliasesStr = callableStatement.getString(i);
        if (StringUtils.isEmpty(aliasesStr)) {
            return new HashMap<>();
        }
        return JacksonUtils.parseObject(aliasesStr, new TypeReference<Map<String, String>>() {});
    }


}
