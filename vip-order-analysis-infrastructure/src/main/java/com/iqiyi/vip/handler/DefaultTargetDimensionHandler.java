package com.iqiyi.vip.handler;

import com.google.common.base.Joiner;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/3 17:33
 */
public class DefaultTargetDimensionHandler extends TargetDimensionHandler {

    @Override
    protected String handler4codes(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets, String uk) {
        List<String> dimensionCodeList = analysisDimensions.stream().map(AnalysisDimension::getCode).collect(Collectors.toList());
        TargetDimensionHandler.Extra extra = getExtra(uk);
        int dsize = analysisDimensions.size();
        extra.setDimensions(new int[]{dsize});
        return Joiner.on(",").join(dimensionCodeList);
    }

    @Override
    protected String handler4codesWithOutTimeType(List<AnalysisDimension> analysisDimensions, String uk) {
        List<String> dimensionCodeList = analysisDimensions.stream()
            .filter(d -> d.getGroup() != null && d.getGroup() != 3)
            .map(AnalysisDimension::getCode).collect(Collectors.toList());
        TargetDimensionHandler.Extra extra = getExtra(uk);
        int dsize = analysisDimensions.size();
        extra.setDimensions(new int[]{dsize});
        return Joiner.on(",").join(dimensionCodeList);
    }

    @Override
    protected String handler4values(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets) {
        return analysisDimensions.stream().map(AnalysisDimension::getValue).collect(Collectors.joining(","));
    }

    @Override
    protected String handler4valuesWithOutTimeType(List<AnalysisDimension> analysisDimensions) {
        return analysisDimensions.stream()
            .filter(d -> d.getGroup() != null && d.getGroup() != 3)
            .map(AnalysisDimension::getValue)
            .collect(Collectors.joining(","));
    }

    @Override
    protected String doGetDimensionName(Integer businessTypeId, Integer themeType, AnalysisDimensionRepository analysisDimensionRepository, String code, String defaultVal) {
        AnalysisDimension dimension = analysisDimensionRepository.selectByThemeType(code, businessTypeId, themeType);
        return dimension == null ? defaultVal : dimension.getName();
    }

    @Override
    protected String doGetTargetName(Integer businessTypeId, AnalysisTargetRepository analysisTargetRepository, String code, String defaultVal) {
        AnalysisTarget target = analysisTargetRepository.selectByCode(code);
        return target == null ? defaultVal : target.getName();
    }
}
