package com.iqiyi.vip.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.WriteContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.merge.LoopMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.handler.base.ExcelGenerateNameHandler;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/8 11:50
 */
@Slf4j
public class TransferExcelGenerateNameHandler extends ExcelGenerateNameHandler {


    // 增加缓存 避免重复计算，影响性能

    @Override
    protected String getExcelName(List<ExcelSheetDataDTO> excelSheetDataDTOS, Function<String, AnalysisTargetGroup> targetGroupFunction) {
        return "购买用户分析_" + Optional.ofNullable(targetGroupFunction.apply(excelSheetDataDTOS.get(0).getSheetName()))
            .orElseGet(() -> new AnalysisTargetGroup()).getName();
    }

    @Override
    protected String getSheetName(ExcelSheetDataDTO excelSheetDataDTO, AnalysisTarget analysisTarget, String sheetNamePrefix) {
        return sheetNamePrefix.concat(analysisTarget.getName());
    }


    private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            return cell.getStringCellValue().getBytes().length;
        } else {
            CellData<?> cellData = cellDataList.get(0);
            CellDataTypeEnum type = cellData.getType();
            if (type == null) {
                return 5;
            } else {
                // 根据数据类型，定义不同的单元格宽度
                switch (type) {
                    case STRING:
                        // 按行拆分文本
                        String[] lines = cellData.getStringValue().split("\\n");

                        // 初始化最大长度为0
                        int maxLength = 0;

                        // 遍历每一行，计算最大长度
                        for (String line : lines) {
                            int currentLength = line.getBytes().length;
                            maxLength = Math.max(maxLength, currentLength);
                        }

                        return maxLength;
                    case BOOLEAN:
                        return cellData.getBooleanValue().toString().getBytes().length;
                    case NUMBER:
                        NumberFormat nf = NumberFormat.getInstance();
                        nf.setGroupingUsed(true);
                        String formattedNumber = nf.format(cellData.getNumberValue());
                        //保留4位小数，需要加上小数点的长度,数字需要而外多加一些长度，否则会出现数字显示不全的情况
                        return formattedNumber.length() + 4 + 5;
                    default:
                        return 5;
                }
            }
        }
    }

    @Override
    protected void export(ExcelWriter excelWriter, int cs, String path, String sn, int no,
        List<List<Object>> descs, List<List<Object>> partitions, List<List<String>> heads, List<List<Object>> datas, Integer excelWidthAdjust) {

        WriteContext writeContext = excelWriter.writeContext();
        WriteWorkbookHolder writeWorkbookHolder = writeContext.writeWorkbookHolder();
        writeWorkbookHolder.setNeedHead(false);
        List<WriteHandler> writeHandlerList = writeWorkbookHolder.getWriteHandlerList();
        boolean descsEmpty = CollectionUtil.isEmpty(descs);
        boolean partitionEmpty = CollectionUtil.isEmpty(partitions);
        Map<Integer, Map<Integer, Integer>> CACHE = new HashMap<>();

        addWriteHandler(writeHandlerList, new CellWriteHandler() {
            @Override
            public void afterCellDispose(CellWriteHandlerContext context) {
                Cell cell = context.getCell();
                WriteCellData<?> cellData = context.getFirstCellData();
                WriteCellStyle orCreateStyle = cellData.getOrCreateStyle();

                //VIPDEV-16323 第4、5点 如果单元格内容是数字，单元格样式和类型转换为数字类型
                if (cellData.getType() == CellDataTypeEnum.NUMBER && cellData.getNumberValue() != null) {
                    DataFormatData dataFormatData = new DataFormatData();
                    BigDecimal number = cellData.getNumberValue();
                    if (number.stripTrailingZeros().scale() > 0) {
                        dataFormatData.setIndex((short) 4);
                        dataFormatData.setFormat("#,##0.00");
                    } else {
                        dataFormatData.setIndex((short) 3);
                        dataFormatData.setFormat("#,##0");
                    }
                    orCreateStyle.setDataFormatData(dataFormatData);
                    cellData.setWriteCellStyle(orCreateStyle);
                }

                if (!descsEmpty && cell.getRowIndex() == 0) {
                    WriteCellStyle writeCellStyle = orCreateStyle;
                    WriteFont writeFont = new WriteFont();
                    writeFont.setColor(Font.COLOR_RED);
                    writeFont.setFontHeightInPoints((short) 12);
                    writeCellStyle.setWriteFont(writeFont);
                    writeCellStyle.setWrapped(true);
                    writeCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
                } else {
                    WriteCellStyle writeCellStyle = orCreateStyle;
                    if (((!descsEmpty && !partitionEmpty) && (cell.getRowIndex() == 2 || cellData.getRowIndex() == 1)) || // 描述、分区都不为空
                        ((!descsEmpty && partitionEmpty) && (cell.getRowIndex() == 1)) || // 描述、分区其中之一为空
                        ((descsEmpty && !partitionEmpty) && (cell.getRowIndex() == 1 || cellData.getRowIndex() == 0)) || // 描述、分区其中之一为空
                        ((descsEmpty && partitionEmpty) && (cell.getRowIndex() == 0))) { // 描述、分区全都为空
                        WriteFont writeFont = new WriteFont();
                        writeFont.setBold(true);
                        writeFont.setFontHeightInPoints(
                            !partitionEmpty && ((!descsEmpty && cellData.getRowIndex() == 1) || (descsEmpty && cellData.getRowIndex() == 0))
                                ? (short) 16 : (short) 13);
                        writeCellStyle.setWriteFont(writeFont);
                        writeCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                        writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                    }
                    writeCellStyle.setWrapped(true);
                    writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                }

                if (orCreateStyle.getWriteFont() != null) {
                    orCreateStyle.getWriteFont().setFontName("微软雅黑");
                } else {
                    WriteFont writeFont = new WriteFont();
                    writeFont.setFontName("微软雅黑");
                    orCreateStyle.setWriteFont(writeFont);
                }
                orCreateStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                orCreateStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                orCreateStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                orCreateStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                orCreateStyle.setBorderRight(BorderStyle.THIN);
                orCreateStyle.setBorderLeft(BorderStyle.THIN);
                orCreateStyle.setBorderTop(BorderStyle.THIN);
                orCreateStyle.setBorderBottom(BorderStyle.THIN);

            }
        }, true); // 表格格式设置



        // Handler 集合
        // Excel 描述行设置  高度可以通过 excelWidthAdjust 控制
        addWriteHandler(writeHandlerList, new LongestMatchColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
                Map<Integer, Integer> maxColumnWidthMap = CACHE.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>());

                // 头部或者是数据行时
                boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
                if (!needSetWidth) {
                    return;
                }
                //描述行先不设置列宽，描述行在后面会合并单元格，会有问题;
                if (!descsEmpty && cell.getRowIndex() == 0) {
                    return;
                }

                Integer columnWidth = dataLength(cellDataList, cell, isHead);
                if (columnWidth < 0) {
                    return;
                }

                //VIPDEV-16323 列宽（最长值的长度小于25时，按最长值的宽度+2，当最长值的长度大于25时，限制列宽为25）
                columnWidth = Math.min(25, columnWidth + 2);
                Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
                if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                    columnWidth= Math.max(columnWidth, 5);
                    maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(),columnWidth  * 256);
                }
            }

        }, true); // 设置表格宽

        if (!partitionEmpty) {
            int c = 0;
            int ic = 0;
            int columnIndex = 0;
            for (List<Object> lts : partitions) {
                for (Object obj : lts) {
                    c++;
                    if ((!Objects.equals(obj, PLACEHOLDER) && c > 1) || c == lts.size()) {
                        int columnExtend = (c == lts.size() ? ic + 1 : ic);
                        if (columnExtend > 1) { // 只有合并列大于1才可以添加Handler
                            addWriteHandler(writeHandlerList, new LoopMergeStrategy(1, columnExtend, columnIndex) {
                                @Override
                                public void afterRowDispose(RowWriteHandlerContext context) {
                                    int cRowIndex = descsEmpty ? 0 : 1;
                                    if (context.getRowIndex() == cRowIndex) {
                                        super.afterRowDispose(context);
                                    }
                                }
                            }, true); // 分区
                        }
                        columnIndex = ic;
                        ic = 0;
                    }
                    ic++;
                }
                c = 0;
                ic = 0;
                columnIndex = 0;
            }
        }


        // Header、分区、描述表格大小
        addWriteHandler(writeHandlerList, new SimpleRowHeightStyleStrategy((short) 10, (short) 20) {
            @Override
            protected void setContentColumnHeight(Row row, int relativeRowIndex) {

                if (!descsEmpty && !partitionEmpty && row.getRowNum() == 0) {
//                    String text = row.getCell(0).getStringCellValue();
                    //todo 目前根据实际效果是随意定的，理论上样式可以定制化设置，但行高等这些是否可以考虑自适应
//                    VIPDEV-16323 PRD第二点与第三点说明描述固定行高150，列宽106
//                    转移分析150
                    row.setHeightInPoints(150);
                }else if(!descsEmpty && partitionEmpty && row.getRowNum() == 0){
//                    诊断分析
                    row.setHeightInPoints(50);

                } else if (((!descsEmpty && row.getRowNum() == 1) || (descsEmpty && row.getRowNum() == 0) && !partitionEmpty)) {
                    row.setHeightInPoints(35);
                } else if ((!descsEmpty && !partitionEmpty && row.getRowNum() == 2) || // 描述、分区都不为空
                    ((!descsEmpty || !partitionEmpty) && row.getRowNum() == 1) || // 描述、分区其中之一为空
                    ((descsEmpty && partitionEmpty) && row.getRowNum() == 0)) { // 描述、分区全都为空
                    row.setHeightInPoints(35);
                }
            }
        }, true);



        // 初始化最大长度为0
        int maxLength = 0;
        if(!descsEmpty){
            // 按行拆分文本
            String[] lines = descs.get(0).toString().split("\\n");

            // 遍历每一行，计算最大长度
            for (String line : lines) {
                int currentLength = line.length() *2;
                maxLength = Math.max(maxLength, currentLength);
            }
        }

        // 合并单元格数量 eachRow 合并行，columnExtend 合并列，columnIndex 在哪列开始合并（向后）
        // 描述，需要合并多少列，通过实际数据评估，如果数据只有 5 列以内，描述一句话会换行，所以数据小于 5 列，则默认合并 16 列，如果大于 5 列，则按照数据长度展示
        // 修订 ：行数应该为大于>cs，避免描述行过短
        int finalMaxLength = maxLength;
        LoopMergeStrategy loopMergeStrategy = new LoopMergeStrategy(1, cs + 1, 0) {
            @Override
            public void afterRowDispose(RowWriteHandlerContext context) {


                if(!descsEmpty && partitionEmpty){
                    context.getWriteSheetHolder().getSheet().setColumnWidth(cs, 8*256);
                }else if(!descsEmpty && !partitionEmpty){
                    Map<Integer, Integer> maxColumnWidthMap = CACHE.computeIfAbsent(context.getWriteSheetHolder().getSheetNo(), k -> new HashMap<>());
                    int totalColumnLength = 0;
                    for (int i = 0; i < cs; i++) {
                        totalColumnLength += maxColumnWidthMap.getOrDefault(i, 0);
                    }

                    Integer actualColumnWidth = maxColumnWidthMap.getOrDefault(cs, 254*255);
                    Integer expectMinColumnWidth = finalMaxLength -totalColumnLength;
                    if(actualColumnWidth > expectMinColumnWidth){
                        actualColumnWidth = Math.max(expectMinColumnWidth, 5);

                        maxColumnWidthMap.put(cs, actualColumnWidth);
                        context.getWriteSheetHolder().getSheet().setColumnWidth(cs, Math.min(actualColumnWidth, 200)*256);
                    }
                }
                if (context.getRowIndex() == 0) {
                    super.afterRowDispose(context);
                }
            }
        };

        addWriteHandler(writeHandlerList, loopMergeStrategy, !descsEmpty); // 描述

        // 输出
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setSheetName(sn);
        writeSheet.setSheetNo(no);

        if (!descsEmpty) {
            excelWriter.write(descs, writeSheet);
        }
        if (!partitionEmpty) {
            excelWriter.write(partitions, writeSheet);
        }
        excelWriter.write(heads, writeSheet);
        excelWriter.write(datas, writeSheet);

        writeHandlerList.remove(loopMergeStrategy); //多sheet时要remove掉
    }
}
