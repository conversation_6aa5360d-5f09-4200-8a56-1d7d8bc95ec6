package com.iqiyi.vip.handler;

import com.alibaba.excel.ExcelWriter;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.handler.base.ExcelGenerateNameHandler;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/8 11:50
 */
public class DefaultExcelGenerateNameHandler extends ExcelGenerateNameHandler {
    @Override
    protected String getExcelName(List<ExcelSheetDataDTO> excelSheetDataDTOS, Function<String, AnalysisTargetGroup> targetGroupFunction) {
        return excelSheetDataDTOS.stream()
                .map(dto -> {
                    final AnalysisTargetGroup analysisTargetGroup = StringUtils.isNotBlank(dto.getSheetName()) ? targetGroupFunction.apply(dto.getSheetName()) : null;
                    return analysisTargetGroup == null ? dto.getSheetName() : analysisTargetGroup.getName();
                })
                .distinct()
                .collect(Collectors.joining("&"));
    }

    @Override
    protected String getSheetName(ExcelSheetDataDTO excelSheetDataDTO, AnalysisTarget analysisTarget, String sheetNamePrefix) {
        return (analysisTarget == null ? excelSheetDataDTO.getSheetName() :
                analysisTarget.getName()).concat("_").concat(excelSheetDataDTO.getDataYM() == null ? "" :
                excelSheetDataDTO.getDataYM());
    }

    @Override
    protected void export(ExcelWriter excelWriter, int cs, String path, String sn, int no,
                          List<List<Object>> descs, List<List<Object>> partitions, List<List<String>> heads, List<List<Object>> datas, Integer excelWidthAdjust) {
        // 输出
//        WriteSheet writeSheet = EasyExcel.writerSheet(no, sn).head(heads).build();
//        excelWriter.write(datas, writeSheet);
        new TransferExcelGenerateNameHandler().export(excelWriter, cs, path, sn, no, descs, partitions, heads, datas, excelWidthAdjust);
    }
}
