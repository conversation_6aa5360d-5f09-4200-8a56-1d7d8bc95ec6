package com.iqiyi.vip.handler.mybatis;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: linpeihui
 * @createTime: 2023/09/18
 */
public class ConditionEnumsTypeHandler extends BaseTypeHandler<List<ConditionPair>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<ConditionPair> conditionPairs, JdbcType jdbcType)
        throws SQLException {
        String enumsStr = JacksonUtils.toJsonString(conditionPairs);
        preparedStatement.setString(i, enumsStr);
    }

    @Override
    public List<ConditionPair> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String enumsStr = resultSet.getString(s);
        if (StringUtils.isEmpty(enumsStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(enumsStr, ConditionPair.class);
    }

    @Override
    public List<ConditionPair> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String enumsStr = resultSet.getString(i);
        if (StringUtils.isEmpty(enumsStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(enumsStr, ConditionPair.class);
    }

    @Override
    public List<ConditionPair> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String enumsStr = callableStatement.getString(i);
        if (StringUtils.isEmpty(enumsStr)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(enumsStr, ConditionPair.class);
    }

}
