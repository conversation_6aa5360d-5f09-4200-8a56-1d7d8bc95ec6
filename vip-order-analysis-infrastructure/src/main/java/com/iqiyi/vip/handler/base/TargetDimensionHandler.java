package com.iqiyi.vip.handler.base;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.handler.DefaultTargetDimensionHandler;
import com.iqiyi.vip.handler.TransferAnalysisTargetDimensionHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 该类的作用是拆分前后维度、名称
 * 例如：
 *  购买类型-separation-选定用户购买类型-separation-转移用户购买类型
 *  维度名称：购买类型
 *          -   是在订单分析收益（http://test-order.analysis.online.qiyi.qae/order），维度 -> 添加维度 中看到的是"购买类型"，而不是未拆分的名称 "购买类型-separation-选定用户购买类型-separation-转移用户购买类型"
 *  对于转移分析：拆分成 选定用户购买类型 和 转移用户购买类型
 *          -   用户转移分析后产生的excel中的header名字
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/3/3 17:28
 */
public abstract class TargetDimensionHandler {

    static Map<String, Extra> extraMap = new ConcurrentHashMap<>(1 << 5);

    /**
     * 模板渲染维度code处理
     */
    public static String retDimensionCodes(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets, Long taskId, Integer businessTypeId) {
        TargetDimensionHandler handler = getDimensionHandler(businessTypeId);
        return handler.handler4codes(analysisDimensions, analysisTargets, getExtraUk(taskId, businessTypeId));
    }

    /**
     * 模板渲染维度code处理
     */
    public static String retDimensionCodesWithOutTimeType(List<AnalysisDimension> analysisDimensions, Long taskId, Integer businessTypeId) {
        TargetDimensionHandler handler = getDimensionHandler(businessTypeId);
        return handler.handler4codesWithOutTimeType(analysisDimensions, getExtraUk(taskId, businessTypeId));
    }

    /**
     * 天眼首页 - 维度分组下面的维度名称处理
     */
    public static String retDimensionName(AnalysisDimension dimension) {
        return StringUtils.isNoneBlank(dimension.getName()) ? Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(dimension.getName())[0]
            : dimension.getName();
    }

    public static String retDimensionCode(AnalysisDimension dimension) {
        return StringUtils.isNoneBlank(dimension.getCode()) ? Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(dimension.getCode())[0]
                : dimension.getCode();
    }

    /**
     * 天眼首页 - 指标分组下面的维度名称处理
     */
    public static String retTargetName(AnalysisTarget target) {
        return StringUtils.isNoneBlank(target.getName()) ? Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(target.getName())[0]
                : target.getName();
    }
    public static String retTargetCode(AnalysisTarget target) {
        return StringUtils.isNoneBlank(target.getCode()) ? Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(target.getCode())[0]
                : target.getCode();
    }

    /**
     * 模板渲染维度value处理
     * @param analysisDimensions
     * @return
     */
    public static String retDimensionValues(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets, Integer businessTypeId) {
        TargetDimensionHandler handler = getDimensionHandler(businessTypeId);
        if (handler == null) {
            return "";
        }
        return handler.handler4values(analysisDimensions, analysisTargets);
    }

    /**
     * 模板渲染维度value处理
     */
    public static String retDimensionValuesWithOutTimeType(List<AnalysisDimension> analysisDimensions, Integer businessTypeId) {
        TargetDimensionHandler handler = getDimensionHandler(businessTypeId);
        return handler.handler4valuesWithOutTimeType(analysisDimensions);
    }

    /**
     * Excel Header 转中文
     */
    public static String getDimensionName(Integer businessTypeId, Integer themeType, AnalysisDimensionRepository analysisDimensionRepository, String code, String defaultVal) {
        return getDimensionHandler(businessTypeId).doGetDimensionName(businessTypeId, themeType, analysisDimensionRepository, code, defaultVal);
    }

    /**
     * Excel Header 转中文
     */
    public static String getTargetName(Integer businessTypeId, AnalysisTargetRepository analysisTargetRepository, String code, String defaultVal) {
        return getDimensionHandler(businessTypeId).doGetTargetName(businessTypeId, analysisTargetRepository, code, defaultVal);
    }

    private static TargetDimensionHandler getDimensionHandler(Integer businessTypeId) {
        boolean isTransfer = businessTypeId != null && businessTypeId.intValue() == 2;
        return isTransfer ? new TransferAnalysisTargetDimensionHandler() : new DefaultTargetDimensionHandler();
    }

    protected static String retDimensionName(AnalysisDimension dimension, String dimensionCode) {
        if (StringUtils.isBlank(dimension.getName())) {
            return dimension.getName();
        }
        String[] split4Codes = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(dimension.getCode());
        String[] split4Names = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(dimension.getName());
        if (split4Names.length == 1 || StringUtils.isBlank(dimensionCode)) {
            return split4Names[0];
        } else {
            for (int idx = 0; idx < split4Codes.length; idx++) {
                if (dimensionCode.equalsIgnoreCase(split4Codes[idx])) {
                    return split4Names[idx + 1];
                }
            }
        }
        return dimension.getName();
    }

    protected static String retTargetName(AnalysisTarget target, String dimensionCode) {
        if (StringUtils.isBlank(target.getName())) {
            return target.getName();
        }
        String[] split4Codes = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(target.getCode());
        String[] split4Names = Constants.TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN.split(target.getName());
        if (split4Names.length == 1 || StringUtils.isBlank(dimensionCode)) {
            return split4Names[0];
        } else {
            for (int idx = 0; idx < split4Codes.length; idx++) {
                if (dimensionCode.equalsIgnoreCase(split4Codes[idx])) {
                    return split4Names[idx + 1];
                }
            }
        }
        return target.getName();
    }

    public static String getExtraUk(Long taskId, Integer businessTypeId) {
        return "" + taskId + "_" + businessTypeId;
    }

    public static Extra getExtra(Long taskId, Integer businessTypeId) {
        return getExtra(getExtraUk(taskId, businessTypeId));
    }

    public static Extra getExtra(String uk) {
        return extraMap.computeIfAbsent(uk, (k) -> Extra.builder().build());
    }

    public static Extra removeExtra(Long taskId, Integer businessTypeId) {
        return extraMap.remove(getExtraUk(taskId, businessTypeId));
    }

    /**
     * 去掉 code 的后缀_xuanding
     * @param code
     * @return
     */
    public static String removeTransCodeSuffix(String code) {
        String suffix = "_xuanding";
        return code.endsWith(suffix) ? code.replace(suffix, "") : code;
    }

    // group by 使用
    protected abstract String handler4codes(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets, String uk);

    protected abstract String handler4codesWithOutTimeType(List<AnalysisDimension> analysisDimensions, String uk);

    // 用户选择的纬度
    protected abstract String handler4values(List<AnalysisDimension> analysisDimensions, List<AnalysisTarget> analysisTargets);

    protected abstract String handler4valuesWithOutTimeType(List<AnalysisDimension> analysisDimensions);

    protected abstract String doGetDimensionName(Integer businessTypeId, Integer themeType, AnalysisDimensionRepository analysisDimensionRepository, String code, String defaultVal);

    protected abstract String doGetTargetName(Integer businessTypeId, AnalysisTargetRepository analysisTargetRepository, String code, String defaultVal);

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Extra implements java.io.Serializable {

        // 维度数量
        // 不同场景取不同的index，转移取[0]和[1]
        private int[] dimensions;

    }
}
