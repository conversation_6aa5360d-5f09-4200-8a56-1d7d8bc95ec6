package com.iqiyi.vip.handler.mybatis;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.utils.JacksonUtils;

/**
 * Created at: 2021-04-30
 *
 * <AUTHOR>
 */
public class ListStringHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JacksonUtils.toJsonString(parameter));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String rsString = rs.getString(columnName);
        if (StringUtils.isBlank(rsString)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(rsString, String.class);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String rsString = rs.getString(columnIndex);
        if (StringUtils.isBlank(rsString)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(rsString, String.class);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String rsString = cs.getString(columnIndex);
        if (StringUtils.isBlank(rsString)) {
            return new ArrayList<>();
        }
        return JacksonUtils.parseArray(rsString, String.class);
    }

}
