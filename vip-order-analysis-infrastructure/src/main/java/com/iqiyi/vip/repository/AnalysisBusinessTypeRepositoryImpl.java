package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.AnalysisBusinessType;
import com.iqiyi.vip.domain.repository.AnalysisBusinessTypeRepository;
import com.iqiyi.vip.mapper.AnalysisBusinessTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Service
@Slf4j
public class AnalysisBusinessTypeRepositoryImpl implements AnalysisBusinessTypeRepository {

    @Resource
    private AnalysisBusinessTypeMapper analysisBusinessTypeMapper;

    @Override
    public List<AnalysisBusinessType> listByThemeType(Integer themeType) {
        List<com.iqiyi.vip.po.AnalysisBusinessType> groups = analysisBusinessTypeMapper.selectAll();
        List<com.iqiyi.vip.po.AnalysisBusinessType> groupsByThemeType = groups.stream().filter(s -> s.getThemeTypes().contains(String.valueOf(themeType)))
            .collect(Collectors.toList());
        return toTransforList(groupsByThemeType);
    }

    private List<AnalysisBusinessType> toTransforList(List<com.iqiyi.vip.po.AnalysisBusinessType> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return null;
        }
        return groups.stream().map(s -> this.adaptor(s)).collect(Collectors.toList());
    }

    private AnalysisBusinessType adaptor(com.iqiyi.vip.po.AnalysisBusinessType group) {
        AnalysisBusinessType newGroup = new AnalysisBusinessType();
        newGroup.setId(group.getId());
        newGroup.setName(group.getName());
        newGroup.setOrder(group.getOrder());
        newGroup.setCreateTime(group.getCreateTime());
        newGroup.setOperator(group.getOperator());
        newGroup.setStatus(group.getStatus());
        newGroup.setUpdateTime(group.getUpdateTime());
        return newGroup;
    }
}
