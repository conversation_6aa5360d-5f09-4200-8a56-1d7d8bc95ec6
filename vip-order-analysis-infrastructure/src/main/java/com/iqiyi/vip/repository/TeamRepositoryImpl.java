package com.iqiyi.vip.repository;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.entity.Team;
import com.iqiyi.vip.domain.repository.TeamRepository;
import com.iqiyi.vip.mapper.TeamMapper;

/**
 * <AUTHOR>
 * @className TeamRepositoryImpl
 * @description
 * @date 2023/2/9
 **/
@Component
public class TeamRepositoryImpl implements TeamRepository {

    @Resource
    private TeamMapper teamMapper;

    @Override
    public List<Team> selectAll() {
        return teamMapper.getAll();
    }
}
