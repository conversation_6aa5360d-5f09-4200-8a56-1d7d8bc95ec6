package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.domain.repository.AnalysisSqlTemplateRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.sqltemplate.AnalysisSqlTemplatePageQryDTO;
import com.iqiyi.vip.mapper.AnalysisSqlTemplateMapper;
import com.iqiyi.vip.po.query.AnalysisSqlTemplatePageQuery;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Service
public class AnalysisSqlTemplateRepositoryImpl implements AnalysisSqlTemplateRepository {

    @Resource
    private AnalysisSqlTemplateMapper analysisSqlTemplateMapper;


    @Override
    public Long save(AnalysisSqlTemplate template) throws Exception {
        com.iqiyi.vip.po.AnalysisSqlTemplate sqlTemplate = this.adapter(template);
        analysisSqlTemplateMapper.insertSelective(sqlTemplate);
        return sqlTemplate.getId();
    }

    @Override
    public void update(AnalysisSqlTemplate template) throws Exception {
        analysisSqlTemplateMapper.updateSelective(this.adapter(template));
    }

    @Override
    public void delete(Long id) {
        analysisSqlTemplateMapper.delete(id);
    }

    @Override
    public AnalysisSqlTemplate selectById(Long id) {
        return this.adapter(analysisSqlTemplateMapper.selectById(id));
    }

    @Override
    public PageListResult<AnalysisSqlTemplate> selectPageList(AnalysisSqlTemplatePageQryDTO pageQryDTO) {
        AnalysisSqlTemplatePageQuery pageQuery = this.convert(pageQryDTO);
        Integer count = analysisSqlTemplateMapper.selectCount(pageQuery);
        List<AnalysisSqlTemplate> dataList = null;
        if (count > 0) {
            List<com.iqiyi.vip.po.AnalysisSqlTemplate> list = analysisSqlTemplateMapper.selectList(pageQuery);
            if (!CollectionUtils.isEmpty(list)) {
                dataList = list.stream().map(l -> this.adapter(l)).collect(Collectors.toList());
            }
        }
        return PageListResult.createSuccess(dataList, pageQryDTO.getPageNo(), pageQryDTO.getPageSize(), count);

    }

    @Override
    public List<AnalysisSqlTemplate> selectAll(BaseQry baseQry) {
        List<com.iqiyi.vip.po.AnalysisSqlTemplate> list = analysisSqlTemplateMapper.selectAll(baseQry.getThemeType(), baseQry.getBusinessTypeId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(s -> this.adapter(s)).collect(Collectors.toList());
    }

    private com.iqiyi.vip.po.AnalysisSqlTemplate adapter(AnalysisSqlTemplate target) throws Exception {
        if (target == null) {
            return null;
        }
        com.iqiyi.vip.po.AnalysisSqlTemplate dbTarget = new com.iqiyi.vip.po.AnalysisSqlTemplate();
        dbTarget.setId(target.getId());
        dbTarget.setCode(target.getCode());
        dbTarget.setName(target.getName());
        dbTarget.setValue(target.getValue());
        dbTarget.setDescription(target.getDescription());
        dbTarget.setCreateOpr(target.getCreateOpr());
        dbTarget.setCreateTime(target.getCreateTime());
        dbTarget.setUpdateOpr(target.getUpdateOpr());
        dbTarget.setUpdateTime(target.getUpdateTime());
        dbTarget.setStatus(target.getStatus());
        dbTarget.setCommitNote(target.getCommitNote());
        dbTarget.setVersion(target.getVersion());
        dbTarget.setBusinessTypeId(target.getBusinessTypeId());
        dbTarget.setThemeType(target.getThemeType());
        return dbTarget;
    }

    private AnalysisSqlTemplate adapter(com.iqiyi.vip.po.AnalysisSqlTemplate target) {
        if (target == null) {
            return null;
        }
        AnalysisSqlTemplate newTarget = new AnalysisSqlTemplate();
        newTarget.setId(target.getId());
        newTarget.setName(target.getName());
        newTarget.setValue(target.getValue());
        newTarget.setDescription(target.getDescription());
        newTarget.setCreateOpr(target.getCreateOpr());
        newTarget.setCreateTime(target.getCreateTime());
        newTarget.setUpdateOpr(target.getUpdateOpr());
        newTarget.setUpdateTime(target.getUpdateTime());
        newTarget.setStatus(target.getStatus());
        newTarget.setVersion(target.getVersion());
        newTarget.setCommitNote(target.getCommitNote());
        newTarget.setBusinessTypeId(target.getBusinessTypeId());
        newTarget.setThemeType(target.getThemeType());
        return newTarget;
    }

    private AnalysisSqlTemplatePageQuery convert(AnalysisSqlTemplatePageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        AnalysisSqlTemplatePageQuery pageQuery = new AnalysisSqlTemplatePageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);

        //填充查询条件
        pageQuery.setId(pageQryDTO.getId());
        pageQuery.setName(pageQryDTO.getName());
        pageQuery.setBusinessTypeId(pageQryDTO.getBusinessTypeId());
        pageQuery.setThemeType(pageQryDTO.getThemeType());
        return pageQuery;
    }
}
