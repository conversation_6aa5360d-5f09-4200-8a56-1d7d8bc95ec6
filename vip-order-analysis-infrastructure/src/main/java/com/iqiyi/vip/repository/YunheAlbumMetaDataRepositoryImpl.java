package com.iqiyi.vip.repository;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.YunheAlbumMetaDataDO;
import com.iqiyi.vip.domain.repository.YunheAlbumMetaDataRepository;
import com.iqiyi.vip.dto.competitor.AlbumContentInfoDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;
import com.iqiyi.vip.mapper.YunheAlbumMetaDataMapper;
import com.iqiyi.vip.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云合专辑元数据仓储实现类
 */
@Component
@Slf4j
public class YunheAlbumMetaDataRepositoryImpl implements YunheAlbumMetaDataRepository {

    public static final List<String> LEVELS = Lists.newArrayList("S", "A+");

    @Resource
    private YunheAlbumMetaDataMapper yunheAlbumMetaDataMapper;

    @ConfigJsonValue("${yunhe.level.whitelist:[\"S\", \"A+\"]}")
    private List<String> yunHeLevelWhitelist;

    @ConfigJsonValue("${yunhe.channel.mapping:{}}")
    private Map<String, String> yunchannelNameMap;

    @Override
    public List<AlbumContentInfoDTO> search(CompetitorPriceSearchParam param) {
        // 将时间戳转换为日期字符串
        String startDate = DateUtil.format(new Date(param.getStartTime()), "yyyy-MM-dd");
        String endDate = DateUtil.format(new Date(param.getEndTime()), "yyyy-MM-dd");
        
        // 如果传入的是平台名称，转换为平台代码
        String brandCode = param.getBrand();
        if (StringUtils.isNotBlank(brandCode)) {
            brandCode = Constants.NameCodeMap().get(brandCode);
        }

        List<YunheAlbumMetaDataDO> albumList = yunheAlbumMetaDataMapper.searchByDateRange(startDate, endDate, brandCode)
            .stream()
            .filter(a -> StringUtils.isNotBlank(a.getYunheLevel()) && yunHeLevelWhitelist.contains(a.getYunheLevel().trim()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(albumList)) {
            return Collections.emptyList();
        }

        return albumList.stream()
            .map(this::convertToAlbumContentInfo)
            .collect(Collectors.toList());
    }

    /**
     * 将YunheAlbumMetaDataDO转换为AlbumContentInfoDTO
     * @param albumData 专辑数据
     * @return 专辑内容信息
     */
    private AlbumContentInfoDTO convertToAlbumContentInfo(YunheAlbumMetaDataDO albumData) {
        // 处理状态：
        // 1. 先判断albumData的episodeNum与totalEpisodeNum相等时显示完结
        // 2. 否则显示"更新至X集"，X取episodeNum的整数部分
        String status;
        String episodeNum = albumData.getEpisodeNum();
        String totalEpisodeNum = albumData.getTotalEpisodeNum();
        
        if (StringUtils.isNotBlank(episodeNum) && StringUtils.isNotBlank(totalEpisodeNum)) {
            // 提取整数部分进行比较
            String episodeNumInt = extractNumber(episodeNum);
            String totalEpisodeNumInt = extractNumber(totalEpisodeNum);
            
            if (episodeNumInt.equals(totalEpisodeNumInt)) {
                status = "完结";
            } else {
                status = "更新至" + episodeNumInt + "集";
            }
        } else {
            String episodeCount = extractNumber(episodeNum);
            status = "更新至" + episodeCount + "集";
        }
        
        // 处理播放平台
        String dayPlatformStart = albumData.getDayPlatformStart();
        String platforms = "";
        String exclusiveType = "独";
        
        if (StringUtils.isNotBlank(dayPlatformStart)) {
            String[] platformCodes = dayPlatformStart.split(",");
            
            // 处理是否独播：dayPlatformStart多于1个元素，返回'联'，否则返回'独'
            if (platformCodes.length > 1) {
                exclusiveType = "联";
            }
            
            // 将平台代码映射为中文名称
            platforms = Arrays.stream(platformCodes)
                    .map(platformCode -> Constants.CodeNameMap().get(platformCode.trim()))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("、"));
        }
        
        return AlbumContentInfoDTO.builder()
                .contentId(albumData.getAlbumId())
                .contentName(albumData.getAlbumName())
                .level(albumData.getYunheLevel())
                .channelName(yunchannelNameMap.getOrDefault(albumData.getWorksType(), "电视剧"))
                .tags(albumData.getTags())
                .onlineTime(albumData.getReleaseTime())
                .finishTime(albumData.getOfflineTime() == null ? albumData.getReleaseTime() : albumData.getOfflineTime())
                .status(status)
                .platforms(platforms)
                .exclusiveType(exclusiveType)
                .build();
    }
    
    /**
     * 从字符串中提取数字部分
     * @param str 原始字符串
     * @return 提取的数字，如果没有则返回"0"
     */
    private String extractNumber(String str) {
        if (StringUtils.isBlank(str)) {
            return "0";
        }
        
        if (str.contains(".")) {
            return str.substring(0, str.indexOf("."));
        }
        
        return str;
    }
} 