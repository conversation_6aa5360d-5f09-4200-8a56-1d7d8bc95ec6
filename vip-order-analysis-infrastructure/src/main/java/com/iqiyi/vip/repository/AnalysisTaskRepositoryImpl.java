package com.iqiyi.vip.repository;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.domain.repository.AnalysisSubTaskRepository;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.TaskQueryInfoRepository;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.task.AnalysisTaskPageQryDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskResDTO;
import com.iqiyi.vip.handler.base.TargetDimensionHandler;
import com.iqiyi.vip.mapper.AnalysisTaskMapper;
import com.iqiyi.vip.mapper.ScheduledAnalysisTaskMapper;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @className AnalysisTaskRepositoryImpl
 * @description
 * @date 2022/6/1
 **/
@Component
@Slf4j
public class AnalysisTaskRepositoryImpl implements AnalysisTaskRepository {

    @Resource
    private AnalysisTaskMapper analysisTaskMapper;
    @Resource
    private AnalysisSubTaskRepository analysisSubTaskRepository;
    @Resource
    private AnalysisTargetRepository analysisTargetRepository;
    @Resource
    private AnalysisDimensionRepository analysisDimensionRepository;
    @Resource
    private TaskQueryInfoRepository taskQueryInfoRepository;
    @Resource
    private ScheduledAnalysisTaskMapper scheduledAnalysisTaskMapper;

    @Override
    public AnalysisTaskDO getTaskByUniqueIdentification(String uniqueIdentification) {
        return analysisTaskMapper.getByUniqueIdentification(uniqueIdentification);
    }

    @Override
    public PageListResult<AnalysisTaskResDTO> getPageListResult(AnalysisTaskPageQryDTO reqDto, boolean isHighLevelUser) {
        List<AnalysisTaskDO> pageList = analysisTaskMapper.getPageList(reqDto);
        List<AnalysisTaskResDTO> analysisTaskResDTOS = pageList.stream()
            .map(t -> convertToAnalysisTaskResDTO(t, isHighLevelUser))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        String targetName = reqDto.getTargetName();
        List<AnalysisTaskResDTO> filteredList = analysisTaskResDTOS;
        if (StringUtils.isNotBlank(targetName)) {
            filteredList = analysisTaskResDTOS.stream()
                .filter(resDTO -> resDTO.getTargetNames().contains(targetName))
                .collect(Collectors.toList());
        }
        Integer pageSize = reqDto.getPageSize();
        Integer pageNo = reqDto.getPageNo();
        int startIndex = pageSize * (pageNo - 1);
        int totalCount = filteredList.size();
        if (startIndex > totalCount) {
            filteredList = new ArrayList<>();
        }
        int endIndex = Math.min(pageSize * pageNo, totalCount);
        List<AnalysisTaskResDTO> finalResult = filteredList.subList(startIndex, endIndex);
        addQueryInfo(isHighLevelUser, finalResult);
        return PageListResult.createSuccess(finalResult, pageNo, pageSize, totalCount);
    }

    /**
     * 为高级用户添加查询信息
     *
     * @param isHighLevelUser 是否是高级用户
     * @param finalResult     分析任务结果列表
     */
    private void addQueryInfo(boolean isHighLevelUser, List<AnalysisTaskResDTO> finalResult) {
        if (!isHighLevelUser || CollectionUtils.isEmpty(finalResult)) {
            return;
        }

        // 获取任务ID列表
        List<Long> taskIds = finalResult.stream()
            .map(AnalysisTaskResDTO::getId)
            .collect(Collectors.toList());

        // 查询并构建任务ID与查询信息的映射
        Map<Long, String> taskIdToQueryInfo = taskQueryInfoRepository.searchByTaskIds(taskIds).stream()
            .filter(queryInfo -> StringUtils.isNotBlank(queryInfo.getQueryInfo()))
            .collect(Collectors.toMap(
                TaskQueryInfo::getTaskId,
                TaskQueryInfo::getQueryInfo,
                (existing, replacement) -> existing));

        // 为每个结果设置查询信息
        finalResult.forEach(result ->
            result.setQuerySql(taskIdToQueryInfo.get(result.getId())));
    }

    @Override
    public long addAnalysisTask(AnalysisTaskDO analysisTaskDO) {
        return analysisTaskMapper.insertSelective(analysisTaskDO);
    }

    @Override
    public AnalysisTaskDO getTaskByIdAndOperator(Long taskId, String operator) {
        return analysisTaskMapper.getTaskByIdAndOperator(taskId, operator);
    }

    @Override
    public AnalysisTaskDO getTaskById(Long taskId) {
        if (taskId == null) {
            return null;
        }
        return analysisTaskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public int reSetTaskStatus(Long taskId, Integer status, String result, Integer executeTime) {
        return analysisTaskMapper.reSetTaskStatus(taskId, status, result, executeTime);
    }

    @Override
    public int reTaskName(Long taskId, String taskName, String operator) {
        return analysisTaskMapper.reTaskName(taskId, taskName, operator);
    }

    @Override
    public int deleteTaskRecord(Long taskId) {
        return analysisTaskMapper.deleteByPrimaryKey(taskId);
    }

    @Override
    public int saveQueryInfo(Long taskId, String querySql) {
        return analysisTaskMapper.saveQueryInfo(taskId, querySql);
    }


    @Override
    public long addScheduledAnalysisTask(AnalysisTaskDO analysisTaskDO) {
        return scheduledAnalysisTaskMapper.insertSelective(analysisTaskDO);
    }

    @Override
    public AnalysisTaskDO getScheduledTaskByUniqueIdentification(String uniqueIdentification) {
        return scheduledAnalysisTaskMapper.getByUniqueIdentification(uniqueIdentification);
    }

    @Override
    public int reScheduledSetTaskStatus(Long taskId, Integer status, String result, Integer executeTime) {
        return scheduledAnalysisTaskMapper.reSetTaskStatus(taskId, status, result, executeTime);
    }

    private AnalysisTaskResDTO convertToAnalysisTaskResDTO(AnalysisTaskDO analysisTaskDO, boolean isHighLevelUser) {
        String targets = analysisTaskDO.getTargets();
        if (StringUtils.isBlank(targets)) {
            return null;
        }
        List<String> targetCodes = Splitter.on(",").splitToList(targets);
        String targetNames = targetCodes.stream().map(code -> {
            final AnalysisTarget analysisTarget = analysisTargetRepository.selectByCode(code);
            return analysisTarget != null ? TargetDimensionHandler.retTargetName(analysisTarget) : null;
        }).filter(v -> !Objects.isNull(v)).collect(Collectors.joining("、"));

        Integer businessTypeId = analysisTaskDO.getBusinessTypeId();
        Integer themeType = analysisTaskDO.getThemeType();
        String dimensionNames = "";
        if (StringUtils.isNotBlank(analysisTaskDO.getDimensions())) {
            List<String> dimensionCodes = Splitter.on(",").splitToList(analysisTaskDO.getDimensions());
            dimensionNames = dimensionCodes.stream()
                .map(code -> analysisDimensionRepository.selectByThemeType(code, businessTypeId, themeType))
                .filter(Objects::nonNull)
                .map(TargetDimensionHandler::retDimensionName)
                .collect(Collectors.joining("、"));
        }
        String condition = analysisTaskDO.getCondition();
        HashMap<String, Object> conditionMap = JacksonUtils.parseObject(condition, HashMap.class);
        if (conditionMap == null) {
            conditionMap = new HashMap<>();
        }
        Long payStartTime = MapUtils.getLong(conditionMap, "payStartTime", (Long) null);
        Long payEndTime = MapUtils.getLong(conditionMap, "payEndTime", (Long) null);
        Long compareStartTime = MapUtils.getLong(conditionMap, "compareStartTime", (Long) null);
        Long compareEndTime = MapUtils.getLong(conditionMap, "compareEndTime", (Long) null);
        if (payStartTime != null) {
            conditionMap.put("payStartTime", DateUtil.format(DateUtil.date(payStartTime), DatePattern.NORM_DATETIME_FORMAT));
        }
        if (payEndTime != null) {
            conditionMap.put("payEndTime", DateUtil.format(DateUtil.date(payEndTime), DatePattern.NORM_DATETIME_FORMAT));
        }
        if (compareStartTime != null) {
            conditionMap.put("compareStartTime", DateUtil.format(DateUtil.date(compareStartTime), DatePattern.NORM_DATETIME_FORMAT));
        }
        if (compareEndTime != null) {
            conditionMap.put("compareEndTime", DateUtil.format(DateUtil.date(compareEndTime), DatePattern.NORM_DATETIME_FORMAT));
        }

        AnalysisSubTaskDO subTask = analysisSubTaskRepository.getSubTaskByTaskIdOrUserGroupId(analysisTaskDO.getId(),null);

        return AnalysisTaskResDTO.builder()
                .id(analysisTaskDO.getId())
                .targetNames(targetNames)
                .dimensionNames(dimensionNames)
                .condition(JacksonUtils.toJsonString(conditionMap))
                .status(analysisTaskDO.getStatus())
                .result(analysisTaskDO.getResult())
                .executeTime(analysisTaskDO.getExecuteTime())
                .createTime(analysisTaskDO.getCreateTime())
                .updateTime(analysisTaskDO.getUpdateTime())
                .operator(analysisTaskDO.getOperator())
                .uniqueIdentification(analysisTaskDO.getUniqueIdentification())
                .taskName(analysisTaskDO.getTaskName())
                .businessTypeId(businessTypeId)
                .dataPermissionType(analysisTaskDO.getDataPermissionType())
                .themeType(themeType)
                .uploadToBeiDouStatus(subTask == null ? -1 : subTask.getStatus())
                .build();
    }
}
