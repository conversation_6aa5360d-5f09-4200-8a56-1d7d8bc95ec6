package com.iqiyi.vip.repository;

import com.google.common.base.Joiner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.domain.repository.FvStructureRepository;
import com.iqiyi.vip.dto.condition.FvChannelPair;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.mapper.FvStructureMapper;

/**
 * <AUTHOR>
 * @className FvStructureRepositoryImpl
 * @description
 * @date 2022/6/13
 **/
@Component
public class FvStructureRepositoryImpl implements FvStructureRepository  {
    @Resource
    private FvStructureMapper fvStructureMapper;

    @Override
    public List<FvChannelPair> getFvChannelsByLevel(Integer level) {
        String businessId = "level" + level + "_id";
        String columnName;
        ArrayList<String> nameElement = new ArrayList<>();
        for (int i = 1; i <= level; i++) {
            columnName = "level" + i + "_name";
            nameElement.add(columnName);
            if (i != level) {
                nameElement.add("'-'");
            }
        }

        String businessName = Joiner.on(",").join(nameElement);
        List<FvChannelPair> channelsByLevel = fvStructureMapper.getFvChannels(businessId, businessName);
        for (FvChannelPair fvChannelPair : channelsByLevel) {
            fvChannelPair.setLevel(level);
        }
        return channelsByLevel;
    }

    @Override
    public List<FvChannelPair> fvChannels(Integer preChannelId, int curLevel, int nextLevel, Integer teamId) {
        if (nextLevel > 8) {
            return new ArrayList<>();
        }

        if (nextLevel > 2 && teamId == null) {
            throw new BizRuntimeException(CodeEnum.TEAMID_NOT_EXIST_ERROR);
        }
        if (nextLevel == LabelEnum.L1.getLevel()) {
            return fvStructureMapper.rootFvChannel();
        }
        String columnsFormat = "level%d_id as id, level%d_name as name, %d as level";
        String whereFormat = teamId != null ? "level%d_id = %d and team_id = %d" : "level%d_id = %d";
        String groupByFormat = "level%d_id, level%d_name";
        if (nextLevel == LabelEnum.T.getLevel()) {
            columnsFormat = "team_id as id, team_name as name, %d as level";
            groupByFormat = "team_id";
            return fvStructureMapper.fvChannels(String.format(columnsFormat, nextLevel),
                teamId != null ? String.format(whereFormat, curLevel, preChannelId, teamId) : String.format(whereFormat, curLevel, preChannelId),
                groupByFormat);
        } else {
            return fvStructureMapper.fvChannels(String.format(columnsFormat, nextLevel, nextLevel, nextLevel),
                teamId != null ? String.format(whereFormat, curLevel, preChannelId, teamId) : String.format(whereFormat, curLevel, preChannelId),
                String.format(groupByFormat, nextLevel, nextLevel));
        }
    }

    @Override
    public List<FvChannelPair> getFvByLevelAndTeamId(Integer level, Integer teamId) {
        String businessId = "level" + level + "_id";
        String columnName = "level" + level + "_name";
        List<FvChannelPair> channelsByLevel = fvStructureMapper.getFvChannelsByTeamId(businessId, columnName, teamId);
        for (FvChannelPair fvChannelPair : channelsByLevel) {
            fvChannelPair.setLevel(level);
        }
        return channelsByLevel;
    }

}
