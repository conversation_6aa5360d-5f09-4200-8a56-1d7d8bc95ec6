package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.PaymentType;
import com.iqiyi.vip.domain.repository.PaymentTypeRepository;
import com.iqiyi.vip.mapper.PaymentTypeMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@Service
public class PaymentTypeRepositoryImpl implements PaymentTypeRepository {

    @Resource
    private PaymentTypeMapper paymentTypeMapper;

    @Override
    public List<PaymentType> selectByChannel(Long payChannel) {
        List<com.iqiyi.vip.po.PaymentType> types = paymentTypeMapper.selectByChannel(payChannel);
        if (CollectionUtils.isEmpty(types)) {
            return null;
        }
        return types.stream().map(this::adaptor).collect(Collectors.toList());
    }

    @Override
    public List<PaymentType> selectAll() {
        List<com.iqiyi.vip.po.PaymentType> types = paymentTypeMapper.selectAll();
        if (CollectionUtils.isEmpty(types)) {
            return null;
        }
        return types.stream().map(this::adaptor).collect(Collectors.toList());
    }

    private PaymentType adaptor(com.iqiyi.vip.po.PaymentType paymentType) {
        PaymentType type = new PaymentType();
        type.setId(paymentType.getId());
        type.setDescription(paymentType.getDescription());
        type.setName(paymentType.getName());
        type.setPayChannel(paymentType.getPayChannel());
        type.setStatus(paymentType.getStatus());
        type.setSubPayChannel(paymentType.getSubPayChannel());
        return type;
    }
}
