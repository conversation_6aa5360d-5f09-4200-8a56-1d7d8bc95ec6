package com.iqiyi.vip.repository;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.domain.entity.AvailableDt;
import com.iqiyi.vip.domain.repository.AvailableDateRepository;
import com.iqiyi.vip.mapper.AvailableDtMapper;

/**
 * <AUTHOR>
 * @className AvailableDateRepositoryImpl
 * @description
 * @date 2023/5/6
 **/
@Component
public class AvailableDateRepositoryImpl implements AvailableDateRepository {

    @Resource
    private AvailableDtMapper availableDtMapper;

    @Override
    public int insert(AvailableDt record) {
        if (record == null) {
            return 0;
        }
        return availableDtMapper.insertSelective(record);
    }

    @Override
    public AvailableDt maxDate(Integer themeType) {
        return availableDtMapper.selectMaxDate(themeType);
    }

    @Override
    public AvailableDt maxDateByThemeTypeAndThemeSubType(Integer themeType, Integer themeSubType) {
        return availableDtMapper.selectMaxDateByThemeTypeAndThemeSubType(themeType, themeSubType);
    }

    @Override
    public List<AvailableDt> dateRangeByThemeTypeAndThemeSubType(Integer themeType, Integer themeSubType) {
        return availableDtMapper.selectDateRangeByThemeTypeAndThemeSubType(themeType, themeSubType);
    }

    @Override
    public AvailableDt getDateByDt(String dt, Integer themeType) {
        return availableDtMapper.selectByTableDt(dt, themeType);
    }

    @Override
    public AvailableDt getDateByDtAndThemeTypeAndThemeSubType(String dt, Integer themeType, Integer themeSubType) {
        return availableDtMapper.selectByTableDtAndThemeTypeAndThemeSubType(dt, themeType, themeSubType);
    }

    @Override
    public int updateStatusByDt(String dt, Integer themeType, Integer status) {
        return availableDtMapper.updateStatusByDt(dt, themeType, status);
    }
}
