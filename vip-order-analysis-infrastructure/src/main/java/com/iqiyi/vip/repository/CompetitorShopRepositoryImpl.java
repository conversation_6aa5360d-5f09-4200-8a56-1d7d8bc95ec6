package com.iqiyi.vip.repository;

import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.repository.CompetitorShopRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSearchParam;
import com.iqiyi.vip.mapper.CompetitorShopMapper;
import com.iqiyi.vip.po.competitor.CompetitorShopPO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: guojing
 * @date: 2024/12/25 10:35
 */
@Repository
@Slf4j
public class CompetitorShopRepositoryImpl implements CompetitorShopRepository {

    private static final Logger log = LoggerFactory.getLogger(CompetitorShopRepositoryImpl.class);

    @Resource
    private CompetitorShopMapper competitorShopMapper;

    @Override
    public List<String> platformList() {
        return competitorShopMapper.selectAllPlatform();
    }

    @Override
    public List<String> brandsList(String platform, String date) {
        return competitorShopMapper.selectAllBrand(platform, date);
    }

    @Override
    public CompetitorDateRangeDTO dateRange() {
        return competitorShopMapper.selectDateRange();
    }

    @Override
    public void save(CompetitorShopSaveDTO param) {
        CompetitorShopPO po = CompetitorShopPO.buildFrom(param);
        po.setCreateTime(new Date());
        po.setUpdateTime(new Date());
        competitorShopMapper.insert(po);
    }

    @Override
    public List<CompetitorShopDTO> search(CompetitorShopSearchParam param) {
        List<CompetitorShopPO> poList = competitorShopMapper.search(param.getDate() != null ? param.getDate().toString() : null, 
                param.getPlatform(), param.getBrand(), param.getStoreType());
        return poList.stream().map(CompetitorShopPO::toDTO).collect(Collectors.toList());
    }

    @Override
    public List<CompetitorShopDTO> search(CompetitorShopSearchParam param, String date) {
        List<CompetitorShopPO> poList = competitorShopMapper.search(date, 
                param.getPlatform(), param.getBrand(), param.getStoreType());
        return poList.stream().map(CompetitorShopPO::toDTO).collect(Collectors.toList());
    }
} 