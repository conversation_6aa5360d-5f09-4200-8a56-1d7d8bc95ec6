package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.domain.repository.ConditionCascadeRepository;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.ConditionCascadePageQryDTO;
import com.iqiyi.vip.mapper.ConditionCascadeMapper;
import com.iqiyi.vip.po.query.ConditionCascadePageQuery;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: linpeihui
 * @createTime: 2023/08/08
 */
@Service
@CacheConfig(cacheNames = "caffeineCacheManager")
public class ConditionCascadeRepositoryImpl implements ConditionCascadeRepository {
    @Resource
    private ConditionCascadeMapper conditionCascadeMapper;

    @Override
    public Long save(ConditionCascade cascade) {
        return conditionCascadeMapper.insertSelective(cascade);
    }

    @Override
    public void update(ConditionCascade cascade) {
        conditionCascadeMapper.updateSelective(cascade);
    }

    @Override
    public void deleteByThemeAndBusiness(Integer themeId, Integer businessId) {
        conditionCascadeMapper.deleteByThemeAndBusiness(themeId, businessId);
    }

    @Override
    public void deleteByConditionId(Long conditionId) {
        conditionCascadeMapper.deleteByConditionId(conditionId);
    }

    @Override
    public ConditionCascade selectByConditions(Integer themeId, Integer businessId, Integer currentConditionId, Integer nextConditionId) {
        return conditionCascadeMapper.selectByConditions(themeId, businessId, currentConditionId, nextConditionId);
    }

    @Override
    public List<ConditionCascade> list(Integer themeId, Integer businessId) {
        return conditionCascadeMapper.list(themeId, businessId);
    }

    @Override
    public PageListResult<ConditionCascade> selectPageList(ConditionCascadePageQryDTO pageQryDTO) {
        ConditionCascadePageQuery pageQuery = this.convert(pageQryDTO);
        Integer count = conditionCascadeMapper.selectCount(pageQuery);
        List<ConditionCascade> dataList = null;
        if (count > 0) {
            dataList = conditionCascadeMapper.selectList(pageQuery);
        }
        return PageListResult.createSuccess(dataList, pageQryDTO.getPageNo(), pageQryDTO.getPageSize(), count);
    }

    private ConditionCascadePageQuery convert(ConditionCascadePageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        ConditionCascadePageQuery pageQuery = new ConditionCascadePageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);
        pageQuery.setThemeId(pageQryDTO.getThemeId());
        pageQuery.setBusinessId(pageQryDTO.getBusinessId());
        pageQuery.setConditionName(pageQryDTO.getConditionName());
        if (pageQryDTO.getStartCreateTime() != null) {
            pageQuery.setStartCreateTime(new Timestamp(pageQryDTO.getStartCreateTime()));
        }
        if (pageQryDTO.getEndCreateTime() != null) {
            pageQuery.setEndCreateTime(new Timestamp(pageQryDTO.getEndCreateTime()));
        }
        if (pageQryDTO.getStartUpdateTime() != null) {
            pageQuery.setStartUpdateTime(new Timestamp(pageQryDTO.getStartUpdateTime()));
        }
        if (pageQryDTO.getEndUpdateTime() != null) {
            pageQuery.setEndUpdateTime(new Timestamp(pageQryDTO.getEndUpdateTime()));
        }
        return pageQuery;
    }

}
