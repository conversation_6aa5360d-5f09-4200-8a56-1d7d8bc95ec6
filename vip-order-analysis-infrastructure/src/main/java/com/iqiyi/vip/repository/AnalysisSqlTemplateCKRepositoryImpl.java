package com.iqiyi.vip.repository;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.entity.AnalysisSqlTemplate;
import com.iqiyi.vip.domain.repository.AnalysisSqlTemplateRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.sqltemplate.AnalysisSqlTemplatePageQryDTO;
import com.iqiyi.vip.mapper.AnalysisSqlTemplateCKMapper;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Service
public class AnalysisSqlTemplateCKRepositoryImpl implements AnalysisSqlTemplateRepository {

    @Resource
    private AnalysisSqlTemplateCKMapper analysisSqlTemplateCKMapper;


    @Override
    public Long save(AnalysisSqlTemplate template) throws Exception {
        return null;
    }

    @Override
    public void update(AnalysisSqlTemplate template) throws Exception {

    }

    @Override
    public void delete(Long id) {

    }

    @Override
    public AnalysisSqlTemplate selectById(Long id) {
        return this.adapter(analysisSqlTemplateCKMapper.selectById(id));
    }

    @Override
    public PageListResult<AnalysisSqlTemplate> selectPageList(AnalysisSqlTemplatePageQryDTO pageQryDTO) {
        return null;
    }

    @Override
    public List<AnalysisSqlTemplate> selectAll(BaseQry baseQry) {
        return null;
    }


    private AnalysisSqlTemplate adapter(com.iqiyi.vip.po.AnalysisSqlTemplate target) {
        if (target == null) {
            return null;
        }
        AnalysisSqlTemplate newTarget = new AnalysisSqlTemplate();
        newTarget.setId(target.getId());
        newTarget.setName(target.getName());
        newTarget.setValue(target.getValue());
        newTarget.setDescription(target.getDescription());
        newTarget.setCreateOpr(target.getCreateOpr());
        newTarget.setCreateTime(target.getCreateTime());
        newTarget.setUpdateOpr(target.getUpdateOpr());
        newTarget.setUpdateTime(target.getUpdateTime());
        newTarget.setStatus(target.getStatus());
        newTarget.setVersion(target.getVersion());
        newTarget.setCommitNote(target.getCommitNote());
        newTarget.setBusinessTypeId(target.getBusinessTypeId());
        return newTarget;
    }

}
