package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.repository.AutorenewSetLogRepository;
import com.iqiyi.vip.dto.pie.AutoRenewCancelSceneDTO;
import com.iqiyi.vip.mapper.AutoRenewSetLogMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/28 下午 04:07
 */
@Component
@Slf4j
public class AutorenewSetLogRepositoryImpl implements AutorenewSetLogRepository {

    @Resource
    AutoRenewSetLogMapper autoRenewSetLogMapper;

    @Override
    public int getCancelNumByRange(String dt, String operateTime, String deadlineStartTime, String deadlineEndTime) {
        try {
            return autoRenewSetLogMapper.getCancelNumByRange(dt, operateTime, deadlineStartTime, deadlineEndTime);
        } catch (SQLException e) {
            log.error("getCancelNumByRange error:", e);
            return 0;
        }

    }

    @Override
    public List<AutoRenewCancelSceneDTO> getCancelSceneData(String dt, String beginTime, String endTime) {
        try {
            return autoRenewSetLogMapper.getCancelSceneData(dt, beginTime, endTime);
        } catch (SQLException e) {
            log.error("getCancelSceneData error:", e);
            return Collections.emptyList();
        }
    }
}
