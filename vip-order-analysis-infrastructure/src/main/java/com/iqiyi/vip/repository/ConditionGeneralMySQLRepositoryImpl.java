package com.iqiyi.vip.repository;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.repository.ConditionGeneralMySQLRepository;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.condition.ConditionPair;
import com.iqiyi.vip.mapper.ConditionGeneralMySQLMapper;

/**
 * @author: linpeihui
 * @createTime: 2023/08/08
 */
@Service
public class ConditionGeneralMySQLRepositoryImpl implements ConditionGeneralMySQLRepository {
    @Resource
    private ConditionGeneralMySQLMapper conditionGeneralMySQLMapper;

    @Override
    @CacheRefresh(refresh = 180, stopRefreshAfterLastAccess = 1800)
    @Cached(cacheNullValue = true, cacheType = CacheType.LOCAL)
    public List<ConditionPair> query(String queryString) {
        return conditionGeneralMySQLMapper.select(queryString);
    }
}
