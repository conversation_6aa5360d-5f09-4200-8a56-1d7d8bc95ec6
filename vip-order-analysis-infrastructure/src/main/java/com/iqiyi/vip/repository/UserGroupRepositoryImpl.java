package com.iqiyi.vip.repository;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.domain.repository.AnalysisSubTaskRepository;
import com.iqiyi.vip.domain.repository.AnalysisTaskRepository;
import com.iqiyi.vip.domain.repository.UserGroupRepository;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.dto.user.group.UpdateUserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupDTO;
import com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO;
import com.iqiyi.vip.dto.user.group.UserGroupQueryDTO;
import com.iqiyi.vip.enums.BusinessTypeEnum;
import com.iqiyi.vip.enums.CreateMethodEnum;
import com.iqiyi.vip.enums.CreateProcessEnum;
import com.iqiyi.vip.enums.GroupTypeEnum;
import com.iqiyi.vip.enums.TaskStatusEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.mapper.OrderPilotComponent;
import com.iqiyi.vip.mapper.UserGroupMapper;
import com.iqiyi.vip.po.UserGroup;
import com.iqiyi.vip.repository.convert.UserGroupConvertMapper;
import com.iqiyi.vip.util.DataStorageUtil;
import com.iqiyi.vip.util.FileUtil;
import com.iqiyi.vip.utils.CloudConfigUtils;

import static com.iqiyi.vip.enums.CodeEnum.USER_GROUP_DATA_UPDATE_FAIL;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserGroupRepositoryImpl implements UserGroupRepository {

    @Resource
    private UserGroupMapper userGroupMapper;

    @Resource
    private OrderPilotComponent orderPilotComponent;

    @Resource
    private AnalysisSubTaskRepository analysisSubTaskRepository;

    @Resource
    private DataStorageUtil dataStorageUtil;

    @Resource
    private AnalysisTaskRepository analysisTaskRepository;

    @Resource
    private FileUtil fileUtil;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    @Value("${oss.client.endpoint}")
    private String ossEndpoint;

    @Value("${oss.client.bucket}")
    private String bucketName;

    @Value("${oss.client.path}")
    private String path;
    private static final Map<Integer, Integer> themeToCreateTypeMap = new HashMap<>();

    static {
        themeToCreateTypeMap.put(ThemeTypeEnum.AUTO_RENEW_THEME.getCode(), CreateMethodEnum.AUTO_RENEW_ANALYSIS.getCode());
        themeToCreateTypeMap.put(ThemeTypeEnum.STORE.getCode(), CreateMethodEnum.STORE.getCode());
        themeToCreateTypeMap.put(ThemeTypeEnum.MEMBER_RETENTION.getCode(), CreateMethodEnum.MEMBER_RETENTION.getCode());
    }

    @Override
    public PageListResult<UserGroupDO> selectPageList(UserGroupPageQueryDTO reqDto) {
        this.convert(reqDto);
        Integer count = userGroupMapper.selectCount(reqDto);
        List<UserGroupDO> userGroupDOS = null;
        if (count > 0) {
            List<com.iqiyi.vip.po.UserGroup> usersGroup = userGroupMapper.selectList(reqDto);
            if (CollectionUtils.isEmpty(usersGroup)) {
                return null;
            }
            userGroupDOS = usersGroup.stream().map(UserGroupConvertMapper.INSTANCE::toUserGroupDO).collect(Collectors.toList());
            userGroupDOS.forEach(userGroupDO -> {
                AnalysisSubTaskDO subTask = analysisSubTaskRepository.getSubTaskByTaskIdOrUserGroupId(null, userGroupDO.getId());
                userGroupDO.setUploadToBeiDouStatus(subTask == null ? -1 : subTask.getStatus());
                userGroupDO.setCreateTypeDesc(CreateMethodEnum.getDescByCode(userGroupDO.getCreateType()));
            });
        }
        return PageListResult.createSuccess(userGroupDOS, reqDto.getPageNo(), reqDto.getPageSize(), count);
    }

    @Override
    public List<UserGroupDO> query(UserGroupQueryDTO userGroupQueryDTO) {
        UserGroupPageQueryDTO reqDto = new UserGroupPageQueryDTO();
        reqDto.setOperator(userGroupQueryDTO.getOperator());
        reqDto.setGroupType(userGroupQueryDTO.getGroupType());
        List<com.iqiyi.vip.po.UserGroup> usersGroup = userGroupMapper.selectList(reqDto);
        if (CollectionUtils.isEmpty(usersGroup)) {
            return Collections.emptyList();
        }
        List<UserGroupDO> result = usersGroup.stream().map(UserGroupConvertMapper.INSTANCE::toUserGroupDO).collect(Collectors.toList());
        //遍历result 设置createType对应的createTypeDesc
        result.forEach(userGroupDO -> userGroupDO.setCreateTypeDesc(CreateMethodEnum.getDescByCode(userGroupDO.getCreateType())));
        return result;
    }

    @Override
    public int deleteUserGroup(Long userGroupId, String operator) {
        return userGroupMapper.deleteByPrimaryKey(userGroupId, operator);
    }


    public void addRunningSubTask(UserGroup userGroup) {
        AnalysisSubTaskDO subTask = new AnalysisSubTaskDO();
        subTask.setUserGroupId(userGroup.getId());
        subTask.setStatus(TaskStatusEnum.RUNNING.getStatus());
        subTask.setUserPackageName(userGroup.getName());

        analysisSubTaskRepository.deleteByUserGroupId(userGroup.getId());
        analysisSubTaskRepository.addAnalysisSubTask(subTask);
        userGroup.setSubTaskId(subTask.getId());
        userGroupMapper.updateByPrimaryKeySelective(userGroup);
    }

    @Override
    public void addUserGroup(UserGroupDTO userGroupDTO) {
        UserGroup userGroup = UserGroupConvertMapper.INSTANCE.toUserGroup(userGroupDTO);
        userGroup.setCreateUser(userGroupDTO.getOperator());
        userGroup.setCreateType(CreateMethodEnum.FILE_UPLOAD.getCode());
        userGroup.setIsBeidouSynced(0);
        userGroup.setUploadProgress(CreateProcessEnum.UPLOADING.getCode());
        int count = userGroupMapper.insertSelective(userGroup);
        try {
            if (count > 0) {
                Long dataCount = orderPilotComponent.generateUserGroupAndGetResult(userGroup);
                userGroup.setDataCount(dataCount);
                userGroup.setUploadProgress(CreateProcessEnum.UPLOAD_SUCCESS.getCode());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = new Date(); // Your Date object
                if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroup.getGroupType())) {
                    if (StringUtils.isNotBlank(userGroupDTO.getName())) {
                        userGroup.setName(String.format("【天眼人群包-%s】", userGroup.getId()).concat(userGroup.getName()).concat(" ")
                            .concat(userGroup.getOperator()));
                    } else {
                        String formattedDate = sdf.format(date);
                        userGroup.setName(String.format("【天眼人群包-%s】", userGroup.getId()).concat(formattedDate).concat(" ")
                            .concat(userGroup.getOperator()));
                    }
                } else {
                    if (StringUtils.isNotBlank(userGroup.getName())) {
                        userGroup.setName(String.format("【天眼订单包-%s】", userGroup.getId()).concat(userGroup.getName()).concat(" ")
                            .concat(userGroup.getOperator()));
                    } else {
                        String formattedDate = sdf.format(date);
                        userGroup.setName(String.format("【天眼订单包-%s】", userGroup.getId()).concat(formattedDate).concat(" ")
                            .concat(userGroup.getOperator()));
                    }
                }
                userGroupMapper.updateByPrimaryKeySelective(userGroup);
                if (CloudConfigUtils.userGroupLimit(dataCount) && Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroup.getGroupType())) {
                    uploadUserGroupToOSS(userGroup);
                }
            } else {
                uploadFailed(userGroup);
            }
        } catch (Exception e) {
            uploadFailed(userGroup);
        }
    }

    @Override
    public void updateUserGroup(UpdateUserGroupDTO updateUserGroupDTO) {
        UserGroup userGroup = new UserGroup();
        userGroup.setId(updateUserGroupDTO.getId());
        userGroup.setName(updateUserGroupDTO.getName());
        userGroupMapper.updateByPrimaryKeySelective(userGroup);
    }

    private void toCreateType(AnalysisTaskDO analysisTaskDO, UserGroup userGroup) {
        Integer themeType = analysisTaskDO.getThemeType();
        Integer businessTypeId = analysisTaskDO.getBusinessTypeId();

        // 对主题类型设置创建类型
        if (themeToCreateTypeMap.containsKey(themeType)) {
            userGroup.setCreateType(themeToCreateTypeMap.get(themeType));
            return;
        }
        // 对业务类型进行设置，这里简化处理，因为主题类型已经完成主要的映射
        // 但如果有需要，也可以为业务类型构建单独的映射关系
        if (BusinessTypeEnum.BASIC_ANALYSIS.getCode().equals(businessTypeId)) {
            userGroup.setCreateType(CreateMethodEnum.BASE_ANALYSIS.getCode());
        } else if (BusinessTypeEnum.TRANSFER_ANALYSIS.getCode().equals(businessTypeId)) {
            userGroup.setCreateType(CreateMethodEnum.TRANSFER_ANALYSIS.getCode());
        } else if (BusinessTypeEnum.DIAGNOSTIC_ANALYSIS.getCode().equals(businessTypeId)) {
            userGroup.setCreateType(CreateMethodEnum.DIAGNOSTIC_ANALYSIS.getCode());
        }
    }

    @Override
    public void generateUserGroup(TargetAnalysisQueryDTO queryDTO, AnalysisTarget analysisTarget) {
        UserGroup userGroup = createUserGroup(queryDTO,analysisTarget);
        int count = userGroupMapper.insertSelective(userGroup);
        try {
            if (count > 0) {
                Long dataCount = orderPilotComponent.generateUserGroupAndGetResult(queryDTO, userGroup);
                userGroup.setDataCount(dataCount);
                userGroup.setUploadProgress(CreateProcessEnum.UPLOAD_SUCCESS.getCode());
                userGroupMapper.updateByPrimaryKeySelective(userGroup);
                if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroup.getGroupType())) {
                    uploadUserGroupToOSS(userGroup);
                }
            } else {
                uploadFailed(userGroup);
            }
        } catch (Exception e) {
            uploadFailed(userGroup);
        }
    }

    @Override
    public UserGroupDO queryById(Integer userGroupId) {
        UserGroup userGroup = userGroupMapper.selectByPrimaryKey(userGroupId);
        if (Objects.nonNull(userGroup)) {
            return UserGroupConvertMapper.INSTANCE.toUserGroupDO(userGroup);
        } else {
            return null;
        }
    }

    private UserGroup createUserGroup(TargetAnalysisQueryDTO queryDTO, AnalysisTarget analysisTarget) {
        AnalysisTaskDO analysisTaskDO = analysisTaskRepository.getTaskByIdAndOperator(queryDTO.getTaskId(), queryDTO.getOperator());
        UserGroup userGroup = new UserGroup();
        userGroup.setCreateUser(queryDTO.getOperator());
        userGroup.setOperator(queryDTO.getOperator());

        // 设置用户组类型
        setUserGroupType(queryDTO, userGroup);

        // 设置用户组名称
        String userGroupName = generateGroupName(analysisTaskDO, analysisTarget, queryDTO);
        userGroup.setName(userGroupName);

        // 设置其他属性
        toCreateType(analysisTaskDO, userGroup);
        userGroup.setIsBeidouSynced(0);
        userGroup.setUploadProgress(CreateProcessEnum.UPLOADING.getCode());

        return userGroup;
    }

    private void setUserGroupType(TargetAnalysisQueryDTO queryDTO, UserGroup userGroup) {
        if (CloudConfigUtils.isUserGroup(queryDTO.getTargetCode()) || !queryDTO.getTargetCode().contains("orderCode")) {
            userGroup.setGroupType(GroupTypeEnum.USER_PACKAGE.getCode());
        } else {
            userGroup.setGroupType(GroupTypeEnum.ORDER_PACKAGE.getCode());
        }
    }

    private String generateGroupName(AnalysisTaskDO analysisTaskDO, AnalysisTarget analysisTarget, TargetAnalysisQueryDTO queryDTO) {
        String baseName = String.format("【天眼%s-%s】", analysisTarget.getName().replaceAll("\\(.*", ""), queryDTO.getTaskId());
        String operator = queryDTO.getOperator();
        String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        if (StringUtils.isNotBlank(analysisTaskDO.getTaskName())) {
            return baseName.concat(analysisTaskDO.getTaskName())
                .concat(" ")
                .concat(operator);
        } else {
            return baseName.concat(formattedDate)
                .concat(" ")
                .concat(operator);
        }
    }


    private void uploadUserGroupToOSS(UserGroup userGroup) {
        // 创建后台线程，执行筛选唯一uid的sql模板，并将结果上传至oss
        new Thread(() -> {
            try {
                log.info("executeQueryAsync start, user group id:{}", userGroup.getId());
                // analysis_sub_task表中插入一个子任务
                addRunningSubTask(userGroup);
                List<String> queryResult = orderPilotComponent.getUserGroupResult(userGroup.getId());
                subTaskResultProcess(queryResult, userGroup.getId());
            } catch (Exception e) {
                log.error("executeQueryAsync error, user group id:{}", userGroup.getId(), e);
            }
        }).start();
    }

    //todo 可再拆分和优化，职责要清晰
    private void subTaskResultProcess(List<String> queryResult, Integer userGroupId) {
        String fileName = userGroupId + "_passport_id.txt";
        String localFile = localFilePath + "/" + fileName;
        fileUtil.createFile(queryResult, localFile);
        // 将txt文件上传至oss，并删除本地文件
        log.info("passport_id文件开始上传至oss");
        CommonResult res = dataStorageUtil.uploadResultFileWithMetaData(new File(localFile), fileName);
        fileUtil.cleanLocalFile(localFile);
        // 修改子任务的数据
        AnalysisSubTaskDO subTask = new AnalysisSubTaskDO();
        subTask.setUserGroupId(userGroupId);
        if ("Q00310".equals(res.getCode())) {
            subTask.setStatus(TaskStatusEnum.FAILED.getStatus());
        } else {
            subTask.setStatus(TaskStatusEnum.FINISHED.getStatus());
        }
        String fileUrl = ossEndpoint + "/" + bucketName + "/" + path + fileName;
        subTask.setFileUrl(fileUrl);
        analysisSubTaskRepository.updateByTaskId(subTask);
        log.info("用户人群包上传OSS成功, fileUrl: {}", fileUrl);
    }


    private void uploadFailed(UserGroup userGroup) {
        userGroup.setDataCount(0L);
        userGroup.setUploadProgress(CreateProcessEnum.UPLOAD_FAILED.getCode());
        userGroupMapper.updateByPrimaryKeySelective(userGroup);
        throw new BizRuntimeException(USER_GROUP_DATA_UPDATE_FAIL.getCode(), USER_GROUP_DATA_UPDATE_FAIL.getMessage());
    }


    private void convert(UserGroupPageQueryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();
        pageQryDTO.setStart((currentPage - 1) * pageSize);
        pageQryDTO.setLimit(pageSize);
    }
}
