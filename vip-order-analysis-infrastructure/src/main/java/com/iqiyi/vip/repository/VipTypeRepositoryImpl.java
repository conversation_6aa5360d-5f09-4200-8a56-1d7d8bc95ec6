package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.repository.VipTypeRepository;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.condition.VipTypePair;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.mapper.VipTypeMapper;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
@Service
@Slf4j
public class VipTypeRepositoryImpl implements VipTypeRepository {

    @Resource
    private VipTypeMapper vipTypeMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "vipType_selectAll", cacheType = CacheType.LOCAL)
    @Override
    public List<CodeDescPair> selectAll() {
        return vipTypeMapper.selectAll();
    }

    @Override
    public List<VipTypePair> getVipTypePairs(String preId, LabelEnum labelEnum) {
        String columnsFormt = "%s_code as id, %s_name as name, %d as level";
        String whereColumnsFormat = "vip_type_group_code = '%s'";
        if (labelEnum == LabelEnum.VT1) {
            whereColumnsFormat = "%s";
        }
        return vipTypeMapper.getVipTypePairs(String.format(columnsFormt, labelEnum.getColumn(), labelEnum.getColumn(), labelEnum.getLevel()),
                String.format(whereColumnsFormat, labelEnum == LabelEnum.VT1 ? "1 = 1" : preId),
                labelEnum.getColumn() + "_code, " + labelEnum.getColumn() + "_order", labelEnum.getColumn() + "_order");
    }
}
