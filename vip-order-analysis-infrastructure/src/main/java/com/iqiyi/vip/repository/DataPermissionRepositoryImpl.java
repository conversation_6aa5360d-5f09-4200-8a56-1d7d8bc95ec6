package com.iqiyi.vip.repository;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.DataPermission;
import com.iqiyi.vip.domain.entity.DataPermissionOa;
import com.iqiyi.vip.domain.repository.DataPermissionRepository;
import com.iqiyi.vip.dto.oa.OaApprovedResponse;
import com.iqiyi.vip.dto.permission.DataPermissionApplyDTO;
import com.iqiyi.vip.dto.permission.DataPermissionApprovalCallbackDTO;
import com.iqiyi.vip.enums.DataPermissionOaStatusEnum;
import com.iqiyi.vip.enums.DataPermissionTypeEnum;
import com.iqiyi.vip.enums.ThemeTypeEnum;
import com.iqiyi.vip.mapper.DataPermissionMapper;
import com.iqiyi.vip.mapper.DataPermissionOaMapper;

/**
 * <AUTHOR>
 * @date 2022/8/17 17:59
 */
@Component
@Slf4j
public class DataPermissionRepositoryImpl implements DataPermissionRepository {

    @Resource
    private DataPermissionOaMapper dataPermissionOaMapper;
    @Resource
    private DataPermissionMapper dataPermissionMapper;

    @Override
    public int saveDataPermissionOa(DataPermissionApplyDTO dataPermissionApplyDTO, OaApprovedResponse oaApprovedResponse) {
        List<DataPermissionOa> dataPermissionOas = Lists.newArrayList(
                dataPermissionApplyDTO.getDataPermissionComposeDTO().getFvData() == null ||
                    CollectionUtils.isEmpty(dataPermissionApplyDTO.getDataPermissionComposeDTO().getFvData().getDataPermissionDTOList()) ? null :
                    // 会员渠道、产品端平台
                    DataPermissionOa.builder()
                        .account(dataPermissionApplyDTO.getOperator())
                        .permissionReason(dataPermissionApplyDTO.getApplyReason())
                        .permissionStatus(DataPermissionOaStatusEnum.APPLYED.getCode())
                        .operators(dataPermissionApplyDTO.getOperator() + ";")
                        .oaIdentifier(oaApprovedResponse.getAcId())
                        .messageId(oaApprovedResponse.getMessageId())
                        .permission(JSON.toJSONString(dataPermissionApplyDTO.getDataPermissionComposeDTO()))
                        .type(ThemeTypeEnum.ORDER_THEME.getCode())
                        .themeType(dataPermissionApplyDTO.getThemeType())
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build(),
                dataPermissionApplyDTO.getDataPermissionComposeDTO().getVipTypeData() == null ||
                    CollectionUtils.isEmpty(dataPermissionApplyDTO.getDataPermissionComposeDTO().getVipTypeData().getDataPermissionDTOList()) ? null :
                    // 会员类型
                    DataPermissionOa.builder()
                        .account(dataPermissionApplyDTO.getOperator())
                        .permissionReason(dataPermissionApplyDTO.getApplyReason())
                        .permissionStatus(DataPermissionOaStatusEnum.APPLYED.getCode())
                        .operators(dataPermissionApplyDTO.getOperator() + ";")
                        .oaIdentifier(oaApprovedResponse.getAcId())
                        .messageId(oaApprovedResponse.getMessageId())
                        .permission(JSON.toJSONString(dataPermissionApplyDTO.getDataPermissionComposeDTO()))
                        .type(ThemeTypeEnum.AUTO_RENEW_THEME.getCode())
                        .themeType(dataPermissionApplyDTO.getThemeType())
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build(),
                dataPermissionApplyDTO.getDataPermissionComposeDTO().getCompetitorMonitorData() == null ? null : // 竞品监控
                    DataPermissionOa.builder()
                        .account(dataPermissionApplyDTO.getOperator())
                        .permissionReason(dataPermissionApplyDTO.getApplyReason())
                        .permissionStatus(DataPermissionOaStatusEnum.APPLYED.getCode())
                        .operators(dataPermissionApplyDTO.getOperator() + ";")
                        .oaIdentifier(oaApprovedResponse.getAcId())
                        .messageId(oaApprovedResponse.getMessageId())
                        .permission(JSON.toJSONString(dataPermissionApplyDTO.getDataPermissionComposeDTO()))
                        .type(DataPermissionTypeEnum.COMPETITOR_DATA_PERMISSION.getCode())
                        .themeType(dataPermissionApplyDTO.getThemeType())
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build()
            ).stream()
            .filter(v -> v != null)
            .collect(Collectors.toList());
        return dataPermissionOaMapper.insertDataPermissionOas(dataPermissionOas);
    }

    @Override
    public int updateDataPermissionOaApprovalStatus(DataPermissionApprovalCallbackDTO dataPermissionApprovalCallbackDTO, DataPermissionOaStatusEnum dataPermissionOaStatus) {
        return dataPermissionOaMapper.updateDataPermissionOaStatusByAcc(dataPermissionApprovalCallbackDTO.getAcId(), dataPermissionApprovalCallbackDTO.getDesc(), dataPermissionOaStatus.getCode());
    }

    @Override
    public int existApprovingDataPermission(String account) {
        return dataPermissionOaMapper.existApprovingDataPermission(account);
    }

    @Override
    public int saveDataPermission(DataPermission dataPermission) {
        return dataPermissionMapper.insertDataPermission(dataPermission);
    }

    @Override
    public int updateDataPermission(DataPermission dataPermission) {
        return dataPermissionMapper.updateDataPermission(dataPermission);
    }

    @Override
    public DataPermission getDataPermission(String account, Integer themeType, Integer dataPermissionType) {
        return dataPermissionMapper.getDataPermission(account, themeType, dataPermissionType);
    }

    @Override
    public List<DataPermission> getDataPermissions(String account) {
        return dataPermissionMapper.getDataPermissions(account);
    }

    @Override
    public List<DataPermission> getDataPermissionsByThemeType(String account, Integer themeType) {
        return dataPermissionMapper.getDataPermissionsByThemeType(account, themeType);
    }

    @Override
    public int ownedDataPermission(String account) {
        return dataPermissionMapper.ownedDataPermission(account);
    }

    @Override
    public List<DataPermissionOa> getDataPermissionOa(String oaIdentifier, String messageId) {
        return dataPermissionOaMapper.getDataPermissionOaByOaIdentifierAndMessageId(oaIdentifier, messageId);
    }

    @Override
    public int updateDataPermissionOaAcIdByMessageId(String oaIdentifier, String messageId) {
        return dataPermissionOaMapper.updateDataPermissionOaAcIdByMessageId(oaIdentifier, messageId);
    }
}
