package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.domain.repository.AnalysisTargetRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.target.AnalysisTargetPageQryDTO;
import com.iqiyi.vip.mapper.AnalysisBusinessTypeMapper;
import com.iqiyi.vip.mapper.AnalysisTargetGroupMapper;
import com.iqiyi.vip.mapper.AnalysisTargetMapper;
import com.iqiyi.vip.mapper.AnalysisThemeTypeMapper;
import com.iqiyi.vip.po.AnalysisBusinessType;
import com.iqiyi.vip.po.AnalysisTargetGroup;
import com.iqiyi.vip.po.query.AnalysisTargetPageQuery;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Service
@CacheConfig(cacheNames = "caffeineCacheManager")
public class AnalysisTargetRepositoryImpl implements AnalysisTargetRepository {

    @Resource
    private AnalysisTargetMapper analysisTargetMapper;

    @Resource
    private AnalysisTargetGroupMapper analysisTargetGroupMapper;

    @Resource
    private AnalysisBusinessTypeMapper analysisBusinessTypeMapper;

    @Resource
    private AnalysisThemeTypeMapper analysisThemeTypeMapper;

    @Override
    public Long save(AnalysisTarget target) {
        com.iqiyi.vip.po.AnalysisTarget analysisTarget = this.adapter(target);
        analysisTargetMapper.insertSelective(analysisTarget);
        return analysisTarget.getId();
    }

    @Override
    @CacheEvict(value = "AnalysisTarget", allEntries = true)
    public void update(AnalysisTarget target) {
        analysisTargetMapper.updateSelective(this.adapter(target));
    }

    @Override
    public void delete(String code) {
        analysisTargetMapper.delete(code);
    }

    @Override
    public void deleteById(Long id) {
        analysisTargetMapper.deleteById(id);
    }

    @Override
    @Cacheable(value = "AnalysisTarget", key = "#code")
    public AnalysisTarget selectByCode(String code) {
        return this.adapter(analysisTargetMapper.selectByCode(code), true);
    }

    @Override
    public AnalysisTarget selectByCodeAndBusinessTypeId(String code, Integer businessTypeId) {
        return this.adapter(analysisTargetMapper.selectByCodeAndBusinessTypeId(code, businessTypeId), true);
    }

    @Override
    @Cacheable(value = "AnalysisTarget", key = "#id")
    public AnalysisTarget selectById(Long id) {
        return this.adapter(analysisTargetMapper.selectById(id), true);
    }

    @Override
    public PageListResult<AnalysisTarget> selectPageList(AnalysisTargetPageQryDTO pageQryDTO) {
        AnalysisTargetPageQuery pageQuery = this.convert(pageQryDTO);
        Integer count = analysisTargetMapper.selectCount(pageQuery);
        List<AnalysisTarget> dataList = null;
        if (count > 0) {
            List<com.iqiyi.vip.po.AnalysisTarget> list = analysisTargetMapper.selectList(pageQuery);
            if (!CollectionUtils.isEmpty(list)) {
                dataList = list.stream().map(l -> this.adapter(l, true)).collect(Collectors.toList());
            }
        }
        return PageListResult.createSuccess(dataList, pageQryDTO.getPageNo(), pageQryDTO.getPageSize(), count);
    }

    @Override
    public List<AnalysisTarget> selectByCodes(List<String> codes) {
        List<com.iqiyi.vip.po.AnalysisTarget> list = analysisTargetMapper.selectByCodes(codes);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(s -> this.adapter(s, false)).collect(Collectors.toList());
    }

    @Override
    public List<AnalysisTarget> selectAll(BaseQry baseQry) {
        List<com.iqiyi.vip.po.AnalysisTarget> list = analysisTargetMapper.selectAll(baseQry.getBusinessTypeId(), baseQry.getThemeType());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(s -> this.adapter(s, true)).collect(Collectors.toList());
    }

    private com.iqiyi.vip.po.AnalysisTarget adapter(AnalysisTarget target) {
        if (target == null) {
            return null;
        }
        com.iqiyi.vip.po.AnalysisTarget dbTarget = new com.iqiyi.vip.po.AnalysisTarget();
        dbTarget.setId(target.getId());
        dbTarget.setCode(target.getCode());
        dbTarget.setName(target.getName());
        dbTarget.setValue(target.getValue());
        dbTarget.setSqlTemplateId(target.getSqlTemplateId());
        dbTarget.setGroup(target.getGroup());
        dbTarget.setOrder(target.getOrder());
        dbTarget.setDocumentation(target.getDocumentation());
        dbTarget.setCreateOpr(target.getCreateOpr());
        dbTarget.setCreateTime(target.getCreateTime());
        dbTarget.setUpdateOpr(target.getUpdateOpr());
        dbTarget.setUpdateTime(target.getUpdateTime());
        dbTarget.setStatus(target.getStatus());
        dbTarget.setCommitNote(target.getCommitNote());
        dbTarget.setVersion(target.getVersion());
        dbTarget.setSource(target.getSource());
        return dbTarget;
    }

    private AnalysisTarget adapter(com.iqiyi.vip.po.AnalysisTarget target, boolean isNeedGroupName) {
        if (target == null) {
            return null;
        }
        AnalysisTarget newTarget = new AnalysisTarget();
        newTarget.setId(target.getId());
        newTarget.setCode(target.getCode());
        newTarget.setName(target.getName());
        newTarget.setValue(target.getValue());
        newTarget.setSqlTemplateId(target.getSqlTemplateId());
        newTarget.setGroup(target.getGroup());
        newTarget.setOrder(target.getOrder());
        newTarget.setDocumentation(target.getDocumentation());
        newTarget.setDescs(target.getDescs());
        newTarget.setPartitions(target.getPartitions());

        if (isNeedGroupName) {
            AnalysisTargetGroup group = analysisTargetGroupMapper.selectById(target.getGroup());
            newTarget.setGroupName(group != null ? group.getName() : null);
            if (group != null) {
                newTarget.setAdvancedOption(group.getAdvancedOption());
                newTarget.setBusinessTypeId(group.getBusinessTypeId());
                newTarget.setBusinessTypeName(group.getBusinessTypeId() != null ?
                        Optional.ofNullable(analysisBusinessTypeMapper.get(group.getBusinessTypeId()))
                                .orElseGet(() -> new AnalysisBusinessType())
                                .getName() :
                        null);
                newTarget.setThemeType(group.getThemeType());
                newTarget.setThemeTypeName(group.getThemeType() != null ?
                        Optional.ofNullable(analysisThemeTypeMapper.selectById(group.getThemeType()))
                                .orElseGet(() -> new AnalysisThemeType())
                                .getName() :
                        null);
            }
        }

        newTarget.setCreateOpr(target.getCreateOpr());
        newTarget.setCreateTime(target.getCreateTime());
        newTarget.setUpdateOpr(target.getUpdateOpr());
        newTarget.setUpdateTime(target.getUpdateTime());
        newTarget.setStatus(target.getStatus());
        newTarget.setVersion(target.getVersion());
        newTarget.setCommitNote(target.getCommitNote());
        newTarget.setSource(target.getSource());
        return newTarget;
    }

    private AnalysisTargetPageQuery convert(AnalysisTargetPageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        AnalysisTargetPageQuery pageQuery = new AnalysisTargetPageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);
        pageQuery.setBusinessTypeId(pageQryDTO.getBusinessTypeId());
        pageQuery.setThemeType(pageQryDTO.getThemeType());
        pageQuery.setName(pageQryDTO.getName());
        pageQuery.setSqlTemplateId(pageQryDTO.getSqlTemplateId());
        return pageQuery;
    }
}
