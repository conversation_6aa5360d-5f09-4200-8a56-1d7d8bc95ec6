package com.iqiyi.vip.repository.convert;


import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.iqiyi.vip.domain.entity.UserGroupDO;
import com.iqiyi.vip.dto.user.group.UserGroupDTO;
import com.iqiyi.vip.po.UserGroup;

@Mapper
public interface UserGroupConvertMapper {

    UserGroupConvertMapper INSTANCE = Mappers.getMapper(UserGroupConvertMapper.class);

    UserGroupDO toUserGroupDO(UserGroup userGroup);

    UserGroup toUserGroup(UserGroupDTO userGroupDTO);
}
