package com.iqiyi.vip.repository;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.repository.CompetitorShopPriceRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSearchParam;
import com.iqiyi.vip.mapper.CompetitorShopPriceMapper;
import com.iqiyi.vip.po.competitor.CompetitorShopPricePO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 竞品店铺价格仓库实现
 */
@Repository
public class CompetitorShopPriceRepositoryImpl implements CompetitorShopPriceRepository {

    private static final Logger log = LoggerFactory.getLogger(CompetitorShopPriceRepositoryImpl.class);

    @Resource
    private CompetitorShopPriceMapper competitorShopPriceMapper;

    @Override
    public List<String> platformList() {
        return competitorShopPriceMapper.selectAllPlatform();
    }

    @Override
    public List<String> brandsList(String platform, String date) {
        return competitorShopPriceMapper.selectAllBrand(platform, date);
    }

    @Override
    public List<String> userGroupList(String platform, String brand, String date) {
        return competitorShopPriceMapper.selectAllUserGroup(platform, brand, date);
    }

    @Override
    public CompetitorDateRangeDTO dateRange() {
        return competitorShopPriceMapper.selectDateRange();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CompetitorShopPriceSaveDTO param) {
        List<CompetitorShopPricePO> priceList = CompetitorShopPricePO.buildFromDTO(param);
        if (priceList != null && !priceList.isEmpty()) {
            // 设置创建和更新时间
            Date now = new Date();
            priceList.forEach(po -> {
                po.setCreateTime(now);
                po.setUpdateTime(now);
            });
            competitorShopPriceMapper.batchInsert(priceList);
        }
    }

    @Override
    public List<CompetitorShopPriceDTO> search(CompetitorShopPriceSearchParam param) {
        // 查询数据
        List<CompetitorShopPricePO> priceList = competitorShopPriceMapper.search(
            param.getDate() != null ? param.getDate().toString() : null, 
            param.getPlatform(), 
            param.getBrand(), 
            param.getStoreType(), 
            param.getUserGroup()
        );
        
        if (priceList == null || priceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 直接转换为DTO列表
        return priceList.stream()
            .map(CompetitorShopPricePO::toDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<CompetitorShopPriceDTO> search(CompetitorShopPriceSearchParam param, String date) {
        // 查询数据
        List<CompetitorShopPricePO> priceList = competitorShopPriceMapper.search(
            date, 
            param.getPlatform(), 
            param.getBrand(), 
            param.getStoreType(), 
            param.getUserGroup()
        );
        
        if (priceList == null || priceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 直接转换为DTO列表
        return priceList.stream()
            .map(CompetitorShopPricePO::toDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<CompetitorShopPriceDTO> searchByDateRange(String startDate, String endDate, Integer storeType, String platform, String brand) {
        // 查询数据
        List<CompetitorShopPricePO> priceList = competitorShopPriceMapper.searchByDateRange(
            startDate, 
            endDate, 
            storeType, 
            platform, 
            brand
        );
        
        if (priceList == null || priceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 直接转换为DTO列表
        return priceList.stream()
            .map(CompetitorShopPricePO::toDTO)
            .collect(Collectors.toList());
    }
} 