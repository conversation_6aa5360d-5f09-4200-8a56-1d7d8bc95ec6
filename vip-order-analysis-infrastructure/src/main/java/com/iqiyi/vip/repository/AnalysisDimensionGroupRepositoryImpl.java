package com.iqiyi.vip.repository;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisDimensionGroup;
import com.iqiyi.vip.domain.repository.AnalysisDimensionGroupRepository;
import com.iqiyi.vip.mapper.AnalysisDimensionGroupMapper;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Service
@Slf4j
public class AnalysisDimensionGroupRepositoryImpl implements AnalysisDimensionGroupRepository {

    @Resource
    private AnalysisDimensionGroupMapper analysisDimensionGroupMapper;

    @Override
    public List<AnalysisDimensionGroup> selectAll() {
        return this.toTransforList(analysisDimensionGroupMapper.selectAll());
    }

    @Override
    public List<AnalysisDimensionGroup> selectByBusinessType(Integer businessTypeId, Integer themeType) {
        return this.toTransforList(analysisDimensionGroupMapper.selectByBusinessType(businessTypeId, themeType));
    }

    private List<AnalysisDimensionGroup> toTransforList(List<com.iqiyi.vip.po.AnalysisDimensionGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return null;
        }
        return groups.stream().map(this::adaptor).collect(Collectors.toList());
    }

    private AnalysisDimensionGroup adaptor(com.iqiyi.vip.po.AnalysisDimensionGroup group) {
        AnalysisDimensionGroup newGroup = new AnalysisDimensionGroup();
        newGroup.setId(group.getId());
        newGroup.setName(group.getName());
        newGroup.setSupportMultiple(group.getSupportMultiple() != 0);
        newGroup.setOrder(group.getOrder());
        newGroup.setCreateTime(group.getCreateTime());
        newGroup.setOperator(group.getOperator());
        newGroup.setStatus(group.getStatus());
        newGroup.setUpdateTime(group.getUpdateTime());
        newGroup.setBusinessTypeId(group.getBusinessTypeId());
        newGroup.setThemeType(group.getThemeType());
        return newGroup;
    }
}
