package com.iqiyi.vip.repository;

import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;
import com.iqiyi.vip.domain.repository.TaskQueryInfoRepository;
import com.iqiyi.vip.mapper.ScheduledTaskQueryInfoMapper;
import com.iqiyi.vip.mapper.TaskQueryInfoMapper;

/**
 * <AUTHOR>
 * @className QueryInfoRepositoryImpl
 * @description
 * @date 2023/1/31
 **/
@Component
public class TaskQueryInfoRepositoryImpl implements TaskQueryInfoRepository {

    @Resource
    private TaskQueryInfoMapper taskQueryInfoMapper;
    @Resource
    private ScheduledTaskQueryInfoMapper scheduledTaskQueryInfoMapper;

    @Override
    public void updateByTaskId(TaskQueryInfo queryInfo) {
        taskQueryInfoMapper.updateByTaskIdSelective(queryInfo);
    }

    @Override
    public void add(TaskQueryInfo queryInfo) {
        taskQueryInfoMapper.insertSelective(queryInfo);
    }

    @Override
    public TaskQueryInfo searchByTaskId(long taskId) {
        return taskQueryInfoMapper.selectByTaskId(taskId);
    }

    @Override
    public List<TaskQueryInfo> searchByTaskIds(List<Long> taskIds) {
        return taskQueryInfoMapper.selectByTaskIds(taskIds);
    }


    @Override
    public void updateByScheduledTaskId(TaskQueryInfo queryInfo) {
        scheduledTaskQueryInfoMapper.updateByTaskIdSelective(queryInfo);
    }

    @Override
    public void addScheduled(TaskQueryInfo queryInfo) {
        scheduledTaskQueryInfoMapper.insertSelective(queryInfo);
    }

    @Override
    public TaskQueryInfo searchByScheduledTaskId(long taskId) {
        return scheduledTaskQueryInfoMapper.selectByTaskId(taskId);
    }
}
