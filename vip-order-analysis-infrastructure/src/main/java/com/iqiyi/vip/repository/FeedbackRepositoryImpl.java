package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.model.QueryFeedback;
import com.iqiyi.vip.domain.repository.FeedbackRepository;
import com.iqiyi.vip.mapper.QueryFeedbackMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class FeedbackRepositoryImpl implements FeedbackRepository {
    @Resource
    private QueryFeedbackMapper queryFeedbackMapper;

    @Override
    public void save(QueryFeedback feedback) {
        queryFeedbackMapper.insert(feedback);
    }

    @Override
    public QueryFeedback findByTaskId(Long taskId) {
        return taskId == null ? null : queryFeedbackMapper.selectByTaskId(taskId);
    }
} 