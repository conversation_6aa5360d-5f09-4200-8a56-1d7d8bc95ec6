package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.domain.repository.AnalysisTargetGroupRepository;
import com.iqiyi.vip.mapper.AnalysisTargetGroupMapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Service
@Slf4j
public class AnalysisTargetGroupRepositoryImpl implements AnalysisTargetGroupRepository {

    @Resource
    private AnalysisTargetGroupMapper analysisTargetGroupMapper;

    @Override
    public List<AnalysisTargetGroup> selectAll() {
         return this.toTransforList(analysisTargetGroupMapper.selectAll());
    }

    @Override
    public List<AnalysisTargetGroup> selectByBusinessType(Integer businessTypeId, Integer themeType) {
        return this.toTransforList(analysisTargetGroupMapper.selectByBusinessTypeAndType(businessTypeId, null, themeType));
    }

    @Override
    public List<AnalysisTargetGroup> selectByBusinessType(Integer businessTypeId, Integer type, Integer themeType) {
        return this.toTransforList(analysisTargetGroupMapper.selectByBusinessTypeAndType(businessTypeId, type, themeType));
    }

    private List<AnalysisTargetGroup> toTransforList(List<com.iqiyi.vip.po.AnalysisTargetGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return null;
        }
        return groups.stream().map(s -> this.adaptor(s)).collect(Collectors.toList());
    }

    @Override
    public AnalysisTargetGroup get(int id) {
        final com.iqiyi.vip.po.AnalysisTargetGroup analysisTargetGroup = analysisTargetGroupMapper.selectById(id);
        return analysisTargetGroup == null ? null : this.adaptor(analysisTargetGroup);
    }

    private AnalysisTargetGroup adaptor(com.iqiyi.vip.po.AnalysisTargetGroup group) {
        AnalysisTargetGroup newGroup = new AnalysisTargetGroup();
        newGroup.setId(group.getId());
        newGroup.setName(group.getName());
        newGroup.setOrder(group.getOrder());
        newGroup.setParent(group.getParent());
        newGroup.setType(group.getType());
        newGroup.setCreateTime(group.getCreateTime());
        newGroup.setOperator(group.getOperator());
        newGroup.setStatus(group.getStatus());
        newGroup.setUpdateTime(group.getUpdateTime());
        newGroup.setBusinessTypeId(group.getBusinessTypeId());
        newGroup.setThemeType(group.getThemeType());
        newGroup.setAdvancedOption(group.getAdvancedOption());
        return newGroup;
    }

}
