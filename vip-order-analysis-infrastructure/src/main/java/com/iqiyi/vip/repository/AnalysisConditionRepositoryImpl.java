package com.iqiyi.vip.repository;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.repository.AnalysisConditionRepository;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.condition.AnalysisConditionPageQryDTO;
import com.iqiyi.vip.mapper.AnalysisConditionMapper;
import com.iqiyi.vip.po.query.AnalysisConditionPageQuery;

/**
 * @author: linpeihui
 * @createTime: 2023/08/08
 */
@Service
@CacheConfig(cacheNames = "caffeineCacheManager")
public class AnalysisConditionRepositoryImpl implements AnalysisConditionRepository {
    @Resource
    private AnalysisConditionMapper analysisConditionMapper;

    @Override
    public Long save(AnalysisCondition condition) {
        analysisConditionMapper.insertSelective(condition);
        return condition.getId();
    }

    @Override
    @CacheEvict(value = "AnalysisCondition", allEntries = true)
    public void update(AnalysisCondition condition) {
        analysisConditionMapper.updateSelective(condition);
    }

    @Override
    public void delete(String code, String operator) {
        analysisConditionMapper.delete(code, operator);
    }

    @Override
    public AnalysisCondition selectById(Long id) {
        return analysisConditionMapper.selectById(id);
    }

    @Override
    public List<AnalysisCondition> selectByIdList(List<Long> idList) {
        return analysisConditionMapper.selectByIdList(idList);
    }

    //TODO cache
    @Override
    public AnalysisCondition selectByCode(String code) {
        return analysisConditionMapper.selectByCode(code);
    }

    @Override
    public List<AnalysisCondition> batchSelectByCode(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return analysisConditionMapper.batchSelectByCode(codes);
    }

    @Override
    public AnalysisCondition selectByName(String name) {
        return analysisConditionMapper.selectByName(name);
    }

    @Override
    public PageListResult<AnalysisCondition> selectPageList(AnalysisConditionPageQryDTO pageQryDTO) {
        AnalysisConditionPageQuery pageQuery = this.convert(pageQryDTO);
        Integer count = analysisConditionMapper.selectCount(pageQuery);
        List<AnalysisCondition> dataList = null;
        if (count > 0) {
            dataList = analysisConditionMapper.selectList(pageQuery);
        }
        return PageListResult.createSuccess(dataList, pageQryDTO.getPageNo(), pageQryDTO.getPageSize(), count);
    }

    @Override
    public List<AnalysisCondition> selectAll() {
        return analysisConditionMapper.selectAll();
    }

    private AnalysisConditionPageQuery convert(AnalysisConditionPageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        AnalysisConditionPageQuery pageQuery = new AnalysisConditionPageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);
        pageQuery.setBusinessTypeId(pageQryDTO.getBusinessTypeId());
        pageQuery.setName(pageQryDTO.getName());
        if (pageQryDTO.getStartCreateTime() != null) {
            pageQuery.setStartCreateTime(new Timestamp(pageQryDTO.getStartCreateTime()));
        }
        if (pageQryDTO.getEndCreateTime() != null) {
            pageQuery.setEndCreateTime(new Timestamp(pageQryDTO.getEndCreateTime()));
        }
        if (pageQryDTO.getStartUpdateTime() != null) {
            pageQuery.setStartUpdateTime(new Timestamp(pageQryDTO.getStartUpdateTime()));
        }
        if (pageQryDTO.getEndUpdateTime() != null) {
            pageQuery.setEndUpdateTime(new Timestamp(pageQryDTO.getEndUpdateTime()));
        }
        return pageQuery;
    }
}
