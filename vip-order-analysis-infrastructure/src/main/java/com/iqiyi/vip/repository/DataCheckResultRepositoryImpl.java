package com.iqiyi.vip.repository;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataCheckResult;
import com.iqiyi.vip.domain.repository.DataCheckResultRepository;
import com.iqiyi.vip.mapper.DataCheckResultDao;

/**
 * <AUTHOR>
 * @className DataCheckResultRepositoryImpl
 * @description
 * @date 2022/10/31
 **/
@Component
public class DataCheckResultRepositoryImpl implements DataCheckResultRepository {

    @Resource
    private DataCheckResultDao dataCheckResultDao;

    @Override
    public List<DataCheckResult> getDataCheckResult(DataCheckResult dataCheckResultDO) {
        return dataCheckResultDao.getDataCheckResult(dataCheckResultDO);
    }

    @Override
    public void saveDataCheckResult(DataCheckResult dataCheckResultDO) {
        dataCheckResultDao.insert(dataCheckResultDO);
    }


}
