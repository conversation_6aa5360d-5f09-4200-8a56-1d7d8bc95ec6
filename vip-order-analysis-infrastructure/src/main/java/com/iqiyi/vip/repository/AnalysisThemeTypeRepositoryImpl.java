package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.domain.repository.AnalysisThemeTypeRepository;
import com.iqiyi.vip.mapper.AnalysisThemeTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 6/22/22
 * @apiNote
 */
@Service
@Slf4j
public class AnalysisThemeTypeRepositoryImpl implements AnalysisThemeTypeRepository {

    @Resource
    private AnalysisThemeTypeMapper analysisThemeTypeMapper;

    @Override
    public List<AnalysisThemeType> selectAll() {
        return analysisThemeTypeMapper.selectAll();
    }

    @Override
    public AnalysisThemeType selectById(Integer id) {
        return analysisThemeTypeMapper.selectById(id);
    }
}
