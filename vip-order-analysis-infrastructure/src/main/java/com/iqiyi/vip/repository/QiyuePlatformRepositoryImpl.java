package com.iqiyi.vip.repository;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.QiyuePlatform;
import com.iqiyi.vip.domain.repository.QiyuePlatformRepository;
import com.iqiyi.vip.dto.condition.QiyuePlatformPair;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.mapper.QiyuePlatformMapper;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
@Service
@Slf4j
public class QiyuePlatformRepositoryImpl implements QiyuePlatformRepository {

    @Resource
    private QiyuePlatformMapper qiyuePlatformMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "QiyuePlatform_selectAll", cacheType = CacheType.LOCAL)
    @Override
    public List<QiyuePlatform> selectAll() {
        List<com.iqiyi.vip.po.QiyuePlatform> list = qiyuePlatformMapper.selectAll();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::adaptor).collect(Collectors.toList());
    }

    @Override
    public List<QiyuePlatform> selectByCodes(List<String> platformCodes) {
        List<com.iqiyi.vip.po.QiyuePlatform> list = qiyuePlatformMapper.selectByCodes(platformCodes);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(this::adaptor).collect(Collectors.toList());
    }

    private QiyuePlatform adaptor(com.iqiyi.vip.po.QiyuePlatform platform) {
        QiyuePlatform platform1 = new QiyuePlatform();
        platform1.setId(platform.getId());
        platform1.setCode(platform.getCode());
        platform1.setDescription(platform.getDescription());
        platform1.setName(platform.getName());
        return platform1;
    }

    @Override
    public List<QiyuePlatformPair> getQiyuePlatforms(String preId, LabelEnum labelEnum) {
        String columnsFormt = "%s as id, %s as name, %d as level";
        String whereColumnsFormat = "biz_name = '%s'";
        if (labelEnum == LabelEnum.P1) {
            whereColumnsFormat = "%s";
        }
        String groupByColumnsFormat = "%s";
        return qiyuePlatformMapper.getQiyuePlatforms(String.format(columnsFormt, labelEnum.getColumn(), labelEnum.getColumn(), labelEnum.getLevel()),
            String.format(whereColumnsFormat, labelEnum == LabelEnum.P1 ? "1 = 1" : preId),
            String.format(groupByColumnsFormat, labelEnum.getColumn()));
    }
}
