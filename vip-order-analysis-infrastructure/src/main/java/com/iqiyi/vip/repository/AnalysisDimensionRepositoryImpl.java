package com.iqiyi.vip.repository;

import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.entity.AnalysisDimension;
import com.iqiyi.vip.domain.entity.AnalysisThemeType;
import com.iqiyi.vip.domain.repository.AnalysisDimensionRepository;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionPageQryDTO;
import com.iqiyi.vip.mapper.AnalysisBusinessTypeMapper;
import com.iqiyi.vip.mapper.AnalysisDimensionGroupMapper;
import com.iqiyi.vip.mapper.AnalysisDimensionMapper;
import com.iqiyi.vip.mapper.AnalysisThemeTypeMapper;
import com.iqiyi.vip.po.AnalysisBusinessType;
import com.iqiyi.vip.po.AnalysisDimensionGroup;
import com.iqiyi.vip.po.query.AnalysisDimensionPageQuery;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@Service
@CacheConfig(cacheNames = "caffeineCacheManager")
public class AnalysisDimensionRepositoryImpl implements AnalysisDimensionRepository {

    @Resource
    private AnalysisDimensionMapper analysisDimensionMapper;

    @Resource
    private AnalysisDimensionGroupMapper analysisDimensionGroupMapper;

    @Resource
    private AnalysisBusinessTypeMapper analysisBusinessTypeMapper;

    @Resource
    private AnalysisThemeTypeMapper analysisThemeTypeMapper;

    @Override
    public Long save(AnalysisDimension dimension) {
        com.iqiyi.vip.po.AnalysisDimension analysisDimension = this.adapter(dimension);
        analysisDimensionMapper.insertSelective(analysisDimension);
        return analysisDimension.getId();
    }

    @Override
    @CacheEvict(value = "AnalysisDimension", allEntries = true)
    public void update(AnalysisDimension dimension) {
        analysisDimensionMapper.updateSelective(this.adapter(dimension));
    }

    @Override
    public void delete(String code) {
        analysisDimensionMapper.delete(code);
    }

    @Override
    public void deleteById(Long id) {
        analysisDimensionMapper.deleteById(id);
    }


    @Override
    @Cacheable(value = "AnalysisDimension")
    public AnalysisDimension selectByThemeType(String code, Integer businessTypeId, Integer themeType) {
        return this.adapter(analysisDimensionMapper.selectByThemeType(code, businessTypeId, themeType));
    }

    @Override
    public AnalysisDimension selectTransferDimension(String code, Integer businessTypeId, Integer themeType) {
        return this.adapter(analysisDimensionMapper.selectTransferDimension(code, businessTypeId, themeType));
    }


    @Override
    @Cacheable(value = "AnalysisDimension", key = "#id")
    public AnalysisDimension selectById(Long id) {
        return this.adapter(analysisDimensionMapper.selectById(id));
    }

    @Override
    public PageListResult<AnalysisDimension> selectPageList(AnalysisDimensionPageQryDTO pageQryDTO) {
        AnalysisDimensionPageQuery pageQuery = this.convert(pageQryDTO);
        Integer count = analysisDimensionMapper.selectCount(pageQuery);
        List<AnalysisDimension> dataList = null;
        if (count > 0) {
            List<com.iqiyi.vip.po.AnalysisDimension> list = analysisDimensionMapper.selectList(pageQuery);
            if (!CollectionUtils.isEmpty(list)) {
                dataList = list.stream().map(l -> this.adapter(l)).collect(Collectors.toList());
            }
        }
        return PageListResult.createSuccess(dataList, pageQryDTO.getPageNo(), pageQryDTO.getPageSize(), count);
    }

    @Override
    public List<AnalysisDimension> selectByCodes(List<String> codes, Integer businessTypeId, Integer themeType) {
        List<com.iqiyi.vip.po.AnalysisDimension> list = analysisDimensionMapper.selectByCodes(codes, businessTypeId, themeType);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(this::adapter).collect(Collectors.toList());
    }

    @Override
    public List<AnalysisDimension> selectAll(BaseQry baseQry) {
        List<com.iqiyi.vip.po.AnalysisDimension> list = analysisDimensionMapper.selectAll(baseQry.getBusinessTypeId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(s -> this.adapter(s)).collect(Collectors.toList());
    }

    private com.iqiyi.vip.po.AnalysisDimension adapter(AnalysisDimension dimension) {
        if (dimension == null) {
            return null;
        }
        com.iqiyi.vip.po.AnalysisDimension dbDimension = new com.iqiyi.vip.po.AnalysisDimension();
        dbDimension.setId(dimension.getId());
        dbDimension.setCode(dimension.getCode());
        dbDimension.setName(dimension.getName());
        dbDimension.setValue(dimension.getValue());
        dbDimension.setGroup(dimension.getGroup());
        dbDimension.setOrder(dimension.getOrder());
        dbDimension.setDocumentation(dimension.getDocumentation());
        dbDimension.setCreateOpr(dimension.getCreateOpr());
        dbDimension.setCreateTime(dimension.getCreateTime());
        dbDimension.setUpdateOpr(dimension.getUpdateOpr());
        dbDimension.setUpdateTime(dimension.getUpdateTime());
        dbDimension.setStatus(dimension.getStatus());
        dbDimension.setCommitNote(dimension.getCommitNote());
        dbDimension.setVersion(dimension.getVersion());
        return dbDimension;
    }

    private AnalysisDimension adapter(com.iqiyi.vip.po.AnalysisDimension dimension) {
        if (dimension == null) {
            return null;
        }
        AnalysisDimension newDimension = new AnalysisDimension();
        newDimension.setId(dimension.getId());
        newDimension.setCode(dimension.getCode());
        newDimension.setName(dimension.getName());
        newDimension.setValue(dimension.getValue());
        newDimension.setGroup(dimension.getGroup());
        newDimension.setOrder(dimension.getOrder());

        AnalysisDimensionGroup group = analysisDimensionGroupMapper.selectById(Long.valueOf(dimension.getGroup()));
        newDimension.setGroupName(group != null ? group.getName() : null);
        newDimension.setCreateOpr(dimension.getCreateOpr());
        newDimension.setCreateTime(dimension.getCreateTime());
        newDimension.setUpdateOpr(dimension.getUpdateOpr());
        newDimension.setUpdateTime(dimension.getUpdateTime());
        newDimension.setStatus(dimension.getStatus());
        newDimension.setVersion(dimension.getVersion());
        newDimension.setCommitNote(dimension.getCommitNote());
        newDimension.setDocumentation(dimension.getDocumentation());
        if (group != null) {
            newDimension.setBusinessTypeId(group.getBusinessTypeId());
            AnalysisBusinessType businessType = analysisBusinessTypeMapper.get(group.getBusinessTypeId());
            if (businessType != null) {
                newDimension.setBusinessTypeName(businessType.getName());
            }
            newDimension.setThemeType(group.getThemeType());
            AnalysisThemeType themeType = analysisThemeTypeMapper.selectById(group.getThemeType());
            if (themeType != null) {
                newDimension.setThemeTypeName(themeType.getName());
            }
        }
        return newDimension;
    }

    private AnalysisDimensionPageQuery convert(AnalysisDimensionPageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        AnalysisDimensionPageQuery pageQuery = new AnalysisDimensionPageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);
        pageQuery.setBusinessTypeId(pageQryDTO.getBusinessTypeId());
        pageQuery.setThemeType(pageQryDTO.getThemeType());
        pageQuery.setName(pageQryDTO.getName());
        return pageQuery;
    }
}
