package com.iqiyi.vip.repository;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.repository.CompetitorPriceRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSearchParam;
import com.iqiyi.vip.mapper.CompetitorPriceMapper;
import com.iqiyi.vip.po.competitor.CompetitorPricePO;
import com.iqiyi.vip.util.DateUtil;
import java.util.Comparator;

/**
 * @author: guojing
 * @date: 2024/10/30 15:12
 */
@Slf4j
@Component
public class CompetitorPriceRepositoryImpl implements CompetitorPriceRepository {

    @Resource
    private CompetitorPriceMapper competitorPriceMapper;
    
    // 提取数字的正则表达式
    private static final Pattern NUMBER_PATTERN = Pattern.compile("(\\d+)");

    @ConfigJsonValue("${price.board.brand.blacklist:{}}")
    private Map<String, List<String>> productPriceBlackList;

    @ConfigJsonValue("${price.board.public.blacklist:{}}")
    private List<String> commonProductPriceBlackList;

//    @ConfigJsonValue("${brand.userstatus.order:{}}")
//    private Map<String, List<String>> brandUserStatusOrder;

    @ConfigJsonValue("${brand.product.order:{}}")
    private Map<String, List<String>> brandProductOrder;

    private static final List<String> vipOrder = Lists.newArrayList("VIP", "会员", "SVIP", "全屏会员");

//    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
//    @Cached(name = "competitorPriceRepo_brandsList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> brandsList() {
        return competitorPriceMapper.selectAllBrand();
    }

//    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
//    @Cached(name = "competitorPriceRepo_userStatusList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> userStatusList(String brand, String date) {
        List<String> list = competitorPriceMapper.selectAllUserStatus(brand, date);
        List<String> order = brandProductOrder.get(brand);
        if (CollectionUtils.isEmpty(order)) {
            return list;
        }
        // 按照order中的顺序排序
        return list.stream()
            .sorted(Comparator.comparingInt(order::indexOf))
            .filter(p -> !"SVIP（自动续费）".equals(p))
            .collect(Collectors.toList());
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorPriceRepo_dateRange", cacheType = CacheType.LOCAL)
    @Override
    public CompetitorDateRangeDTO dateRange() {
        return competitorPriceMapper.selectDateRange();
    }

    @Override
    public List<CompetitorPriceDTO> search(CompetitorPriceSearchParam param) {

        // 将时间戳转换为日期字符串
        String startTime = null;
        String endTime = null;
        if (param.getStartTime() != null) {
            startTime = DateUtil.format(new Date(param.getStartTime()), "yyyy-MM-dd");
        }
        if (param.getEndTime() != null) {
            endTime = DateUtil.format(new Date(param.getEndTime()), "yyyy-MM-dd");
        }
        String brand = param.getBrand();
        List<String> order = brandProductOrder.get(brand);

        // 先比较vipType顺序
        // 如果vipType顺序相同，再比较product顺序
        List<CompetitorPricePO> competitorPricePOS = competitorPriceMapper.search(brand, param.getUserStatus(), startTime, endTime, param.getDate())
            .stream()
            .filter(a -> !errorData(a))
            .map(this::normalizeProperty)
            .filter(Objects::nonNull)
            // 先按vipType在vipOrder中的顺序排序，再按product在order中的顺序排序
            .sorted(Comparator.comparingInt((CompetitorPricePO a) -> {
                String vipType = a.getVipType() != null ? a.getVipType().toUpperCase() : null;
                int index = vipOrder.indexOf(vipType);
                if (index == -1) {
                    log.info("not find vip order:{}", a);
                }
                return index != -1 ? index : Integer.MAX_VALUE; // 找不到的放在最后
            }).thenComparingInt(a -> {
                Integer index = order != null ? order.indexOf(a.getProduct()) : -1;
                if (index == -1) {
                    log.info("not find product order:{}", a);
                }
                return index != -1 ? index : Integer.MAX_VALUE; // 找不到的放在最后
            }))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(competitorPricePOS)) {
            return Collections.emptyList();
        }

        return competitorPricePOS.stream()
            .map(CompetitorPricePO::toDTO)
            .collect(Collectors.toList());
    }

    private CompetitorPricePO normalizeProperty(CompetitorPricePO pricePO) {
        String brand = pricePO.getBrand();
        String vipType = pricePO.getVipType();
        if ("芒果".equals(brand) && ("vip".equalsIgnoreCase(vipType) || "svip".equalsIgnoreCase(vipType))) {
//            pricePO.setVipType(vipType.replaceAll("(?i)vip", "会员").replaceAll("(?i)svip", "宽屏会员"));
            return null;
        }
        if ("vip".equalsIgnoreCase(vipType) || "svip".equalsIgnoreCase(vipType)) {
            pricePO.setVipType(vipType.toUpperCase());
        }
        return pricePO;
    }

    private boolean errorData(CompetitorPricePO a) {
        String brand = a.getBrand();
        List<String> list = productPriceBlackList.get(brand);
        String userStatus = a.getUserStatus();
        if ("SVIP（自动续费）".equals(userStatus)) {
            return true;
        }
        String product = a.getProduct();
        if ("腾讯".equals(brand)) {
            return list.stream().anyMatch(product::contains) || commonProductPriceBlackList.stream().anyMatch(product::contains);
        }

        if ("优酷".equals(brand)) {
            return (product.contains("升级") || product.contains("优酷 ×") || product.contains("其他 ×") ||
                list.stream().anyMatch(product::contains)) || commonProductPriceBlackList.stream().anyMatch(product::contains);
        }
        if ("芒果".equals(brand)) {
            return list.stream().anyMatch(product::contains) || commonProductPriceBlackList.stream().anyMatch(product::contains);
        }
        return false;
    }


    @Override
    public void save(List<CompetitorPriceDTO> competitorPriceDTOS) {
        if (CollectionUtils.isEmpty(competitorPriceDTOS)) {
            return;
        }
        List<CompetitorPricePO> competitorPricePOS = competitorPriceDTOS.stream()
            .map(CompetitorPricePO::buildFrom)
            .collect(Collectors.toList());
        competitorPriceMapper.batchInsert(competitorPricePOS);
    }
}
