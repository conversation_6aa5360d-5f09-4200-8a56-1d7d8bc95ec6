package com.iqiyi.vip.repository;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.entity.BizTargetMonitorConfigDO;
import com.iqiyi.vip.domain.repository.BizTargetMonitorConfigRepository;
import com.iqiyi.vip.dto.base.PageListResult;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigCondition;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigPageQryDTO;
import com.iqiyi.vip.dto.realmonitor.BizTargetMonitorConfigSaveDTO;
import com.iqiyi.vip.enums.OffsetTimeUnit;
import com.iqiyi.vip.mapper.BizTargetMonitorConfigMapper;
import com.iqiyi.vip.po.BizTargetMonitorConfigPO;
import com.iqiyi.vip.po.query.BizTargetMonitorConfigPageQuery;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/3/1 11:34
 */
@Slf4j
@Component
public class BizTargetMonitorConfigRepositoryImpl implements BizTargetMonitorConfigRepository {

    @Resource
    private BizTargetMonitorConfigMapper bizTargetMonitorConfigMapper;

    @Override
    public int create(BizTargetMonitorConfigSaveDTO createParam) {
        BizTargetMonitorConfigPO monitorPO = saveDtoToPO(createParam);
        bizTargetMonitorConfigMapper.insert(monitorPO);
        return monitorPO.getId();
    }

    @Override
    public boolean update(BizTargetMonitorConfigSaveDTO updateParam) {
        BizTargetMonitorConfigPO monitorPO = saveDtoToPO(updateParam);
        bizTargetMonitorConfigMapper.update(monitorPO);
        return true;
    }

    @Override
    public boolean updateJobIdById(Integer id, Integer jobId) {
        bizTargetMonitorConfigMapper.updateJobIdById(id, jobId);
        return true;
    }

    @Override
    public boolean updateMonitorIdById(Integer id, Integer monitorId) {
        bizTargetMonitorConfigMapper.updateMonitorIdById(id, monitorId);
        return true;
    }

    @Override
    public boolean delete(Integer id) {
        return bizTargetMonitorConfigMapper.resetStatus(id, 0) > 0;
    }

    @Override
    public BizTargetMonitorConfigDO getDetailById(Integer id) {
        BizTargetMonitorConfigPO po = bizTargetMonitorConfigMapper.selectByPrimaryKey(id);
        return poToDO(po);
    }

    @Override
    public boolean existByName(String name) {
        return bizTargetMonitorConfigMapper.existByName(name) > 0;
    }

    @Override
    public PageListResult<BizTargetMonitorConfigDO> search(BizTargetMonitorConfigPageQryDTO param) {
        BizTargetMonitorConfigPageQuery pageQuery = convert(param);
        int count = bizTargetMonitorConfigMapper.searchCount(pageQuery);
        List<BizTargetMonitorConfigDO> dataList = Collections.emptyList();
        if (count > 0) {
            List<BizTargetMonitorConfigPO> searchResult = bizTargetMonitorConfigMapper.search(pageQuery);
            if (CollectionUtils.isNotEmpty(searchResult)) {
                dataList = searchResult.stream().map(BizTargetMonitorConfigRepositoryImpl::poToDO).collect(Collectors.toList());
            }
        }
        return PageListResult.createSuccess(dataList, param.getPageNo(), param.getPageSize(), count);
    }

    public static BizTargetMonitorConfigDO poToDO(BizTargetMonitorConfigPO po) {
        if (po == null) {
            return null;
        }
        List<String> dimensions = StringUtils.isNotBlank(po.getDimensions()) ? Arrays.asList(StringUtils.split(po.getDimensions(), ",")) : null;
        return BizTargetMonitorConfigDO.builder()
            .id(po.getId())
            .name(po.getName())
            .themeType(po.getThemeType())
            .conditions(JacksonUtils.parseArray(po.getConditions(), BizTargetMonitorConfigCondition.class))
            .target(po.getTarget())
            .dimensions(dimensions)
            .execFrequency(OffsetTimeUnit.toOffsetTimeUnitName(po.getExecFrequency()))
            .createOpr(po.getCreateOpr())
            .updateOpr(po.getUpdateOpr())
            .createTime(po.getCreateTime())
            .updateTime(po.getUpdateTime())
            .status(po.getStatus())
            .jobId(po.getJobId())
            .monitorId(po.getMonitorId())
            .build();
    }

    public BizTargetMonitorConfigPO saveDtoToPO(BizTargetMonitorConfigSaveDTO monitorDO) {
        if (monitorDO == null) {
            return null;
        }
        String dimensions = CollectionUtils.isNotEmpty(monitorDO.getDimensions()) ? String.join(",", monitorDO.getDimensions()) : null;
        return BizTargetMonitorConfigPO.builder()
            .id(monitorDO.getId())
            .name(monitorDO.getName())
            .themeType(monitorDO.getThemeType())
            .conditions(JacksonUtils.toJsonString(monitorDO.getConditions()))
            .target(monitorDO.getTarget())
            .dimensions(dimensions)
            .execFrequency(OffsetTimeUnit.toSeconds(monitorDO.getExecFrequency()))
            .createOpr(monitorDO.getCreateOpr())
            .updateOpr(monitorDO.getUpdateOpr())
            .build();
    }

    private BizTargetMonitorConfigPageQuery convert(BizTargetMonitorConfigPageQryDTO pageQryDTO) {
        Integer currentPage = pageQryDTO.getPageNo();
        Integer pageSize = pageQryDTO.getPageSize();

        BizTargetMonitorConfigPageQuery pageQuery = new BizTargetMonitorConfigPageQuery();
        pageQuery.setStart((currentPage - 1) * pageSize);
        pageQuery.setLimit(pageSize);
        pageQuery.setThemeType(pageQryDTO.getThemeType());
        pageQuery.setName(pageQryDTO.getName());
        pageQuery.setTarget(pageQryDTO.getTarget());
        pageQuery.setCreateOpr(pageQryDTO.getCreateOpr());
        return pageQuery;
    }

}
