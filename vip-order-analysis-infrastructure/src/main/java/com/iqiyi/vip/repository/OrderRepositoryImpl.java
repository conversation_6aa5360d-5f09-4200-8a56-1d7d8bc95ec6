package com.iqiyi.vip.repository;

import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.SQLException;

import com.iqiyi.vip.domain.repository.OrderRepository;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.mapper.OrderPilotComponent;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午 10:20
 */
@Repository
public class OrderRepositoryImpl implements OrderRepository {


    @Resource
    private OrderPilotComponent orderPilotComponent;

    @Override
    @Retryable(maxAttempts = 1, backoff = @Backoff(delay = 60000))
    public OriginalQueryResultDTO queryTargetResult(TargetAnalysisQueryDTO targetAnalysisQueryDTO) throws SQLException {
        return orderPilotComponent.queryTargetResult(targetAnalysisQueryDTO);
    }

    @Override
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public OriginalQueryResultDTO queryTargetResultAfter3SecRetry(TargetAnalysisQueryDTO targetAnalysisQueryDTO) throws SQLException {
        return orderPilotComponent.queryTargetResult(targetAnalysisQueryDTO);
    }
}
