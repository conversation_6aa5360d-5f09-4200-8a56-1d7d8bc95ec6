package com.iqiyi.vip.repository;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.repository.DiagnosisRepository;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResult;
import com.iqiyi.vip.dto.diagnosis.DiagnosisResultDTO;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.mapper.DiagnosisSnapshotMapper;
import com.iqiyi.vip.po.DiagnosisSnapshot;

import static com.iqiyi.vip.constant.Constants.CURRENT_PERIOD;
import static com.iqiyi.vip.constant.Constants.DIFF_PERIOD;
import static com.iqiyi.vip.constant.Constants.DIMENSIONS;
import static com.iqiyi.vip.constant.Constants.DIMENSION_ITEM;
import static com.iqiyi.vip.constant.Constants.EXCEEDING_AVERAGE_CONTRIBUTION;
import static com.iqiyi.vip.constant.Constants.INCREMENT;
import static com.iqiyi.vip.constant.Constants.INCREMENTAL_CONTRIBUTION_RATIO;
import static com.iqiyi.vip.constant.Constants.INCREMENTAL_RATIO;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DiagnosisRepositoryImpl implements DiagnosisRepository {
    @Resource
    private DiagnosisSnapshotMapper diagnosisSnapshotMapper;

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    @Override
    public int addDiagnosisResult(DiagnosisResultDTO diagnosisResultDTO, String diagnosisPath) {
        DiagnosisSnapshot diagnosisSnapshot = buildDiagnosis(diagnosisResultDTO, diagnosisPath);
        return diagnosisSnapshotMapper.insert(diagnosisSnapshot);
    }

    @Override
    public List<ExcelSheetDataDTO> generateDiagoisisExcel(Long taskId) {
        List<DiagnosisSnapshot> diagnosisSnapshotList = diagnosisSnapshotMapper.findByTaskId(taskId);
        if (diagnosisSnapshotList == null || diagnosisSnapshotList.isEmpty()) {
            log.error("taskId:{} 诊断结果不存在", taskId);
            return null;
        }
        return getExcelSheetDataDto(diagnosisSnapshotList);
    }

    @Override
    public List<ExcelSheetDataDTO> generateDiagoisisExcel(String uniqueId) {
        List<DiagnosisSnapshot> diagnosisSnapshotList = diagnosisSnapshotMapper.findByUniqueId(uniqueId);
        if (diagnosisSnapshotList == null || diagnosisSnapshotList.isEmpty()) {
            log.error("taskId:{} 诊断结果不存在", uniqueId);
            return null;
        }
        return getExcelSheetDataDto(diagnosisSnapshotList);
    }

    /**
     * @param diagnosisSnapshotList 诊断结果
     * @return 诊断结果Excel data
     */
    private static List<ExcelSheetDataDTO> getExcelSheetDataDto(List<DiagnosisSnapshot> diagnosisSnapshotList) {
        List<ExcelSheetDataDTO> excelSheetDatas = diagnosisSnapshotList.stream()
                .map(diagnosisSnapshot -> {
                    Map<String, Object> diagnosisMap = GSON.fromJson(diagnosisSnapshot.getResult(),
                            new TypeToken<Map<String, Object>>() {
                            }.getType());
                    LinkedHashMap<String, String> columnNames = GSON.fromJson(String.valueOf(diagnosisMap.get("columnNames")),
                            new TypeToken<LinkedHashMap<String, String>>() {
                            }.getType());

                    //调整excel的展示名称
                    for (Map.Entry<String, String> entry : columnNames.entrySet()) {
                        entry.setValue(entry.getKey());
                    }

                    LinkedTreeMap<String, List<DiagnosisResult>> diagnosisResults = GSON.fromJson(
                            GSON.toJson(diagnosisMap.get("data")),
                            new TypeToken<LinkedTreeMap<String, List<DiagnosisResult>>>() {
                            }.getType());

                    List<DiagnosisResult> diagnosisResultList = diagnosisResults.values()
                            .stream()
                            .flatMap(List::stream)
                            .collect(Collectors.toList());

                    List<LinkedHashMap<String, Object>> dataList = diagnosisResultList.stream()
                            .map(diagnosisResult -> {
                                LinkedHashMap<String, Object> dataMap = new LinkedHashMap<>();
                                dataMap.put(DIMENSIONS, diagnosisResult.getDimensionLayerDesc());
                                dataMap.put(DIMENSION_ITEM, diagnosisResult.getDimensionName());
                                dataMap.put(CURRENT_PERIOD, convertBigDecimalIfNeed(diagnosisResult.getTargetValueCurrentPeriod()));
                                dataMap.put(DIFF_PERIOD, convertBigDecimalIfNeed(diagnosisResult.getTargetValueDiffPeriod()));
                                dataMap.put(INCREMENT, convertBigDecimalIfNeed(diagnosisResult.getIncrement()));
                                dataMap.put(INCREMENTAL_CONTRIBUTION_RATIO, diagnosisResult.getIncrementalContributionRatio());
                                dataMap.put(INCREMENTAL_RATIO, diagnosisResult.getIncrementalRatio());
                                dataMap.put(EXCEEDING_AVERAGE_CONTRIBUTION, diagnosisResult.getExceedingAverageContribution());
                                return dataMap;
                            })
                            .collect(Collectors.toList());
                    return ExcelSheetDataDTO.builder()
                            .sheetName("sheet".concat(diagnosisSnapshotList.indexOf(diagnosisSnapshot) + ""))
                            .headColumnMap(columnNames)
                            .dataList(dataList)
                            .diagnosisPath(StringUtils.isNotBlank(diagnosisSnapshot.getDiagnosisPath()) ? "下钻路径：".concat(diagnosisSnapshot.getDiagnosisPath()) : null)
                            .build();
                })
                .collect(Collectors.toList());
        return excelSheetDatas;
    }

    private static Object convertBigDecimalIfNeed(Object value) {
        if (value instanceof String) {
            String stringValue = (String) value;
            //由于值不知道在哪里转的千分位，已经是72,023,171，需要去除,符号后判断是否为数字类型，转为BigDecimal存储
            String realValue= stringValue.replace(",", "");
            if(NumberUtils.isCreatable(realValue)){
                return NumberUtils.createBigDecimal(realValue);
            }
        }
        return value;
    }

    private static class ValueAsKeyDeserializer implements JsonDeserializer<LinkedHashMap<?, ?>> {
        @Override
        public LinkedHashMap<?, ?> deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            LinkedHashMap<Object, Object> result = new LinkedHashMap<>();
            JsonObject jsonObject = json.getAsJsonObject();
            for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
                result.put(entry.getValue(), entry.getKey());
            }
            return result;
        }
    }
    /**
     * 构建诊断结果
     *
     * @param diagnosisResultDTO 诊断结果
     * @return 诊断结果
     */
    private DiagnosisSnapshot buildDiagnosis(DiagnosisResultDTO diagnosisResultDTO, String diagnosisPath) {
        DiagnosisSnapshot diagnosisSnapshot = new DiagnosisSnapshot();
        diagnosisSnapshot.setUniqueId(diagnosisResultDTO.getUniqueId());
        diagnosisSnapshot.setTaskId(diagnosisResultDTO.getTaskId());
        Map<String, Object> result = new HashMap<>();
        result.put("columnNames", diagnosisResultDTO.getColumnNames());
        result.put("data", diagnosisResultDTO.getDiagnosisResults());
        diagnosisSnapshot.setResult(GSON.toJson(result));
        diagnosisSnapshot.setDiagnosisPath(diagnosisPath);
        diagnosisSnapshot.setCreateTime(new Date());
        diagnosisSnapshot.setUpdateTime(new Date());
        return diagnosisSnapshot;
    }
}
