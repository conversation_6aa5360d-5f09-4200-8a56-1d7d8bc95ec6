package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.BusinessCondition;
import com.iqiyi.vip.domain.repository.BusinessConditionRepository;
import com.iqiyi.vip.mapper.BusinessConditionMapper;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: linpeihui
 * @createTime: 2023/08/11
 */
@Service
@CacheConfig(cacheNames = "caffeineCacheManager")
public class BusinessConditionRepositoryImpl implements BusinessConditionRepository {
    @Resource
    private BusinessConditionMapper businessConditionMapper;

    @Override
    public void batchInsert(List<BusinessCondition> businessConditionList) {
        businessConditionMapper.batchInsert(businessConditionList);
    }

    @Override
    public int deleteByThemeAndBusiness(Long themeId, Long businessId) {
        return businessConditionMapper.deleteByThemeAndBusiness(themeId, businessId);
    }

    @Override
    public int deleteByConditionId(Long conditionId) {
        return businessConditionMapper.deleteByConditionId(conditionId);
    }

    @Override
    public List<BusinessCondition> selectByThemeAndBusiness(Long themeId, Long businessId) {
        return businessConditionMapper.selectByThemeAndBusiness(themeId, businessId);
    }
}
