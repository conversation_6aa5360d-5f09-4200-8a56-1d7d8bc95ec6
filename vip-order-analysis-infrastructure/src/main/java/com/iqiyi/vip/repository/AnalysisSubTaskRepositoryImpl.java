package com.iqiyi.vip.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;
import com.iqiyi.vip.domain.repository.AnalysisSubTaskRepository;
import com.iqiyi.vip.mapper.AnalysisSubTaskMapper;

/**
 * <AUTHOR> @date 2024/1/4 20:32
 */
@Component
@Slf4j
public class AnalysisSubTaskRepositoryImpl implements AnalysisSubTaskRepository {

    @Resource
    private AnalysisSubTaskMapper analysisSubTaskMapper;

    @Override
    public int addAnalysisSubTask(AnalysisSubTaskDO analysisSubTaskDO) {
        return analysisSubTaskMapper.insert(analysisSubTaskDO);
    }

    @Override
    @Cacheable(value = "AnalysisSubTaskDO")
    public AnalysisSubTaskDO getSubTaskByTaskIdOrUserGroupId(Long taskId, Integer userGroupId) {
        return analysisSubTaskMapper.selectByTaskId(taskId, userGroupId);
    }

    @Override
    public int updateByTaskId(AnalysisSubTaskDO analysisSubTaskDO) {
        return analysisSubTaskMapper.updateByTask(analysisSubTaskDO);
    }

    @Override
    public int deleteByTaskId(Long taskId) {
        return analysisSubTaskMapper.deleteByParentTaskId(taskId);
    }

    @Override
    public int deleteByUserGroupId(Integer userGroupId) {
        return analysisSubTaskMapper.deleteByUserGroupId(userGroupId);
    }
}
