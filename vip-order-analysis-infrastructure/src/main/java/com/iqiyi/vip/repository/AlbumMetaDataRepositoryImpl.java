package com.iqiyi.vip.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.iqiyi.vip.domain.entity.AlbumMetaDataDO;
import com.iqiyi.vip.domain.repository.AlbumMetaDataRepository;
import com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO;
import com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO;
import com.iqiyi.vip.mapper.AlbumMetaDataMapper;

/**
 * <AUTHOR>
 * @className AlbumMetaDataRepositoryImpl
 * @description
 * @date 2024/9/28
 **/
@Service
@Slf4j
public class AlbumMetaDataRepositoryImpl implements AlbumMetaDataRepository {

    @Resource
    private AlbumMetaDataMapper albumMetaDataMapper;

    @Override
    public List<AlbumMetaDataDO> getPageQueryResult(AlbumMetaDataPageQueryDTO query) {
        query.setOrderBy("first_episode_online_time");
        if (query.getPageSize() == null || query.getPageSize() == 0) {
            query.setPageSize(20);
        }
        if (query.getPageNo() == null || query.getPageNo() == 0) {
            query.setPageNo(1);
        }
        query.setStart((query.getPageNo() - 1) * query.getPageSize());
        return albumMetaDataMapper.getPageQuery(query);
    }

    @Override
    public Integer selectCount(AlbumMetaDataPageQueryDTO albumQuery) {
        return albumMetaDataMapper.selectCount(albumQuery);
    }

    @Override
    public List<AlbumMetaDataDO> getByRemainTime(String startDate, String endDate) {
        return albumMetaDataMapper.getByRemainTime(startDate, endDate);
    }

    @Override
    public int updateMetaData(UpdateAlbumMetaDataDTO updateAlbumMetaDataDTO) {
        return albumMetaDataMapper.updateMetaData(updateAlbumMetaDataDTO);
    }
}
