package com.iqiyi.vip.repository;

import com.iqiyi.vip.domain.entity.PaymentChannel;
import com.iqiyi.vip.domain.repository.PaymentChannelRepository;
import com.iqiyi.vip.mapper.PaymentChannelMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/27/22
 * @apiNote
 */
@Service
public class PaymentChannelRepositoryImpl implements PaymentChannelRepository {

    @Resource
    private PaymentChannelMapper paymentChannelMapper;

    @Override
    public List<PaymentChannel> selectAll() {
        List<com.iqiyi.vip.po.PaymentChannel> channels = paymentChannelMapper.selectAll();
        if (CollectionUtils.isEmpty(channels)) {
            return null;
        }
        return channels.stream().map(this::adaptor).collect(Collectors.toList());
    }

    private PaymentChannel adaptor(com.iqiyi.vip.po.PaymentChannel channel) {
        PaymentChannel channel1 = new PaymentChannel();
        channel1.setId(channel.getId());
        channel1.setCategoryCode(channel.getCategoryCode());
        channel1.setCode(channel.getCode());
        channel1.setDescription(channel.getDescription());
        channel1.setDutAgreementName(channel.getDutAgreementName());
        channel1.setDutAgreementUrl(channel.getDutAgreementUrl());
        channel1.setIconUrl(channel.getIconUrl());
        channel1.setPromotionText(channel.getPromotionText());
        return channel1;
    }
}
