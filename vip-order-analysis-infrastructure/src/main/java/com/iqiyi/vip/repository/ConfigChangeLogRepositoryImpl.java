package com.iqiyi.vip.repository;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Comparator;
import java.util.List;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.domain.entity.ConfigChangeLog;
import com.iqiyi.vip.domain.repository.ConfigChangeLogRepository;
import com.iqiyi.vip.mapper.ConfigChangeLogDao;

/**
 * <AUTHOR>
 * @className ConfigChangeLogRepositoryImpl
 * @description
 * @date 2022/11/23
 **/
@Service
public class ConfigChangeLogRepositoryImpl implements ConfigChangeLogRepository {

    @Resource
    private ConfigChangeLogDao configChangeLogDao;

    @Override
    public void save(ConfigChangeLog configChangeLog) {
        configChangeLogDao.insertSelective(configChangeLog);
    }

    @Override
    public Integer getMaxVersion(Integer configType, Long configId) {
        List<ConfigChangeLog> configChangeLogs = configChangeLogDao.getByConfigTypeAndConfigId(configType, configId.intValue(), null);
        if (CollectionUtils.isEmpty(configChangeLogs)) {
            return null;
        }
        return configChangeLogs.stream()
            .max(Comparator.comparing(ConfigChangeLog::getVersion))
            .map(ConfigChangeLog::getVersion)
            .orElse(Constants.DEFAULT_VERSION);
    }

    @Override
    public List<ConfigChangeLog> getConfigChangeLogs(Integer configType, Long configId, Integer version) {
        return configChangeLogDao.getByConfigTypeAndConfigId(configType, configId.intValue(), version);
    }

    @Override
    public int delete(Integer configType, Long configId) {
        return configChangeLogDao.delByConfigTypeAndConfigId(configType, configId.intValue());
    }

}
