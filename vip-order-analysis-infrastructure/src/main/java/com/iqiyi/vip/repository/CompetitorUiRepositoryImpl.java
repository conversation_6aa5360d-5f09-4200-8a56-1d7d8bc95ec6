package com.iqiyi.vip.repository;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.repository.CompetitorUiRepository;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSearchParam;
import com.iqiyi.vip.mapper.CompetitorUiMapper;
import com.iqiyi.vip.po.competitor.CompetitorUiPO;

/**
 * @author: guojing
 * @date: 2024/10/30 15:12
 */
@Slf4j
@Component
public class CompetitorUiRepositoryImpl implements CompetitorUiRepository {

    @Resource
    private CompetitorUiMapper competitorUiMapper;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorUiRepo_clientVersionList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> clientVersionList() {
        return competitorUiMapper.selectAllClientVersion();
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorUiRepo_brandsList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> brandsList() {
        return competitorUiMapper.selectAllBrand();
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorUiRepo_dateRange", cacheType = CacheType.LOCAL)
    @Override
    public CompetitorDateRangeDTO dateRange() {
        return competitorUiMapper.selectDateRange();
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorUiRepo_pageList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> pageList(String brand, String date) {
        return competitorUiMapper.selectAllPage(brand, date);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "competitorUiRepo_userStatusList", cacheType = CacheType.LOCAL)
    @Override
    public List<String> userStatusList(String brand, String date) {
        return competitorUiMapper.selectAllUserStatus(brand, date);
    }

    @Override
    public void save(CompetitorUiSaveDTO param) {
        CompetitorUiPO competitorUiPO = CompetitorUiPO.buildFrom(param);
        if (competitorUiPO == null) {
            return;
        }
        competitorUiMapper.insert(competitorUiPO);
    }

    @Override
    public List<CompetitorUiDTO> search(CompetitorUiSearchParam param) {
        List<CompetitorUiPO> competitorUiPOS = competitorUiMapper.search(param.getDate(), param.getBrand(), param.getClientVersion(), param.getPage(), param.getUserStatus());
        if (CollectionUtils.isEmpty(competitorUiPOS)) {
            return Collections.emptyList();
        }
        return competitorUiPOS.stream()
            .map(CompetitorUiPO::toDTO)
            .collect(Collectors.toList());
    }
}
