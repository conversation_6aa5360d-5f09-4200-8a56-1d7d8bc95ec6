package com.iqiyi.vip.memory;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListeners;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataNode;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.domain.memory.DataPermissionMemory;
import com.iqiyi.vip.domain.repository.CommonLayerRepository;
import com.iqiyi.vip.domain.repository.FvStructureRepository;
import com.iqiyi.vip.domain.repository.QiyuePlatformRepository;
import com.iqiyi.vip.domain.repository.VipTypeRepository;
import com.iqiyi.vip.dto.condition.CommonPair;
import com.iqiyi.vip.dto.condition.FvChannelPair;
import com.iqiyi.vip.dto.condition.QiyuePlatformPair;
import com.iqiyi.vip.dto.condition.VipTypePair;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ConditionEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.CloudConfigUtils;
import com.iqiyi.vip.utils.DataPermissionNodeTrans;

/**
 * <AUTHOR>
 * @date 2022/8/17 14:34
 */
@Component
@Slf4j
@DependsOn({"cloudConfigUtils"})
public class DataPermissionMemoryImpl implements DataPermissionMemory, InitializingBean {

    @Resource
    private FvStructureRepository fvStructureRepository;
    @Resource
    private QiyuePlatformRepository qiyuePlatformRepository;
    @Resource
    private VipTypeRepository vipTypeRepository;

    private volatile boolean updateNodes = false;
    private volatile boolean updateDimensionLayer = false;

    // 渠道
    private DataPermissionNode fvRoot;
    private Map<LabelEnum, List<DataPermissionNode>> fvLayeredNodes;
    private LoadingCache<LabelEnum, DataPermissionDTO> fvLayeredDataPermissionCache;

    // 产品
    private DataPermissionNode proRoot;
    private Map<LabelEnum, List<DataPermissionNode>> proLayeredNodes;
    private LoadingCache<LabelEnum, DataPermissionDTO> proLayeredDataPermissionCache;

    // 会员类型
    private DataPermissionNode vipTypeRoot;
    private Map<LabelEnum, List<DataPermissionNode>> vipTypeLayeredNodes;
    private LoadingCache<LabelEnum, DataPermissionDTO> vipTypeLayeredDataPermissionCache;

    // 竞品监控
    private DataPermissionNode competitorMonitorRoot;
    private Map<LabelEnum, List<DataPermissionNode>> competitorMonitorLayeredNodes;
    private LoadingCache<LabelEnum, DataPermissionDTO> competitorMonitorLayeredDataPermissionCache;

    public HashMap<String, DimensionLayerNode> getDimensionLayerRootHashMap(String targetCode) {
        return (HashMap<String, DimensionLayerNode>) CloudConfigUtils.getDimensionLayerPair(targetCode)
            .getChildren()
            .stream()
            .collect(Collectors.toMap(DimensionLayerNode::getGroupCode, it -> it));
    }

    private HashMap<String, DimensionLayerNode> dimensionLayerRootHashMap = new HashMap<>();

    private ReentrantReadWriteLock reentrantReadWriteLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock.ReadLock readLock = reentrantReadWriteLock.readLock();
    private ReentrantReadWriteLock.WriteLock writeLock = reentrantReadWriteLock.writeLock();

    private AtomicLong timerAutoAdd = new AtomicLong();

    private CommonLayerRepository commonLayerRepository;

    private ExecutorService executorService = new ThreadPoolExecutor(1 << 4, 1 << 5,
        0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(1 << 3), Executors.defaultThreadFactory(),
        new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void afterPropertiesSet() throws Exception {

//        log.info("[dimension-layer-init][start-task]]");
//        if (this.setUpdateDimensionLayers()) {
//            StopWatch stopWatch = new StopWatch("dimension-layer-init-time");
//            stopWatch.start();
//            // 初始化维度层级树
//            this.dimensionLayerInit();
//            // 计时停止
//            stopWatch.stop();
//            log.info("[dimension-layer-init][time-consuming: {}秒 {}毫秒]",
//                stopWatch.getLastTaskTimeMillis() / 1000, stopWatch.getLastTaskTimeMillis() % 1000);
//        }

        log.info("[base-data-init][start-task]");
        if (this.setUpdateFvNodes()) {
            StopWatch stopWatch = new StopWatch("base-data-init-time");
            stopWatch.start();
            // 初始化权限树
            this.dataPermissionInit();
            // 初始化权限树缓存
            this.dataPermissionCacheInit();
            // 恢复
            this.updateNodes = false;
            // 计时停止
            stopWatch.stop();
            log.info("[base-data-init-time][time-consuming: {}秒 {}毫秒]",
                stopWatch.getLastTaskTimeMillis() / 1000, stopWatch.getLastTaskTimeMillis() % 1000);
        }
        this.scheduleDataPermissionAutoUpdate(); // 定时更新数据权限树
    }

    /**
     * 构建权限树初始化
     */
    private void buildDataPermissionsTree(Map<LabelEnum, List<DataPermissionNode>> layeredNodes, Supplier<Map<LabelEnum, List<DataPermissionNode>>> supplier4LayeredNodes,
        DataPermissionNode root, Supplier<DataPermissionNode> supplier4Root) {
        if (layeredNodes == null) {
            layeredNodes = supplier4LayeredNodes.get();
        }
        if (root == null) {
            root = supplier4Root.get();
        }
        layeredNodes.put(root.getLabel(), Lists.newArrayList(root));
    }

    /**
     * 初始化权限树
     */
    private void dataPermissionInit() {
        // 权限树构建
        Lists.newArrayList(
            // Fv渠道
            CompletableFuture.runAsync(
                    // 初始化第一个节点（基节点）
                    () -> this.buildDataPermissionsTree(
                        fvLayeredNodes,
                        () -> DataPermissionMemoryImpl.this.fvLayeredNodes = new EnumMap<>(LabelEnum.class),
                        fvRoot,
                        () -> {
                            final List<FvChannelPair> rootFvChannelPairs = fvStructureRepository.fvChannels(0, 0, LabelEnum.L1.getLevel(), null);
                            final FvChannelPair fvChannelPair = rootFvChannelPairs.get(0); // 目前1级渠道只有一个
                            return DataPermissionMemoryImpl.this.fvRoot = this.createDataPermissionNode4Fv(fvChannelPair, null);
                        }
                    ), executorService)
                // 构建权限树
                .thenRunAsync(
                    () -> this.buildDataPermissionTree4Fv(fvRoot, fvLayeredNodes), executorService),
            // 产品、端平台
            CompletableFuture.runAsync(
                    () -> this.buildDataPermissionsTree(
                        proLayeredNodes,
                        () -> DataPermissionMemoryImpl.this.proLayeredNodes = new EnumMap<>(LabelEnum.class),
                        proRoot,
                        () ->
                            DataPermissionMemoryImpl.this.proRoot = this.createDataPermissionNode4Pro(
                                QiyuePlatformPair.builder()
                                    .id("-1")
                                    .level(LabelEnum.P0.getLevel())
                                    .name("产品")
                                    .build(), null)
                    ), executorService)
                .thenRunAsync(() -> this.buildDataPermissionTree4Pro(proRoot, proLayeredNodes), executorService),
            // 会员类型
            CompletableFuture.runAsync(
                    () -> this.buildDataPermissionsTree(
                        vipTypeLayeredNodes,
                        () -> DataPermissionMemoryImpl.this.vipTypeLayeredNodes = new EnumMap<>(LabelEnum.class),
                        vipTypeRoot,
                        () ->
                            DataPermissionMemoryImpl.this.vipTypeRoot = this.createDataPermissionNode4VipType(
                                VipTypePair.builder()
                                    .id("-1")
                                    .level(LabelEnum.VT0.getLevel())
                                    .name("会员类型")
                                    .build(), null)
                    ), executorService)
                .thenRunAsync(() -> this.buildDataPermissionTree4VipType(vipTypeRoot, vipTypeLayeredNodes), executorService),
            // 竞品监控
            CompletableFuture.runAsync(
                    () -> this.buildDataPermissionsTree(
                        competitorMonitorLayeredNodes,
                        () -> DataPermissionMemoryImpl.this.competitorMonitorLayeredNodes = new EnumMap<>(LabelEnum.class),
                        competitorMonitorRoot,
                        () ->
                            DataPermissionMemoryImpl.this.competitorMonitorRoot = DataPermissionNode.builder()
                                .name("竞品监控")
                                .id("-1")
                                .label(LabelEnum.COMPETITOR_MONITOR)
                                .parent(null)
                                .build()
                    ), executorService)
                .thenRunAsync(() -> this.buildDataPermissionTree4CommonLayer(competitorMonitorRoot, competitorMonitorLayeredNodes), executorService)
        ).forEach(v -> v.join());
    }


//    private DimensionLayerNode buildCommonDimensionLayer(String groupEntryKey, List<DimensionLayerNode> layers) {
//        int size = layers.size();
//        for (int index = 0; index < size - 1; index++) {
//            DimensionLayerNode dimensionLayerNode = layers.get(index);
//            dimensionLayerNode.setSubNode(layers.get(index + 1));
//        }
//        return layers.get(0);
//    }


    /**
     * 初始化维度层级
     */
//    private void dimensionLayerInit() {
//        DimensionLayerNode layerNode = CloudConfigUtils.getDimensionLayerPair();
//        if (layerNode == null) {
//            return;
//        }
//        List<DimensionLayerNode> children = layerNode.getChildren();
//        for (DimensionLayerNode child : children) {
//            dimensionLayerRootHashMap.put(child.getGroupCode(), child);
//        }
//    }

    /**
     * 初始化权限树缓存
     */
    private void dataPermissionCacheInit() {
        Lists.newArrayList(
            // 渠道
            CompletableFuture.runAsync(
                    () -> this.fvLayeredDataPermissionCache = this.buildDataPermissionCache(key -> {
                        DataPermissionNode curNode = DataPermissionMemoryImpl.this.fvRoot;
                        DataPermissionDTO ret = DataPermissionNodeTrans.copyDataPermissionNode(curNode);
                        DataPermissionMemoryImpl.this.createFvDataPermission(ret, curNode, key);
                        return ret;
                    }), executorService)
                // 初始化L3数据权限树
                .thenRunAsync(() -> this.dataPermissionCacheBuild(fvLayeredDataPermissionCache, LabelEnum.datePermissionCtrlLabel()), executorService),
            // 产品端平台
            CompletableFuture.runAsync(
                    () ->
                        proLayeredDataPermissionCache = this.buildDataPermissionCache(key -> {
                            DataPermissionNode curNode = DataPermissionMemoryImpl.this.proRoot;
                            DataPermissionDTO ret = DataPermissionNodeTrans.copyDataPermissionNode(curNode);
                            DataPermissionMemoryImpl.this.createProOrVipTypeDataPermission(ret, curNode);
                            return ret;
                        }), executorService)
                .thenRunAsync(() -> this.dataPermissionCacheBuild(proLayeredDataPermissionCache, LabelEnum.P0)),
            // 会员类型
            CompletableFuture.runAsync(
                    () -> vipTypeLayeredDataPermissionCache = this.buildDataPermissionCache(key -> {
                        DataPermissionNode curNode = DataPermissionMemoryImpl.this.vipTypeRoot;
                        DataPermissionDTO ret = DataPermissionNodeTrans.copyDataPermissionNode(curNode);
                        DataPermissionMemoryImpl.this.createProOrVipTypeDataPermission(ret, curNode);
                        return ret;
                    }), executorService)
                .thenRunAsync(() -> this.dataPermissionCacheBuild(vipTypeLayeredDataPermissionCache, LabelEnum.VT0)),
            // 竞品监控
            CompletableFuture.runAsync(
                    () -> competitorMonitorLayeredDataPermissionCache = this.buildDataPermissionCache(key -> {
                        DataPermissionNode curNode = DataPermissionMemoryImpl.this.competitorMonitorRoot;
                        DataPermissionDTO ret = DataPermissionNodeTrans.copyDataPermissionNode(curNode);
                        DataPermissionMemoryImpl.this.createProOrVipTypeDataPermission(ret, curNode);
                        return ret;
                    }), executorService)
                .thenRunAsync(() -> this.dataPermissionCacheBuild(competitorMonitorLayeredDataPermissionCache, LabelEnum.COMPETITOR_MONITOR))
        ).forEach(CompletableFuture::join);
    }

    private DataPermissionDTO dataPermissionCacheBuild(LoadingCache<LabelEnum, DataPermissionDTO> loadingCache, LabelEnum labelEnum) {
        try {
            return loadingCache.get(labelEnum);
        } catch (ExecutionException e) {}
        return null;
    }

    /**
     * 构建对应权限的缓存工具
     * @param function
     * @return
     */
    private LoadingCache<LabelEnum, DataPermissionDTO> buildDataPermissionCache(Function<LabelEnum, DataPermissionDTO> function) {
        return CacheBuilder.newBuilder()
            //设置并发级别为8，并发级别是指可以同时写缓存的线程数
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())
            //设置缓存容器的初始容量为10
            .initialCapacity(2)
            //设置缓存最大容量为100，超过100之后就会按照LRU最近虽少使用算法来移除缓存项
            .maximumSize(8)
            //是否需要统计缓存情况,该操作消耗一定的性能,生产环境应该去除
            .recordStats()
            //设置写缓存后n秒钟过期
            .expireAfterWrite(10, TimeUnit.MINUTES)
            //设置读写缓存后n秒钟过期,实际很少用到,类似于expireAfterWrite
            //.expireAfterAccess(17, TimeUnit.SECONDS)
            //只阻塞当前数据加载线程，其他线程返回旧值
            //.refreshAfterWrite(13, TimeUnit.SECONDS)
            //设置缓存的移除通知
            .removalListener(RemovalListeners.asynchronous(notification -> log.warn(
                    notification.getKey() + " " + notification.getValue() + " 被移除,原因:" + notification.getCause()),
                new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
                    Runtime.getRuntime().availableProcessors(), 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(1 << 10))))
            //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存z
            .build(new CacheLoader<LabelEnum, DataPermissionDTO>() {
                @Override
                public DataPermissionDTO load(LabelEnum key) throws Exception {
                    return function.apply(key);
                }
            });
    }

    private void scheduleDataPermissionAutoUpdate() {
        // 定时更新
        Timer timer = new Timer("DataPermission-Tree-AutoUpdate-Timer-" + timerAutoAdd.getAndIncrement(), true);
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    if (DataPermissionMemoryImpl.this.setUpdateFvNodes()) {
                        // 渠道
                        DataPermissionNode dataPermissionNode4Fv = fvRoot.copyDataPermissionNodeSimple();
                        Map<LabelEnum, List<DataPermissionNode>> layeredNodes4Fv = new EnumMap<>(LabelEnum.class);
                        DataPermissionMemoryImpl.this.buildDataPermissionsTree(layeredNodes4Fv, () -> null, dataPermissionNode4Fv, () -> null);
                        DataPermissionMemoryImpl.this.buildDataPermissionTree4Fv(dataPermissionNode4Fv, layeredNodes4Fv);
                        // 产品端平台
                        DataPermissionNode dataPermissionNode4Pro = proRoot.copyDataPermissionNodeSimple();
                        Map<LabelEnum, List<DataPermissionNode>> layeredNodes4Pro = new EnumMap<>(LabelEnum.class);
                        DataPermissionMemoryImpl.this.buildDataPermissionsTree(layeredNodes4Pro, () -> null, dataPermissionNode4Pro, () -> null);
                        DataPermissionMemoryImpl.this.buildDataPermissionTree4Pro(dataPermissionNode4Pro, layeredNodes4Pro);
                        // 会员类型
                        DataPermissionNode dataPermissionNode4VipType = vipTypeRoot.copyDataPermissionNodeSimple();
                        Map<LabelEnum, List<DataPermissionNode>> layeredNodes4VipType = new EnumMap<>(LabelEnum.class);
                        DataPermissionMemoryImpl.this.buildDataPermissionsTree(layeredNodes4VipType, () -> null, dataPermissionNode4VipType, () -> null);
                        DataPermissionMemoryImpl.this.buildDataPermissionTree4VipType(dataPermissionNode4VipType, layeredNodes4VipType);
                        // 竞品监控
                        DataPermissionNode dataPermissionNode4CompetitorMonitor = competitorMonitorRoot.copyDataPermissionNodeSimple();
                        Map<LabelEnum, List<DataPermissionNode>> layeredNodes4CompetitorMonitor = new EnumMap<>(LabelEnum.class);
                        DataPermissionMemoryImpl.this.buildDataPermissionsTree(layeredNodes4CompetitorMonitor, () -> null, dataPermissionNode4CompetitorMonitor, () -> null);
                        DataPermissionMemoryImpl.this.buildDataPermissionTree4CommonLayer(dataPermissionNode4CompetitorMonitor, layeredNodes4CompetitorMonitor);
                        try {
                            writeLock.lock();
                            DataPermissionMemoryImpl.this.fvRoot = dataPermissionNode4Fv;
                            DataPermissionMemoryImpl.this.fvLayeredNodes = layeredNodes4Fv;
                            DataPermissionMemoryImpl.this.proRoot = dataPermissionNode4Pro;
                            DataPermissionMemoryImpl.this.proLayeredNodes = layeredNodes4Pro;
                            DataPermissionMemoryImpl.this.vipTypeRoot = dataPermissionNode4VipType;
                            DataPermissionMemoryImpl.this.vipTypeLayeredNodes = layeredNodes4VipType;
                            DataPermissionMemoryImpl.this.competitorMonitorRoot = dataPermissionNode4CompetitorMonitor;
                            DataPermissionMemoryImpl.this.competitorMonitorLayeredNodes = layeredNodes4CompetitorMonitor;
                            // 缓存清空
                            fvLayeredDataPermissionCache.invalidate(LabelEnum.datePermissionCtrlLabel());
                            proLayeredDataPermissionCache.invalidate(LabelEnum.P0);
                            vipTypeLayeredDataPermissionCache.invalidate(LabelEnum.VT0);
                            competitorMonitorLayeredDataPermissionCache.invalidate(LabelEnum.COMPETITOR_MONITOR);
                        } finally {
                            writeLock.unlock();
                        }
                        DataPermissionMemoryImpl.this.updateNodes = false;// 恢复
                        // timer 取消
                        timer.cancel();
                    }
                } catch (Exception e) {
                    log.error("[order-analysis][base-data-update] fail. ", e);
                    DataPermissionMemoryImpl.this.updateNodes = false;// 恢复
                } finally {
                    DataPermissionMemoryImpl.this.scheduleDataPermissionAutoUpdate();
                }
            }
        }, 60 * 60 * 1000L);
    }

    /**
     * 搭建数据权限树
     * @param cur 父节点
     */
    private void buildDataPermissionTree4Fv(DataPermissionNode cur, Map<LabelEnum, List<DataPermissionNode>> fvLayeredNodes) {
        final List<FvChannelPair> fvChannelPairs = fvStructureRepository.fvChannels(Integer.valueOf(cur.getInitDataPermissionCurNodeId()),
            cur.getInitDataPermissionCurNodeLevel(), cur.getInitDataPermissionNextNodeLevel(),
            cur.getTeamId() == null ? null : Integer.valueOf(cur.getTeamId()));
        if (cur.getLabel() == LabelEnum.L1) {
            fvChannelPairs.sort(Comparator.comparing(fvChannelPair -> { // 对站内渠道排序
                final int idx = Constants.unMainStationIndexOf(fvChannelPair.getId());
                return idx == -1 ? Short.MAX_VALUE : idx;
            }));
        }
        if (CollectionUtils.isEmpty(fvChannelPairs) && cur.getSubDataNodeList() != null) { // 已被删除，则将父类中对应的Node删除
            cur.getSubDataNodeList().clear();
            return;
        }
        for (FvChannelPair fvChannelPair : fvChannelPairs) {
            if (fvChannelPair.getId() == null) { // 不采用数据
                continue;
            }
            if (cur.getLabel() == LabelEnum.L2) {
                fvChannelPair.setTeamId(fvChannelPair.getId());
                fvChannelPair.setTeamName(fvChannelPair.getName());
            } else {
                fvChannelPair.setTeamId(cur.getTeamId());
                fvChannelPair.setTeamName(cur.getTeamName());
            }
            final DataPermissionNode dataPermissionNode = this.createDataPermissionNode4Fv(fvChannelPair, cur);
            if (!cur.contains(dataPermissionNode)) { // 添加内容不在cur节点存在才添加
                this.buildLayeredDataPermission4Fv(dataPermissionNode, fvLayeredNodes); // 维护层级结构渠道树
                this.buildDataPermissionTree4Fv((DataPermissionNode) cur.addDataNode(dataPermissionNode), fvLayeredNodes);
            } else { // 找到已存在的节点，继续向后遍历，支持 !cur.contains(dataPermissionNode)) 操作，可以判断节点是不是已经存在。
                this.buildDataPermissionTree4Fv((DataPermissionNode) cur.getSubDataNodeList().stream()
                    .filter(v -> dataPermissionNode.equals(v))
                    .findFirst().orElseGet(() -> dataPermissionNode), fvLayeredNodes
                );
            }
        }
    }

    /**
     * 搭建数据权限树
     * @param cur 父节点
     */
    private void buildDataPermissionTree4Pro(DataPermissionNode cur, Map<LabelEnum, List<DataPermissionNode>> proLayeredNodes) {
        if (cur.getLabel().getNextLabelEnum() == null) {
            return;
        }
        final List<QiyuePlatformPair> qiyuePlatforms = qiyuePlatformRepository.getQiyuePlatforms(cur.getId(), cur.getLabel().getNextLabelEnum());
        if (CollectionUtils.isEmpty(qiyuePlatforms) && cur.getSubDataNodeList() != null) { // 已被删除，则将父类中对应的Node删除
            cur.getSubDataNodeList().clear();
            return;
        }
        //增加基础数据中不包含的端平台
        if (isIqiyiVideoBizPro(cur)) {
            addAdditionalEndKey(qiyuePlatforms);
        }
        for (QiyuePlatformPair qiyuePlatformPair : qiyuePlatforms) {
            if (qiyuePlatformPair.getId() == null) {
                continue;
            }
            final DataPermissionNode dataPermissionNode = this.createDataPermissionNode4Pro(qiyuePlatformPair, cur);
            this.buildLayeredDataPermission4Pro(dataPermissionNode, proLayeredNodes); // 维护层级结构渠道树
            if (!cur.contains(dataPermissionNode)) { // 添加内容不在cur节点存在才添加
                this.buildDataPermissionTree4Pro((DataPermissionNode) cur.addDataNode(dataPermissionNode), proLayeredNodes);
            } else { // 找到已存在的节点，继续向后遍历，支持 !cur.contains(dataPermissionNode)) 操作，可以判断节点是不是已经存在。
                this.buildDataPermissionTree4Pro((DataPermissionNode) cur.getSubDataNodeList().stream()
                    .filter(v -> dataPermissionNode.equals(v))
                    .findFirst().orElseGet(() -> dataPermissionNode), proLayeredNodes
                );
            }
        }
    }

    /**
     * 搭建数据权限树
     *
     * @param cur 父节点
     */
    private void buildDataPermissionTree4CommonLayer(DataPermissionNode cur, Map<LabelEnum, List<DataPermissionNode>> vipCardTypeLayeredNodes) {
        if (cur.getLabel().getNextLabelEnum() == null) {
            return;
        }
        List<CommonPair> commonPairs = commonLayerRepository.getCommonPair(cur.getId(), cur.getLabel().getNextLabelEnum());
        if (CollectionUtils.isEmpty(commonPairs) && cur.getSubDataNodeList() != null) { // 已被删除，则将父类中对应的Node删除
            cur.getSubDataNodeList().clear();
            return;
        }
        for (CommonPair commonPair : commonPairs) {
            if (commonPair.getId() == null) {
                continue;
            }
            final DataPermissionNode dataPermissionNode = this.createDataPermissionNode4Common(commonPair, cur);
            this.buildLayeredDataPermission4Common(dataPermissionNode, vipCardTypeLayeredNodes); // 维护层级结构渠道树
            if (!cur.contains(dataPermissionNode)) {
                // 添加内容不在cur节点存在才添加
                this.buildDataPermissionTree4CommonLayer((DataPermissionNode) cur.addDataNode(dataPermissionNode), vipCardTypeLayeredNodes);
            } else {
                // 找到已存在的节点，继续向后遍历，支持 !cur.contains(dataPermissionNode)) 操作，可以判断节点是不是已经存在。
                this.buildDataPermissionTree4CommonLayer((DataPermissionNode)
                        cur.getSubDataNodeList().stream()
                            .filter(v -> dataPermissionNode.equals(v))
                            .findFirst().orElseGet(() -> dataPermissionNode),
                    vipCardTypeLayeredNodes
                );
            }
        }
    }

    /**
     * 搭建数据权限树
     *
     * @param cur 父节点
     */
    private void buildDataPermissionTree4VipType(DataPermissionNode cur, Map<LabelEnum, List<DataPermissionNode>> vipTypeLayeredNodes) {
        if (cur.getLabel().getNextLabelEnum() == null) {
            return;
        }
        List<VipTypePair> vipTypePairs = vipTypeRepository.getVipTypePairs(cur.getId(), cur.getLabel().getNextLabelEnum());
        if (CollectionUtils.isEmpty(vipTypePairs) && cur.getSubDataNodeList() != null) { // 已被删除，则将父类中对应的Node删除
            cur.getSubDataNodeList().clear();
            return;
        }
        for (VipTypePair vipTypePair : vipTypePairs) {
            if (vipTypePair.getId() == null) {
                continue;
            }
            final DataPermissionNode dataPermissionNode = this.createDataPermissionNode4VipType(vipTypePair, cur);
            this.buildLayeredDataPermission4VipType(dataPermissionNode, vipTypeLayeredNodes); // 维护层级结构渠道树
            if (!cur.contains(dataPermissionNode)) { // 添加内容不在cur节点存在才添加
                this.buildDataPermissionTree4VipType((DataPermissionNode) cur.addDataNode(dataPermissionNode), vipTypeLayeredNodes);
            } else { // 找到已存在的节点，继续向后遍历，支持 !cur.contains(dataPermissionNode)) 操作，可以判断节点是不是已经存在。
                this.buildDataPermissionTree4VipType((DataPermissionNode) cur.getSubDataNodeList().stream()
                    .filter(v -> dataPermissionNode.equals(v))
                    .findFirst().orElseGet(() -> dataPermissionNode), vipTypeLayeredNodes
                );
            }
        }
    }

    private void addAdditionalEndKey(List<QiyuePlatformPair> endKeyPairs) {
        QiyuePlatformPair youXianWangTV = QiyuePlatformPair.builder()
            .level(22)
            .id("有线网TV")
            .name("有线网TV")
            .build();
        endKeyPairs.add(youXianWangTV);
        QiyuePlatformPair dianXinYunYingShangTV = QiyuePlatformPair.builder()
            .level(22)
            .id("电信运营商TV")
            .name("电信运营商TV")
            .build();
        endKeyPairs.add(dianXinYunYingShangTV);

        QiyuePlatformPair changshangTV = QiyuePlatformPair.builder()
            .level(22)
            .id("厂商TV")
            .name("厂商TV")
            .build();
        endKeyPairs.add(changshangTV);
    }

    private boolean isIqiyiVideoBizPro(DataPermissionNode cur) {
        if (cur == null) {
            return false;
        }
        return "爱奇艺视频".equals(cur.getId()) && "爱奇艺视频".equals(cur.getName());
    }

    /**
     * 维护层级结构渠道树
     */
    private void buildLayeredDataPermission4Fv(final DataPermissionNode dataPermissionNode, Map<LabelEnum, List<DataPermissionNode>> fvLayeredNodes) {
        List<DataPermissionNode> dataPermissionNodes = fvLayeredNodes.get(dataPermissionNode.getLabel());
        if (dataPermissionNodes == null) {
            fvLayeredNodes.put(dataPermissionNode.getLabel(), new ArrayList<>());
            dataPermissionNodes = fvLayeredNodes.get(dataPermissionNode.getLabel());
        }
        dataPermissionNodes.add(dataPermissionNode);
    }

    /**
     * 维护层级结构渠道树
     * @param dataPermissionNode
     */
    private void buildLayeredDataPermission4Pro(final DataPermissionNode dataPermissionNode, Map<LabelEnum, List<DataPermissionNode>> proLayeredNodes) {
        List<DataPermissionNode> dataPermissionNodes = proLayeredNodes.get(dataPermissionNode.getLabel());
        if (dataPermissionNodes == null) {
            proLayeredNodes.put(dataPermissionNode.getLabel(), new ArrayList<>());
            dataPermissionNodes = proLayeredNodes.get(dataPermissionNode.getLabel());
        }
        dataPermissionNodes.add(dataPermissionNode);
    }

    /**
     * 维护层级结构渠道树
     * @param dataPermissionNode
     */
    private void buildLayeredDataPermission4VipType(final DataPermissionNode dataPermissionNode, Map<LabelEnum, List<DataPermissionNode>> vipTypeLayeredNodes) {
        List<DataPermissionNode> dataPermissionNodes = vipTypeLayeredNodes.get(dataPermissionNode.getLabel());
        if (dataPermissionNodes == null) {
            vipTypeLayeredNodes.put(dataPermissionNode.getLabel(), new ArrayList<>());
            dataPermissionNodes = vipTypeLayeredNodes.get(dataPermissionNode.getLabel());
        }
        dataPermissionNodes.add(dataPermissionNode);
    }


    private void buildLayeredDataPermission4Common(final DataPermissionNode dataPermissionNode, Map<LabelEnum, List<DataPermissionNode>> commonLayeredNodes) {
        List<DataPermissionNode> dataPermissionNodes = commonLayeredNodes.get(dataPermissionNode.getLabel());
        if (dataPermissionNodes == null) {
            commonLayeredNodes.put(dataPermissionNode.getLabel(), new ArrayList<>());
            dataPermissionNodes = commonLayeredNodes.get(dataPermissionNode.getLabel());
        }
        dataPermissionNodes.add(dataPermissionNode);
    }


    private DataPermissionNode createDataPermissionNode4Fv(FvChannelPair fvChannelPair, DataPermissionNode parent) {
        return DataPermissionNode.builder()
            .name(fvChannelPair.getName())
            .id(fvChannelPair.getId())
            .label(LabelEnum.levelOf(fvChannelPair.getLevel()))
            .teamId(fvChannelPair.getTeamId())
            .teamName(fvChannelPair.getTeamName())
            .parent(parent)
            .build();
    }

    private DataPermissionNode createDataPermissionNode4Pro(QiyuePlatformPair qiyuePlatformPair, DataPermissionNode parent) {
        return DataPermissionNode.builder()
            .name(qiyuePlatformPair.getName())
            .id(qiyuePlatformPair.getId())
            .label(LabelEnum.levelOf(qiyuePlatformPair.getLevel()))
            .parent(parent)
            .build();
    }

    private DataPermissionNode createDataPermissionNode4VipType(VipTypePair vipTypePair, DataPermissionNode parent) {
        return DataPermissionNode.builder()
            .name(vipTypePair.getName())
            .id(vipTypePair.getId())
            .label(LabelEnum.levelOf(vipTypePair.getLevel()))
            .parent(parent)
            .build();
    }


    private DataPermissionNode createDataPermissionNode4Common(CommonPair commonPair, DataPermissionNode parent) {
        return DataPermissionNode.builder()
            .name(commonPair.getName())
            .id(commonPair.getId())
            .label(LabelEnum.levelOf(commonPair.getLevel()))
            .parent(parent)
            .build();
    }

    private DataPermissionNode createDataPermissionNode4VipCardType(VipTypePair vipTypePair, DataPermissionNode parent) {
        return DataPermissionNode.builder()
            .name(vipTypePair.getName())
            .id(vipTypePair.getId())
            .label(LabelEnum.levelOf(vipTypePair.getLevel()))
            .parent(parent)
            .build();
    }

    /**
     * 获取渠道基础数据权限
     */
    public DataPermissionDTO getFvBaseDataPermission(LabelEnum labelEnum) {
        labelEnum = labelEnum == null ? LabelEnum.datePermissionCtrlLabel() : labelEnum;
        return this.getBaseDatePermission(fvLayeredDataPermissionCache, labelEnum);
    }

    @Override
    public DataPermissionDTO getProBaseDataPermission() {
        return this.getBaseDatePermission(proLayeredDataPermissionCache, LabelEnum.P0);
    }

    @Override
    public DataPermissionDTO getVipTypeBaseDataPermission() {
        return this.getBaseDatePermission(vipTypeLayeredDataPermissionCache, LabelEnum.VT0);
    }

    @Override
    public DataPermissionDTO getCompetitorMonitorBaseDataPermission() {
        return this.getBaseDatePermission(competitorMonitorLayeredDataPermissionCache, LabelEnum.COMPETITOR_MONITOR);
    }

    private DataPermissionDTO getBaseDatePermission(LoadingCache<LabelEnum, DataPermissionDTO> loadingCache, LabelEnum labelEnum) {
        try {
            readLock.lock();
            DataPermissionDTO dataPermissionDTO = loadingCache.get(labelEnum);
            Gson gson = new Gson();
            String json = gson.toJson(dataPermissionDTO);
            return gson.fromJson(json, DataPermissionDTO.class);
        } catch (ExecutionException e) {
            throw new BizRuntimeException(CodeEnum.BASE_INIT_ERROR);
        } finally {
            readLock.unlock();
        }
    }

    public List<DataPermissionNode> getBaseDataLayeredDataPermission(ConditionEnum conditionEnum, LabelEnum labelEnum) {
        List<DataPermissionNode> dataPermissionNodes = null;
        try {
            readLock.lock();
            switch (conditionEnum) {
                case BUSINESS_LEVEL:
                    dataPermissionNodes = this.fvLayeredNodes.get(labelEnum);
                    break;
                case PRO:
                    dataPermissionNodes = this.proLayeredNodes.get(labelEnum);
                    break;
                case VIP_TYPE:
                    dataPermissionNodes = this.vipTypeLayeredNodes.get(labelEnum);
                    break;
            }
            return dataPermissionNodes;
        } finally {
            readLock.unlock();
        }
    }

    @Override
    public DataPermissionNode getBaseDataLayeredDataPermission4One(ConditionEnum conditionEnum, LabelEnum labelEnum, String permissionId) {
        final List<DataPermissionNode> baseDataLayeredDataPermission = this.getBaseDataLayeredDataPermission(conditionEnum, labelEnum);
        return CollectionUtils.isEmpty(baseDataLayeredDataPermission) ? null : baseDataLayeredDataPermission.stream()
            .filter(v -> v.getId().equals(permissionId))
            .findAny()
            .orElseGet(() -> null);
    }

    private void createFvDataPermission(DataPermissionDTO ret, DataPermissionNode curNode, LabelEnum labelEnum) {
        if (labelEnum == curNode.getLabel()) {
            ret.setDataPermissionDTOList(null);
        } else {
            for (DataNode node : curNode.getSubDataNodeList()) {
                final DataPermissionDTO dataPermissionDTO = DataPermissionNodeTrans.copyDataPermissionNode((DataPermissionNode) node);
                ret.addDataNode(dataPermissionDTO);
                createFvDataPermission(dataPermissionDTO, (DataPermissionNode) node, labelEnum);
            }
        }
    }

    private void createProOrVipTypeDataPermission(DataPermissionDTO ret, DataPermissionNode curNode) {
        if (curNode.getLabel().getNextLabelEnum() == null) {
            ret.setDataPermissionDTOList(null);
        } else {
            for (DataNode node : curNode.getSubDataNodeList()) {
                final DataPermissionDTO dataPermissionDTO = DataPermissionNodeTrans.copyDataPermissionNode((DataPermissionNode) node);
                ret.addDataNode(dataPermissionDTO);
                createProOrVipTypeDataPermission(dataPermissionDTO, (DataPermissionNode) node);
            }
        }
    }

    private synchronized boolean setUpdateFvNodes() {
        if (!this.updateNodes) {
            this.updateNodes = true;
            return true;
        }
        return false;
    }

    private synchronized boolean setUpdateDimensionLayers() {
        if (!this.updateDimensionLayer) {
            this.updateDimensionLayer = true;
            return true;
        }
        return false;
    }
}