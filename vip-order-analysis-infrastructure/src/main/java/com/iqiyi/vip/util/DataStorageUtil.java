package com.iqiyi.vip.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;

import com.iqiyi.oss.OSS;
import com.iqiyi.oss.model.CannedAccessControlList;
import com.iqiyi.oss.model.OSSObjectMetadata;
import com.iqiyi.oss.model.PutObjectResult;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.enums.CodeEnum;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataStorageUtil {

    @Resource
    private OSS ossClient;

    @Value("${oss.client.bucket}")
    private String bucketName;

    @Value("${oss.client.path}")
    private String path;


    public CommonResult uploadResultFileWithMetaData(File file, String fileName) {
        try {
            OSSObjectMetadata metadata = new OSSObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);

            String objName = path + fileName;
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, objName, file, metadata);
            log.info("analysis result upload result:{}", putObjectResult.getETag());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("analysis result upload error, fileName:{}, error:", fileName, e);
            return CommonResult.create(CodeEnum.OSS_ERROR);
        }
    }
}
