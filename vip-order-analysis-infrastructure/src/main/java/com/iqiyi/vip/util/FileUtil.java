package com.iqiyi.vip.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FileUtil {

    /**
     * 删除本地文件
     *
     * @param absoluteFilePath 绝对路径
     */
    public void cleanLocalFile(String absoluteFilePath) {
        File file = new File(absoluteFilePath);
        if (!file.exists() || !file.isFile() || !file.delete()) {
            log.warn("[order-analysis][delete file: {}] fail. ", absoluteFilePath);
        }
    }

    /**
     * 创建本地文件
     *
     * @param queryResult 数据
     * @param localFile 本地文件路径
     */
    public void createFile(List<String> queryResult, String localFile) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(localFile))) {
            for (String orderId : queryResult) {
                writer.write(orderId);
                writer.newLine();
            }
            writer.flush(); // 刷新缓冲区，将数据写入文件
        } catch (FileNotFoundException e) {
            log.info("文件未找到：" + e.getMessage());
        } catch (IOException e) {
            log.info("IO错误：" + e.getMessage());
        }
    }
}
