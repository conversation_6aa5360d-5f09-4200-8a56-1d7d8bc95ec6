package com.iqiyi.vip.util;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Map.Entry;

import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;
import com.iqiyi.vip.enums.MetricTypeEnum;

/**
 * Created at: 2022-11-05
 *
 * <AUTHOR>
 */
public class EagleMeterReporter {

    public static void reportValue(MetricTypeEnum metricTypeEnum,
        String metricName,
        Map<String, String> labels,
        double value
    ) {
        if (StringUtils.isBlank(metricName) || MapUtils.isEmpty(labels)) {
            return;
        }
        EagleParam eagleParam = new EagleParam(metricName);
        for (Entry<String, String> entry : labels.entrySet()) {
            eagleParam.tag(entry.getKey(), entry.getValue());
        }
        switch (metricTypeEnum) {
            case Counter:
            EagleMonitor.counterInc(eagleParam, value);
                break;
            case Gauge:
            EagleMonitor.gaugeSetValue(eagleParam, value);
                break;
            default:
                break;
        }
    
    }

}
