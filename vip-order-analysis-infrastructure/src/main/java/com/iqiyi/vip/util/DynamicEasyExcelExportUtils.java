package com.iqiyi.vip.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.iqiyi.vip.domain.entity.AnalysisTarget;
import com.iqiyi.vip.domain.entity.AnalysisTargetGroup;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.target.ExcelSheetDataDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;
import com.iqiyi.vip.handler.base.ExcelGenerateNameHandler;
import org.apache.commons.lang3.ObjectUtils;

/**
 * https://easyexcel.opensource.alibaba.com/ Excel工具
 *
 * @className DynamicEasyExcelExportUtils
 * @description
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Slf4j
public class DynamicEasyExcelExportUtils {


    private static final String DEFAULT_SHEET_NAME = "sheet1";

    /**
     * 动态生成导出模版(单表头)
     * @param headColumns 列名称
     * @return            excel文件流
     */
    public static byte[] exportTemplateExcelFile(List<String> headColumns){
        List<List<String>> excelHead = Lists.newArrayList();
        headColumns.forEach(columnName -> { excelHead.add(Lists.newArrayList(columnName)); });
        byte[] stream = createExcelFile(excelHead, new ArrayList<>());
        return stream;
    }

    /**
     * 动态生成模版(复杂表头)
     *
     * @param excelHead 列名称
     */
    public static byte[] exportTemplateExcelFileCustomHead(List<List<String>> excelHead) {
        byte[] stream = createExcelFile(excelHead, new ArrayList<>());
        return stream;
    }

    public static void doWriteExcel(String fileName, String sheetName, int sheetIndex, List<List<Object>> excelRows, List<List<String>> excelHead) {
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(fileName).build()) {
            // 去调用写入,这里我调用了五次，实际使用时根据数据库分页的总的页数来。这里最终会写到5个sheet里面
            // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样。这里注意DemoData.class 可以每次都变，我这里为了方便 所以用的同一个class
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, sheetName).head(excelHead).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(excelRows, writeSheet);
        }
    }

    /**
     * 多sheetExcel文件导出
     */
    @Deprecated
    public static byte[] exportExcelFile(List<ExcelSheetDataDTO> excelSheetDataDTOS, AnalysisTaskExecuteDTO executeDTO, Function<String, AnalysisTarget> targetFunction) {
        //获取列名称
        if (CollectionUtils.isEmpty(excelSheetDataDTOS)) {
            return new byte[]{};
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        int sheetIndex = 0;
        for (ExcelSheetDataDTO excelSheetDataDTO : excelSheetDataDTOS) {
            LinkedHashMap<String, String> headColumnMap = excelSheetDataDTO.getHeadColumnMap();
            List<List<String>> excelHead = new ArrayList<>();
            if(MapUtils.isNotEmpty(headColumnMap)){
                //key为匹配符，value为列名，如果多级列名用逗号隔开
                headColumnMap.entrySet().forEach(entry -> {
                    excelHead.add(Lists.newArrayList(entry.getValue().split(",")));
                });
            }

            List<List<Object>> excelRows = new ArrayList<>();
            List<LinkedHashMap<String, Object>> dataList = excelSheetDataDTO.getDataList();
            if(MapUtils.isNotEmpty(headColumnMap) && CollectionUtils.isNotEmpty(dataList)){
                for (Map<String, Object> dataMap : dataList) {
                    List<Object> rows = new ArrayList<>();
                    headColumnMap.entrySet().forEach(headColumnEntry -> {
                        if(dataMap.containsKey(headColumnEntry.getKey())){
                            Object data = dataMap.get(headColumnEntry.getKey());
                            rows.add(data);
                        }
                    });
                    excelRows.add(rows);
                }
            }
            ConditionParamContext conditionParamContext = executeDTO.getConditionParamContext();
            Long payStartTime = conditionParamContext.getPayStartTime();
            Long payEndTime = conditionParamContext.getPayEndTime();
            String sheetName = ExcelGenerateNameHandler.getSheetName(payStartTime, payEndTime, executeDTO.getBusinessTypeId(), targetFunction, excelSheetDataDTO);
            WriteSheet sheet = EasyExcel.writerSheet(sheetIndex, sheetName).head(excelHead).build();
            sheetIndex = sheetIndex + 1;
            excelWriter.write(excelRows, sheet);
        }
        excelWriter.finish();
        return outputStream.toByteArray();
    }

    /**
     * 生成文件
     * @param excelHead
     * @param excelRows
     * @return
     */
    private static byte[] createExcelFile(List<List<String>> excelHead, List<List<Object>> excelRows){
        try {
            if(CollectionUtils.isNotEmpty(excelHead)){
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                EasyExcel.write(outputStream).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(excelHead)
                    .sheet(DEFAULT_SHEET_NAME)
                    .doWrite(excelRows);
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            log.error(
                "动态生成excel文件失败，headColumns：" + JSONArray.toJSONString(excelHead) + "，excelRows：" + JSONArray.toJSONString(excelRows), e);
        }
        return null;
    }

    public static String generateResultExcelFile2(List<ExcelSheetDataDTO> excelSheetDataDTOS, String operator, String path, Function<String, AnalysisTarget> targetFunction
            , Function<String, AnalysisTargetGroup> targetGroupFunction, Long taskId, AnalysisTaskExecuteDTO executeDTO, Integer excelWidthAdjust) {
        String fileName = getFileName(operator, taskId, executeDTO, targetGroupFunction);
        String absoluteFilePath = path + "/" + fileName;
        ExcelGenerateNameHandler.exportExcel(taskId, executeDTO.getBusinessTypeId(), absoluteFilePath, excelSheetDataDTOS, executeDTO, targetFunction, excelWidthAdjust);
        return fileName;
    }

    /**
     * 有任务名时优先用任务名，无任务名时用指标分组名
     */
    public static String getFileName(String operator, Long taskId, AnalysisTaskExecuteDTO executeDTO, Function<String, AnalysisTargetGroup> targetGroupFunction) {
        List<String> names = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(executeDTO.getTargetCodes())) {
            for (String code : executeDTO.getTargetCodes()) {
                AnalysisTargetGroup analysisTargetGroup = targetGroupFunction.apply(code);
                if (ObjectUtils.isNotEmpty(analysisTargetGroup)) {
                    names.add(analysisTargetGroup.getName());
                }
            }
        }
        String fileNamePattern = "%s_%s_%s.xlsx";
        String fileNamePatternTaskName = "%s_%s.xlsx";
        String fileName;
        if (StringUtils.isNotBlank(executeDTO.getTaskName())) {
            fileName = String.format(fileNamePatternTaskName, taskId, executeDTO.getTaskName());
        } else {
            //names 转成,分隔的字符串
            fileName = String.format(fileNamePattern, taskId, Joiner.on(",").join(names.stream().distinct().collect(Collectors.toList())), operator);
        }
        return fileName;
    }

    @Deprecated
    public static String generateResultExcelFile(List<ExcelSheetDataDTO> excelSheetDataDTOS, String operator, String path, Function<String, AnalysisTarget> targetFunction
        , Function<String, AnalysisTargetGroup> targetGroupFunction, Long taskId, AnalysisTaskExecuteDTO executeDTO) {
        try {
            String fileNamePattern = "%s_%s_%s.xlsx";
            String sheetNames = ExcelGenerateNameHandler.getExcelName(executeDTO.getBusinessTypeId(), excelSheetDataDTOS, targetGroupFunction);
            String fileName = String.format(fileNamePattern, taskId, sheetNames, operator);
            byte[] bytes = exportExcelFile(excelSheetDataDTOS, executeDTO, targetFunction);
            String absoluteFilePath = path + "/" + fileName;
            FileOutputStream fileOutputStream = new FileOutputStream(new File(absoluteFilePath));
            fileOutputStream.write(bytes);
            fileOutputStream.close();
            return fileName;
        } catch (Exception e) {
            log.error("generateResultExcelFile error! excelSheetDataDTOS:{}, operator:{}， error:", excelSheetDataDTOS, operator, e);
            return null;
        }
    }

    private static String bucketName = "vip-order-analysis";
    private static String ak = "cfsuchh2p49v269mkgg926gb43leo9rp";
    private static String sk = "mx24p3u4fpo0mqxgg6n1g9no50cvylku";
    private static String endpoint = "http://bj.oss.qiyi.storage";
    private static String key_prefix = "target/result/test/";

    public static void main(String[] args) throws IOException {
        //导出包含数据内容的文件
        LinkedHashMap<String, String> headColumnMap = Maps.newLinkedHashMap();
        headColumnMap.put("className","班级");
        headColumnMap.put("name","姓名");
        headColumnMap.put("sex","性别");
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("className", "一年级");
            dataMap.put("name", "张三" + i);
            dataMap.put("sex", "男");
            dataList.add(dataMap);
        }
    }
}

