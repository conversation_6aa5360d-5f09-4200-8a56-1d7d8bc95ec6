package com.iqiyi.vip.mapper;

import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2022/3/23 下午 07:04
 */
@Component
public class AutoRenewUserMapper {

    private Connection creatConnection() throws SQLException {
        Properties properties = new Properties();
        properties.setProperty("hiveconf.mapreduce.job.queuename", "root.vip.marketing_streaming");
        return DriverManager.getConnection(
            "************************************************************************************************************************************;",
            properties);
    }
}
