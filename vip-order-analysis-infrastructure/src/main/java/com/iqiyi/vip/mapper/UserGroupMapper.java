package com.iqiyi.vip.mapper;

import com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO;
import com.iqiyi.vip.po.UserGroup;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface UserGroupMapper {
    int deleteByPrimaryKey(Long id,String operator);

    int insert(UserGroup record);

    int insertSelective(UserGroup record);

    UserGroup selectByPrimaryKey(Integer id);

    Integer selectCount(UserGroupPageQueryDTO pageQryDTO);

    List<UserGroup> selectList(UserGroupPageQueryDTO pageQryDTO);

    int updateByPrimaryKeySelective(UserGroup record);

    int updateByPrimaryKey(UserGroup record);
}