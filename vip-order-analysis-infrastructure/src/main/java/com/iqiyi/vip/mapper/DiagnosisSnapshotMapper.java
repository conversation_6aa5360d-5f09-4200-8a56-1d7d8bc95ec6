package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.DiagnosisSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DiagnosisSnapshotMapper {
    DiagnosisSnapshot findById(@Param("id") Long id);

    List<DiagnosisSnapshot> findByTaskId(@Param("taskId") Long taskId);

    List<DiagnosisSnapshot> findByUniqueId(@Param("uniqueId") String uniqueId);

    int insert(DiagnosisSnapshot diagnosisSnapshot);

    int update(DiagnosisSnapshot diagnosisSnapshot);
}
