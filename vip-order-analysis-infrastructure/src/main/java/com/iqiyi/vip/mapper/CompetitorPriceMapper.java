package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.po.competitor.CompetitorPricePO;

@Mapper
public interface CompetitorPriceMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CompetitorPricePO record);

    int batchInsert(List<CompetitorPricePO> records);

    int updateByPrimaryKey(CompetitorPricePO record);

    List<String> selectAllBrand();

    CompetitorDateRangeDTO selectDateRange();

    List<String> selectAllUserStatus(@Param("brand") String brand, @Param("date") String date);

    CompetitorPricePO selectByPrimaryKey(Long id);

    List<CompetitorPricePO> search(@Param("brand") String brand, 
                                 @Param("userStatus") String userStatus,
                                 @Param("startTime") String startTime,
                                 @Param("endTime") String endTime,
                                 @Param("date") String date);
}