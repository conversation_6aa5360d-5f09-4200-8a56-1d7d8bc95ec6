package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.domain.entity.AvailableDt;

@Mapper
public interface AvailableDtMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(AvailableDt record);

    int insertSelective(AvailableDt record);

    AvailableDt selectByPrimaryKey(Integer id);

    AvailableDt selectMaxDate(@Param("themeType") Integer themeType);

    AvailableDt selectMaxDateByThemeTypeAndThemeSubType(@Param("themeType") Integer themeType, @Param("themeSubType") Integer themeSubType);

    List<AvailableDt> selectDateRangeByThemeTypeAndThemeSubType(@Param("themeType") Integer themeType, @Param("themeSubType") Integer themeSubType);

    AvailableDt selectByTableDt(String dt, @Param("themeType") Integer themeType);

    AvailableDt selectByTableDtAndThemeTypeAndThemeSubType(@Param("dt") String dt, @Param("themeType") Integer themeType, @Param("themeSubType") Integer themeSubType);

    int updateByPrimaryKeySelective(AvailableDt record);

    int updateByPrimaryKey(AvailableDt record);

    int updateStatusByDt(String dt, @Param("themeType") Integer themeType, Integer status);
}