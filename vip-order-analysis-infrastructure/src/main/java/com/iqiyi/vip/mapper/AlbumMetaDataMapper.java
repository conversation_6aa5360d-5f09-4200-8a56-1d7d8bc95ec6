package com.iqiyi.vip.mapper;
import com.iqiyi.vip.domain.entity.AlbumMetaDataDO;
import com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO;
import com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface AlbumMetaDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AlbumMetaDataDO record);

    int insertSelective(AlbumMetaDataDO record);

    AlbumMetaDataDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AlbumMetaDataDO record);


    int selectCount(AlbumMetaDataPageQueryDTO albumQuery);

    List<AlbumMetaDataDO> getByRemainTime(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<AlbumMetaDataDO> getPageQuery(AlbumMetaDataPageQueryDTO query);

    int updateMetaData(UpdateAlbumMetaDataDTO updateDTO);
}