package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.po.competitor.CompetitorUiPO;

@Mapper
public interface CompetitorUiMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CompetitorUiPO record);

    int updateByPrimaryKey(CompetitorUiPO record);

    List<String> selectAllBrand();

    List<String> selectAllUserStatus(@Param("brand") String brand, @Param("date") String date);

    List<String> selectAllClientVersion();

    CompetitorDateRangeDTO selectDateRange();

    List<String> selectAllPage(@Param("brand") String brand, @Param("date") String date);

    CompetitorUiPO selectByPrimaryKey(Long id);

    List<CompetitorUiPO> search(@Param("date") String date, @Param("brand") String brand,
        @Param("clientVersion") String clientVersion, @Param("page") String page, @Param("userStatus") String userStatus);
}