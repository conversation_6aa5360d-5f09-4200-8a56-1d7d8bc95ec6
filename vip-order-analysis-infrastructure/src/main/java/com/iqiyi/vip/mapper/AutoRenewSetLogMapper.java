package com.iqiyi.vip.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import com.iqiyi.vip.dto.pie.AutoRenewCancelSceneDTO;

/**
 * <AUTHOR>
 * @date 2022/3/28 下午 04:08
 */
@Component
@Slf4j
public class AutoRenewSetLogMapper implements InitializingBean {

    private final String cancelNumSql = "  SELECT\n"
        + "  count(distinct a.user_id) \n"
        + "FROM \n"
        + "    boss.boss_dut_renew_set_log a \n"
        + "  inner JOIN   boss.hive_boss_dut_user_new   b on a.user_id = b.user_id \n"
        + "  and a.dt = '%s' \n"
        + "  and b.dt = '%s' \n"
        + "  and a.operator = 0 \n"
        + "  and a.operate_time >= '%s'\n"
        + "  and b.deadline >= '%s' \n"
        + "  and  b.deadline <= '%s'\n"
        + "  and b.auto_renew = 0 \n"
        + "  and a.type not in (15,16,28,53,99,100,114,115,122,123,186,187,130,131,154,155,138,139,178,179,162,163,194,195,146,147,170,171,202,203,210,211,218,219,106,361,363,362,926,933,934,965,992)";
    private final String cancelSceneSql = "SELECT get_json_object(description, '$.scene') scene, count(1) as num FROM boss.boss_dut_renew_set_log "
        + "WHERE dt = '%s' and operator = 0 and operate_time >= '%s' and operate_time <= '%s' group by get_json_object(description, '$.scene') order by num desc";

    private Connection conn;

    private Connection creatConnection() throws SQLException {
        Properties properties = new Properties();
        properties.setProperty("hiveconf.mapreduce.job.queuename", "root.vip.nova");
        properties.setProperty("useSpark3", "true");
        return DriverManager.getConnection(
            "**********************************************************************************************************************************;",
            properties);
    }

    public int getCancelNumByRange(String dt, String operateTime, String deadlineStartTime, String deadlineEndTime) throws SQLException {
        log.info("getCancelNumByRange start at:{}", DateUtil.now());
        String querySql = String.format(cancelNumSql, dt, dt, operateTime, deadlineStartTime, deadlineEndTime);
        ResultSet resultSet = executeQuery(querySql);
        if (resultSet != null && resultSet.next()) {
            int cancelNum = resultSet.getInt(1);
            log.info("getCancelNumByRange result:{}, sql:{}", cancelNum, querySql);
            return cancelNum;
        }
        return 0;
    }

    private ResultSet executeQuery(String sql) {
        try {
            log.info("autoRenewSetLogMapper executeQuery at:{}, sql:{}", DateUtil.now(), sql);
            TimeInterval timer = DateUtil.timer();
            conn = creatConnection();
            Statement statement = conn.createStatement();
            ResultSet resultSet = statement.executeQuery(sql);
            log.info("autoRenewSetLogMapper executeQuery at:{}, cost:{}s, sql:{}", DateUtil.now(), timer.intervalSecond(), sql);
            return resultSet;
        } catch (Exception e) {
            log.error("getCancelNumByRange error:", e);
        }
        return null;
    }

    public List<AutoRenewCancelSceneDTO> getCancelSceneData(String dt, String beginTime, String endTime) throws SQLException {
        log.info("getCancelSceneData start at:{}", DateUtil.now());
        TimeInterval timer = DateUtil.timer();
        String querySql = String.format(cancelSceneSql, dt, beginTime, endTime);
        ResultSet rs = executeQuery(querySql);
        List<AutoRenewCancelSceneDTO> cancelSceneDtoList = new ArrayList<>();
        while (rs.next()) {
            AutoRenewCancelSceneDTO cancelSceneDto = new AutoRenewCancelSceneDTO();
            cancelSceneDto.setName(rs.getString(1));
            cancelSceneDto.setValue(rs.getInt(2));
            cancelSceneDtoList.add(cancelSceneDto);
        }
        log.info("getCancelSceneData start at:{}s, cost:{}", DateUtil.now(), timer.intervalSecond());
        return cancelSceneDtoList;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
//        conn = creatConnection();
    }
}
