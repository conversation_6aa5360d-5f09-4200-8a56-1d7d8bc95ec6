package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.AnalysisTarget;
import com.iqiyi.vip.po.query.AnalysisTargetPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 指标管理
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface AnalysisTargetMapper {

    int insertSelective(AnalysisTarget target);

    int updateSelective(AnalysisTarget target);

    int delete(@Param("code") String code);

    int deleteById(@Param("id") Long id);


    AnalysisTarget selectByCode(@Param("code") String code);

    AnalysisTarget selectByCodeAndBusinessTypeId(@Param("code") String code, @Param("businessTypeId") Integer businessTypeId);

    AnalysisTarget selectById(@Param("id") Long id);

    List<AnalysisTarget> selectList(AnalysisTargetPageQuery query);

    int selectCount(AnalysisTargetPageQuery query);

    List<AnalysisTarget> selectByCodes(List<String> codes);

    List<AnalysisTarget> selectAll(@Param("businessTypeId") Integer businessTypeId, @Param("themeType") Integer themeType);
}