package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.iqiyi.vip.po.AnalysisDimensionGroup;

/**
 * 维度分组管理
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface AnalysisDimensionGroupMapper {

    AnalysisDimensionGroup selectById(@Param("id") Long id);

    List<AnalysisDimensionGroup> selectAll();

    List<AnalysisDimensionGroup> selectByBusinessType(@Param("businessTypeId") Integer businessTypeId, @Param("themeType") Integer themeType);
}