package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;
import com.iqiyi.vip.po.competitor.CompetitorShopPO;

@Mapper
public interface CompetitorShopMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CompetitorShopPO record);

    int updateByPrimaryKey(CompetitorShopPO record);

    List<String> selectAllPlatform();

    List<String> selectAllBrand(@Param("platform") String platform, @Param("date") String date);

    CompetitorDateRangeDTO selectDateRange();

    CompetitorShopPO selectByPrimaryKey(Long id);

    List<CompetitorShopPO> search(@Param("date") String date, @Param("platform") String platform,
        @Param("brand") String brand, @Param("storeType") Integer storeType);
} 