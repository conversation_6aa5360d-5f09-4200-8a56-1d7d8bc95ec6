package com.iqiyi.vip.mapper;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.domain.entity.ConditionCascade;
import com.iqiyi.vip.po.query.ConditionCascadePageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 条件管理
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Mapper
public interface ConditionCascadeMapper {

    Long insertSelective(ConditionCascade cascade);

    int updateSelective(ConditionCascade cascade);

    int deleteByThemeAndBusiness(@Param("themeId") Integer id, @Param("businessId") Integer businessId);

    int deleteByConditionId(Long conditionId);

    ConditionCascade selectByConditions(@Param("themeId") Integer id, @Param("businessId") Integer businessId,
                                        @Param("currentConditionId") Integer currentConditionId, @Param("nextConditionId") Integer nextConditionId);

    List<ConditionCascade> list(@Param("themeId")Integer themeId, @Param("businessId") Integer businessId);

    List<ConditionCascade> selectList(ConditionCascadePageQuery query);

    int selectCount(ConditionCascadePageQuery query);
}