package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.po.BizTargetMonitorConfigPO;
import com.iqiyi.vip.po.query.BizTargetMonitorConfigPageQuery;

@Mapper
public interface BizTargetMonitorConfigMapper {

    int deleteByPrimaryKey(Integer id);

    int resetStatus(@Param("id") Integer id, @Param("status") Integer status);

    int insert(BizTargetMonitorConfigPO record);

    int update(BizTargetMonitorConfigPO record);

    int updateJobIdById(@Param("id") Integer id, @Param("jobId") Integer jobId);

    int updateMonitorIdById(@Param("id") Integer id, @Param("monitorId") Integer monitorId);

    BizTargetMonitorConfigPO selectByPrimaryKey(Integer id);

    int existByName(String name);

    int searchCount(BizTargetMonitorConfigPageQuery query);
    
    List<BizTargetMonitorConfigPO> search(BizTargetMonitorConfigPageQuery query);
    
}