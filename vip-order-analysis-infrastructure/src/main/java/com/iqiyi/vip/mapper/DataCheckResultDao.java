package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataCheckResult;

@Mapper
public interface DataCheckResultDao {
    int deleteByPrimaryKey(Integer id);

    int insert(DataCheckResult record);

    int insertSelective(DataCheckResult record);

    DataCheckResult selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DataCheckResult record);

    int updateByPrimaryKey(DataCheckResult record);

    List<DataCheckResult> getDataCheckResult(DataCheckResult record);
}