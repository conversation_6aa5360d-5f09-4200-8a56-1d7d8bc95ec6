package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;

import com.iqiyi.vip.domain.entity.DataPermission;

import java.util.List;


/**
 * 数据权限
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface DataPermissionMapper {

    List<DataPermission> getDataPermissions(String account);
    List<DataPermission> getDataPermissionsByThemeType(String account, Integer themeType);
    DataPermission getDataPermission(String account, Integer themeType, Integer type);

    int ownedDataPermission(String account);

    int insertDataPermission(DataPermission dataPermission);

    int updateDataPermission(DataPermission dataPermission);
}