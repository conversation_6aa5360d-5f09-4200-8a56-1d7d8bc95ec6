package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.domain.entity.FvStructureDO;
import com.iqiyi.vip.dto.condition.FvChannelPair;

@Mapper
public interface FvStructureMapper {

    int insert(FvStructureDO record);

    int insertSelective(FvStructureDO record);

    List<FvChannelPair> getFvChannels(@Param("businessIdColumn") String businessIdColumn, @Param("businessNameColumn") String businessNameColumn);

    List<FvChannelPair> getFvChannelsByTeamId(@Param("businessIdColumn") String businessIdColumn, @Param("businessNameColumn") String businessNameColumn, Integer teamId);

    List<FvChannelPair> fvChannels(String columns, String whereColumns, String groupByColumns);

    List<FvChannelPair> rootFvChannel();
}