package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.AnalysisTargetGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 指标分组管理
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface AnalysisTargetGroupMapper {

    AnalysisTargetGroup selectById(@Param("id") Integer id);

    List<AnalysisTargetGroup> selectAll();
    List<AnalysisTargetGroup> selectByBusinessTypeAndType(@Param("businessTypeId") Integer businessTypeId, @Param("type") Integer type, @Param("themeType") Integer themeType);
}