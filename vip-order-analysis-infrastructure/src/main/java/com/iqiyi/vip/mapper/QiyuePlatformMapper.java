package com.iqiyi.vip.mapper;

import com.iqiyi.vip.dto.condition.QiyuePlatformPair;
import com.iqiyi.vip.po.QiyuePlatform;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 平台表
 * <AUTHOR>
 * @date 2022-06-27
 */
@Mapper
public interface QiyuePlatformMapper {

    List<QiyuePlatform> selectAll();

    List<QiyuePlatform> selectByCodes(List<String> codes);

    List<QiyuePlatformPair> getQiyuePlatforms(String columns, String whereColumns, String groupByColumns);
}