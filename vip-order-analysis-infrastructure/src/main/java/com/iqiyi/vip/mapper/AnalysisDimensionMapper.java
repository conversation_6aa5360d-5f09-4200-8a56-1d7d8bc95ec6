package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.po.AnalysisDimension;
import com.iqiyi.vip.po.query.AnalysisDimensionPageQuery;


/**
 * 维度管理
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface AnalysisDimensionMapper {

    int insertSelective(AnalysisDimension dimension);

    int updateSelective(AnalysisDimension dimension);

    int delete(@Param("code") String code);

    int deleteById(@Param("id") Long id);

    AnalysisDimension selectByCode(@Param("code") String code);

    AnalysisDimension selectTransferDimension(@Param("code") String code, @Param("businessTypeId") Integer businessTypeId, @Param("themeType") Integer themeType);

    AnalysisDimension selectById(@Param("id") Long id);

    List<AnalysisDimension> selectList(AnalysisDimensionPageQuery query);

    int selectCount(AnalysisDimensionPageQuery query);

    List<AnalysisDimension> selectByCodes(@Param("codes") List<String> codes, Integer businessTypeId, Integer themeType);

    List<AnalysisDimension> selectAll(@Param("businessTypeId") Integer businessTypeId);

    AnalysisDimension selectByThemeType(String code, Integer businessTypeId, Integer themeType);
}