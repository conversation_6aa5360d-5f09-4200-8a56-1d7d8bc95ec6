package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.iqiyi.vip.domain.entity.TaskQueryInfo;

/**
 * <AUTHOR>
 * @className TaskQueryInfoMapper
 * @description
 * @date 2023/1/11
 **/
@Mapper
public interface TaskQueryInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TaskQueryInfo record);

    int insertSelective(TaskQueryInfo record);

    TaskQueryInfo selectByTaskId(long taskId);

    List<TaskQueryInfo> selectByTaskIds(List<Long> taskIds);

    int updateByTaskIdSelective(TaskQueryInfo record);

    int updateByPrimaryKey(TaskQueryInfo record);
}
