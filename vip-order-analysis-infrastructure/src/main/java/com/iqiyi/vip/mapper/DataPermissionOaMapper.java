package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.domain.entity.DataPermissionOa;


/**
 * 数据权限Oa记录
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface DataPermissionOaMapper {

    List<DataPermissionOa> getDataPermissionOaByOaIdentifierAndMessageId(@Param("oaIdentifier") String oaIdentifier, @Param("messageId") String messageId);

    int updateDataPermissionOaAcIdByMessageId(@Param("oaIdentifier") String oaIdentifier, @Param("messageId") String messageId);

    DataPermissionOa getDataPermissionOaById(int id);

    int existApprovingDataPermission(String account);

    int updateDataPermissionOaStatusById(int id, String rejectReason, int status);

    int updateDataPermissionOaStatusByAcc(String oaIdentifier, String rejectReason, int permissionStatus);

    int insertDataPermissionOa(DataPermissionOa dataPermissionOa);

    int insertDataPermissionOas(@Param("dataPermissionOas") List<DataPermissionOa> dataPermissionOas);

}