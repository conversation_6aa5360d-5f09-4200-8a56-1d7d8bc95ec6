package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.iqiyi.vip.po.competitor.CompetitorShopPricePO;
import com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO;

/**
 * 竞品店铺价格Mapper
 */
@Mapper
public interface CompetitorShopPriceMapper {

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int insert(CompetitorShopPricePO record);

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(List<CompetitorShopPricePO> records);

    /**
     * 根据主键更新
     *
     * @param record 记录
     * @return 影响行数
     */
    int updateByPrimaryKey(CompetitorShopPricePO record);

    /**
     * 查询所有平台
     *
     * @return 平台列表
     */
    List<String> selectAllPlatform();

    /**
     * 查询品牌列表
     *
     * @param platform 平台
     * @param date 日期
     * @return 品牌列表
     */
    List<String> selectAllBrand(@Param("platform") String platform, @Param("date") String date);

    /**
     * 查询人群列表
     *
     * @param platform 平台
     * @param brand 品牌
     * @param date 日期
     * @return 人群列表
     */
    List<String> selectAllUserGroup(@Param("platform") String platform, @Param("brand") String brand, @Param("date") String date);

    /**
     * 查询日期范围
     *
     * @return 日期范围
     */
    CompetitorDateRangeDTO selectDateRange();

    /**
     * 根据条件查询
     *
     * @param date 日期
     * @param platform 平台
     * @param brand 品牌
     * @param storeType 店铺类型
     * @param userGroup 人群
     * @return 查询结果
     */
    List<CompetitorShopPricePO> search(
        @Param("date") String date,
        @Param("platform") String platform,
        @Param("brand") String brand,
        @Param("storeType") Integer storeType,
        @Param("userGroup") String userGroup
    );
    
    /**
     * 根据日期范围查询
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param storeType 店铺类型
     * @param platform 平台
     * @param brand 品牌
     * @return 查询结果
     */
    List<CompetitorShopPricePO> searchByDateRange(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("storeType") Integer storeType,
        @Param("platform") String platform,
        @Param("brand") String brand
    );
} 