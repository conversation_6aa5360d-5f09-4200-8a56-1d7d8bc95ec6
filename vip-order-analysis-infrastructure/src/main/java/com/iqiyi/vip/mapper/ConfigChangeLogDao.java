package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

import com.iqiyi.vip.domain.entity.ConfigChangeLog;

@Mapper
public interface ConfigChangeLogDao {

    int deleteByPrimaryKey(Long id);

    int insert(ConfigChangeLog record);

    int insertSelective(ConfigChangeLog record);

    ConfigChangeLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ConfigChangeLog record);

    int updateByPrimaryKey(ConfigChangeLog record);

    List<ConfigChangeLog> getByConfigTypeAndConfigId(@Param("configType") Integer configType, @Param("configId") Integer configId, @Param("version") Integer version);

    int delByConfigTypeAndConfigId(@Param("configType") Integer configType, @Param("configId") Integer configId);
}