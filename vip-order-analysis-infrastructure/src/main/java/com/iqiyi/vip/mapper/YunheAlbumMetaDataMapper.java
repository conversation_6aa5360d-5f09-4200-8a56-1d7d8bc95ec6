package com.iqiyi.vip.mapper;

import com.iqiyi.vip.domain.entity.YunheAlbumMetaDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 云合专辑元数据Mapper
 */
@Mapper
public interface YunheAlbumMetaDataMapper {

    YunheAlbumMetaDataDO selectByPrimaryKey(@Param("albumId") Long albumId);

    int insert(YunheAlbumMetaDataDO record);

    int updateByPrimaryKey(YunheAlbumMetaDataDO record);

    int deleteByPrimaryKey(@Param("albumId") Long albumId);

    List<YunheAlbumMetaDataDO> selectAll();
    
    /**
     * 根据平台和时间范围查询专辑元数据
     * @param platform 平台关键字
     * @param startDate 开始日期字符串
     * @param endDate 结束日期字符串
     * @return 专辑元数据列表
     */
    List<YunheAlbumMetaDataDO> selectByPlatformAndDateRange(
            @Param("platform") String platform,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

//    /**
//     * 根据平台和时间范围查询专辑内容信息
//     * @param platform 平台关键字
//     * @param startDate 开始日期
//     * @param endDate 结束日期
//     * @return 专辑内容信息列表
//     */
//    List<YunheAlbumMetaDataDO> searchByPlatformAndDateRange(
//            @Param("platform") String platform,
//            @Param("startDate") String startDate,
//            @Param("endDate") String endDate);

    /**
     * 根据时间范围和品牌查询专辑内容信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param brand 品牌
     * @return 专辑内容信息列表
     */
    List<YunheAlbumMetaDataDO> searchByDateRange(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("brand") String brand);
} 