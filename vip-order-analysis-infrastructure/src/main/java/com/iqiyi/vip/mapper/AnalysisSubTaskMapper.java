package com.iqiyi.vip.mapper;

import com.iqiyi.vip.domain.entity.AnalysisSubTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/1/4 20:34
 */
@Mapper
public interface AnalysisSubTaskMapper {

    AnalysisSubTaskDO selectByTaskId(@Param("parentTaskId") Long taskId, @Param("userGroupId") Integer userGroupId);

    int insert(AnalysisSubTaskDO task);

    int updateByTask(AnalysisSubTaskDO task);

    int deleteByParentTaskId(Long parentTaskId);

    int deleteByUserGroupId(Integer parentTaskId);
}
