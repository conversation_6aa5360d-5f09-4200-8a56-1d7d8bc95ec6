package com.iqiyi.vip.mapper;

import com.iqiyi.vip.domain.entity.AnalysisCondition;
import com.iqiyi.vip.po.query.AnalysisConditionPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 条件管理
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Mapper
public interface AnalysisConditionMapper {

    int insertSelective(AnalysisCondition condition);

    int updateSelective(AnalysisCondition condition);

    int delete(@Param("code") String code, @Param("operator") String operator);

    AnalysisCondition selectByCode(@Param("code") String code);

    List<AnalysisCondition> batchSelectByCode(@Param("codes") List<String> codes);

    AnalysisCondition selectByName(@Param("name") String name);

    AnalysisCondition selectById(@Param("id") Long id);

    List<AnalysisCondition> selectByIdList(@Param("idList") List<Long> idList);

    List<AnalysisCondition> selectList(AnalysisConditionPageQuery query);

    List<AnalysisCondition> selectAll();

    int selectCount(AnalysisConditionPageQuery query);
}