package com.iqiyi.vip.mapper;

import com.iqiyi.vip.domain.entity.BusinessCondition;
import com.iqiyi.vip.domain.entity.ConditionCascade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 分析类型下的条件
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Mapper
public interface BusinessConditionMapper {

    int batchInsert(@Param("list")List<BusinessCondition> businessConditionList);

    int deleteByThemeAndBusiness(@Param("themeId")Long themeId, @Param("businessId") Long businessId);

    int deleteByConditionId(Long conditionId);

    List<BusinessCondition> selectByThemeAndBusiness(@Param("themeId")Long themeId, @Param("businessId")Long businessId);
}