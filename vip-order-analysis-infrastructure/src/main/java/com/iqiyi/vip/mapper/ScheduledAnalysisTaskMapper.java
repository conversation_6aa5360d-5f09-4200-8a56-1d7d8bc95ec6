package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.iqiyi.vip.domain.entity.AnalysisTaskDO;

@Mapper
public interface ScheduledAnalysisTaskMapper {

    long insertSelective(AnalysisTaskDO record);

    AnalysisTaskDO selectByPrimaryKey(Long id);

    AnalysisTaskDO getByUniqueIdentification(@Param("uniqueIdentification") String uniqueIdentification);

    int reSetTaskStatus(@Param("taskId") Long taskId, @Param("status") Integer status, @Param("result") String result, @Param("executeTime") Integer executeTime);

}