package com.iqiyi.vip.mapper;

import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.condition.VipTypePair;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 *  会员类型表
 * <AUTHOR>
 * @date 2022-06-27
 */
@Mapper
public interface VipTypeMapper {

    List<CodeDescPair> selectAll();

    List<VipTypePair> getVipTypePairs(String columns, String whereColumns, String groupByColumns, String orderByColumns);
}