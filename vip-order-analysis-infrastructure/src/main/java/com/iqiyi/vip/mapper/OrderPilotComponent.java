package com.iqiyi.vip.mapper;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import com.iqiyi.bigdata.pilot.rpc.client.TPilotStatement;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.dto.target.OriginalQueryResultDTO;
import com.iqiyi.vip.dto.target.TargetAnalysisQueryDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;
import com.iqiyi.vip.enums.DataBaseTypeEnum;
import com.iqiyi.vip.enums.GroupTypeEnum;
import com.iqiyi.vip.po.UserGroup;
import com.iqiyi.vip.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午 10:26
 */
@Component
@Slf4j
@Data
public class OrderPilotComponent {

    @Resource(name = "cloudConfig")
    private CloudConfig cloudConfig;

    @Value("${hive.spark.conn.url:}")
    private String hiveSparkConnUrl;

    @Value("${user.group.ck.table:dist_vip_user_group_order_analysis}")
    private String userGroupCKTable;

    @Value("${clickhouseconf.token}")
    private String clickHouseConfToken;

    @Value("${clickhouseconf.database}")
    private String clickHouseConfDataBase;

    @Value("${clickhouseconf.cluster}")
    private String clickHouseConfCluster;

    @Value("${clickhouseconf.project}")
    private String clickHouseConfProject;

    @Value("${clickhouseconf.env}")
    private String clickHouseConfEnv;

    @Value("${clickhouseconf.url}")
    private String clickHouseConfUrl;

    @ConfigJsonValue("${starrocks.conn.conf:{\"hadoopUser\":\"boss\",\"cluster\":\"sr-bdxs-viporder\",\"token\":\"a2ae1c2819be5743dbd63ed924caf7aecbc3eab2\"}}")
    private Map<String, Object> starRocksConnConf;

    @Value("${starrocksconf.url}")
    private String starRocksConfUrl;

    @Value("${local.file.path:/data/logs}")
    private String localFilePath;

    private Connection createDataConnection(String dataBase, Long taskId) throws SQLException {
        if (Objects.equals(DataBaseTypeEnum.STAR_ROCKS.getCode(), dataBase)) {
            return createStarRocksConnection();
        }
        if (DataBaseTypeEnum.CLICK_HOUSE.getCode().equals(dataBase) || Objects.equals("tianyan_ck", dataBase)) {
            return createClickhouseConnection();
        }
        return createHiveConnection(taskId);
    }

    private Connection createStarRocksConnection() throws SQLException {
        Properties properties = new Properties();
        properties.putAll(starRocksConnConf);
        log.info("use StarRocks conn config from cloudConfig:{}", starRocksConnConf);
        return DriverManager.getConnection(starRocksConfUrl, properties);
    }

    private Connection createClickhouseConnection() throws SQLException {
        Properties properties = new Properties();
        String property = cloudConfig.getProperty("clickhouse.conn.conf", null);
        Map<String, Object> configMap = JacksonUtils.parseMap(property);
        for (String key : configMap.keySet()) {
            properties.setProperty(key, MapUtils.getString(configMap, key));
        }
        log.info("use clickhouse conn config from cloudConfig:{}", properties);
        return DriverManager.getConnection(clickHouseConfUrl, properties);
    }

    private Connection createHiveConnection(Long taskId) throws SQLException {
        Properties properties = new Properties();
        String property = cloudConfig.getProperty("hive.spark.conn.conf", null);
        Map<String, Object> configMap = JacksonUtils.parseMap(property);
        for (String key : configMap.keySet()) {
            properties.setProperty(key, MapUtils.getString(configMap, key));
        }
        log.info("use hive conn config from cloudConfig:{}", properties);
        return DriverManager.getConnection(hiveSparkConnUrl, properties);
    }

    public OriginalQueryResultDTO queryTargetResult(TargetAnalysisQueryDTO queryDTO) throws SQLException {
        LinkedHashMap<String, String> columnNameHashMap = new LinkedHashMap<>();
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        String targetCode = queryDTO.getTargetCode();
        String dateYM = queryDTO.getDateYM();
        String groupCode = queryDTO.getGroupCode();
        String dimensionCode = queryDTO.getDimensionCode();
        Integer targetType = queryDTO.getTargetType();
        queryAndGetResult(queryDTO, columnNameHashMap, dataList);
        return new OriginalQueryResultDTO(targetCode, dateYM, groupCode, dimensionCode, columnNameHashMap, dataList, targetType, Boolean.TRUE);
    }

    private void queryAndGetResult(TargetAnalysisQueryDTO queryDTO, LinkedHashMap<String, String> columnNameHashMap, List<LinkedHashMap<String, Object>> dataList)
        throws SQLException {
        Connection connection = null;
        try {
            String dataBase = queryDTO.getDataBase();
            Long taskId = queryDTO.getTaskId();
            connection = createDataConnection(dataBase, taskId);
            executeQuery(connection, queryDTO, columnNameHashMap, dataList);
        } finally {
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                log.error("close conn failed, queryDTO:{}", queryDTO, e);
            }
        }
    }

    public Long generateUserGroupAndGetResult(TargetAnalysisQueryDTO queryDTO, UserGroup userGroup) throws SQLException {
        String insertSQLStr;
        if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroup.getGroupType())) {
            insertSQLStr = "INSERT INTO %s select %s,'',passport_id FROM (%s)"
                + " SETTINGS distributed_product_mode = 'local',max_bytes_before_external_group_by = '100000000000000000000000';";
        } else {
            insertSQLStr = "INSERT INTO %s select %s,order_code,passport_id FROM (%s)"
                + " SETTINGS distributed_product_mode = 'local',max_bytes_before_external_group_by = '100000000000000000000000';";
        }
        String insertSql = String.format(insertSQLStr, userGroupCKTable, userGroup.getId(), queryDTO.getQuerySql());
        return execute(userGroup, insertSql);
    }

    private Long execute(UserGroup userGroup, String insertSql) throws SQLException {
        Connection connection = createDataConnection(DataBaseTypeEnum.CLICK_HOUSE.getCode(), null);
        try {
            String countSQLStr = "SELECT count(1) FROM %s where user_group_id='%s'";
            TPilotStatement statement = (TPilotStatement) connection.createStatement();
            statement.setFetchDuration(500);
            long start = System.currentTimeMillis();
            statement.executeUpdate(insertSql);
            log.info("execute insert user_group_order_analysis cost:" + (System.currentTimeMillis() - start));
            String countSql = String.format(countSQLStr, userGroupCKTable, userGroup.getId());
            start = System.currentTimeMillis();
            Thread.sleep(1000); // 等待数据写入
            ResultSet resultSet = statement.executeQuery(countSql);
            while (resultSet != null && resultSet.next()) {
                return resultSet.getLong(1);
            }
            log.info("execute select count cost:" + (System.currentTimeMillis() - start));
            return null;
        } catch (Exception e) {
            log.error("插入用户分群数据失败! 分群id：{}, 网盘地址：{}, error:", userGroup.getId(), userGroup.getDataUrl(), e);
            throw new RuntimeException("插入用户分群数据失败! 分群id：" + userGroup.getId() + ", 网盘地址：" + userGroup.getDataUrl());
        } finally {
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                log.error("close conn failed, queryDTO:{}", userGroup, e);
            }
        }
    }

    public Long generateUserGroupAndGetResult(UserGroup userGroup) throws SQLException {
        String insertSQLStr;
        if (Objects.equals(GroupTypeEnum.USER_PACKAGE.getCode(), userGroup.getGroupType())) {
            insertSQLStr = "INSERT INTO %s SELECT %s,'',passport_id FROM url('%s', CSV, 'passport_id String')";
        } else {
            insertSQLStr = "INSERT INTO %s SELECT %s,order_code,passport_id FROM url('%s', CSV, 'order_code String, passport_id String')";
        }
        String insertSql = String.format(insertSQLStr, userGroupCKTable, userGroup.getId(), userGroup.getDataUrl());
        return execute(userGroup, insertSql);
    }


    public List<String> getUserGroupResult(Integer userGroupId) {
        Connection connection = null;
        List<String> result = new ArrayList<>();
        try {
            connection = createDataConnection(DataBaseTypeEnum.CLICK_HOUSE.getCode(), null);
            String countSQLStr = "SELECT passport_id FROM %s where user_group_id='%s'";
            TPilotStatement statement = (TPilotStatement) connection.createStatement();
            statement.setFetchDuration(5000);
            long start = System.currentTimeMillis();
            log.info("execute SELECT passport_id from user_group_order_analysis cost:" + (System.currentTimeMillis() - start));
            String countSql = String.format(countSQLStr, userGroupCKTable, userGroupId);
            start = System.currentTimeMillis();
            ResultSet resultSet = statement.executeQuery(countSql);
            while (resultSet != null && resultSet.next()) {
                Object data = resultSet.getObject(1);
                result.add(String.valueOf(data));
            }
            log.info("execute select count cost:" + (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("获取用户分群数据失败! 分群id：{}, error:", userGroupId, e);
            throw new RuntimeException("查询用户分群数据失败! 分群id：" + userGroupId);
        } finally {
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                log.error("close conn failed, queryDTO:{}", userGroupId, e);
            }
        }
        return result;
    }

    private void executeQuery(Connection conn, TargetAnalysisQueryDTO queryDTO, LinkedHashMap<String, String> columnNameHashMap, List<LinkedHashMap<String, Object>> dataList)
        throws SQLException {
        String querySql = queryDTO.getQuerySql();
        Long taskId = queryDTO.getTaskId();
        String targetCode = queryDTO.getTargetCode();
        try {
            TPilotStatement statement = (TPilotStatement) conn.createStatement();
            statement.setFetchDuration(500);
            if (Objects.equals(DataBaseTypeEnum.STAR_ROCKS.getCode(), queryDTO.getDataBase())) {
                statement.setQueryTimeout(30);
            }
            ResultSet resultSet = statement.executeQuery(querySql);
            ResultSetMetaData rsMetaData = resultSet.getMetaData();
            if (rsMetaData == null) {
                return;
            }
            int columnCount;
            columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                columnNameHashMap.put(columnName, columnName);
            }
            log.info("resultSet columnName:{}", columnNameHashMap.keySet());
            if (MapUtils.isEmpty(columnNameHashMap)) {
                return;
            }
            while (resultSet.next()) {
                LinkedHashMap<String, Object> rowDataMap = new LinkedHashMap<>();
                for (String columnName : columnNameHashMap.keySet()) {
                    rowDataMap.put(columnName, resultSet.getObject(columnName));
                }
                dataList.add(rowDataMap);
            }
            log.info("result size:{}, taskId:{}, targetCode:{}", dataList.size(), taskId, targetCode);
        } catch (Exception e) {
            log.error("指标查询子任务内部异常! 任务id：{}, 指标标识：{}, error:", taskId, targetCode, e);
            throw e;
        }
    }
}
