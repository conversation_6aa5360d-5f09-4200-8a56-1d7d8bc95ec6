package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.AnalysisSqlTemplate;
import com.iqiyi.vip.po.query.AnalysisSqlTemplatePageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * sql模板管理
 * <AUTHOR>
 * @date 2021-05-25
 */
@Mapper
public interface AnalysisSqlTemplateMapper {

    int insertSelective(AnalysisSqlTemplate template);

    int updateSelective(AnalysisSqlTemplate template);

    int delete(@Param("id") Long id);

    AnalysisSqlTemplate selectById(@Param("id") Long id);

    List<AnalysisSqlTemplate> selectList(AnalysisSqlTemplatePageQuery query);

    int selectCount(AnalysisSqlTemplatePageQuery query);

    List<AnalysisSqlTemplate> selectAll(@Param("themeType") Integer themeType, @Param("businessTypeId") Integer businessTypeId);
}