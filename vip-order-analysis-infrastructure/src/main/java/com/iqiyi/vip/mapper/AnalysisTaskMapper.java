package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.domain.entity.AnalysisTaskDO;
import com.iqiyi.vip.dto.task.AnalysisTaskPageQryDTO;
@Mapper
public interface AnalysisTaskMapper {

    int deleteByPrimaryKey(Long id);

    long insertSelective(AnalysisTaskDO record);

    AnalysisTaskDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AnalysisTaskDO record);

    AnalysisTaskDO getByUniqueIdentification(@Param("uniqueIdentification") String uniqueIdentification);

    AnalysisTaskDO getTaskByIdAndOperator(@Param("taskId") Long taskId, @Param("operator") String operator);

    List<AnalysisTaskDO> getPageList(AnalysisTaskPageQryDTO analysisTaskPageQryDTO);

    int reSetTaskStatus(@Param("taskId") Long taskId, @Param("status") Integer status, @Param("result") String result, @Param("executeTime") Integer executeTime);
    // 任务重命名
    int reTaskName(@Param("taskId") Long taskId, @Param("taskName") String taskName, @Param("operator") String operator);

    int saveQueryInfo(@Param("taskId") Long taskId, @Param("queryInfo") String querySql);
}