package com.iqiyi.vip.config;

import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/2/11
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory clientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    /*此写法与鹰眼有循环依赖问题
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder, ClientHttpRequestFactory clientHttpRequestFactory) {
        return restTemplateBuilder.requestFactory(() -> clientHttpRequestFactory).build();
    }*/

    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(2000);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(30000);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(1000);
        return clientHttpRequestFactory;
    }

    @Bean
    public HttpClientBuilder httpClientBuilder(HttpClientConnectionManager poolingConnectionManager) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setConnectionManager(poolingConnectionManager);
        return httpClientBuilder;
    }

    @Bean
    public HttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        // 整个连接池最大连接数
        poolingConnectionManager.setMaxTotal(1000);
        // 设定默认单个路由的最大连接数
        poolingConnectionManager.setDefaultMaxPerRoute(100);
        // 检查有效连接的间隔
        poolingConnectionManager.setValidateAfterInactivity(3000);
        return poolingConnectionManager;
    }

    private List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters) {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        //TODO:其他jackson配置根据需要自行配置
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.TEXT_HTML);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        jacksonConverter.setSupportedMediaTypes(fastMediaTypes);

        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        for (HttpMessageConverter converter : oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
            } else if (converter instanceof MappingJackson2HttpMessageConverter) {
                messageConverters.add(jacksonConverter);
            } else {
                messageConverters.add(converter);
            }
        }

        return messageConverters;
    }

    @Bean("chartClient")
    public RestTemplate priceEngineClient(@Qualifier("chartClientHttpRequestFactory") ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

    @Bean
    public ClientHttpRequestFactory chartClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setReadTimeout(2000);
        factory.setConnectTimeout(1000);
        return factory;
    }

    @Bean("zeusClient")
    public RestTemplate zeusClient(@Qualifier("chartClientHttpRequestFactory") ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

}
