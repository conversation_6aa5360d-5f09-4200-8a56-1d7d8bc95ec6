package com.iqiyi.vip.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.iqiyi.vip.constant.Constants.CHECk_THREAD_POOL_NAME;
import static com.iqiyi.vip.constant.Constants.DIAGNOSIS_THREAD_POOL_NAME;
import static com.iqiyi.vip.constant.Constants.FAST_THREAD_POOL_NAME;
import static com.iqiyi.vip.constant.Constants.SLOW_THREAD_POOL_NAME;

/**
 * <AUTHOR>
 * @date 2022/12/3 01:04
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    @Bean
    public ExecutorService fastThreadPoolExecutor() {

        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(FAST_THREAD_POOL_NAME).build();
        return new ThreadPoolExecutor(
            5,
            10,
            1000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1 << 10),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                String name = t.getName();
                if (StringUtils.isNotBlank(name)) {
                    String[] split = name.split("&");
                    t.setName(split[0] + "&");
                }
            }
        };

    }


    @Bean
    public ExecutorService slowThreadPoolExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(SLOW_THREAD_POOL_NAME).build();
        return new ThreadPoolExecutor(
            5,
            10,
            1000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1 << 10),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                String name = t.getName();
                if (StringUtils.isNotBlank(name)) {
                    String[] split = name.split("&");
                    t.setName(split[0] + "&");
                }
            }
        };
    }


    @Bean
    public ExecutorService checkThreadPoolExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(CHECk_THREAD_POOL_NAME).build();
        return new ThreadPoolExecutor(
            5,
            10,
            1000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1 << 10),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                String name = t.getName();
                if (StringUtils.isNotBlank(name)) {
                    String[] split = name.split("&");
                    t.setName(split[0] + "&");
                }
            }
        };
    }


    @Bean
    public ExecutorService diagnosisThreadPoolExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(DIAGNOSIS_THREAD_POOL_NAME).build();
        return new ThreadPoolExecutor(
            5,
            10,
            1000, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1 << 10),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                String name = t.getName();
                if (StringUtils.isNotBlank(name)) {
                    String[] split = name.split("&");
                    t.setName(split[0] + "&");
                }
            }
        };
    }

    @Bean
    public ExecutorService scheduleTaskThreadPoolExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("schedule-thread-pool-%s").build();
        return new ThreadPoolExecutor(
            10,
            30,
            70, TimeUnit.SECONDS,
            new SynchronousQueue<>(),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

}
