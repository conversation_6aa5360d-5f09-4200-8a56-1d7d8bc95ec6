package com.iqiyi.vip.config;

import com.iqiyi.oss.ClientBuilderConfiguration;
import com.iqiyi.oss.OSS;
import com.iqiyi.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 6/1/22
 * @apiNote
 */
@Configuration
public class OssClientConfig {

    @Value("${oss.client.endpoint}")
    private String ossEndpoint;
    @Value("${oss.client.ak}")
    private String ossAk;
    @Value("${oss.client.sk}")
    private String ossSk;

    /**
     * http://oss.gitlab.qiyi.domain/doc/oss/sdk/oss-java-sdk.html#%E5%AE%8C%E6%95%B4%E7%A4%BA%E4%BE%8B
     * @return
     */
    @Bean(name = "ossClient")
    public OSS init() {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setRequestTimeoutEnabled(true); // 启用超时客户端
//        conf.setSocketTimeout(5000);
//        conf.setRequestTimeout(5000);
//        conf.setMaxErrorRetry(1);    // 设置失败重试次数，默认为3次
        return new OSSClientBuilder().build(ossEndpoint, ossAk, ossSk, conf);
    }
}
