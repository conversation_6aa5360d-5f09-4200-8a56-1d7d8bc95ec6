package com.iqiyi.vip.config;

import com.ctrip.framework.apollo.core.ConfigRegion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.CloudConfigChange;
import com.iqiyi.solar.config.client.CloudConfigService;

/**
 * <AUTHOR>
 * @className CloudConfigComponent
 * @description
 * @date 2022/6/9
 **/
@Slf4j
@Configuration
public class CloudConfigComponent {

    @Value(value = "${config.application.name}")
    private String appName;
    @Value(value = "${config.application.env}")
    private String appEnv;

    @Value("${config.application.region}")
    private String appRegion;

    @Bean(name = "cloudConfig")
    public CloudConfig cloudConfig() {
        CloudConfig cloudConfig = CloudConfigService.builder().withConfigRegion(ConfigRegion.fromRegion(appRegion)).withAppID(appName).withEnv(appEnv).build();
        //监听配置变化
        cloudConfig.addChangeListener(changeEvent -> {
            for (String key : changeEvent.changedKeys()) {
                CloudConfigChange change = changeEvent.getChange(key);
                log.info("cloud config property changed: key = {}, old value = {}, new value = {}", key, change.getOldValue(), change.getNewValue());
            }
        });
        return cloudConfig;
    }

}
