package com.iqiyi.vip.config;

import org.aopalliance.aop.Advice;
import org.springframework.aop.Pointcut;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.AbstractPointcutAdvisor;
import org.springframework.stereotype.Component;
import com.iqiyi.vip.component.log.interceptor.AccessLogInterceptor;

/**
 * <AUTHOR>
 * @className VipLogAdvisor
 * @description
 * @date 2024/7/12
 **/
@Component
public class VipLogAdvisor extends AbstractPointcutAdvisor {

    @Override
    public Pointcut getPointcut() {
        AspectJExpressionPointcut cut = new AspectJExpressionPointcut();
        //自定义切点表达式，拦截哪些Controller请求
        //表达式可参照：https://blog.csdn.net/ABCD898989/article/details/50809321
        cut.setExpression("execution(public * com.iqiyi.vip.controller.*.*(..))");
        return cut;
    }

    @Override
    public Advice getAdvice() {
        return new AccessLogInterceptor();
    }
}