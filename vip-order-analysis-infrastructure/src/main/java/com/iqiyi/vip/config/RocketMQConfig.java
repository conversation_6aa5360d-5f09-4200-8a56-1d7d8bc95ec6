package com.iqiyi.vip.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.CloudMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @className RocketMQConfig
 * @description
 * @date 2022/6/6
 **/
@Configuration
@Slf4j
public class RocketMQConfig {

//    @Bean
//    @ConfigurationProperties(prefix = "analysis.async.task.producer")
//    public RocketMQProperties analysisAsyncTaskProducerProperties() {
//        return new RocketMQProperties();
//    }
//
//    @Bean
//    @ConfigurationProperties(prefix = "oa.approved.async.task.producer")
//    public RocketMQProperties oaApprovedAsyncTaskProducerProperties() {
//        return new RocketMQProperties();
//    }

    /**
     * JavaBean类型消息
     * @return
     */
//    @Bean(initMethod = "init",destroyMethod = "destroy")
//    public RocketMQTemplate<AnalysisTaskExecuteDTO> javaObjectRocketMQTemplate(){
//        RocketMQTemplate<AnalysisTaskExecuteDTO> rocketMQTemplate = new RocketMQTemplate<>();
//        this.initRocketMQTemplate(rocketMQTemplate, analysisAsyncTaskProducerProperties());
//        rocketMQTemplate.setRocketMQSerializer(new Jackson2JsonRocketMQSerializer(AnalysisTaskExecuteDTO.class));
//        ProducerConfigOptions producerConfigOptions = rocketMQTemplate.getProducerConfigOptions();
//        if (producerConfigOptions == null) {
//            producerConfigOptions = new ProducerConfigOptions();
//            rocketMQTemplate.setProducerConfigOptions(producerConfigOptions);
//        }
//        rocketMQTemplate.getProducerConfigOptions().setSendMsgTimeout(10000);
//        return rocketMQTemplate;
//    }
//
//    @Bean(initMethod = "init",destroyMethod = "destroy")
//    public RocketMQTemplate<OaApprovedNoticeMsgDTO> oaApprovedRocketMQTemplate(){
//        RocketMQTemplate<OaApprovedNoticeMsgDTO> rocketMQTemplate = new RocketMQTemplate<>();
//        this.initRocketMQTemplate(rocketMQTemplate, oaApprovedAsyncTaskProducerProperties());
//        rocketMQTemplate.setRocketMQSerializer(new Jackson2JsonRocketMQSerializer(OaApprovedNoticeMsgDTO.class));
//        return rocketMQTemplate;
//    }

//    private void initRocketMQTemplate(RocketMQTemplate<?> rocketMQTemplate, RocketMQProperties properties) {
//        rocketMQTemplate.setNameSrvAddr(properties.getAddress());
//        rocketMQTemplate.setDefaultTopic(properties.getTopic());
//        rocketMQTemplate.setProducerGroup(properties.getGroupname());
//        rocketMQTemplate.setToken(properties.getToken());
//    }

    @Bean
    public CloudMQProducer asyncTaskMsgProducer() {
        return CloudMQProducer.fromCloudConfig("analysis_async_task_producer");
    }

    @Bean
    public CloudMQProducer oaApprovedAsyncTaskMsgProducer() {
        return CloudMQProducer.fromCloudConfig("oaApproved_async_task_producer");
    }

}
