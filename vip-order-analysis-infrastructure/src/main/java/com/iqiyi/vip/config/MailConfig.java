package com.iqiyi.vip.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.vip.uitls.mail.MailHelper;

/**
 * <AUTHOR>
 * @date 2022/4/1 下午 05:10
 */
@Configuration
public class MailConfig {

    @Value("${vip.mail.prefix}")
    private String prefix;

    @Value("${vip.mail.token}")
    private String token;

    @Value("${vip.mail.name}")
    private String userName;

    @Bean
    public MailHelper mailHelper() {
        MailHelper mailHelper = new MailHelper();
        mailHelper.setFrom(userName);
        mailHelper.setToken(token);
        mailHelper.setUserName(prefix);
        return mailHelper;
    }
}
