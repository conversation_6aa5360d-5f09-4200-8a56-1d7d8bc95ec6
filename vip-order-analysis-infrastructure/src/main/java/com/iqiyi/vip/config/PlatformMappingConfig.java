package com.iqiyi.vip.config;

import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

/**
 * 平台映射配置
 */

@Slf4j
public class PlatformMappingConfig {

    // 平台代码到平台名称的映射
    private static final Map<String, String> codeToNameMap = new HashMap<>();
    
    // 平台名称到平台代码的映射
    private static final Map<String, String> nameToCodeMap = new HashMap<>();

    static {
        addMapping("iqiyi", "爱奇艺");
        addMapping("qq", "腾讯");
        addMapping("youku", "优酷");
        addMapping("mangguo", "芒果");
        addMapping("bilibili", "哔哩哔哩");
        addMapping("sohu", "搜狐");
        addMapping("pptv", "PPTV");
        addMapping("letv", "乐视");
        addMapping("xigua", "西瓜");
    }
    /**
     * 添加映射关系
     * @param code 平台代码
     * @param name 平台名称
     */
    private static void addMapping(String code, String name) {
        codeToNameMap.put(code, name);
        nameToCodeMap.put(name, code);
    }
    
    /**
     * 根据平台代码获取平台名称
     * @param code 平台代码
     * @return 平台名称，如果不存在则返回原代码
     */
    public static String getPlatformName(String code) {
        return codeToNameMap.getOrDefault(code, code);
    }
    
    /**
     * 根据平台名称获取平台代码
     * @param name 平台名称
     * @return 平台代码，如果不存在则返回原名称
     */
    public static String getPlatformCode(String name) {
        log.info("传入的品牌名称为：{}", name);
        return nameToCodeMap.getOrDefault(name, name);
    }

} 