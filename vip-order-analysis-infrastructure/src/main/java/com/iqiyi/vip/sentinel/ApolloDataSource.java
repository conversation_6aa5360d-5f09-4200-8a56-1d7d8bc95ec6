package com.iqiyi.vip.sentinel;

import com.alibaba.csp.sentinel.datasource.AbstractDataSource;
import com.alibaba.csp.sentinel.datasource.Converter;
import com.alibaba.csp.sentinel.log.RecordLog;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

/**
 * 获取配置中心配置
 *
 * <AUTHOR>
 */
public class ApolloDataSource<T> extends AbstractDataSource<String, T> {

    private final Config config;
    private final String rulesKey;
    private final String defaultRuleValue;

    private ConfigChangeListener configChangeListener;

    public ApolloDataSource(String namespaceName, String flowRulesKey, String defaultFlowRuleValue,
        Converter<String, T> parser) {
        super(parser);

        Preconditions.checkArgument(!Strings.isNullOrEmpty(namespaceName), "Namespace name could not be null or empty");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(flowRulesKey), "FlowRuleKey could not be null or empty!");

        this.rulesKey = flowRulesKey;
        this.defaultRuleValue = defaultFlowRuleValue;

        this.config = ConfigService.getConfig(namespaceName);

        initialize();

        RecordLog.info(String.format("Initialized rule for namespace: %s, flow rules key: %s",
            namespaceName, flowRulesKey));
    }

    private void initialize() {
        initializeConfigChangeListener();
        loadAndUpdateRules();
    }

    private void loadAndUpdateRules() {
        try {
            T newValue = loadConfig();
            if (newValue == null) {
                RecordLog.warn("[ApolloDataSource] WARN: rule config is null, you may have to check your data source");
            }
            getProperty().updateValue(newValue);
        } catch (Throwable ex) {
            RecordLog.warn("[ApolloDataSource] Error when loading rule config", ex);
        }
    }

    private void initializeConfigChangeListener() {

        configChangeListener = changeEvent -> {
            ConfigChange change = changeEvent.getChange(rulesKey);
            //change is never null because the listener will only notify for this key
            if (change != null) {
                RecordLog.info("[ApolloDataSource] Received config changes: " + change.toString());
            }
            loadAndUpdateRules();
        };
        config.addChangeListener(configChangeListener);
    }

    @Override
    public String readSource() throws Exception {
        return config.getProperty(rulesKey, defaultRuleValue);
    }

    @Override
    public void close() throws Exception {
        config.removeChangeListener(configChangeListener);
    }
}