package com.iqiyi.vip.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.CloudMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.nio.charset.StandardCharsets;

import com.iqiyi.vip.constants.MQSerializerConstants;
import com.iqiyi.vip.dto.oa.OaApprovedNoticeMsgDTO;
import com.iqiyi.vip.dto.task.AnalysisTaskExecuteDTO;

/**
 * @author: guojing
 * @date: 2025/3/18 22:31
 */
@Slf4j
@Component
public class MsgSender {

    @Resource
    private CloudMQProducer asyncTaskMsgProducer;

    @Resource
    private CloudMQProducer oaApprovedAsyncTaskMsgProducer;

    public boolean sendAsyncTaskMsg(AnalysisTaskExecuteDTO msgContent) {
        String msgKey = msgContent.getTaskId() != null ? msgContent.getTaskId().toString() : null;
        byte[] msgBytes = MQSerializerConstants.ASYNC_TASK_SERIALIZER.serialize(msgContent);
        Message msg = new Message(asyncTaskMsgProducer.getTopic(), msgBytes);
        if (msgKey != null) {
            msg.setKeys(msgKey);
        }
        return doSend(asyncTaskMsgProducer, msg);
    }

    public boolean sendOaApprovedAsyncTaskMsg(OaApprovedNoticeMsgDTO msgContent) {
        byte[] msgBytes = MQSerializerConstants.OA_APPROVED_SERIALIZER.serialize(msgContent);
        Message msg = new Message(oaApprovedAsyncTaskMsgProducer.getTopic(), msgBytes);
        if (StringUtils.isNotBlank(msgContent.getAccount())) {
            msg.setKeys(msgContent.getAccount());
        }
        return doSend(oaApprovedAsyncTaskMsgProducer, msg);
    }

    protected boolean doSend(CloudMQProducer producer, Message message) {
        StopWatch stopWatch = StopWatch.createStarted();
        String topic = message.getTopic();
        String msgContent = StringUtils.toEncodedString(message.getBody(), StandardCharsets.UTF_8);
        try {
            log.info("begin send message, topic:{}, msgContent:{}", topic,  msgContent);
            SendResult sendResult = null;
            try {
                sendResult = producer.send(message);
            } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                //发送异常进行重试一次
                sendResult = producer.send(message);
            }
            if (sendResult == null || sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.error("send msg to topic: {} failed, cost: {}ms, sendResult: {}, msgContent: {}", topic, stopWatch.getTime(), sendResult, msgContent);
                return false;
            }
            log.info("send msg to topic: {} success, msgId: {}, msgKeys: {}, cost: {}ms", topic, sendResult.getMsgId(), message.getKeys(), stopWatch.getTime());
            return true;
        } catch (Exception e) {
            log.error("send msg to topic: {} occur exception, msgContent: {}", topic, msgContent, e);
            throw (e instanceof RuntimeException) ? (RuntimeException) e : new RuntimeException(e.getMessage(), e);
        }
    }

}
