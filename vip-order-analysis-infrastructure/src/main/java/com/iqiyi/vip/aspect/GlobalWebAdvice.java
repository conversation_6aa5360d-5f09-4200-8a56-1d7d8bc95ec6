package com.iqiyi.vip.aspect;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.iqiyi.vip.dto.base.CommonResult;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2021/1/24 02:01
 */
@RestControllerAdvice
@Slf4j
public class GlobalWebAdvice {

    public static final ObjectMapper MAPPER = new ObjectMapper();

    @ExceptionHandler(value = BizRuntimeException.class)
    public CommonResult bizExceptionHandler(HttpServletRequest request, BizRuntimeException e)
        throws JsonProcessingException {
        CommonResult result = new CommonResult(e.getCodeEnum().getCode(), e.getMessage());
        log.warn("bizExceptionHandler requestURI:{}, parameterMap:{}, result:{}", request.getRequestURI(), MAPPER.writeValueAsString(request
            .getParameterMap()), result);
        return result;
    }

    @ExceptionHandler(value = Exception.class)
    public CommonResult defaultErrorHandler(HttpServletRequest request, Exception e) {
        String reason = e.getMessage();
        if (StringUtils.isBlank(reason)) {
            reason = CodeEnum.ERROR_SYSTEM.getMessage();
        }
        CommonResult result = new CommonResult(CodeEnum.ERROR_SYSTEM.getCode(), reason);
        log.error("defaultErrorHandler requestURI:{}, result:{}, reason:{}", request.getRequestURI(), result, reason, e);
        return result;
    }

    @ExceptionHandler(value = {ValidationException.class})
    @ResponseBody
    public CommonResult validationExceptionHandler(HttpServletRequest servletRequest, ValidationException exception) {
        log.error("validationExceptionHandler, error occurred: uri={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()), exception);
        if(exception instanceof ConstraintViolationException){
            ConstraintViolationException exs = (ConstraintViolationException) exception;

            Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
            for (ConstraintViolation<?> item : violations) {
                return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), item.getMessage());
            }
        }
        return CommonResult.create(CodeEnum.ERROR_PARAM);
    }

    @ExceptionHandler(value = {BindException.class})
    @ResponseBody
    public CommonResult paramBindErrorHandler(HttpServletRequest servletRequest, BindException exception) throws Exception {
        // TODO：此处待一尘查到问题后再优化
        if ("/order-analysis/permission/auth".equalsIgnoreCase(servletRequest.getRequestURI())) {
            return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), exception.getBindingResult().getFieldError().getDefaultMessage());
        }
        log.error("request param illegal occurred: url={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()), exception);
        List<ObjectError> objectErrors = exception.getAllErrors();
        String errorMsg = null;
        if (CollectionUtils.isNotEmpty(objectErrors)) {
            List<String> paramErrorMsgs = objectErrors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
            errorMsg = String.join("; ", paramErrorMsgs);
        }
        errorMsg = errorMsg != null ? errorMsg : CodeEnum.ERROR_PARAM.getMessage();
        return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), errorMsg);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public CommonResult MethodArgumentNotValidExceptionHandler(HttpServletRequest request, MethodArgumentNotValidException e) {
        log.error("MethodArgumentNotValidExceptionHandler: url={}", request.getRequestURI(), e);
        String message = e.getBindingResult()
            .getAllErrors()
            .stream()
            .map(DefaultMessageSourceResolvable::getDefaultMessage)
            .collect(Collectors.joining());
        return new CommonResult(CodeEnum.ERROR_PARAM.getCode(), message);
    }
}
