package com.iqiyi.vip.po.competitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorUiDTO;
import com.iqiyi.vip.dto.competitor.CompetitorUiSaveDTO;
import com.iqiyi.vip.utils.DateUtils;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CompetitorUiPO {

    private Long id;

    private Date date;

    private String brand;

    private String clientVersion;

    private String clientType;

    private String page;

    private String userStatus;

    private List<String> screenshotUrls;

    private String screenRecording;

    private Date createTime;

    private Date updateTime;

    public static CompetitorUiPO buildFrom(CompetitorUiSaveDTO dto) {
        if (dto == null) {
            return null;
        }
        return CompetitorUiPO.builder()
            .date(DateUtils.string2Date(dto.getCreateTime()))
            .brand(dto.getBrand())
            .clientVersion(dto.getClientVersion())
            .clientType(dto.getClientType())
            .page(dto.getPage())
            .userStatus(dto.getUserStatus())
            .screenshotUrls(dto.getScreenshotUrls())
            .screenRecording(dto.getScreenRecordingUrl())
            .build();
    }

    public CompetitorUiDTO toDTO() {
        return CompetitorUiDTO.builder()
            .date(DateUtils.dateDatetimeFormat(date.getTime()))
            .brand(brand)
            .clientVersion(clientVersion)
            .clientType(clientType)
            .page(page)
            .userStatus(userStatus)
            .screenshotUrls(screenshotUrls)
            .screenRecordingUrl(screenRecording)
            .build();
    }

}