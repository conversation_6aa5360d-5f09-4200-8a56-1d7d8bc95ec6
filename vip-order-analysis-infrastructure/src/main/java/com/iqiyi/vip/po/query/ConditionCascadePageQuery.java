package com.iqiyi.vip.po.query;

import lombok.Data;
import java.sql.Timestamp;
import com.iqiyi.vip.dto.base.PageQry;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
public class ConditionCascadePageQuery extends PageQry {

    private Integer start;
    private Integer limit;
    private Long themeId;
    private Long businessId;
    private String conditionName;
    private Timestamp startCreateTime;
    private Timestamp endCreateTime;
    private Timestamp startUpdateTime;
    private Timestamp endUpdateTime;

}
