package com.iqiyi.vip.po.competitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

import com.iqiyi.vip.dto.competitor.CompetitorShopDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopSaveDTO;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CompetitorShopPO {

    private Long id;

    private String date;

    private String platform;

    private String brand;

    private Integer storeType;

    private List<String> screenshotUrls;

    private String interactionVideo;

    private Date createTime;

    private Date updateTime;

    public static CompetitorShopPO buildFrom(CompetitorShopSaveDTO dto) {
        if (dto == null) {
            return null;
        }
        return CompetitorShopPO.builder()
            .date(dto.getDate())
            .platform(dto.getPlatform())
            .brand(dto.getBrand())
            .storeType(dto.getStoreType())
            .screenshotUrls(dto.getScreenshotUrls())
            .interactionVideo(dto.getInteractionVideoUrl())
            .build();
    }

    public CompetitorShopDTO toDTO() {
        return CompetitorShopDTO.builder()
            .date(date)
            .platform(platform)
            .brand(brand)
            .storeType(storeType)
            .storeTypeName(storeType != null && storeType == 1 ? "旗舰店" : "非旗舰店")
            .screenshotUrls(screenshotUrls)
            .interactionVideoUrl(interactionVideo)
            .build();
    }
} 