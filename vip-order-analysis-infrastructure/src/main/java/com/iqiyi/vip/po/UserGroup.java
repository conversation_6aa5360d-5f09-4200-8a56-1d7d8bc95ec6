package com.iqiyi.vip.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 人群包
 */
@Data
public class UserGroup implements Serializable {
    private Integer id;

    /**
     * 分群名称
     */
    private String name;

    /**
     * 人群包类型,1:人群包 2:订单包
     */
    private Integer groupType;

    /**
     * 创建方式
     */
    private Integer createType;

    /**
     * 人群包的地址
     */
    private String dataUrl;

    /**
     * 预估的数据量
     */
    private Long dataCount;

    /**
     * 上传进度,0:上传中 1:上传成功 2:上传失败
     */
    private Integer uploadProgress;

    /**
     * 是否已同步北斗
     */
    private Integer isBeidouSynced;

    /**
     * 0:无效 1:有效
     */
    private Byte status;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 生成人群包任务Id
     */
    private Long subTaskId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}