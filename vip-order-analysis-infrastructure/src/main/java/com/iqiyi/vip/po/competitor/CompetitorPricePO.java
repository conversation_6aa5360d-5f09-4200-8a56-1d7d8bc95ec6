package com.iqiyi.vip.po.competitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.iqiyi.vip.dto.competitor.CompetitorPriceDTO;
import com.iqiyi.vip.dto.competitor.CompetitorPriceSaveParam;
import com.iqiyi.vip.utils.DateUtils;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CompetitorPricePO {

    private Long id;

    private Date date;

    private String brand;

    private String userStatus;

    private String vipType;

    private String product;

    private String price;

    private Date createTime;

    private Date updateTime;

    public CompetitorPriceDTO toDTO() {
        return CompetitorPriceDTO.builder()
            .date(DateUtils.dateDatetimeFormat(date.getTime()))
            .brand(brand)
            .userStatus(userStatus)
            .vipType(vipType.replace("vip","VIP"))
            .product(product.replace("vip","VIP"))
            .price(price)
            .build();
    }

    public static CompetitorPricePO buildFrom(CompetitorPriceDTO competitorPriceDTO) {
        return CompetitorPricePO.builder()
            .date(DateUtils.string2Date(competitorPriceDTO.getDate()))
            .brand(competitorPriceDTO.getBrand())
            .userStatus(competitorPriceDTO.getUserStatus())
            .vipType(competitorPriceDTO.getVipType())
            .product(competitorPriceDTO.getProduct())
            .price(competitorPriceDTO.getPrice())
            .build();
    }

}