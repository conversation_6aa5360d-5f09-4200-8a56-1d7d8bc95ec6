package com.iqiyi.vip.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizTargetMonitorConfigPO {

    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 主题
     */
    private Integer themeType;

    /**
     * 条件
     */
    private String conditions;

    /**
     * 业务指标
     */
    private String target;
    /**
     * 维度
     */
    private String dimensions;

    /**
     * 执行频率
     */
    private Integer execFrequency;

    /**
     * 创建人
     */
    private String createOpr;

    /**
     * 更新人
     */
    private String updateOpr;

    private Date createTime;

    private Date updateTime;

    private Integer status;

    /**
     * qiyi job id
     */
    private Integer jobId;
    /**
     * 宙斯监控Id
     */
    private Integer monitorId;

}