package com.iqiyi.vip.po.competitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceDTO;
import com.iqiyi.vip.utils.DateUtils;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CompetitorShopPricePO {

    private Long id;

    private String date;

    private String platform;

    private String brand;

    private Integer storeType;

    private String userGroup;
    
    private String vipType;
    
    private String product;
    
    private String price;
    
    private String priceText;

    private Date createTime;

    private Date updateTime;

    public static List<CompetitorShopPricePO> buildFromDTO(CompetitorShopPriceSaveDTO dto) {
        if (dto == null) {
            return null;
        }
        
        return dto.getProducts().stream()
            .map(product -> CompetitorShopPricePO.builder()
                .date(dto.getDate())
                .platform(dto.getPlatform())
                .brand(dto.getBrand())
                .storeType(dto.getStoreType())
                .userGroup(product.getUserGroup())
                .vipType(product.getVipType())
                .product(product.getProduct())
                .price(product.getPrice())
                .priceText(product.getPriceText())
                .build())
            .collect(Collectors.toList());
    }

    public CompetitorShopPriceDTO toDTO() {
        String upperCaseVipType = this.vipType != null ? this.vipType.toUpperCase() : null;
        return CompetitorShopPriceDTO.builder()
            .date(this.date)
            .platform(this.platform)
            .brand(this.brand)
            .storeType(this.storeType)
            .storeTypeName(this.storeType != null && this.storeType == 1 ? "旗舰店" : "非旗舰店")
            .vipType(upperCaseVipType)
            .product(this.product)
            .price(this.price)
            .priceText(this.priceText == null ? null : this.priceText.replace("null", "").replace("无", ""))
            .userGroup(this.userGroup)
            .build();
    }
} 