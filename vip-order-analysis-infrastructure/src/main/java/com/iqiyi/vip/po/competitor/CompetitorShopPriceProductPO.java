package com.iqiyi.vip.po.competitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.iqiyi.vip.dto.competitor.CompetitorShopPriceSaveDTO;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CompetitorShopPriceProductPO {

    private Long id;

    private Long promotionId;

    private String vipType;

    private String product;

    private String price;

    private String promotionText;

    private Date createTime;

    private Date updateTime;

    public static List<CompetitorShopPriceProductPO> buildFrom(Long promotionId, List<CompetitorShopPriceSaveDTO.ProductInfo> products) {
        if (products == null || products.isEmpty()) {
            return null;
        }
        
        return products.stream()
            .map(item -> CompetitorShopPriceProductPO.builder()
                .promotionId(promotionId)
                .vipType(item.getVipType())
                .product(item.getProduct())
                .price(item.getPrice())
                .promotionText(item.getPriceText())
                .build())
            .collect(Collectors.toList());
    }
} 