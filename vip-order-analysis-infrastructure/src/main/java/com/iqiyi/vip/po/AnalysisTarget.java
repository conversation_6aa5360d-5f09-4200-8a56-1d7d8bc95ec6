package com.iqiyi.vip.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
public class AnalysisTarget {

    private Long id;

    private String name;

    private String code;

    private String value;

    /**
     * 关联的sql模板code
     */
    private Long sqlTemplateId;

    private Integer group;

    private Integer order;

    private String documentation;

    private String descs;

    private String partitions;

    private Date createTime;

    private Date updateTime;

    private String createOpr;

    private String updateOpr;

    private Integer status;

    private String commitNote;

    private Integer version;

    private Integer source;
}
