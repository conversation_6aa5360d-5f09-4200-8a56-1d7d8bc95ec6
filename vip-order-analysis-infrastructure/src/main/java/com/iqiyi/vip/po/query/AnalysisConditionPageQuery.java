package com.iqiyi.vip.po.query;

import com.iqiyi.vip.dto.base.BaseQry;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
public class AnalysisConditionPageQuery extends BaseQry {

    private Integer start;
    private Integer limit;

    private String name;
    private Timestamp startCreateTime;
    private Timestamp endCreateTime;
    private Timestamp startUpdateTime;
    private Timestamp endUpdateTime;

}
