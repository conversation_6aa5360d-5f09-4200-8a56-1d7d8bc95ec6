<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisSubTaskMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AnalysisSubTaskDO">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="parent_task_id" property="parentTaskId" jdbcType="BIGINT"/>
        <result column="user_group_id" property="userGroupId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="file_url" property="fileUrl" jdbcType="VARCHAR"/>
        <result column="user_package_name" property="userPackageName" jdbcType="VARCHAR"/>
        <result column="business_Ids" property="businessIds" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据 parent_task_id 查询 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT * FROM analysis_sub_task WHERE 1=1
        <if test="parentTaskId != null and parentTaskId != ''">
            and parent_task_id = #{parentTaskId}
        </if>
        <if test="userGroupId != null and userGroupId != ''">
            and user_group_id = #{userGroupId}
        </if>
    </select>

    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AnalysisSubTaskDO">
        INSERT INTO analysis_sub_task
        (parent_task_id,user_group_id,status, file_url, user_package_name, business_Ids, create_time, update_time)
        VALUES
            (#{parentTaskId},#{userGroupId}, #{status}, #{fileUrl}, #{userPackageName}, #{businessIds}, NOW(), NOW())
    </insert>

    <update id="updateByTask" parameterType="com.iqiyi.vip.domain.entity.AnalysisSubTaskDO">
        UPDATE analysis_sub_task
        <set>
            <if test="fileUrl != null">
                file_url = #{fileUrl},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="userPackageName != null">
                user_package_name = #{userPackageName},
            </if>
            <if test="businessIds != null">
                business_Ids = #{businessIds}
            </if>
        </set>
        WHERE 1=1
        <if test="parentTaskId != null and parentTaskId != ''">
            and parent_task_id = #{parentTaskId}
        </if>
        <if test="userGroupId != null and userGroupId != ''">
            and user_group_id = #{userGroupId}
        </if>
    </update>

    <delete id="deleteByParentTaskId" parameterType="java.lang.Long">
        DELETE FROM analysis_sub_task
        WHERE parent_task_id = #{parentTaskId}
    </delete>

    <delete id="deleteByUserGroupId" parameterType="java.lang.Integer">
        DELETE FROM analysis_sub_task
        WHERE user_group_id = #{userGroupId}
    </delete>
</mapper>