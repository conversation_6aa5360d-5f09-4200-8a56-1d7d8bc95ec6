<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iqiyi.vip.mapper.DiagnosisSnapshotMapper">

    <!-- 定义 ResultMap，用于映射查询结果 -->
    <resultMap id="DiagnosisResultMap" type="com.iqiyi.vip.po.DiagnosisSnapshot">
        <id column="id" property="id" />
        <result column="unique_id" property="uniqueId" />
        <result column="task_id" property="taskId" />
        <result column="diagnosis_path" property="diagnosisPath" />
        <result column="result" property="result" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <!-- 根据 ID 查询 Diagnosis -->
    <select id="findById" parameterType="java.lang.Long" resultMap="DiagnosisResultMap">
        SELECT * FROM diagnosis_snapshot WHERE id = #{id}
    </select>

    <!-- 根据 taskId 查询 Diagnosis -->
    <select id="findByTaskId" parameterType="java.lang.Long" resultMap="DiagnosisResultMap">
        SELECT * FROM diagnosis_snapshot WHERE task_id = #{taskId}
    </select>

    <!-- 根据 uniqueId 查询 Diagnosis -->
    <select id="findByUniqueId" parameterType="java.lang.String" resultMap="DiagnosisResultMap">
        SELECT * FROM diagnosis_snapshot WHERE unique_id = #{uniqueId}
    </select>

    <!-- 插入一条 Diagnosis 记录 -->
    <insert id="insert" parameterType="com.iqiyi.vip.po.DiagnosisSnapshot" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO diagnosis_snapshot (unique_id, task_id, result, diagnosis_path, create_time, update_time)
        VALUES (#{uniqueId}, #{taskId}, #{result}, #{diagnosisPath}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新一条 Diagnosis 记录 -->
    <update id="update" parameterType="com.iqiyi.vip.po.DiagnosisSnapshot">
        UPDATE diagnosis_snapshot
        <set>
            <if test="uniqueId != null">unique_id = #{uniqueId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="result != null">result = #{result},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>
