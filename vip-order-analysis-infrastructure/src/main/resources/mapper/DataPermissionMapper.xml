<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.DataPermissionMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.DataPermission">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account" jdbcType="VARCHAR" property="account"/>
        <result column="permission" jdbcType="VARCHAR" property="permission"/>
        <result column="layered_permission" jdbcType="VARCHAR" property="layeredPermission"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, account, permission, layered_permission, theme_type, `type`, status, create_time, update_time
    </sql>
    <sql id="Base_Column_Insert">
        account, permission, layered_permission, theme_type, `type`, status, create_time, update_time
    </sql>

    <select id="getDataPermission" resultType="com.iqiyi.vip.domain.entity.DataPermission">
        select
        <include refid="Base_Column_List"/>
        from data_permission
        <trim suffixOverrides="and">
            <where>
                account = #{account, jdbcType=VARCHAR} and
                `type` = #{type, jdbcType=INTEGER} and
                `theme_type` = #{themeType, jdbcType=INTEGER}
            </where>
        </trim>
    </select>

    <select id="getDataPermissions" resultType="com.iqiyi.vip.domain.entity.DataPermission">
        select
        <include refid="Base_Column_List"/>
        from data_permission
        <trim suffixOverrides="and">
            <where>
                account = #{account, jdbcType=VARCHAR} and status = 1
            </where>
        </trim>
    </select>

    <select id="getDataPermissionsByThemeType" resultType="com.iqiyi.vip.domain.entity.DataPermission">
        select
        <include refid="Base_Column_List"/>
        from data_permission
        <trim suffixOverrides="and">
            <where>
                account = #{account, jdbcType=VARCHAR} and
                `theme_type` = #{themeType, jdbcType=INTEGER}
            </where>
        </trim>
    </select>

    <select id="ownedDataPermission" resultType="java.lang.Integer">
        select count(*) from data_permission
        <trim suffixOverrides="and">
            <where>
                account = #{account, jdbcType=VARCHAR}
            </where>
        </trim>
    </select>

    <insert id="insertDataPermission" parameterType="com.iqiyi.vip.domain.entity.DataPermission">
        insert into data_permission (
        <include refid="Base_Column_Insert"/>
        ) values (
        #{account, jdbcType=VARCHAR}, #{permission, jdbcType=VARCHAR}, #{layeredPermission, jdbcType=VARCHAR}
        , #{themeType, jdbcType=INTEGER}, #{type, jdbcType=INTEGER}, #{status, jdbcType=INTEGER}
        , #{createTime, jdbcType=TIMESTAMP}, #{updateTime, jdbcType=TIMESTAMP}
        )
    </insert>

    <update id="updateDataPermission" parameterType="com.iqiyi.vip.domain.entity.DataPermission">
        update data_permission
        <trim suffixOverrides=",">
            <set>
                <if test="permission != null">
                    permission = #{permission, jdbcType=VARCHAR},
                </if>
                <if test="layeredPermission != null">
                    layered_permission = #{layeredPermission, jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
        </trim>
        <where>
            account = #{account, jdbcType=VARCHAR} and
            `type` = #{type, jdbcType=INTEGER} and
            `theme_type` = #{themeType, jdbcType=INTEGER}
        </where>
    </update>

    <update id="updateDataPermissionByMsgId" parameterType="com.iqiyi.vip.domain.entity.DataPermission">
        update data_permission
        <trim suffixOverrides=",">
            <set>
                <if test=" != null">
                    permission = #{permission, jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
        </trim>
        <where>

        </where>
    </update>
    <update id="updateAcIdByMessgeId"></update>

</mapper>