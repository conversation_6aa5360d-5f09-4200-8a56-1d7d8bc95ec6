<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.BizTargetMonitorConfigMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.BizTargetMonitorConfigPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="conditions" jdbcType="VARCHAR" property="conditions"/>
        <result column="target" jdbcType="VARCHAR" property="target"/>
        <result column="dimensions" jdbcType="VARCHAR" property="dimensions"/>
        <result column="exec_frequency" jdbcType="INTEGER" property="execFrequency"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="job_id" jdbcType="INTEGER" property="jobId"/>
        <result column="monitor_id" jdbcType="INTEGER" property="monitorId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, theme_type, conditions, target, dimensions, exec_frequency, create_opr, update_opr, create_time, update_time, status, job_id, monitor_id
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from biz_target_monitor_config where id = #{id,jdbcType=INTEGER}
    </delete>

    <update id="resetStatus">
        update biz_target_monitor_config set status = #{status} where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="com.iqiyi.vip.po.BizTargetMonitorConfigPO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into biz_target_monitor_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="themeType != null">
                theme_type,
            </if>
            <if test="conditions != null">
                conditions,
            </if>
            <if test="target != null">
                target,
            </if>
            <if test="dimensions != null">
                dimensions,
            </if>
            <if test="execFrequency != null">
                exec_frequency,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="jobId != null">
                job_id,
            </if>
            <if test="monitorId != null">
                monitor_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="themeType != null">
                #{themeType,jdbcType=INTEGER},
            </if>
            <if test="conditions != null">
                #{conditions,jdbcType=VARCHAR},
            </if>
            <if test="target != null">
                #{target,jdbcType=VARCHAR},
            </if>
            <if test="dimensions != null">
                #{dimensions,jdbcType=VARCHAR},
            </if>
            <if test="execFrequency != null">
                #{execFrequency,jdbcType=INTEGER},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="jobId != null">
                #{jobId,jdbcType=INTEGER},
            </if>
            <if test="monitorId != null">
                #{monitorId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.iqiyi.vip.po.BizTargetMonitorConfigPO">
        update biz_target_monitor_config
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="themeType != null">
                theme_type = #{themeType,jdbcType=INTEGER},
            </if>
            <if test="conditions != null">
                conditions = #{conditions,jdbcType=VARCHAR},
            </if>
            <if test="target != null">
                target = #{target,jdbcType=VARCHAR},
            </if>
            <if test="dimensions != null">
                dimensions = #{dimensions,jdbcType=VARCHAR},
            </if>
            <if test="execFrequency != null">
                exec_frequency = #{execFrequency,jdbcType=INTEGER},
            </if>
            <if test="updateOpr != null">
                update_opr = #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="jobId!= null">
                job_id = #{jobId,jdbcType=INTEGER},
            </if>
            <if test="monitorId!= null">
                monitor_id = #{monitorId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateJobIdById">
        update biz_target_monitor_config set job_id = #{jobId} where id = #{id}
    </update>

    <update id="updateMonitorIdById">
        update biz_target_monitor_config set monitor_id = #{monitorId} where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from biz_target_monitor_config
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="existByName" parameterType="java.lang.String" resultType="int">
        select count(1)
        from biz_target_monitor_config
        where name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="search" parameterType="com.iqiyi.vip.po.query.BizTargetMonitorConfigPageQuery" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from biz_target_monitor_config
        where status = 1
            <if test="name != null and name != ''">
                and name like concat('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="themeType != null">
                and theme_type = #{themeType,jdbcType=INTEGER}
            </if>
            <if test="target != null and target != ''">
                and target = #{target,jdbcType=VARCHAR}
            </if>
            <if test="createOpr != null and createOpr != ''">
                and create_opr = #{createOpr,jdbcType=VARCHAR}
            </if>
        order by update_time desc
        limit ${start}, ${limit}
    </select>
    <select id="searchCount" parameterType="com.iqiyi.vip.po.query.BizTargetMonitorConfigPageQuery" resultType="int">
        select count(1)
        from biz_target_monitor_config
        where status = 1
            <if test="name != null and name != ''">
                and name like concat('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="themeType != null">
                and theme_type = #{themeType,jdbcType=INTEGER}
            </if>
            <if test="target != null and target != ''">
                and target = #{target,jdbcType=VARCHAR}
            </if>
            <if test="createOpr != null and createOpr != ''">
                and create_opr = #{createOpr,jdbcType=VARCHAR}
            </if>
    </select>

</mapper>