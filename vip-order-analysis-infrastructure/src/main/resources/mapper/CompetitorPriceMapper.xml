<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.CompetitorPriceMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.competitor.CompetitorPricePO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="user_status" jdbcType="VARCHAR" property="userStatus"/>
        <result column="vip_type" jdbcType="VARCHAR" property="vipType"/>
        <result column="product" jdbcType="VARCHAR" property="product"/>
        <result column="price" jdbcType="VARCHAR" property="price"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap id="DateRangeResultMap" type="com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO">
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, date, brand, user_status, vip_type, product, price, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from competitor_price where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.po.competitor.CompetitorPricePO" keyProperty="id" useGeneratedKeys="true">
        insert into competitor_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="userStatus != null">
                user_status,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="product != null">
                product,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                #{userStatus,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=VARCHAR},
            </if>
            <if test="product != null">
                #{product,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into competitor_price (date, brand, user_status, vip_type, product, price) values
        <foreach collection="list" item="item" separator=",">
            ( #{item.date,jdbcType=DATE},
            #{item.brand,jdbcType=VARCHAR},
            #{item.userStatus,jdbcType=VARCHAR},
            #{item.vipType,jdbcType=VARCHAR},
            #{item.product,jdbcType=VARCHAR},
            #{item.price,jdbcType=VARCHAR} )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.competitor.CompetitorPricePO">
        update competitor_price
        <set>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                user_status = #{userStatus,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=VARCHAR},
            </if>
            <if test="product != null">
                product = #{product,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAllBrand" resultType="java.lang.String">
        select brand from competitor_price group by brand order by brand
    </select>

    <select id="selectDateRange" resultMap="DateRangeResultMap">
        select MIN(date) AS start_date, MAX(date) AS end_date from competitor_price
    </select>

    <select id="selectAllUserStatus" resultType="java.lang.String">
        select user_status
        from competitor_price
        where brand = #{brand}
        <if test="date != null and !date.isEmpty()">
            and date = #{date}
        </if>
        group by user_status
        order by user_status
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_price
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="search" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_price
        where 1=1
        <if test="brand != null and !brand.isEmpty()">
            and brand = #{brand}
        </if>
        <if test="userStatus != null and !userStatus.isEmpty()">
            and user_status = #{userStatus}
        </if>
        <if test="startTime != null">
            and date >= #{startTime}
        </if>
        <if test="endTime != null">
            and date &lt;= #{endTime}
        </if>
        <if test="date != null and date != ''">
            and date = #{date}
        </if>
    </select>

</mapper>