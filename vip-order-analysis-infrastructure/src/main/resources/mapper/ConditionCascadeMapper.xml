<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.ConditionCascadeMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.ConditionCascade">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="theme_id" jdbcType="INTEGER" property="themeId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="current_condition_id" jdbcType="INTEGER" property="currentConditionId"/>
        <result column="next_condition_id" jdbcType="INTEGER" property="nextConditionId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , theme_id, business_id, current_condition_id, next_condition_id, status, create_opr, update_opr, create_time, update_time
    </sql>

    <sql id="Excluded_Id_Column_List">
        theme_id
        , business_id, current_condition_id, next_condition_id, status, create_opr, update_opr, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.domain.entity.ConditionCascade" useGeneratedKeys="true"
            keyColumn="id" keyProperty="id">
        insert into condition_cascade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="themeId != null">
                theme_id,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="currentConditionId != null">
                current_condition_id,
            </if>
            <if test="nextConditionId != null">
                next_condition_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="themeId != null">
                #{themeId, jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId, jdbcType=INTEGER},
            </if>
            <if test="currentConditionId != null">
                #{currentConditionId, jdbcType=INTEGER},
            </if>
            <if test="nextConditionId != null">
                #{nextConditionId, jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status, jdbcType=INTEGER},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.iqiyi.vip.domain.entity.ConditionCascade">
        UPDATE condition_cascade
        <set>
            <if test="themeId != null">
                theme_id = #{themeId, jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                business_id = #{businessId, jdbcType=INTEGER},
            </if>
            <if test="currentConditionId != null">
                current_condition_id = #{currentConditionId, jdbcType=INTEGER},
            </if>
            <if test="nextConditionId != null">
                next_condition_id = #{nextConditionId, jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status, jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="deleteByThemeAndBusiness">
        DELETE
        FROM condition_cascade
        WHERE theme_id = #{themeId}
          and business_id = #{businessId}
    </delete>

    <delete id="deleteByConditionId">
        DELETE
        FROM condition_cascade
        WHERE current_condition_id = #{conditionId}
           or next_condition_id = #{conditionId}
    </delete>

    <select id="selectByConditions" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        condition_cascade
        where status=1
        <if test="themeId != null">
            theme_id = #{themeId},
        </if>
        <if test="businessId != null">
            business_id = #{businessId},
        </if>
        <if test="currentConditionId != null">
            current_condition_id = #{currentConditionId},
        </if>
        <if test="nextConditionId != null">
            next_condition_id = #{nextConditionId},
        </if>
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        condition_cascade
        where status=1
        and theme_id = #{themeId}
        and business_id = #{businessId}
        order by update_time desc
    </select>

    <select id="selectList" parameterType="com.iqiyi.vip.po.query.ConditionCascadePageQuery"
            resultMap="BaseResultMap">
        select
        distinct a.id, a.*
        from
        condition_cascade a left join analysis_condition b on (a.current_condition_id = b.id or a.next_condition_id =
        b.id)
        where a.status=1
        <if test="businessId != null">
            and a.business_id=#{businessId}
        </if>
        <if test="themeId != null">
            and a.theme_id=#{themeId}
        </if>
        <if test="startCreateTime != null">
            and a.create_time >= #{startCreateTime}
        </if>
        <if test="endCreateTime != null">
            and a.create_time &lt;= #{endCreateTime}
        </if>
        <if test="startUpdateTime != null">
            and a.update_time >= #{startUpdateTime}
        </if>
        <if test="endUpdateTime != null">
            and a.update_time &lt;= #{endUpdateTime}
        </if>
        <if test="conditionName != null and conditionName != ''">
            and ( b.name like concat('%', #{conditionName}, '%') or  b.aliases like concat('%', #{conditionName}, '%') )
        </if>
        order by a.update_time desc
        limit ${start}, ${limit}
    </select>

    <select id="selectCount" parameterType="com.iqiyi.vip.po.query.ConditionCascadePageQuery"
            resultType="java.lang.Integer">
        select
        count(distinct a.id)
        from
        condition_cascade a left join analysis_condition b on (a.current_condition_id = b.id or a.next_condition_id =
        b.id)
        where a.status=1
        <if test="businessId != null">
            and a.business_id=#{businessId}
        </if>
        <if test="themeId != null">
            and a.theme_id=#{themeId}
        </if>
        <if test="startCreateTime != null">
            and a.create_time >= #{startCreateTime}
        </if>
        <if test="endCreateTime != null">
            and a.create_time &lt;= #{endCreateTime}
        </if>
        <if test="startUpdateTime != null">
            and a.update_time >= #{startUpdateTime}
        </if>
        <if test="endUpdateTime != null">
            and a.update_time &lt;= #{endUpdateTime}
        </if>
        <if test="conditionName != null and conditionName != ''">
            and ( b.name like concat('%', #{conditionName}, '%') or  b.aliases like concat('%', #{conditionName}, '%') )
        </if>
    </select>

</mapper>