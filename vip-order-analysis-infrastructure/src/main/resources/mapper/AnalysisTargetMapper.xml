<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisTargetMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.AnalysisTarget">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="sql_template_id" jdbcType="VARCHAR" property="sqlTemplateId"/>
        <result column="group" jdbcType="INTEGER" property="group"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="documentation" jdbcType="VARCHAR" property="documentation"/>
        <result column="descs" jdbcType="VARCHAR" property="descs"/>
        <result column="partitions" jdbcType="VARCHAR" property="partitions"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="commit_note" jdbcType="VARCHAR" property="commitNote"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, code, `value`, sql_template_id, `group`, `order`, `documentation`, descs, partitions, create_opr, update_opr, create_time, update_time, status, commit_note, version, source
    </sql>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.po.AnalysisTarget" useGeneratedKeys="true" keyColumn="id"
            keyProperty="id">
        insert into analysis_target
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="sqlTemplateId != null">
                sql_template_id,
            </if>
            <if test="group != null">
                `group`,
            </if>
            <if test="order != null">
                `order`,
            </if>
            <if test="documentation != null">
                `documentation`,
            </if>
            <if test="descs != null">
                `descs`,
            </if>
            <if test="partitions != null">
                `partitions`,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="commitNote != null">
                commit_note,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="source != null">
                source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="sqlTemplateId != null">
                #{sqlTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="group != null">
                #{group,jdbcType=INTEGER},
            </if>
            <if test="order != null">
                #{order,jdbcType=INTEGER},
            </if>
            <if test="documentation != null">
                #{documentation,jdbcType=VARCHAR},
            </if>
            <if test="descs != null">
                #{descs,jdbcType=VARCHAR},
            </if>
            <if test="partitions != null">
                #{partitions,jdbcType=VARCHAR},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="commitNote != null">
                #{commitNote,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER}
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.iqiyi.vip.po.AnalysisTarget">
        UPDATE analysis_target
        <set>
            <if test="code!=null">
                code = #{code} ,
            </if>
            <if test="name!=null">
                name = #{name} ,
            </if>
            <if test="value!=null">
                value = #{value} ,
            </if>
            <if test="sqlTemplateId!=null">
                sql_template_id = #{sqlTemplateId} ,
            </if>
            <if test="group!=null">
                `group` = #{group} ,
            </if>
            <if test="order!=null">
                `order` = #{order} ,
            </if>
            <if test="documentation!=null">
                `documentation` = #{documentation} ,
            </if>
            <if test="descs!=null">
                `descs` = #{descs} ,
            </if>
            <if test="partitions!=null">
                `partitions` = #{partitions} ,
            </if>
            <if test="version != null">
                version = #{version} ,
            </if>
            <if test="source != null">
                source = #{source},
            </if>
            update_opr = #{updateOpr},
            `commit_note` = #{commitNote} ,
            update_time = now()
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="delete" parameterType="java.lang.String">
        update analysis_target
        set status=0
        where code = #{code}
    </delete>
    <delete id="deleteById">
        update analysis_target
        set status=0
        where id = #{id}
    </delete>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from analysis_target
        where code =#{code}
    </select>

    <select id="selectByCodeAndBusinessTypeId" resultMap="BaseResultMap">
        select a.*
        from analysis_target a
        left join analysis_target_group adg on a.group = adg.id
        where (a.code like concat('%-separation-', #{code}, '%') or a.code like concat('%', #{code}, '-separation-%') or a.code = #{code})
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from analysis_target
        where id =#{id}
    </select>

    <select id="selectList" parameterType="com.iqiyi.vip.po.query.AnalysisTargetPageQuery"
            resultMap="BaseResultMap">
        select a.*
        from analysis_target a
        left join analysis_target_group atg on a.group = atg.id
        where a.status=1
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        <if test="sqlTemplateId != null">
            and a.sql_template_id = #{sqlTemplateId}
        </if>
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        order by a.update_time desc
        limit ${start}, ${limit}
    </select>

    <select id="selectCount" parameterType="com.iqiyi.vip.po.query.AnalysisTargetPageQuery"
            resultType="java.lang.Integer">
        select count(1)
        from analysis_target a left join analysis_target_group atg on a.group = atg.id
        where a.status=1
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        <if test="sqlTemplateId != null">
            and a.sql_template_id = #{sqlTemplateId}
        </if>
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
    </select>

    <select id="selectByCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_target
        WHERE code in
        <foreach collection="codes" item="code" index="index" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT a.*
        FROM analysis_target a
        left join analysis_target_group atg on a.group = atg.id
        where a.status=1 and a.source = 1
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        <if test="themeType != null">
            and theme_type = #{themeType}
        </if>
        order by a.`order`, a.id
    </select>
</mapper>