<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.CompetitorShopMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.competitor.CompetitorShopPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date" jdbcType="VARCHAR" property="date"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="store_type" jdbcType="INTEGER" property="storeType"/>
        <result column="screenshot_urls" jdbcType="VARCHAR" property="screenshotUrls" typeHandler="com.iqiyi.vip.handler.mybatis.ListStringHandler"/>
        <result column="interaction_video" jdbcType="VARCHAR" property="interactionVideo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap id="DateRangeResultMap" type="com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO">
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, date, platform, brand, store_type, screenshot_urls, interaction_video, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from competitor_shop where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.po.competitor.CompetitorShopPO" keyProperty="id" useGeneratedKeys="true">
        insert into competitor_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="storeType != null">
                store_type,
            </if>
            <if test="interactionVideo != null">
                interaction_video,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="screenshotUrls != null">
                screenshot_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="storeType != null">
                #{storeType,jdbcType=INTEGER},
            </if>
            <if test="interactionVideo != null">
                #{interactionVideo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="screenshotUrls != null">
                #{screenshotUrls,jdbcType=LONGVARCHAR,typeHandler=com.iqiyi.vip.handler.mybatis.ListStringHandler},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        date = #{date,jdbcType=VARCHAR},
        platform = #{platform,jdbcType=VARCHAR},
        brand = #{brand,jdbcType=VARCHAR},
        store_type = #{storeType,jdbcType=INTEGER},
        interaction_video = #{interactionVideo,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        screenshot_urls = #{screenshotUrls,jdbcType=LONGVARCHAR,typeHandler=com.iqiyi.vip.handler.mybatis.ListStringHandler}
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.competitor.CompetitorShopPO">
        update competitor_shop
        <set>
            <if test="date != null">
                date = #{date,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="storeType != null">
                store_type = #{storeType,jdbcType=INTEGER},
            </if>
            <if test="interactionVideo != null">
                interaction_video = #{interactionVideo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="screenshotUrls != null">
                screenshot_urls = #{screenshotUrls,jdbcType=LONGVARCHAR,typeHandler=com.iqiyi.vip.handler.mybatis.ListStringHandler},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAllPlatform" resultType="java.lang.String">
        select distinct platform from competitor_shop order by platform
    </select>

    <select id="selectAllBrand" resultType="java.lang.String">
        select distinct brand
        from competitor_shop
        <where>
            <if test="platform != null">
                platform = #{platform}
            </if>   
            <if test="date != null and !date.isEmpty()">
                and date = #{date}
            </if>
        </where>
        order by brand
    </select>

    <select id="selectDateRange" resultMap="DateRangeResultMap">
        select MIN(date) AS start_date, MAX(date) AS end_date from competitor_shop
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_shop
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="search" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_shop
        where date = #{date}
        and platform = #{platform}
        <if test="brand != null">
            and brand = #{brand}
        </if>
        <if test="storeType != null">
            and store_type = #{storeType}
        </if>
    </select>
</mapper> 