<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.CompetitorUiMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.competitor.CompetitorUiPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="client_version" jdbcType="VARCHAR" property="clientVersion"/>
        <result column="client_type" jdbcType="VARCHAR" property="clientType"/>
        <result column="page" jdbcType="VARCHAR" property="page"/>
        <result column="user_status" jdbcType="VARCHAR" property="userStatus"/>
        <result column="screenshot_urls" jdbcType="VARCHAR" property="screenshotUrls" typeHandler="com.iqiyi.vip.handler.mybatis.ListStringHandler"/>
        <result column="screen_recording" jdbcType="VARCHAR" property="screenRecording"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap id="DateRangeResultMap" type="com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO">
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, date, brand, client_version, client_type, page, user_status, screenshot_urls, screen_recording, create_time,
        update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from competitor_ui where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.po.competitor.CompetitorUiPO" keyProperty="id" useGeneratedKeys="true">
        insert into competitor_ui
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="clientVersion != null">
                client_version,
            </if>
            <if test="clientType != null">
                client_type,
            </if>
            <if test="page != null">
                page,
            </if>
            <if test="userStatus != null">
                user_status,
            </if>
            <if test="screenRecording != null">
                screen_recording,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="screenshotUrls != null">
                screenshot_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="clientVersion != null">
                #{clientVersion,jdbcType=VARCHAR},
            </if>
            <if test="clientType != null">
                #{clientType,jdbcType=VARCHAR},
            </if>
            <if test="page != null">
                #{page,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                #{userStatus,jdbcType=VARCHAR},
            </if>
            <if test="screenRecording != null">
                #{screenRecording,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="screenshotUrls != null">
                #{screenshotUrls,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.competitor.CompetitorUiPO">
        update competitor_ui
        <set>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="clientVersion != null">
                client_version = #{clientVersion,jdbcType=VARCHAR},
            </if>
            <if test="clientType != null">
                client_type = #{clientType,jdbcType=VARCHAR},
            </if>
            <if test="page != null">
                page = #{page,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null">
                user_status = #{userStatus,jdbcType=VARCHAR},
            </if>
            <if test="screenRecording != null">
                screen_recording = #{screenRecording,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="screenshotUrls != null">
                screenshot_urls = #{screenshotUrls,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAllBrand" resultType="java.lang.String">
        select brand from competitor_ui group by brand order by brand
    </select>

    <select id="selectAllUserStatus" resultType="java.lang.String">
        select user_status
        from competitor_ui
        where brand = #{brand}
        <if test="date != null and !date.isEmpty()">
            and date = #{date}
        </if>
        group by user_status
        order by user_status
    </select>

    <select id="selectAllClientVersion" resultType="java.lang.String">
        select client_version from competitor_ui group by client_version order by client_version
    </select>

    <select id="selectDateRange" resultMap="DateRangeResultMap">
        select MIN(date) AS start_date, MAX(date) AS end_date from competitor_ui
    </select>

    <select id="selectAllPage" resultType="java.lang.String">
        select page
        from competitor_ui
        where brand = #{brand}
        <if test="date != null and !date.isEmpty()">
            and date = #{date}
        </if>
        group by page
        order by page
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_ui
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="search" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from competitor_ui
        where date = #{date}
        and brand = #{brand}
        <if test="userStatus != null">
            and user_status = #{userStatus}
        </if>
        <if test="clientVersion != null">
            and client_version = #{clientVersion}
        </if>
        <if test="page != null">
            and page = #{page}
        </if>
    </select>

</mapper>