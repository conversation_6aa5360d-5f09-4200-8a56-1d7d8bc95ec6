<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.BusinessConditionMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.BusinessCondition">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="theme_id" jdbcType="INTEGER" property="themeId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="condition_id" jdbcType="INTEGER" property="conditionId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , theme_id, business_id, condition_id, sort, operator, create_time, update_time
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into business_condition (theme_id, business_id, condition_id, sort, operator, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.themeId,jdbcType=INTEGER}, #{item.businessId,jdbcType=INTEGER}, #{item.conditionId,jdbcType=INTEGER}, #{item.sort,jdbcType=INTEGER},
            #{item.operator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <delete id="deleteByThemeAndBusiness">
        DELETE FROM business_condition
        WHERE theme_id = #{themeId}
          and business_id = #{businessId}
    </delete>

    <delete id="deleteByConditionId">
        DELETE FROM business_condition
        WHERE condition_id = #{conditionId}
    </delete>

    <select id="selectByThemeAndBusiness" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        business_condition
        where theme_id = #{themeId}
        and business_id = #{businessId}
        order by sort asc
    </select>

</mapper>