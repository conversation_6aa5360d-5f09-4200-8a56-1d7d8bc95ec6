<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.DataPermissionOaMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.DataPermissionOa">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="oa_identifier" jdbcType="VARCHAR" property="oaIdentifier"/>
        <result column="account" jdbcType="VARCHAR" property="account"/>
        <result column="permission_reason" jdbcType="VARCHAR" property="permissionReason"/>
        <result column="permission" jdbcType="VARCHAR" property="permission"/>
        <result column="permission_status" jdbcType="INTEGER" property="permissionStatus"/>
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="operators" jdbcType="VARCHAR" property="operators"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, oa_identifier, account, permission_reason, permission, permission_status, reject_reason, operators, create_time, update_time, message_id, theme_type, `type`
    </sql>
    <sql id="Base_Column_Insert">
        oa_identifier, account, permission_reason, permission, permission_status, reject_reason, operators, create_time, update_time, message_id, theme_type, `type`
    </sql>

    <select id="getDataPermissionOaByOaIdentifierAndMessageId" resultType="com.iqiyi.vip.domain.entity.DataPermissionOa">
        select
        <include refid="Base_Column_List"/>
        from data_permission_oa
        <trim suffixOverrides="and">
            <where>
                <if test="oaIdentifier != null">
                    oa_identifier = #{oaIdentifier}
                </if>
                <if test="messageId != null">
                    and message_id = #{messageId, jdbcType=VARCHAR}
                </if>
            </where>
        </trim>
    </select>

    <select id="getDataPermissionOaById" resultType="com.iqiyi.vip.domain.entity.DataPermissionOa">
        select
        <include refid="Base_Column_List"/>
        from data_permission_oa
        <trim suffixOverrides="and">
            <where>
                id = #{id, jdbcType=INTEGER}
            </where>
        </trim>
    </select>

    <update id="updateDataPermissionOaStatusById">
        update data_permission_oa
        <trim suffixOverrides=",">
            <set>
                <if test="permissionStatus != null">
                    permission_status = #{permissionStatus, jdbcType=INTEGER},
                </if>
                <if test="permission != null">
                    permission = #{permission, jdbcType=VARCHAR},
                </if>
                <if test="rejectReason != null">
                    reject_reason = #{rejectReason, jdbcType=VARCHAR},
                </if>
                <if test="operators != null">
                    operators = operators + #{operators, jdbcType=VARCHAR},
                </if>
                <if test="oaIdentifier != null">
                    oa_identifier = #{oaIdentifier, jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
        </trim>
        <where>
            id = #{id, jdbcType=INTEGER}
        </where>
    </update>

    <update id="updateDataPermissionOaStatusByAcc">
        update data_permission_oa
        <trim suffixOverrides=",">
            <set>
                <if test="permissionStatus != null">
                    permission_status = #{permissionStatus, jdbcType=INTEGER},
                </if>
                <if test="rejectReason != null">
                    reject_reason = #{rejectReason, jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
        </trim>
        <where>
            oa_identifier = #{oaIdentifier, jdbcType=VARCHAR}
        </where>
    </update>

    <update id="updateDataPermissionOaAcIdByMessageId">
        update data_permission_oa
        <trim suffixOverrides=",">
            <set>
                <if test="oaIdentifier != null">
                    oa_identifier = #{oaIdentifier, jdbcType=VARCHAR},
                </if>
                update_time = now()
            </set>
        </trim>
        <where>
            message_id = #{messageId,jdbcType=VARCHAR}
        </where>
    </update>

    <insert id="insertDataPermissionOa" parameterType="com.iqiyi.vip.domain.entity.DataPermissionOa">
        insert into data_permission_oa (
        <include refid="Base_Column_Insert"/>
        ) values (
        #{oaIdentifier, jdbcType=VARCHAR}, #{account, jdbcType=VARCHAR}, #{permissionReason, jdbcType=VARCHAR}, #{permission, jdbcType=VARCHAR}
        , #{permissionStatus, jdbcType=INTEGER}, #{rejectReason, jdbcType=VARCHAR}, #{operators, jdbcType=VARCHAR}
        , #{createTime, jdbcType=TIMESTAMP}, #{updateTime, jdbcType=TIMESTAMP}, #{messageId, jdbcType=VARCHAR}, #{themeType, jdbcType=INTEGER}, #{type, jdbcType=INTEGER}
        )
    </insert>

    <!-- 批量 -->
    <insert id="insertDataPermissionOas" parameterType="com.iqiyi.vip.domain.entity.DataPermissionOa">
        insert into data_permission_oa (
        <include refid="Base_Column_Insert"/>
        ) values
        <foreach collection="dataPermissionOas" item="dataPermissionOa" separator=",">
            (
            #{dataPermissionOa.oaIdentifier, jdbcType=VARCHAR}, #{dataPermissionOa.account, jdbcType=VARCHAR}, #{dataPermissionOa.permissionReason, jdbcType=VARCHAR}, #{dataPermissionOa.permission, jdbcType=VARCHAR}
            , #{dataPermissionOa.permissionStatus, jdbcType=INTEGER}, #{dataPermissionOa.rejectReason, jdbcType=VARCHAR}, #{dataPermissionOa.operators, jdbcType=VARCHAR}
            , #{dataPermissionOa.createTime, jdbcType=TIMESTAMP}, #{dataPermissionOa.updateTime, jdbcType=TIMESTAMP}, #{dataPermissionOa.messageId, jdbcType=VARCHAR}
            , #{dataPermissionOa.themeType, jdbcType=INTEGER}, #{dataPermissionOa.type, jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="existApprovingDataPermission" resultType="java.lang.Integer">
        select count(*) from data_permission_oa
        <trim suffixOverrides="and">
            <where>
                account = #{account, jdbcType=VARCHAR} and permission_status = 1
            </where>
        </trim>
    </select>

</mapper>