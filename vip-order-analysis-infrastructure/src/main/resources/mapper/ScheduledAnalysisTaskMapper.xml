<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.ScheduledAnalysisTaskMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AnalysisTaskDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="targets" jdbcType="VARCHAR" property="targets"/>
        <result column="condition" jdbcType="VARCHAR" property="condition"/>
        <result column="diagnosis_condition" jdbcType="VARCHAR" property="diagnosisCondition"/>
        <result column="dimensions" jdbcType="VARCHAR" property="dimensions"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="result" jdbcType="VARCHAR" property="result"/>
        <result column="execute_time" jdbcType="INTEGER" property="executeTime"/>
        <result column="execute_cycle" jdbcType="VARCHAR" property="executeCycle"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="unique_identification" jdbcType="VARCHAR" property="uniqueIdentification"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="business_type_id" jdbcType="INTEGER" property="businessTypeId"/>
        <result column="data_permission_type" jdbcType="TINYINT" property="dataPermissionType"/>
        <result column="theme_type" jdbcType="TINYINT" property="themeType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, `task_name`, targets, `condition`, diagnosis_condition, dimensions, `status`, `result`, `execute_time`, execute_cycle,
        create_time, update_time, operator, unique_identification, delete_status, business_type_id,
        data_permission_type, theme_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from scheduled_analysis_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getByUniqueIdentification" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from scheduled_analysis_task
        where unique_identification = #{uniqueIdentification, jdbcType=VARCHAR}
        and status != 3
        order by update_time limit 1
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AnalysisTaskDO" useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID()
        </selectKey>
        insert into scheduled_analysis_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">
                task_name,
            </if>
            <if test="targets != null">
                targets,
            </if>
            <if test="condition != null">
                `condition`,
            </if>
            <if test="diagnosisCondition != null">
                `diagnosis_condition`,
            </if>
            <if test="dimensions != null">
                dimensions,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="result != null">
                `result`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="uniqueIdentification != null">
                unique_identification,
            </if>
            <if test="businessTypeId != null">
                business_type_id,
            </if>
            <if test="dataPermissionType != null">
                data_permission_type,
            </if>
            <if test="themeType != null">
                theme_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="targets != null">
                #{targets,jdbcType=VARCHAR},
            </if>
            <if test="condition != null">
                #{condition,jdbcType=VARCHAR},
            </if>
            <if test="diagnosisCondition != null">
                #{diagnosisCondition,jdbcType=VARCHAR},
            </if>
            <if test="dimensions != null">
                #{dimensions,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="result != null">
                #{result,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="uniqueIdentification != null">
                #{uniqueIdentification,jdbcType=VARCHAR},
            </if>
            <if test="businessTypeId != null">
                #{businessTypeId},
            </if>
            <if test="dataPermissionType != null">
                #{dataPermissionType},
            </if>
            <if test="themeType != null">
                #{themeType},
            </if>
        </trim>
    </insert>

    <update id="reSetTaskStatus">
        update scheduled_analysis_task
        set status = #{status, jdbcType=INTEGER} , result = #{result, jdbcType=VARCHAR}, update_time = now()
        <if test="executeTime != null">
            , `execute_time` = #{executeTime,jdbcType=INTEGER}
        </if>
        where id = #{taskId, jdbcType=BIGINT}
    </update>

</mapper>