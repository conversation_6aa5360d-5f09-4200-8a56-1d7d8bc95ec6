<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.ConditionGeneralMySQLMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.dto.condition.ConditionPair">
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
    </resultMap>
    <select id="select" resultMap="BaseResultMap">
        ${querySQL}
    </select>


</mapper>