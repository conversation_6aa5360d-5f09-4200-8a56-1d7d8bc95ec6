<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.QiyuePlatformMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.QiyuePlatform">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="biz_name" jdbcType="VARCHAR" property="bizName"/>
        <result column="end_key" jdbcType="VARCHAR" property="endKey"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, `code`, `name`, description, biz_name, end_key
  </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM qiyue_platform
    </select>

    <select id="selectByCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qiyue_platform
        WHERE code in
        <foreach collection="codes" item="code" index="index" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="getQiyuePlatforms" resultType="com.iqiyi.vip.dto.condition.QiyuePlatformPair">
        select ${columns} from qiyue_platform where ${whereColumns} group by ${groupByColumns}
    </select>

</mapper>