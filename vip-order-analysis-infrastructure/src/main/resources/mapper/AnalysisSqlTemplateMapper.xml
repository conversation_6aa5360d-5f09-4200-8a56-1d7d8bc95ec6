<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisSqlTemplateMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.AnalysisSqlTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="commit_note" jdbcType="VARCHAR" property="commitNote"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="business_type_id" jdbcType="INTEGER" property="businessTypeId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, `value`, description, create_opr, update_opr, create_time, update_time, status, commit_note, version, theme_type, business_type_id
    </sql>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.po.AnalysisSqlTemplate" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into analysis_sql_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="commitNote != null">
                commit_note,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="themeType != null">
                theme_type,
            </if>
            <if test="businessTypeId != null">
                business_type_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="commitNote != null">
                #{commitNote,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="themeType != null">
                #{themeType,jdbcType=INTEGER},
            </if>
            <if test="businessTypeId != null">
                #{businessTypeId,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.iqiyi.vip.po.AnalysisSqlTemplate">
        UPDATE analysis_sql_template
        <set>
            <if test="code!=null">
                code = #{code} ,
            </if>
            <if test="name!=null">
                name = #{name} ,
            </if>
            <if test="value!=null">
                value = #{value} ,
            </if>
            <if test="description!=null">
                description = #{description} ,
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="themeType != null">
                theme_type = #{themeType},
            </if>
            <if test="businessTypeId != null">
                business_type_id = #{businessTypeId},
            </if>
            update_opr = #{updateOpr},
            commit_note = #{commitNote} ,
            update_time = now()
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        update analysis_sql_template
        set status=0
        where id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_sql_template
        where id =#{id}
    </select>

    <select id="selectList" parameterType="com.iqiyi.vip.po.query.AnalysisSqlTemplatePageQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_sql_template
        where status=1
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="name != null">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        order by update_time desc
        limit ${start}, ${limit}
    </select>

    <select id="selectCount" parameterType="com.iqiyi.vip.po.query.AnalysisSqlTemplatePageQuery"
            resultType="java.lang.Integer">
        select
        count(1)
        from
        analysis_sql_template
        where status=1
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="name != null">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_sql_template
        where
        status = 1
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
    </select>
</mapper>