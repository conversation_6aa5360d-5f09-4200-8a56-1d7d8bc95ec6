<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisThemeTypeMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AnalysisThemeType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, `name`, `sort`, create_time, update_time, operator, status
  </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tianyan_theme_type
        where
        status = 1
        order by `sort`, id
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tianyan_theme_type
        where id = #{id}
        and status = 1
    </select>

</mapper>