<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisConditionMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AnalysisCondition">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="required" jdbcType="BOOLEAN" property="required"/>
        <result column="default_prompt" jdbcType="VARCHAR" property="defaultPrompt"/>
        <result column="data_load_addr" jdbcType="VARCHAR" property="dataLoadAddr"/>
        <result column="select_all" jdbcType="BOOLEAN" property="selectAll"/>
        <result column="has_tips_icon" jdbcType="BOOLEAN" property="hasTipsIcon"/>
        <result column="tips" jdbcType="VARCHAR" property="tips"/>
        <result column="field_type" jdbcType="INTEGER" property="fieldType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="multi_choice" jdbcType="INTEGER" property="multiChoice"/>
        <result column="biz_field_name" jdbcType="VARCHAR" property="bizFieldName" typeHandler="com.iqiyi.vip.handler.mybatis.BizFieldNameTypeHandler"/>
        <result column="aliases" jdbcType="VARCHAR" property="aliases" typeHandler="com.iqiyi.vip.handler.mybatis.ConditionAliasesTypeHandler"/>
        <result column="enums" jdbcType="VARCHAR" property="enums" typeHandler="com.iqiyi.vip.handler.mybatis.ConditionEnumsTypeHandler"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="clause_op" jdbcType="VARCHAR" property="clauseOp"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, code, `type`, required, default_prompt, data_load_addr, select_all, has_tips_icon, tips, field_type, multi_choice, status, biz_field_name,
        aliases, enums, create_opr, update_opr, create_time, update_time, default_value, clause_op
    </sql>

    <insert id="insertSelective" parameterType="com.iqiyi.vip.domain.entity.AnalysisCondition" useGeneratedKeys="true" keyColumn="id"
        keyProperty="id">
        insert into analysis_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="required != null">
                required,
            </if>
            <if test="defaultPrompt != null">
                `default_prompt`,
            </if>
            <if test="dataLoadAddr != null">
                `data_load_addr`,
            </if>
            <if test="selectAll != null">
                `select_all`,
            </if>
            <if test="hasTipsIcon != null">
                `has_tips_icon`,
            </if>
            <if test="tips != null">
                `tips`,
            </if>
            <if test="fieldType != null">
                field_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="multiChoice != null">
                multi_choice,
            </if>
            <if test="aliases != null">
                aliases,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="defaultValue != null">
                default_value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                #{required,jdbcType=BOOLEAN},
            </if>
            <if test="defaultPrompt != null">
                #{defaultPrompt,jdbcType=VARCHAR},
            </if>
            <if test="dataLoadAddr != null">
                #{dataLoadAddr,jdbcType=VARCHAR},
            </if>
            <if test="selectAll != null">
                #{selectAll,jdbcType=BOOLEAN},
            </if>
            <if test="hasTipsIcon != null">
                #{hasTipsIcon,jdbcType=BOOLEAN},
            </if>
            <if test="tips != null">
                #{tips,jdbcType=VARCHAR},
            </if>
            <if test="fieldType != null">
                #{fieldType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="multiChoice != null">
                #{multiChoice,jdbcType=INTEGER},
            </if>
            <if test="aliases != null">
                #{aliases,jdbcType=VARCHAR,typeHandler=com.iqiyi.vip.handler.mybatis.ConditionAliasesTypeHandler},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="defaultValue != null">
                #{defaultValue,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.iqiyi.vip.domain.entity.AnalysisCondition">
        UPDATE analysis_condition
        <set>
            <if test="code!=null">
                code = #{code} ,
            </if>
            <if test="name!=null">
                name = #{name} ,
            </if>
            <if test="type!=null">
                type = #{type} ,
            </if>
            <if test="required != null">
                required = #{required},
            </if>
            <if test="defaultPrompt != null">
                default_prompt = #{defaultPrompt},
            </if>
            <if test="dataLoadAddr != null">
                data_load_addr = #{dataLoadAddr},
            </if>
            <if test="selectAll != null">
                select_all = #{selectAll},
            </if>
            <if test="hasTipsIcon != null">
                has_tips_icon = #{hasTipsIcon},
            </if>
            <if test="tips != null">
                tips = #{tips},
            </if>
            <if test="fieldType != null">
                field_type = #{fieldType},
            </if>
            <if test="multiChoice != null">
                multi_choice = #{multiChoice},
            </if>
            <if test="aliases != null">
                aliases = #{aliases,jdbcType=VARCHAR,typeHandler=com.iqiyi.vip.handler.mybatis.ConditionAliasesTypeHandler},
            </if>
            <if test="defaultValue != null">
                default_value = #{defaultValue},
            </if>
            update_opr = #{updateOpr}
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <delete id="delete">
        update analysis_condition
        set status=0,
            update_opr = #{operator}
        where code = #{code}
    </delete>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_condition
        where code =#{code}
    </select>

    <select id="batchSelectByCode" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from analysis_condition
        where code in
        <foreach collection="codes" item="code" index="index" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_condition
        where name =#{name}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_condition
        where id =#{id}
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_condition
        WHERE id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectList" parameterType="com.iqiyi.vip.po.query.AnalysisConditionPageQuery"
        resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_condition
        where status=1
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="startCreateTime != null">
            and create_time >= #{startCreateTime}
        </if>
        <if test="endCreateTime != null">
            and create_time &lt;= #{endCreateTime}
        </if>
        <if test="startUpdateTime != null">
            and update_time >= #{startUpdateTime}
        </if>
        <if test="endUpdateTime != null">
            and update_time &lt;= #{endUpdateTime}
        </if>
        order by update_time desc
        limit ${start}, ${limit}
    </select>

    <select id="selectCount" parameterType="com.iqiyi.vip.po.query.AnalysisConditionPageQuery"
        resultType="java.lang.Integer">
        select
        count(1)
        from
        analysis_condition
        where status=1
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="startCreateTime != null">
            and create_time >= #{startCreateTime}
        </if>
        <if test="endCreateTime != null">
            and create_time &lt;= #{endCreateTime}
        </if>
        <if test="startUpdateTime != null">
            and update_time >= #{startUpdateTime}
        </if>
        <if test="endUpdateTime != null">
            and update_time &lt;= #{endUpdateTime}
        </if>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_condition
        where status=1
        order by update_time desc
    </select>

</mapper>