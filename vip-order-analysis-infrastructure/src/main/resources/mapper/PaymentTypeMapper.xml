<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.PaymentTypeMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.PaymentType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="sub_pay_channel" jdbcType="INTEGER" property="subPayChannel"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, `name`, description, status, pay_channel, sub_pay_channel
  </sql>

    <select id="selectByChannel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM payment_type
        where
        pay_channel = #{payChannel} and status = 1
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM payment_type
        where
        status = 1
    </select>
</mapper>