<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisSqlTemplateCKMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.AnalysisSqlTemplate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="commit_note" jdbcType="VARCHAR" property="commitNote"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="business_type_id" jdbcType="INTEGER" property="businessTypeId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, `value`, description, create_opr, update_opr, create_time, update_time, status, commit_note, version, business_type_id
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        analysis_sql_template_ck
        where id =#{id}
    </select>
</mapper>