<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.TeamMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.Team">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="INTEGER" property="name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , name
    </sql>
    <select id="getAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fv_team
    </select>
</mapper>