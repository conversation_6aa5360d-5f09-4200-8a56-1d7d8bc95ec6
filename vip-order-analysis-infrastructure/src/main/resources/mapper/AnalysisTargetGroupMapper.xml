<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisTargetGroupMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.AnalysisTargetGroup">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="parent" jdbcType="INTEGER" property="parent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="business_type_id" jdbcType="INTEGER" property="businessTypeId"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="advanced_option" jdbcType="INTEGER" property="advancedOption"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, `order`, `parent`, `type`, create_time, update_time, operator, status, business_type_id, theme_type, advanced_option
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_target_group
        where
        id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_target_group
        where
        status = 1
        order by `order`, id
    </select>

    <select id="selectByBusinessTypeAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM analysis_target_group
        where
        status = 1
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="themeType != null">
            and theme_type = #{themeType}
        </if>
        order by `order`, id
    </select>

</mapper>