<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.YunheAlbumMetaDataMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.YunheAlbumMetaDataDO">
        <id column="album_id" jdbcType="BIGINT" property="albumId"/>
        <result column="album_name" jdbcType="VARCHAR" property="albumName"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
        <result column="episode_num" jdbcType="VARCHAR" property="episodeNum"/>
        <result column="total_episode_num" jdbcType="VARCHAR" property="totalEpisodeNum"/>
        <result column="channels" jdbcType="VARCHAR" property="channels"/>
        <result column="release_time" jdbcType="VARCHAR" property="releaseTime"/>
        <result column="works_type" jdbcType="VARCHAR" property="worksType"/>
        <result column="day_platform" jdbcType="VARCHAR" property="dayPlatform"/>
        <result column="not_vip_downtime" jdbcType="VARCHAR" property="notVipDowntime"/>
        <result column="db_rating" jdbcType="DOUBLE" property="dbRating"/>
        <result column="yunhe_level" jdbcType="VARCHAR" property="yunheLevel"/>
        <result column="offline_time" jdbcType="VARCHAR" property="offlineTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="day_platform_start" jdbcType="VARCHAR" property="dayPlatformStart"/>
    </resultMap>

    <sql id="Base_Column_List">
        album_id, album_name, tags, episode_num, total_episode_num, channels, release_time, works_type, day_platform, not_vip_downtime, db_rating, yunhe_level, offline_time, create_time, update_time, day_platform_start
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from yunhe_album_meta_data
        where album_id = #{albumId,jdbcType=BIGINT}
    </select>
    
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from yunhe_album_meta_data
    </select>
    
    <select id="selectByPlatformAndDateRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from yunhe_album_meta_data
        where 1=1
        <if test="platform != null and platform != ''">
            and day_platform_start like CONCAT('%', #{platform}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            <![CDATA[ and release_time >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ and (offline_time <= #{endDate} or offline_time is null) ]]>
        </if>
    </select>

    <insert id="insert" parameterType="com.iqiyi.vip.domain.entity.YunheAlbumMetaDataDO" useGeneratedKeys="true" keyProperty="albumId">
        insert into yunhe_album_meta_data (album_id, album_name, tags, episode_num, total_episode_num, channels, release_time, works_type, day_platform, not_vip_downtime, db_rating, yunhe_level, offline_time, day_platform_start, create_time, update_time)
        values (#{albumId,jdbcType=BIGINT}, #{albumName,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR}, #{episodeNum,jdbcType=VARCHAR}, #{totalEpisodeNum,jdbcType=VARCHAR}, #{channels,jdbcType=VARCHAR}, #{releaseTime,jdbcType=VARCHAR}, #{worksType,jdbcType=VARCHAR}, #{dayPlatform,jdbcType=VARCHAR}, #{notVipDowntime,jdbcType=VARCHAR}, #{dbRating,jdbcType=DOUBLE}, #{yunheLevel,jdbcType=VARCHAR}, #{offlineTime,jdbcType=VARCHAR}, #{dayPlatformStart,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.domain.entity.YunheAlbumMetaDataDO">
        update yunhe_album_meta_data
        set album_name = #{albumName,jdbcType=VARCHAR},
          tags = #{tags,jdbcType=VARCHAR},
          episode_num = #{episodeNum,jdbcType=VARCHAR},
          total_episode_num = #{totalEpisodeNum,jdbcType=VARCHAR},
          channels = #{channels,jdbcType=VARCHAR},
          release_time = #{releaseTime,jdbcType=VARCHAR},
          works_type = #{worksType,jdbcType=VARCHAR},
          day_platform = #{dayPlatform,jdbcType=VARCHAR},
          not_vip_downtime = #{notVipDowntime,jdbcType=VARCHAR},
          db_rating = #{dbRating,jdbcType=DOUBLE},
          yunhe_level = #{yunheLevel,jdbcType=VARCHAR},
          offline_time = #{offlineTime,jdbcType=VARCHAR},
          day_platform_start = #{dayPlatformStart,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where album_id = #{albumId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yunhe_album_meta_data
        where album_id = #{albumId,jdbcType=BIGINT}
    </delete>

    <!-- 根据时间范围查询专辑内容信息 -->
    <select id="searchByDateRange" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from yunhe_album_meta_data
        where release_time >= #{startDate}
        and (offline_time is null or offline_time = '' or #{endDate} >= offline_time)
        <if test="brand != null and brand != ''">
            and day_platform_start like CONCAT('%', #{brand}, '%')
        </if>
    </select>

</mapper> 