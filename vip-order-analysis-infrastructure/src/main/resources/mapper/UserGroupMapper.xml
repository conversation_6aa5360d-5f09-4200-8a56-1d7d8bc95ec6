<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.UserGroupMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.UserGroup">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="group_type" jdbcType="INTEGER" property="groupType"/>
        <result column="create_type" jdbcType="INTEGER" property="createType"/>
        <result column="data_url" jdbcType="VARCHAR" property="dataUrl"/>
        <result column="data_count" jdbcType="BIGINT" property="dataCount"/>
        <result column="upload_progress" jdbcType="INTEGER" property="uploadProgress"/>
        <result column="is_beidou_synced" jdbcType="INTEGER" property="isBeidouSynced"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="sub_task_id" jdbcType="BIGINT" property="subTaskId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , `name`, group_type, create_type, data_url, data_count, upload_progress, is_beidou_synced,
    `status`, create_time, update_time, `operator`,create_user,sub_task_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_group
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectCount" parameterType="com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO"
        resultType="java.lang.Integer">
        select
        count(1)
        from user_group
        where status = 1
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="operator != null and operator != ''">
            and operator = #{operator}
        </if>
        <if test="createUser != null and createUser != ''">
            and create_user = #{createUser}
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="groupType != null">
            and group_type = #{groupType}
        </if>
        <if test="createType != null">
            and create_type = #{createType}
        </if>
        <if test="isBeidouSynced != null">
            and is_beidou_synced = #{isBeidouSynced}
        </if>
        <if test="startTime != null">
            and create_time > #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[<]]> #{endTime}
        </if>
    </select>

    <select id="selectList" parameterType="com.iqiyi.vip.dto.user.group.UserGroupPageQueryDTO"
        resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_group
        where status = 1
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="operator != null and operator != ''">
            and operator = #{operator}
        </if>
        <if test="createUser != null and createUser != ''">
            and create_user = #{createUser}
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="groupType != null">
            and group_type = #{groupType}
        </if>
        <if test="createType != null">
            and create_type = #{createType}
        </if>
        <if test="isBeidouSynced != null">
            and is_beidou_synced = #{isBeidouSynced}
        </if>
        <if test="startTime != null">
            and create_time > #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[<]]> #{endTime}
        </if>
        order by update_time desc
        <if test="start != null and limit != null">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        update user_group
        set `status`= 0
        where id = #{id,jdbcType=INTEGER}
          and operator = #{operator}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.po.UserGroup" useGeneratedKeys="true">
        insert into user_group (`name`, group_type, create_type,
                                data_url, data_count, upload_progress,
                                is_beidou_synced, `status`, create_time,
                                update_time, `operator`, `create_user`, `sub_task_id`)
        values (#{name,jdbcType=VARCHAR}, #{groupType,jdbcType=INTEGER}, #{createType,jdbcType=INTEGER},
                #{dataUrl,jdbcType=VARCHAR}, #{dataCount,jdbcType=BIGINT}, #{uploadProgress,jdbcType=INTEGER},
                #{isBeidouSynced,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{subTaskId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.po.UserGroup" useGeneratedKeys="true">
        insert into user_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="groupType != null">
                group_type,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="dataUrl != null">
                data_url,
            </if>
            <if test="dataCount != null">
                data_count,
            </if>
            <if test="uploadProgress != null">
                upload_progress,
            </if>
            <if test="isBeidouSynced != null">
                is_beidou_synced,
            </if>
            <if test="status != null">
                `status`,
            </if>
            create_time,
            update_time,
            <if test="operator != null">
                `operator`,
            </if>
            <if test="createUser != null">
                `create_user`,
            </if>
            <if test="subTaskId != null">
                `sub_task_id`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="groupType != null">
                #{groupType,jdbcType=INTEGER},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="dataUrl != null">
                #{dataUrl,jdbcType=VARCHAR},
            </if>
            <if test="dataCount != null">
                #{dataCount,jdbcType=BIGINT},
            </if>
            <if test="uploadProgress != null">
                #{uploadProgress,jdbcType=INTEGER},
            </if>
            <if test="isBeidouSynced != null">
                #{isBeidouSynced,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            now(),
            now(),
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="subTaskId != null">
                #{subTaskId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.UserGroup">
        update user_group
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="groupType != null">
                group_type = #{groupType,jdbcType=INTEGER},
            </if>
            <if test="createType != null">
                create_type = #{createType,jdbcType=INTEGER},
            </if>
            <if test="dataUrl != null">
                data_url = #{dataUrl,jdbcType=VARCHAR},
            </if>
            <if test="dataCount != null">
                data_count = #{dataCount,jdbcType=BIGINT},
            </if>
            <if test="uploadProgress != null">
                upload_progress = #{uploadProgress,jdbcType=INTEGER},
            </if>
            <if test="isBeidouSynced != null">
                is_beidou_synced = #{isBeidouSynced,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            update_time = now(),
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="subTaskId != null">
                `sub_task_id` = #{subTaskId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.UserGroup">
        update user_group
        set `name`           = #{name,jdbcType=VARCHAR},
            group_type       = #{groupType,jdbcType=INTEGER},
            create_type      = #{createType,jdbcType=INTEGER},
            data_url         = #{dataUrl,jdbcType=VARCHAR},
            data_count       = #{dataCount,jdbcType=BIGINT},
            upload_progress  = #{uploadProgress,jdbcType=INTEGER},
            is_beidou_synced = #{isBeidouSynced,jdbcType=INTEGER},
            `status`         = #{status,jdbcType=TINYINT},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_time      = now(),
            `operator`       = #{operator,jdbcType=VARCHAR},
            `create_user`    = #{createUser,jdbcType=VARCHAR},
            `sub_task_id`    = #{subTaskId,jdbcType=BIGINT},
            where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>