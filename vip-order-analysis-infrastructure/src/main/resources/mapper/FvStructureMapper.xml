<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.FvStructureMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.FvStructureDO">
      <result column="level1_id" jdbcType="INTEGER" property="level1Id"/>
      <result column="level2_id" jdbcType="INTEGER" property="level2Id"/>
      <result column="level3_id" jdbcType="INTEGER" property="level3Id"/>
      <result column="level4_id" jdbcType="INTEGER" property="level4Id"/>
      <result column="level5_id" jdbcType="INTEGER" property="level5Id"/>
      <result column="level6_id" jdbcType="INTEGER" property="level6Id"/>
      <result column="level7_id" jdbcType="INTEGER" property="level7Id"/>
      <result column="level8_id" jdbcType="INTEGER" property="level8Id"/>
      <result column="level1_name" jdbcType="VARCHAR" property="level1Name"/>
      <result column="level2_name" jdbcType="VARCHAR" property="level2Name"/>
    <result column="level3_name" jdbcType="VARCHAR" property="level3Name" />
    <result column="level4_name" jdbcType="VARCHAR" property="level4Name" />
    <result column="level5_name" jdbcType="VARCHAR" property="level5Name" />
    <result column="level6_name" jdbcType="VARCHAR" property="level6Name" />
    <result column="level7_name" jdbcType="VARCHAR" property="level7Name" />
    <result column="level8_name" jdbcType="VARCHAR" property="level8Name" />
  </resultMap>

    <resultMap id="ChannelMap" type="com.iqiyi.vip.dto.condition.FvChannelPair">
        <result column="business_id" jdbcType="INTEGER" property="id"/>
        <result column="business_name" jdbcType="VARCHAR" property="name"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="team_name" jdbcType="VARCHAR" property="teamName"/>
    </resultMap>

    <insert id="insert" parameterType="com.iqiyi.vip.domain.entity.FvStructureDO">
        insert into fv_structure (level1_id, level2_id, level3_id,
                                  level4_id, level5_id, level6_id,
                                  level7_id, level8_id, level1_name,
                                  level2_name, level3_name, level4_name,
                                  level5_name, level6_name, level7_name,
                                  level8_name)
        values (#{level1Id,jdbcType=INTEGER}, #{level2Id,jdbcType=INTEGER}, #{level3Id,jdbcType=INTEGER},
                #{level4Id,jdbcType=INTEGER}, #{level5Id,jdbcType=INTEGER}, #{level6Id,jdbcType=INTEGER},
                #{level7Id,jdbcType=INTEGER}, #{level8Id,jdbcType=INTEGER}, #{level1Name,jdbcType=VARCHAR},
                #{level2Name,jdbcType=VARCHAR}, #{level3Name,jdbcType=VARCHAR}, #{level4Name,jdbcType=VARCHAR},
      #{level5Name,jdbcType=VARCHAR}, #{level6Name,jdbcType=VARCHAR}, #{level7Name,jdbcType=VARCHAR}, 
      #{level8Name,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.iqiyi.vip.domain.entity.FvStructureDO">
        insert into fv_structure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="level1Id != null">
                level1_id,
            </if>
            <if test="level2Id != null">
                level2_id,
            </if>
            <if test="level3Id != null">
                level3_id,
      </if>
      <if test="level4Id != null">
        level4_id,
      </if>
      <if test="level5Id != null">
        level5_id,
      </if>
      <if test="level6Id != null">
        level6_id,
      </if>
      <if test="level7Id != null">
        level7_id,
      </if>
      <if test="level8Id != null">
        level8_id,
      </if>
      <if test="level1Name != null">
        level1_name,
      </if>
      <if test="level2Name != null">
        level2_name,
      </if>
      <if test="level3Name != null">
        level3_name,
      </if>
      <if test="level4Name != null">
        level4_name,
      </if>
      <if test="level5Name != null">
        level5_name,
      </if>
      <if test="level6Name != null">
        level6_name,
      </if>
      <if test="level7Name != null">
        level7_name,
      </if>
      <if test="level8Name != null">
        level8_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="level1Id != null">
        #{level1Id,jdbcType=INTEGER},
      </if>
      <if test="level2Id != null">
        #{level2Id,jdbcType=INTEGER},
      </if>
      <if test="level3Id != null">
        #{level3Id,jdbcType=INTEGER},
      </if>
      <if test="level4Id != null">
        #{level4Id,jdbcType=INTEGER},
      </if>
      <if test="level5Id != null">
        #{level5Id,jdbcType=INTEGER},
      </if>
      <if test="level6Id != null">
        #{level6Id,jdbcType=INTEGER},
      </if>
      <if test="level7Id != null">
        #{level7Id,jdbcType=INTEGER},
      </if>
      <if test="level8Id != null">
        #{level8Id,jdbcType=INTEGER},
      </if>
      <if test="level1Name != null">
        #{level1Name,jdbcType=VARCHAR},
      </if>
      <if test="level2Name != null">
        #{level2Name,jdbcType=VARCHAR},
      </if>
      <if test="level3Name != null">
        #{level3Name,jdbcType=VARCHAR},
      </if>
      <if test="level4Name != null">
        #{level4Name,jdbcType=VARCHAR},
      </if>
      <if test="level5Name != null">
        #{level5Name,jdbcType=VARCHAR},
      </if>
      <if test="level6Name != null">
        #{level6Name,jdbcType=VARCHAR},
      </if>
      <if test="level7Name != null">
        #{level7Name,jdbcType=VARCHAR},
      </if>
      <if test="level8Name != null">
        #{level8Name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

    <select id="getFvChannels" resultMap="ChannelMap">
        select ${businessIdColumn} as business_id, concat(${businessNameColumn}) as business_name
        from fv_structure
        GROUP BY business_id, business_name
    </select>

    <select id="rootFvChannel" resultType="com.iqiyi.vip.dto.condition.FvChannelPair">
        select level1_id as id, level1_name as name, 1 as level
        from fv_structure
        where level1_id = 2084
        group by level1_id
    </select>

    <select id="fvChannels" resultType="com.iqiyi.vip.dto.condition.FvChannelPair">
        select ${columns}
        from fv_structure
        where ${whereColumns}
        group by ${groupByColumns}
    </select>
    <select id="getFvChannelsByTeamId" resultMap="ChannelMap">
        select ${businessIdColumn} as business_id, ${businessNameColumn} as business_name, team_name
        <if test="teamId != null">
            , team_id
        </if>
        from fv_structure
        <where>
            <if test="teamId != null">
                team_id = ${teamId}
            </if>
        </where>
        GROUP BY business_id, business_name, team_name
        <if test="teamId != null">
            , team_id
        </if>
    </select>

</mapper>