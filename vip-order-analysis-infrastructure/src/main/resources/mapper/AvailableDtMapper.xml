<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AvailableDtMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AvailableDt">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="table_dt" jdbcType="VARCHAR" property="tableDt"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="theme_sub_type" jdbcType="INTEGER" property="themeSubType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, table_dt, create_time, update_time, status, theme_type, theme_sub_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectMaxDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where status = 1
        and theme_type = #{themeType}
        order by table_dt desc limit 1
    </select>

    <select id="selectMaxDateByThemeTypeAndThemeSubType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where status = 1
        and theme_type = #{themeType}
        <if test="themeSubType != null">
            and theme_sub_type = #{themeSubType}
        </if>
        order by table_dt desc limit 1
    </select>

    <select id="selectDateRangeByThemeTypeAndThemeSubType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where status = 1
        and theme_type = #{themeType}
        <if test="themeSubType != null">
            and theme_sub_type = #{themeSubType}
        </if>
        order by table_dt desc
    </select>

    <select id="selectByTableDt" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where table_dt = #{dt,jdbcType=VARCHAR}
        and theme_type = #{themeType}
        order by table_dt desc limit 1
    </select>

    <select id="selectByTableDtAndThemeTypeAndThemeSubType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from available_dt
        where table_dt = #{dt,jdbcType=VARCHAR}
        and theme_type = #{themeType}
        <if test="themeSubType != null">
            and theme_sub_type = #{themeSubType}
        </if>
        order by table_dt desc limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from available_dt
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AvailableDt" useGeneratedKeys="true">
        insert into available_dt (table_dt, create_time, update_time
        )
        values (#{tableDt,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AvailableDt" useGeneratedKeys="true">
        insert into available_dt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableDt != null">
                table_dt,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="themeType != null">
                theme_type,
            </if>
            <if test="themeSubType != null">
                theme_sub_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableDt != null">
                #{tableDt,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="themeType != null">
                #{themeType,jdbcType=INTEGER},
            </if>
            <if test="themeSubType != null">
                #{themeSubType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.domain.entity.AvailableDt">
        update available_dt
        <set>
            <if test="tableDt != null">
                table_dt = #{tableDt,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.domain.entity.AvailableDt">
        update available_dt
        set table_dt = #{tableDt,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateStatusByDt">
        update available_dt
        set status = #{status,jdbcType=INTEGER}, update_time = now()
        where table_dt = #{dt,jdbcType=VARCHAR}
          and theme_type = #{themeType}
    </update>
</mapper>