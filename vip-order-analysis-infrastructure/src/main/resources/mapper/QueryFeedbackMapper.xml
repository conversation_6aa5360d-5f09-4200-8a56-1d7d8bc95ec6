<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.QueryFeedbackMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.model.QueryFeedback">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="unique_id" property="uniqueId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="feedback" property="feedback" jdbcType="VARCHAR"/>
        <result column="query" property="query" jdbcType="VARCHAR"/>
        <result column="original_query" property="originalQuery" jdbcType="VARCHAR"/>
        <result column="pipeline_id" property="pipelineId" jdbcType="VARCHAR"/>
        <result column="answer" property="answer" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, unique_id, status, feedback, query, original_query, pipeline_id, answer, operator, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.iqiyi.vip.domain.model.QueryFeedback" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO query_feedback
        (task_id, unique_id, status, feedback, query, original_query, pipeline_id, answer, operator, create_time, update_time)
        VALUES
        (#{taskId}, #{uniqueId}, #{status}, #{feedback}, #{query}, #{originalQuery}, #{pipelineId}, #{answer}, #{operator}, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        <if test="status != null">status = #{status},</if>
        <if test="feedback != null">feedback = #{feedback},</if>
        <if test="query != null">query = #{query},</if>
        <if test="originalQuery != null">original_query = #{originalQuery},</if>
        <if test="pipelineId != null">pipeline_id = #{pipelineId},</if>
        <if test="answer != null">answer = #{answer},</if>
        <if test="operator != null">operator = #{operator},</if>
        update_time = NOW()
    </insert>

    <select id="selectByTaskId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM query_feedback
        WHERE task_id = #{taskId}
    </select>
</mapper> 