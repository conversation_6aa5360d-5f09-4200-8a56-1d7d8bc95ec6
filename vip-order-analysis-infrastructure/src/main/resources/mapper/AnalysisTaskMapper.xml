<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisTaskMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AnalysisTaskDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="targets" jdbcType="VARCHAR" property="targets"/>
        <result column="condition" jdbcType="VARCHAR" property="condition"/>
        <result column="diagnosis_condition" jdbcType="VARCHAR" property="diagnosisCondition"/>
        <result column="dimensions" jdbcType="VARCHAR" property="dimensions"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="result" jdbcType="VARCHAR" property="result"/>
        <result column="execute_time" jdbcType="INTEGER" property="executeTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="unique_identification" jdbcType="VARCHAR" property="uniqueIdentification" />
        <result column="execute_cycle" jdbcType="VARCHAR" property="executeCycle" />
        <result column="delete_status" jdbcType="VARCHAR" property="deleteStatus"/>
        <result column="business_type_id" jdbcType="INTEGER" property="businessTypeId"/>
        <result column="data_permission_type" jdbcType="INTEGER" property="dataPermissionType"/>
        <result column="theme_type" jdbcType="INTEGER" property="themeType"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `task_name`, targets, `condition`, dimensions, `status`, `result`, `execute_time`, create_time, update_time, operator
        , unique_identification, execute_cycle, delete_status, business_type_id, data_permission_type, theme_type, source, diagnosis_condition
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from analysis_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getByUniqueIdentification" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from analysis_task
        where unique_identification = #{uniqueIdentification, jdbcType=VARCHAR}
        and status != 3
        order by update_time limit 1
    </select>

    <select id="getPageList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from analysis_task
        where operator = #{operator, jdbcType=VARCHAR}
        <if test="taskId != null">
            and id = #{taskId, jdbcType=BIGINT}
        </if>
        <if test="taskName != null and taskName != ''">
            and task_name like concat('%', #{taskName}, '%')
        </if>
        <if test="startTime != null">
            and create_time > #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="themeType != null">
            and theme_type=#{themeType}
        </if>
        <if test="businessTypeId != null">
            and business_type_id=#{businessTypeId}
        </if>
        and delete_status = 0
        and source = 1
        order by update_time desc
    </select>
    <select id="getTaskByIdAndOperator" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from analysis_task
        where id = #{taskId} and operator = #{operator} and delete_status = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update analysis_task
        set delete_status = 1,
            update_time   = now()
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AnalysisTaskDO" useGeneratedKeys="true">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            select LAST_INSERT_ID()
        </selectKey>
        insert into analysis_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">
                task_name,
            </if>
            <if test="targets != null">
                targets,
            </if>
            <if test="condition != null">
                `condition`,
            </if>
            <if test="diagnosisCondition != null">
                `diagnosis_condition`,
            </if>
            <if test="dimensions != null">
                dimensions,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="result != null">
                `result`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="uniqueIdentification != null">
                unique_identification,
            </if>
            <if test="businessTypeId != null">
                business_type_id,
            </if>
            <if test="dataPermissionType != null">
                data_permission_type,
            </if>
            <if test="themeType != null">
                theme_type,
            </if>
            <if test="source != null">
                source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="targets != null">
                #{targets,jdbcType=VARCHAR},
            </if>
            <if test="condition != null">
                #{condition,jdbcType=VARCHAR},
            </if>
            <if test="diagnosisCondition != null">
                #{diagnosisCondition,jdbcType=VARCHAR},
            </if>
            <if test="dimensions != null">
                #{dimensions,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="result != null">
                #{result,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="uniqueIdentification != null">
                #{uniqueIdentification,jdbcType=VARCHAR},
            </if>
            <if test="businessTypeId != null">
                #{businessTypeId},
            </if>
            <if test="dataPermissionType != null">
                #{dataPermissionType},
            </if>
            <if test="themeType != null">
                #{themeType},
            </if>
            <if test="source != null">
                #{source},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.domain.entity.AnalysisTaskDO">
        update analysis_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="targets != null">
                targets = #{targets,jdbcType=VARCHAR},
            </if>
            <if test="condition != null">
                `condition` = #{condition,jdbcType=VARCHAR},
            </if>
            <if test="diagnosisCondition != null">
                `diagnosis_condition` = #{diagnosisCondition,jdbcType=VARCHAR},
            </if>
            <if test="dimensions != null">
                dimensions = #{dimensions,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="result != null">
                `result` = #{result,jdbcType=VARCHAR},
            </if>
            <if test="executeTime != null">
                `execute_time` = #{executeTime,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="uniqueIdentification != null">
                unique_identification = #{uniqueIdentification,jdbcType=VARCHAR},
            </if>
            <if test="businessTypeId != null">
                business_type_id=#{businessTypeId},
            </if>
            <if test="dataPermissionType != null">
                data_permission_type=#{dataPermissionType},
            </if>
            <if test="themeType != null">
                theme_type=#{themeType},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="reSetTaskStatus">
        update analysis_task
        set status = #{status, jdbcType=INTEGER} , result = #{result, jdbcType=VARCHAR}, update_time = now()
        <if test="executeTime != null">
            , `execute_time` = #{executeTime,jdbcType=INTEGER}
        </if>
        where id = #{taskId, jdbcType=BIGINT}
    </update>

    <update id="reTaskName" >
        update analysis_task
        <trim suffixOverrides=",">
            <set>
                task_name = #{taskName,jdbcType=VARCHAR},
            </set>
        </trim>
        <where>
            id = #{taskId, jdbcType=BIGINT} and `operator` = #{operator,jdbcType=VARCHAR}
        </where>
    </update>

    <update id="saveQueryInfo">
        update analysis_task
        set query_info = #{queryInfo}, update_time = now()
        where id = #{taskId, jdbcType=BIGINT}
    </update>
</mapper>