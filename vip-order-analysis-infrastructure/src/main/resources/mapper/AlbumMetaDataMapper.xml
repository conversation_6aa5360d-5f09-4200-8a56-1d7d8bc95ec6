<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AlbumMetaDataMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.AlbumMetaDataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="qipu_album_id" jdbcType="VARCHAR" property="qipuAlbumId" />
    <result column="tv_name" jdbcType="VARCHAR" property="tvName" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="first_episode_online_time" jdbcType="VARCHAR" property="firstEpisodeOnlineTime" />
    <result column="vip_end_time" jdbcType="VARCHAR" property="vipEndTime" />
    <result column="common_end_time" jdbcType="VARCHAR" property="commonEndTime" />
    <result column="resource_rating" jdbcType="VARCHAR" property="resourceRating" />
    <result column="remain_related" jdbcType="TINYINT" property="remainRelated" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="is_finished" jdbcType="TINYINT" property="finished"/>

  </resultMap>

  <sql id="Base_Column_List">
    id, qipu_album_id, tv_name, channel_id, first_episode_online_time, vip_end_time, 
    common_end_time, resource_rating, remain_related, channel_name, create_time, update_time, is_finished
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from album_meta_data
    where id = #{id,jdbcType=BIGINT}
  </select>


    <select id="selectCount" resultType="java.lang.Integer" parameterType="com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO">
        select
        count(1)
        from
        album_meta_data
        where 1 = 1
        <if test="query != null and query != ''">
            and ( qipu_album_id like concat('%', #{query}, '%') or tv_name like concat('%', #{query}, '%') or channel_name like concat('%', #{query}, '%') )
        </if>
        <if test="resourceRating != null and resourceRating != ''">
            and resource_rating = #{resourceRating}
        </if>
        <if test="remainRelated!= null">
            and remain_related = #{remainRelated}
        </if>
        <if test="firstOnlineTimeStart != null and firstOnlineTimeStart != ''">
            and first_episode_online_time >= #{firstOnlineTimeStart}
        </if>
        <if test="firstOnlineTimeEnd != null and firstOnlineTimeEnd != ''">
            and  #{firstOnlineTimeEnd} >= first_episode_online_time
        </if>
        <if test="vipEndTimeStart != null and vipEndTimeStart != ''">
            and vip_end_time >= #{vipEndTimeStart}
        </if>
        <if test="vipEndTimeEnd != null and vipEndTimeEnd != ''">
            and  #{vipEndTimeEnd} >= vip_end_time
        </if>
        <if test="commonEndTimeStart != null and commonEndTimeStart != ''">
            and common_end_time >= #{commonEndTimeStart}
        </if>
        <if test="commonEndTimeEnd != null and commonEndTimeEnd != ''">
            and  #{commonEndTimeEnd} >= common_end_time
        </if>
    </select>


    <select id="getByRemainTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from album_meta_data
        where remain_related = 1
        <if test="startDate != null">
            and (    (common_end_time is not null and  common_end_time >= #{startDate} and  #{endDate} >= common_end_time )
                     or (common_end_time is not null and  common_end_time >= #{endDate} and  #{endDate} >= first_episode_online_time)
                     or ( vip_end_time is not null and  vip_end_time >= #{startDate} and  #{endDate} >= vip_end_time )
                     or ( vip_end_time is not null and  vip_end_time >= #{endDate} and  #{endDate} >= first_episode_online_time)

                )
        </if>
    </select>


    <select id="getPageQuery" resultMap="BaseResultMap" parameterType="com.iqiyi.vip.dto.remain.AlbumMetaDataPageQueryDTO">
        select
        <include refid="Base_Column_List" />
        from album_meta_data
        where 1 = 1
        <if test="query!= null and query!= ''">
            and ( qipu_album_id like concat('%', #{query}, '%') or tv_name like concat('%', #{query}, '%') or channel_name like concat('%', #{query}, '%') )
        </if>
        <if test="resourceRating!= null and resourceRating!= ''">
            and resource_rating = #{resourceRating}
        </if>
        <if test="remainRelated!= null">
            and remain_related = #{remainRelated}
        </if>
        <if test="firstOnlineTimeStart != null and firstOnlineTimeStart != ''">
            and first_episode_online_time >= #{firstOnlineTimeStart}
        </if>
        <if test="firstOnlineTimeEnd != null and firstOnlineTimeEnd != ''">
            and  #{firstOnlineTimeEnd} >= first_episode_online_time
        </if>
        <if test="vipEndTimeStart != null and vipEndTimeStart != ''">
            and vip_end_time >= #{vipEndTimeStart}
        </if>
        <if test="vipEndTimeEnd != null and vipEndTimeEnd != ''">
            and  #{vipEndTimeEnd} >= vip_end_time
        </if>

        <if test="commonEndTimeStart != null and commonEndTimeStart != ''">
            and common_end_time >= #{commonEndTimeStart}
        </if>
        <if test="commonEndTimeEnd != null and commonEndTimeEnd != ''">
            and  #{commonEndTimeEnd} >= common_end_time
        </if>

        <if test="orderBy!= null and orderBy!= ''">
            order by ${orderBy} desc
        </if>
        limit #{start}, #{pageSize}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from album_meta_data
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AlbumMetaDataDO" useGeneratedKeys="true">
    insert into album_meta_data (qipu_album_id, tv_name, channel_id, 
      first_episode_online_time, vip_end_time, 
      common_end_time, resource_rating, remain_related, 
      channel_name)
    values (#{qipuAlbumId,jdbcType=VARCHAR}, #{tvName,jdbcType=VARCHAR}, #{channelId,jdbcType=INTEGER}, 
      #{firstEpisodeOnlineTime,jdbcType=TIMESTAMP}, #{vipEndTime,jdbcType=TIMESTAMP}, 
      #{commonEndTime,jdbcType=TIMESTAMP}, #{resourceRating,jdbcType=VARCHAR}, #{remainRelated,jdbcType=TINYINT}, 
      #{channelName,jdbcType=VARCHAR})
  </insert>


    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.AlbumMetaDataDO" useGeneratedKeys="true">
    insert into album_meta_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="qipuAlbumId != null">
        qipu_album_id,
      </if>
      <if test="tvName != null">
        tv_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="firstEpisodeOnlineTime != null">
        first_episode_online_time,
      </if>
      <if test="vipEndTime != null">
        vip_end_time,
      </if>
      <if test="commonEndTime != null">
        common_end_time,
      </if>
      <if test="resourceRating != null">
        resource_rating,
      </if>
      <if test="remainRelated != null">
        remain_related,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="qipuAlbumId != null">
        #{qipuAlbumId,jdbcType=VARCHAR},
      </if>
      <if test="tvName != null">
        #{tvName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="firstEpisodeOnlineTime != null">
        #{firstEpisodeOnlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vipEndTime != null">
        #{vipEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commonEndTime != null">
        #{commonEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceRating != null">
        #{resourceRating,jdbcType=VARCHAR},
      </if>
      <if test="remainRelated != null">
        #{remainRelated,jdbcType=TINYINT},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>


    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.domain.entity.AlbumMetaDataDO">
    update album_meta_data
    <set>
      <if test="qipuAlbumId != null">
        qipu_album_id = #{qipuAlbumId,jdbcType=VARCHAR},
      </if>
      <if test="tvName != null">
        tv_name = #{tvName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="firstEpisodeOnlineTime != null">
        first_episode_online_time = #{firstEpisodeOnlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vipEndTime != null">
        vip_end_time = #{vipEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commonEndTime != null">
        common_end_time = #{commonEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceRating != null">
        resource_rating = #{resourceRating,jdbcType=VARCHAR},
      </if>
      <if test="remainRelated != null">
        remain_related = #{remainRelated,jdbcType=TINYINT},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


    <update id="updateMetaData" parameterType="com.iqiyi.vip.dto.remain.UpdateAlbumMetaDataDTO">
        update album_meta_data
        <set>
            <if test="vipEndTime != null">
                vip_end_time = #{vipEndTime,jdbcType=VARCHAR},
            </if>
            <if test="commonEndTime != null">
                common_end_time = #{commonEndTime,jdbcType=VARCHAR},
            </if>
            <if test="resourceRating != null">
                resource_rating = #{resourceRating,jdbcType=VARCHAR},
            </if>
            <if test="remainRelated != null">
                remain_related = #{remainRelated,jdbcType=TINYINT}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and qipu_album_id = #{qipuAlbumId,jdbcType=VARCHAR}
    </update>
</mapper>