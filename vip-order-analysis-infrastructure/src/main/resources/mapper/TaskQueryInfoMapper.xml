<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.TaskQueryInfoMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.TaskQueryInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="query_info" jdbcType="VARCHAR" property="queryInfo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , task_id, query_info, create_time, update_time
    </sql>
    <select id="selectByTaskId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_query_info
        where task_id = #{taskId,jdbcType=BIGINT}
    </select>

    <select id="selectByTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_query_info
        where task_id in
        <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from task_query_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into task_query_info (task_id, query_info, create_time,
                                     update_time)
        values (#{taskId,jdbcType=BIGINT}, #{queryInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into task_query_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="queryInfo != null">
                query_info,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="queryInfo != null">
                #{queryInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByTaskIdSelective">
        update task_query_info
        <set>
            <if test="queryInfo != null">
                query_info = #{queryInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where task_id = #{taskId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey">
        update task_query_info
        set task_id     = #{taskId,jdbcType=BIGINT},
            query_info  = #{queryInfo,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>