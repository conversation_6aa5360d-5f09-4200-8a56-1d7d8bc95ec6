<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.DataCheckResultDao">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.DataCheckResult">
      <id column="id" jdbcType="INTEGER" property="id"/>
      <result column="case_id" jdbcType="INTEGER" property="caseId"/>
      <result column="key" jdbcType="VARCHAR" property="key"/>
      <result column="value" jdbcType="DOUBLE" property="value"/>
      <result column="dt" jdbcType="VARCHAR" property="dt"/>
      <result column="begin_time" jdbcType="VARCHAR" property="beginTime"/>
      <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
      <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, `key`, `value`, dt, begin_time, end_time, data_source, create_time, 
    update_time,dimension
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_check_result
    where id = #{id,jdbcType=INTEGER}
  </select>

    <select id="getDataCheckResult" resultMap="BaseResultMap">
     select <include refid="Base_Column_List"/>
     from data_check_result
     <where>
         <if test="key != null">
             and key = #{key,jdbcType=VARCHAR}
         </if>
         <if test="value != null">
             and value = #{value,jdbcType=DOUBLE}
         </if>
         <if test="dt != null">
             and dt = #{dt,jdbcType=VARCHAR}
         </if>
         <if test="beginTime != null">
             and begin_time = #{beginTime,jdbcType=VARCHAR}
         </if>
         <if test="endTime != null">
             and end_time = #{endTime,jdbcType=VARCHAR}
         </if>
         <if test="dataSource != null">
             and data_source = #{dataSource,jdbcType=VARCHAR}
         </if>
         <if test="dimension != null">
             and dimension = #{dimension,jdbcType=VARCHAR}
         </if>
         <if test="caseIds != null and caseIds.size() >0">
             and case_id IN
             <foreach collection="caseIds" item="value" separator="," open="(" close=")">
                 #{value}
             </foreach>
         </if>
        </where>

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from data_check_result
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.DataCheckResult" useGeneratedKeys="true">
        insert into data_check_result (case_id, `key`, `value`,
                                       dt, begin_time, end_time,
                                       data_source, create_time, update_time, dimension)
        values (#{caseId,jdbcType=INTEGER}, #{key,jdbcType=VARCHAR}, #{value,jdbcType=DOUBLE},
                #{dt,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR},
                #{dataSource,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{dimension,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.DataCheckResult" useGeneratedKeys="true">
        insert into data_check_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">
                case_id,
            </if>
            <if test="key != null">
                `key`,
            </if>
            <if test="value != null">
                `value`,
      </if>
      <if test="dt != null">
        dt,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
        <if test="dimension != null">
            dimension,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=INTEGER},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=DOUBLE},
      </if>
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.domain.entity.DataCheckResult">
        update data_check_result
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=INTEGER},
            </if>
            <if test="key != null">
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                `value` = #{value,jdbcType=DOUBLE},
      </if>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.domain.entity.DataCheckResult">
        update data_check_result
        set case_id     = #{caseId,jdbcType=INTEGER},
            `key`       = #{key,jdbcType=VARCHAR},
            `value`     = #{value,jdbcType=DOUBLE},
            dt          = #{dt,jdbcType=VARCHAR},
            begin_time  = #{beginTime,jdbcType=VARCHAR},
            end_time    = #{endTime,jdbcType=VARCHAR},
            data_source = #{dataSource,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
      dimension = #{dimension,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>