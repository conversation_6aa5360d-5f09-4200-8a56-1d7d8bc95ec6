<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.ConfigChangeLogDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.domain.entity.ConfigChangeLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="config_type" jdbcType="INTEGER" property="configType"/>
        <result column="config_id" jdbcType="INTEGER" property="configId"/>
        <result column="config_name" jdbcType="VARCHAR" property="configName"/>
        <result column="config_value" jdbcType="VARCHAR" property="configValue"/>
        <result column="commit_note" jdbcType="VARCHAR" property="commitNote"/>
        <result column="config_desc" jdbcType="VARCHAR" property="configDesc"/>
        <result column="config_code" jdbcType="VARCHAR" property="configCode"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="group_id" jdbcType="INTEGER" property="configGroupId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , config_type, config_id, config_name, config_value, commit_note, `operator`, `status`, config_code, config_desc,
    create_time, update_time, version, template_id, group_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from config_change_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getByConfigTypeAndConfigId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from config_change_log
        <where>
            <if test="configType != null">
                and config_type = #{configType,jdbcType=INTEGER}
            </if>
            <if test="configId != null">
                and config_id = #{configId}
            </if>
            <if test="version != null">
                and version = #{version,jdbcType=INTEGER}
            </if>
        </where>
        order by version, create_time desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from config_change_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="delByConfigTypeAndConfigId">
        delete
        from config_change_log
        where config_type = #{configType}
          and config_id = #{configId}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.ConfigChangeLog" useGeneratedKeys="true">
        insert into config_change_log (config_type, config_id, config_name,
                                       config_value, commit_note, `operator`,
                                       `status`, create_time, update_time, version)
        values (#{configType,jdbcType=INTEGER}, #{configId,jdbcType=INTEGER}, #{configName,jdbcType=VARCHAR},
                #{configValue,jdbcType=VARCHAR}, #{commitNote,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{version, jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iqiyi.vip.domain.entity.ConfigChangeLog"
        useGeneratedKeys="true">
        insert into config_change_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configType != null">
                config_type,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="configName != null">
                config_name,
            </if>
            <if test="configValue != null">
                config_value,
            </if>
            <if test="commitNote != null">
                commit_note,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="configDesc != null">
                config_desc,
            </if>
            <if test="configCode != null">
                config_code,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="configGroupId != null">
                group_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configType != null">
                #{configType,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="configName != null">
                #{configName,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="commitNote != null">
                #{commitNote,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="configDesc != null">
                #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="configCode != null">
                #{configCode,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                #{templateId,jdbcType=BIGINT},
            </if>
            <if test="configGroupId != null">
                #{configGroupId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.domain.entity.ConfigChangeLog">
        update config_change_log
        <set>
            <if test="configType != null">
                config_type = #{configType,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="configName != null">
                config_name = #{configName,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                config_value = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="commitNote != null">
                commit_note = #{commitNote,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=TIMESTAMP}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.domain.entity.ConfigChangeLog">
        update config_change_log
        set config_type  = #{configType,jdbcType=INTEGER},
            config_id    = #{configId,jdbcType=INTEGER},
            config_name  = #{configName,jdbcType=VARCHAR},
            config_value = #{configValue,jdbcType=VARCHAR},
            commit_note  = #{commitNote,jdbcType=VARCHAR},
            `operator`   = #{operator,jdbcType=VARCHAR},
            `status`     = #{status,jdbcType=INTEGER},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            version      = #{version,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>