<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.VipTypeMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.VipType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="vip_type_group_code" jdbcType="INTEGER" property="vipTypeGroupCode"/>
        <result column="vip_type_group_name" jdbcType="VARCHAR" property="vipTypeGroupName"/>
        <result column="vip_type_code" jdbcType="INTEGER" property="vipTypeCode"/>
        <result column="vip_type_id" jdbcType="INTEGER" property="vipTypeId"/>
        <result column="vip_type_name" jdbcType="VARCHAR" property="vipTypeName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, vip_type_group_code, vip_type_group_name, vip_type_code, vip_type_name, create_time, update_time, status
  </sql>

    <select id="selectAll" resultType="com.iqiyi.vip.dto.condition.CodeDescPair">
        SELECT vip_type_id as code, vip_type_name as `desc` FROM tianyan_vip_type where status = 1
    </select>

    <select id="getVipTypePairs" resultType="com.iqiyi.vip.dto.condition.VipTypePair">
        select ${columns} from tianyan_vip_type where ${whereColumns} group by ${groupByColumns} order by ${orderByColumns}
    </select>

</mapper>