<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.CompetitorShopPriceMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.competitor.CompetitorShopPricePO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date" jdbcType="VARCHAR" property="date"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="store_type" jdbcType="INTEGER" property="storeType"/>
        <result column="user_group" jdbcType="VARCHAR" property="userGroup"/>
        <result column="vip_type" jdbcType="VARCHAR" property="vipType"/>
        <result column="product" jdbcType="VARCHAR" property="product"/>
        <result column="price" jdbcType="VARCHAR" property="price"/>
        <result column="price_text" jdbcType="VARCHAR" property="priceText"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap id="DateRangeResultMap" type="com.iqiyi.vip.dto.competitor.CompetitorDateRangeDTO">
        <result column="start_date" jdbcType="VARCHAR" property="startDate"/>
        <result column="end_date" jdbcType="VARCHAR" property="endDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, date, platform, brand, store_type, user_group, vip_type, product, price, price_text, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from competitor_shop_price where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.po.competitor.CompetitorShopPricePO" keyProperty="id" useGeneratedKeys="true">
        insert into competitor_shop_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="storeType != null">
                store_type,
            </if>
            <if test="userGroup != null">
                user_group,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="product != null">
                product,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="priceText != null">
                price_text,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="storeType != null">
                #{storeType,jdbcType=INTEGER},
            </if>
            <if test="userGroup != null">
                #{userGroup,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=VARCHAR},
            </if>
            <if test="product != null">
                #{product,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=VARCHAR},
            </if>
            <if test="priceText != null">
                #{priceText,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        date = #{date,jdbcType=VARCHAR},
        platform = #{platform,jdbcType=VARCHAR},
        brand = #{brand,jdbcType=VARCHAR},
        store_type = #{storeType,jdbcType=INTEGER},
        user_group = #{userGroup,jdbcType=VARCHAR},
        vip_type = #{vipType,jdbcType=VARCHAR},
        product = #{product,jdbcType=VARCHAR},
        price = #{price,jdbcType=VARCHAR},
        price_text = #{priceText,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into competitor_shop_price (
        date, platform, brand, store_type, user_group, vip_type, product, price, price_text, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.date,jdbcType=VARCHAR},
            #{item.platform,jdbcType=VARCHAR},
            #{item.brand,jdbcType=VARCHAR},
            #{item.storeType,jdbcType=INTEGER},
            #{item.userGroup,jdbcType=VARCHAR},
            #{item.vipType,jdbcType=VARCHAR},
            #{item.product,jdbcType=VARCHAR},
            #{item.price,jdbcType=VARCHAR},
            #{item.priceText,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.competitor.CompetitorShopPricePO">
        update competitor_shop_price
        <set>
            <if test="date != null">
                date = #{date,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="storeType != null">
                store_type = #{storeType,jdbcType=INTEGER},
            </if>
            <if test="userGroup != null">
                user_group = #{userGroup,jdbcType=VARCHAR},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=VARCHAR},
            </if>
            <if test="product != null">
                product = #{product,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=VARCHAR},
            </if>
            <if test="priceText != null">
                price_text = #{priceText,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAllPlatform" resultType="java.lang.String">
        select distinct platform from competitor_shop_price order by platform
    </select>

    <select id="selectAllBrand" resultType="java.lang.String">
        select distinct brand
        from competitor_shop_price
        <where>
            <if test="platform != null">
                platform = #{platform}
            </if>   
            <if test="date != null and !date.isEmpty()">
                and date = #{date}
            </if>
        </where>
        order by brand
    </select>

    <select id="selectAllUserGroup" resultType="java.lang.String">
        select distinct user_group
        from competitor_shop_price
        where
            store_type = 1
            <if test="platform != null">
                and platform = #{platform}
            </if>
            <if test="brand != null">
                and brand = #{brand}
            </if>   
            <if test="date != null and !date.isEmpty()">
                and date = #{date}
            </if>

        order by user_group
    </select>

    <select id="selectDateRange" resultMap="DateRangeResultMap">
        select 
            min(date) as start_date,
            max(date) as end_date
        from competitor_shop_price
    </select>

    <select id="search" resultMap="BaseResultMap">
        select 
            <include refid="Base_Column_List" />
        from competitor_shop_price
        <where>
            <if test="date != null and !date.isEmpty()">
                date = #{date}
            </if>
            <if test="platform != null and !platform.isEmpty()">
                and platform = #{platform}
            </if>
            <if test="brand != null and !brand.isEmpty()">
                and brand = #{brand}
            </if>
            <if test="storeType != null">
                and store_type = #{storeType}
            </if>
            <if test="userGroup != null and !userGroup.isEmpty()">
                and user_group = #{userGroup}
            </if>
        </where>
        order by id desc
    </select>

    <select id="searchByDateRange" resultMap="BaseResultMap">
        select 
            <include refid="Base_Column_List" />
        from competitor_shop_price
        <where>
            <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
                date between #{startDate} and #{endDate}
            </if>
            <if test="storeType != null">
                and store_type = #{storeType}
            </if>
            <if test="platform != null and !platform.isEmpty()">
                and platform = #{platform}
            </if>
            <if test="brand != null and !brand.isEmpty()">
                and brand = #{brand}
            </if>
        </where>
        order by date asc, platform, brand
    </select>
</mapper> 