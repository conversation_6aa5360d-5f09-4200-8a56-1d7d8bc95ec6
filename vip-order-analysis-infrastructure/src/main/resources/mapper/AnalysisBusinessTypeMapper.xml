<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AnalysisBusinessTypeMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.AnalysisBusinessType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="theme_types" jdbcType="VARCHAR" property="themeTypes"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, `name`, `order`, create_time, update_time, operator, status, theme_types
  </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tianyan_business_type
        where
        status = 1
        order by `order`, id
    </select>

    <select id="get" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tianyan_business_type
        where
        status = 1 and id = #{id}
        order by `order`, id
    </select>

</mapper>