<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.PaymentChannelMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.PaymentChannel">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="category_code" jdbcType="VARCHAR" property="categoryCode"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="dut_agreement_name" jdbcType="VARCHAR" property="dutAgreementName"/>
        <result column="dut_agreement_url" jdbcType="VARCHAR" property="dutAgreementUrl"/>
        <result column="promotion_text" jdbcType="VARCHAR" property="promotionText"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, `code`, category_code, description, dut_agreement_name, dut_agreement_url, promotion_text, icon_url
  </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM payment_channel
    </select>
</mapper>