<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="mybatisGenerator" targetRuntime="MyBatis3">
        <commentGenerator>
            <!-- 是否不生成注释 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
            connectionURL="******************************************************************************************************"
            userId="vip_test" password="rg_z_6UF)w=Y">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <!-- 生成model模型 -->
        <javaModelGenerator targetPackage="com.iqiyi.vip.po" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--对应的xml mapper文件  -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.iqiyi.vip.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>

<!--        <table tableName="competitor_ui" domainObjectName="CompetitorUi" enableCountByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="competitor_price" domainObjectName="CompetitorPrice" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->

    </context>
</generatorConfiguration>
