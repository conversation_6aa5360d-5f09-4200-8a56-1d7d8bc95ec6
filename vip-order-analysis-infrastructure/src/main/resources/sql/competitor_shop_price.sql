-- 竞品店铺价格信息表
CREATE TABLE `competitor_shop_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date` varchar(20) NOT NULL COMMENT '日期，格式:yyyy-MM-dd',
  `platform` varchar(50) NOT NULL COMMENT '平台，如拼多多、支付宝会员、抖音',
  `brand` varchar(50) NOT NULL COMMENT '品牌，如腾讯、优酷、芒果、爱奇艺',
  `store_type` tinyint(4) NOT NULL COMMENT '店铺类型，1-旗舰店，0-非旗舰店',
  `user_group` varchar(50) NOT NULL COMMENT '人群，如店铺会员',
  `vip_type` varchar(50) NOT NULL COMMENT '会员类型',
  `product` varchar(100) NOT NULL COMMENT '商品',
  `price` varchar(50) NOT NULL COMMENT '价格',
  `price_text` varchar(255) DEFAULT NULL COMMENT '价格文案',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_date_platform_brand` (`date`,`platform`,`brand`),
  KEY `idx_platform` (`platform`),
  KEY `idx_brand` (`brand`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞品店铺价格信息表'; 