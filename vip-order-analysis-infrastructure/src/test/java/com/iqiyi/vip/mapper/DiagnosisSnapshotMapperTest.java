package com.iqiyi.vip.mapper;

import com.iqiyi.vip.Mybatis3Utils;
import com.iqiyi.vip.po.DiagnosisSnapshot;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DiagnosisSnapshotMapperTest {

    private DiagnosisSnapshotMapper diagnosisSnapshotMapper;
    SqlSession sqlSession;

    @BeforeEach
    void setUp() {
        sqlSession = Mybatis3Utils.getCurrentSqlSession();
        diagnosisSnapshotMapper = sqlSession.getMapper(DiagnosisSnapshotMapper.class);
    }

    @Test
    void findById() {
        DiagnosisSnapshot diagnosisSnapshot = diagnosisSnapshotMapper.findById(1L);
        assertEquals("123", diagnosisSnapshot.getUniqueId());
    }

    @Test
    void findByTaskId() {
        List<DiagnosisSnapshot> diagnosisSnapshotList = diagnosisSnapshotMapper.findByTaskId(9L);
        assertEquals(1, diagnosisSnapshotList.size());
    }

    @Test
    void findByUniqueId() {
        List<DiagnosisSnapshot> diagnosisSnapshotList = diagnosisSnapshotMapper.findByUniqueId("123");
        assertEquals(1, diagnosisSnapshotList.size());
    }

    @Test
    void insert() {
        DiagnosisSnapshot diagnosisSnapshot = new DiagnosisSnapshot();
        diagnosisSnapshot.setUniqueId("123");
        diagnosisSnapshot.setTaskId(1L);
        diagnosisSnapshot.setResult("success");
        diagnosisSnapshot.setCreateTime(new Date());
        diagnosisSnapshot.setUpdateTime(new Date());
        diagnosisSnapshotMapper.insert(diagnosisSnapshot);
    }

    @Test
    void update() {
        DiagnosisSnapshot diagnosisSnapshot = new DiagnosisSnapshot();
        diagnosisSnapshot.setId(9L);
        diagnosisSnapshot.setUniqueId("456");
        diagnosisSnapshot.setTaskId(2L);
        diagnosisSnapshot.setResult("success");
        int i = diagnosisSnapshotMapper.update(diagnosisSnapshot);
        assertEquals(1, i);
    }
}