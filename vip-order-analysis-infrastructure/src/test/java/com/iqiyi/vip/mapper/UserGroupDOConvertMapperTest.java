package com.iqiyi.vip.mapper;

import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.iqiyi.vip.Mybatis3Utils;
import com.iqiyi.vip.po.UserGroup;

import static org.junit.jupiter.api.Assertions.assertEquals;

class UserGroupDOConvertMapperTest {

    private UserGroupMapper userGroupMapper;
    SqlSession sqlSession;

    @BeforeEach
    void setUp() {
        sqlSession = Mybatis3Utils.getCurrentSqlSession();
        userGroupMapper = sqlSession.getMapper(UserGroupMapper.class);
    }


    @Test
    public void testInsert() {
        UserGroup userGroup = new UserGroup();
        userGroup.setId(1);
        userGroup.setName("Admin");

        // Assuming you have a way to set up a mock database or in-memory database that inserts the userGroup
        // and returns the expected number of rows affected (usually 1).

        int result = userGroupMapper.insert(userGroup);
        assertEquals(1, result);
    }

    @Test
    public void testSelectByPrimaryKey() {
        int id = 3;

        UserGroup userGroup = new UserGroup();
        userGroup.setId(id);

        // Assuming you have a way to set up a mock database or in-memory database with test data
        // that returns the expected UserGroup when queried by the given primary key.

        UserGroup actualUserGroup = userGroupMapper.selectByPrimaryKey(id);
        assertEquals(userGroup, actualUserGroup);
    }

    @Test
    public void testUpdateByPrimaryKeySelective() {
        UserGroup record = new UserGroup();
        record.setId(3);
        record.setName("Super Admin");

        // Assuming you have a way to set up a mock database or in-memory database that updates the UserGroup
        // according to the given record, and returns the expected number of rows affected.

        int result = userGroupMapper.updateByPrimaryKeySelective(record);
        assertEquals(1, result);
    }

    @Test
    public void testUpdateByPrimaryKey() {
        UserGroup record = new UserGroup();
        record.setId(1);
        record.setName("Super Admin");

        // Assuming you have a way to set up a mock database or in-memory database that updates the UserGroup
        // according to the given record, and returns the expected number of rows affected.

        int result = userGroupMapper.updateByPrimaryKey(record);
        assertEquals(1, result);
    }
}