<?xml version="1.0" encoding="UTF-8" ?>
<!-- mybatis的配置文件 -->
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!--myql数据库连接信息-->
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver"/>
                <property name="url" value="***************************************************************************************************************************************************************************************************************************"/>
                <property name="username" value="vip_test"/>
                <property name="password" value="rg_z_6UF)w=Y"/>
            </dataSource>
        </environment>
    </environments>

	<mappers>
        <mapper resource="mapper/UserGroupMapper.xml"/>
    </mappers>
</configuration>