<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.example</groupId>
    <artifactId>vip-order-analysis</artifactId>
      <version>1.8.77</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>vip-order-analysis-infrastructure</artifactId>
  <packaging>jar</packaging>
  <dependencies>
      <dependency>
          <groupId>org.mapstruct</groupId>
          <artifactId>mapstruct</artifactId>
          <version>1.5.5.Final</version>
      </dependency>
      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
          <scope>test</scope>
      </dependency>
    <dependency>
      <groupId>org.example</groupId>
      <artifactId>vip-order-analysis-domain</artifactId>
        <version>1.8.77</version>
    </dependency>
    <dependency>
        <groupId>org.example</groupId>
        <artifactId>vip-order-analysis-common</artifactId>
        <version>1.8.77</version>
    </dependency>

      <dependency>
          <groupId>com.iqiyi.bigdata</groupId>
          <artifactId>pilot-client</artifactId>
      </dependency>
      <!--springboot项目都不用引入版本号-->
      <dependency>
          <groupId>org.springframework.retry</groupId>
          <artifactId>spring-retry</artifactId>
      </dependency>

      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-aspects</artifactId>
      </dependency>
      <dependency>
          <groupId>com.iqiyi.v</groupId>
          <artifactId>v-spring-boot-starter-eagle</artifactId>
      </dependency>
      <dependency>
          <groupId>com.iqiyi.db</groupId>
          <artifactId>mysql-dal-spring-boot-starter</artifactId>
      </dependency>
<!--      <dependency>-->
<!--          <groupId>com.iqiyi.db</groupId>-->
<!--          <artifactId>spring-boot-starter-qdbm-redis2</artifactId>-->
<!--      </dependency>-->

  </dependencies>

    <build>
        <plugins>
<!--            <plugin>-->
<!--                <groupId>org.mybatis.generator</groupId>-->
<!--                <artifactId>mybatis-generator-maven-plugin</artifactId>-->
<!--                <version>1.3.7</version>-->
<!--                <configuration>-->
<!--                    <configurationFile>src/main/resources/mybatis-generator.xml</configurationFile>-->
<!--                    <includeCompileDependencies>true</includeCompileDependencies>-->
<!--                </configuration>-->
<!--                &lt;!&ndash; 自动生成 &ndash;&gt;-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>Generate MyBatis Artifacts</id>-->
<!--                        <goals>-->
<!--                            <goal>generate</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
