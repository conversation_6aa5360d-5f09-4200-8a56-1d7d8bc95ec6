package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 条件层级
 *
 * <AUTHOR>
 * @date 2022/8/17 14:22
 */
@Getter
@AllArgsConstructor
public enum LabelEnum {

    L8("八级渠道", 8, ConditionEnum.BUSINESS_LEVEL, null, null),
    L7("七级渠道", 7, ConditionEnum.BUSINESS_LEVEL, L8, null),
    L6("六级渠道", 6, ConditionEnum.BUSINESS_LEVEL, L7, null),
    L5("五级渠道", 5, ConditionEnum.BUSINESS_LEVEL, L6, null),
    L4("四级渠道", 4, ConditionEnum.BUSINESS_LEVEL, L5, null),
    L3("三级渠道", 3, ConditionEnum.BUSINESS_LEVEL, L4, null),
    T("业务团队", -1, ConditionEnum.BUSINESS_LEVEL, L3, null),
    L2("二级渠道", 2, ConditionEnum.BUSINESS_LEVEL, T, null),
    L1("一级渠道", 1, ConditionEnum.BUSINESS_LEVEL, L2, null),

    P2("二层级(端平台)", 22, ConditionEnum.PRO, null, "end_key"),
    P1("一层级(产品)", 21, ConditionEnum.PRO, P2, "biz_name"),
    P0("0层级(产品)", 20, ConditionEnum.PRO, P1, null), // 假分级，并没有产品分级

    VT2("套餐类型", 32, ConditionEnum.VIP_TYPE, null, "vip_type"),
    VT1("会员类型", 31, ConditionEnum.VIP_TYPE, VT2, "vip_type_group"),
    VT0("0层级()", 30, ConditionEnum.VIP_TYPE, VT1, null), // 假分级，并没有会员分级

    CARD_TYPE1("会员卡类型", 41, ConditionEnum.VIP_CARD_TYPE, null, "vip_card_type"),
    CARD_TYPE0("0层级()", 40, ConditionEnum.VIP_CARD_TYPE, CARD_TYPE1, "vip_card_type"),

    COMPETITOR_MONITOR("0层级(竞品监控)", 50, ConditionEnum.COMPETITOR_MONITOR, null, null),

    ;

    private String desc;
    private int level;
    private ConditionEnum conditionEnum;
    private LabelEnum nextLabelEnum;
    private String column;

    public static LabelEnum levelOf(int level) {
        for (LabelEnum labelEnum : LabelEnum.values()) {
            if (labelEnum.level == level) {
                return labelEnum;
            }
        }
        return null;
    }

    /**
     * 获取首个层级Label
     * @return
     */
    public static LabelEnum getFirstLevelLabel() {
        return L1;
    }

    /**
     * 获取首个产品Label
     * @return
     */
    public static LabelEnum getFirstProLabel() {
        return P0;
    }

    public static LabelEnum getFirstVipTypeLabel() {
        return VT0;
    }


    /**
     * 权限控制级别
     * @return
     */
    public static LabelEnum datePermissionCtrlLabel() {
        return LabelEnum.L3;
    }
}
