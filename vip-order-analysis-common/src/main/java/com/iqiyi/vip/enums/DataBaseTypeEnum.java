package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className DataBaseTypeEnum
 * @description
 * @date 2022/11/28
 **/
@AllArgsConstructor
@Getter
public enum DataBaseTypeEnum {
    HIVE(1, "hive"),
    CLICK_HOUSE(2, "clickhouse"),
    STAR_ROCKS(3, "starrocks"),
    ;
    private Integer type;

    private String code;

    public static List<String> getAllCodes() {

        List<String> codes = new ArrayList<>();
        for (DataBaseTypeEnum typeEnum : values()) {
            codes.add(typeEnum.getCode());
        }
        return codes;
    }

}
