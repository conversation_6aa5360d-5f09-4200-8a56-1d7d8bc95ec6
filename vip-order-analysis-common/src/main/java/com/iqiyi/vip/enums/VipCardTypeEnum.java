package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum VipCardTypeEnum {

    TIAN(1, "天卡"),
    YUE(2, "月卡"),
    JI(3, "季卡"),
    BANNIAN(4, "半年卡"),
    NIAN(5, "年卡"),
    ;
    private Integer vipCardType;
    private String value;

    VipCardTypeEnum(Integer vipCardType, String value) {
        this.vipCardType = vipCardType;
        this.value = value;
    }

    public static List<String> getValues(List<Integer> vipCardTypes) {
        if (CollectionUtils.isEmpty(vipCardTypes)) {
            return Collections.emptyList();
        }
        List<String> values = new ArrayList<>();
        for (VipCardTypeEnum cardTypeEnum : VipCardTypeEnum.values()) {
            if (vipCardTypes.contains(cardTypeEnum.getVipCardType())) {
                values.add("'" + cardTypeEnum.getValue() + "'");
            }
        }
        return values;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(VipCardTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getVipCardType(), s.getValue()));
        });
        return list;
    }


    public static VipCardTypeEnum findEnumByVipCardType(Integer vipCardType) {
        for (VipCardTypeEnum vipCardTypeEnum : VipCardTypeEnum.values()) {
            if (vipCardTypeEnum.getVipCardType().equals(vipCardType)) {
                return vipCardTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByVipCardTypes(List<Integer> vipCardTypes) {
        return vipCardTypes == null ? new ArrayList<>() : vipCardTypes.stream().map(VipCardTypeEnum::findEnumByVipCardType)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getVipCardType(), e.getValue()))
            .collect(Collectors.toList());
    }
}
