package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * 创建方式
 */
@Getter
public enum CreateProcessEnum {

    UPLOADING(0, "文件上传中"),
    UPLOAD_SUCCESS(1, "上传成功"),
    UPLOAD_FAILED(2, "上传失败"),
    ;

    private final Integer code;
    private final String desc;

    CreateProcessEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(CreateProcessEnum.values()).forEach(s -> list.add(new CodeDescPair(s.getCode(), s.getDesc())));
        return list;
    }
}
