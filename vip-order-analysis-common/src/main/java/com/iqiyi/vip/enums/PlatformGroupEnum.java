package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import com.iqiyi.vip.dto.platform.PlatformGroup;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum PlatformGroupEnum {

    TV(1, "TV端"),
    PC(2, "PC端"),
    H5(3, "H5"),
    IOS_APP(4, "爱奇艺IOS APP"),
    ANDROID_APP(5, "Android App"),
    DH_IOS_APP(6, "动画屋IOS APP"),
    DH_ANDROID_APP(7, "动画屋Android APP"),
    OTHER(8, "其他"),
    TV_FRUIT(9, "电视果"),
    VR(10, "VR"),
    PARTNER(11, "合作方"),
    ;
    private Integer code;
    private String desc;

    PlatformGroupEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(Integer id) {
        if (id == null) {
            return null;
        }
        for (PlatformGroupEnum platformGroupEnum : PlatformGroupEnum.values()) {
            if (Objects.equals(id, platformGroupEnum.getCode())) {
                return platformGroupEnum.getDesc();
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(PlatformGroupEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }
}
