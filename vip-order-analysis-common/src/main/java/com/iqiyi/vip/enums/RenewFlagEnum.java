package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 续费标识 0:新增
 * 1:到期前续费
 * 2:到期后[0,7]天内续费
 * 3:到期后[8,30]天内续费
 * 4:到期后[31,60]天内续费
 * 5:到期后[61,90]天内续费
 * 6:到期后[91,180]天内续费
 * 7:到期后[181,~]天内续费
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum RenewFlagEnum {

    RENEW_FLAG_0(0, "新增"),
    RENEW_FLAG_1(1, "到期前续费"),
    RENEW_FLAG_2(2, "到期后(0,7]天内续费"),
    RENEW_FLAG_3(3, "到期后[8,30]天内续费"),
    RENEW_FLAG_4(4, "到期后[31,60]天内续费"),
    RENEW_FLAG_5(5, "到期后[61,90]天内续费"),
    RENEW_FLAG_6(6, "到期后[91,180]天内续费"),
    RENEW_FLAG_7(7, "到期后[181,365]天内续费"),
    RENEW_FLAG_8(8, "召回[到期大于365]天后续费"),
    ;
    private Integer renewFlag;
    private String desc;

    RenewFlagEnum(Integer renewFlag, String desc) {
        this.renewFlag = renewFlag;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(RenewFlagEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getRenewFlag(), s.getDesc()));
        });
        return list;
    }


    public static RenewFlagEnum findEnumByRenewFlag(Integer renewFlag) {
        for (RenewFlagEnum renewFlagEnum : RenewFlagEnum.values()) {
            if (renewFlagEnum.getRenewFlag().equals(renewFlag)) {
                return renewFlagEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByRenewFlags(List<Integer> renewFlags) {
        return renewFlags == null ? new ArrayList<>() : renewFlags.stream().map(RenewFlagEnum::findEnumByRenewFlag)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getRenewFlag(), e.getDesc()))
            .collect(Collectors.toList());
    }
}
