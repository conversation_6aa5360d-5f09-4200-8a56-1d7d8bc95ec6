package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum TeamEnum {

    UNKNOW(0, "未知团队"),
    IUNION(1, "i联盟"),
    IQY_SNS(2, "爱奇艺SNS"),
    VIP(4, "大客户组"),
    DX_MOBILE(6, "电信运营商移动版"),
    FININCE(7, "金融组"),
    CHANNEL(8, "渠道组"),
    BUSINESS(9, "商务组"),
    SALE_DEPART(10, "销售部其他组"),
    DX_TV(11, "电信运营商TV"),
    QYG_SNS(12, "奇异果SNS"),
    QYG_OUT(13, "奇异果外广"),
    WIRED_TV(14, "有线网TV"),
    FIRM_TV(15, "厂商TV"),
    PC_STATION(16, "PC/PH/TB-站内"),
    LITERATURE_STATION(17, "文学-站内"),
    VR_STATION(18, "VR-站内"),
    IV_STATION(19, "IV-站内"),
    IV_OUT(20, "IV-站外"),
    IQY_RIVER_OUT(21, "爱奇艺导流媒体-站外"),
    HM_STATION(22, "HM-站内"),
    HM_OUT(23, "HM-站外"),
    CLOUD_TICKET(24, "云票"),
    ;
    private Integer code;
    private String desc;

    TeamEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(TeamEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }
}
