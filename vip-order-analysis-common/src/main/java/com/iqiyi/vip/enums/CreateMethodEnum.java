package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * 创建方式
 */
@Getter
public enum CreateMethodEnum {

    FILE_UPLOAD(1, "文件上传"),
    STORE(2, "收银台"),
    BASE_ANALYSIS(3, "订单基础分析"),
    TRANSFER_ANALYSIS(4, "订单转移分析"),
    AUTO_RENEW_ANALYSIS(5, "自动续费"),
    MEMBER_RETENTION(6, "会员留存"),
    DIAGNOSTIC_ANALYSIS(7, "诊断分析"),
    ;

    private final Integer code;
    private final String desc;

    CreateMethodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //通过code获取desc
    public static String getDescByCode(Integer code) {
        for (CreateMethodEnum e : CreateMethodEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(CreateMethodEnum.values()).forEach(s -> list.add(new CodeDescPair(s.getCode(), s.getDesc())));
        return list;
    }
}
