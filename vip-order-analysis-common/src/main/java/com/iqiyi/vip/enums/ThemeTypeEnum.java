package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/19 20:41
 */
@AllArgsConstructor
@Getter
public enum ThemeTypeEnum {

    ORDER_THEME(1, "订单分析"),
    AUTO_RENEW_THEME(2, "自动续费"),
    STORE(3, "收银台"),
    MEMBER_RETENTION(4, "会员留存"),
    COMPETITOR_MONITOR(6, "竞品监控"),
    ;

    private Integer code;
    private String msg;

    public static ThemeTypeEnum findEnumByThemeType(Integer themeType) {
        for (ThemeTypeEnum themeTypeEnum : ThemeTypeEnum.values()) {
            if (themeTypeEnum.code.equals(themeType)) {
                return themeTypeEnum;
            }
        }
        return null;
    }
}
