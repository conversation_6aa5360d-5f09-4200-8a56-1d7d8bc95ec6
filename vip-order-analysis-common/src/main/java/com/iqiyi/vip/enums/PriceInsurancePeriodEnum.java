package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * <AUTHOR>
 * @className PriceInsurancePeriodEnum
 * @description
 * @date 2023/8/22
 **/
public enum PriceInsurancePeriodEnum {
    PRICE_OLD(1, "老价格"),

    PRICE_2020(2, "新价格2020"),

    PRICE_2021(3, "新价格2021"),

    PRICE_2022(4, "新价格2022"),
    ;

    private Integer id;
    private String desc;

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(PriceInsurancePeriodEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getId(), s.getDesc()));
        });
        return list;
    }

    public static PriceInsurancePeriodEnum findEnumById(Integer id) {
        for (PriceInsurancePeriodEnum periodEnum : PriceInsurancePeriodEnum.values()) {
            if (periodEnum.id.equals(id)) {
                return periodEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByIds(List<Integer> ids) {
        return CollectionUtils.isEmpty(ids) ? new ArrayList<>() : ids.stream().map(PriceInsurancePeriodEnum::findEnumById)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getId(), e.getDesc()))
            .collect(Collectors.toList());
    }

    PriceInsurancePeriodEnum(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public Integer getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }
}
