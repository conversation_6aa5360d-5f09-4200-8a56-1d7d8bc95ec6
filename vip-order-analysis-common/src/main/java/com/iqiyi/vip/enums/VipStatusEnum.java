package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import com.iqiyi.vip.dto.condition.CodeDescPair;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum VipStatusEnum {

//    NOT_VIP(0, "非会员"),
    VIP(1, "会员");
    ;
    private Integer code;
    private String desc;

    VipStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(VipStatusEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }
}
