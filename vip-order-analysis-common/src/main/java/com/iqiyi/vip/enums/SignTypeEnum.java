package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * <AUTHOR>
 * @className SignTypeEnum
 * @description
 * @date 2023/8/22
 **/
public enum SignTypeEnum {
    PURE_SIGN(0, "纯签约"),
    PAID_SIGN(1, "付费签约"),
    ;

    private Integer id;

    private String desc;

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(SignTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getId(), s.getDesc()));
        });
        return list;
    }

    public static SignTypeEnum findEnumById(Integer id) {
        for (SignTypeEnum signTypeEnum : SignTypeEnum.values()) {
            if (signTypeEnum.id.equals(id)) {
                return signTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByIds(List<Integer> ids) {
        return CollectionUtils.isEmpty(ids) ? new ArrayList<>() : ids.stream().map(SignTypeEnum::findEnumById)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getId(), e.getDesc()))
            .collect(Collectors.toList());
    }


    SignTypeEnum(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public Integer getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }
}
