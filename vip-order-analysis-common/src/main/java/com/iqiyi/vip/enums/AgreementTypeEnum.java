package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * 协议类型 Created at: 2021-06-17
 *
 * <AUTHOR>
 */
public enum AgreementTypeEnum {

    AUTO_RENEW(1, "常规自动续费"),
    ZHIMA_GO(2, "芝麻go"),
    WECHAT_PAY_SCORE(3, "微信支付分"),
    ;

    private Integer id;
    private String desc;


    AgreementTypeEnum(int value, String desc) {
        this.id = value;
        this.desc = desc;
    }


    public Integer getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(AgreementTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getId(), s.getDesc()));
        });
        return list;
    }


    public static AgreementTypeEnum findEnumById(Integer id) {
        for (AgreementTypeEnum agreementTypeEnum : AgreementTypeEnum.values()) {
            if (agreementTypeEnum.id.equals(id)) {
                return agreementTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByIds(List<Integer> ids) {
        return CollectionUtils.isEmpty(ids) ? new ArrayList<>() : ids.stream().map(AgreementTypeEnum::findEnumById)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getId(), e.getDesc()))
            .collect(Collectors.toList());
    }
}
