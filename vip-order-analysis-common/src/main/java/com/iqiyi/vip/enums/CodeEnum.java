package com.iqiyi.vip.enums;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @date 2021-05-25
 */
public enum CodeEnum {

    SUCCESS("A00000", "操作成功"),
    IDEMPOTENT("A00001", "重复请求"),
    ERROR_PARAM("Q00301", "参数错误"),
    ERROR_SIGN("Q00302", "签名错误"),
    RESPONSE_LOAD_TOO_HIGH("Q00303", "当前活动火爆，请稍后重试～"),

    DUPLICATE("Q00304", "重复添加"),
    NOT_EXIST("Q00305", "数据不存在"),
    EXTERNAL_SYSTEM_REQUEST_ERROR("Q00306", "第三方调用异常"),
    CONFIG_ERROR("Q00307", "配置错误"),
    CONDITION_PARAM_IS_EMPTY("Q00308", "查询条件为空"),

    OSS_ERROR("Q00310", "文件上传下载处理失败"),
    QUERY_ERROR("Q00311", "查询失败"),
    ERROR_SYSTEM("Q00332", "系统错误"),

    NOT_RIGHTFUL("Q00600", "数据权限不合法"),
    TEAMID_NOT_EXIST_ERROR("Q00601", "数据权限不合法"),
    BASE_INIT_ERROR("Q00602", "数据权限不合法"),
    DATA_PERMISSION_AUTH_LIMIT("Q00603", "有正在审批的数据权限"),
    DATA_PERMISSION_UN_SELECTED("Q00604", "用户拥有多种数据权限，未选择使用哪个权限"),
    DATA_PERMISSION_UN_OWNED("Q00605", "参数中使用的权限未申请"),
    UPLOAD_TO_BEI_DOU_ERROR("Q00606", "上传北斗失败"),
    USER_GROUP_DATA_UPDATE_FAIL("Q00607", "文件格式不合格,请检查后重新上传!"),
    GET_BEI_DOU_BUSINESS_ERROR("E00001", "该用户无任何业务线的权限，请先去北斗申请"),

    OA_APPROVED_ERROR("OA000100", "oa申请创建异常"),
    OA_APPROVED_SAVE_DATABASE_ERROR("OA000101", "oa审批通过落库异常"),

    DEVOPS_PERMISSION_REQUEST_ERROR("OA000201", "devops权限请求异常"),

    ZEUS_MONITOR_SAVE_ERROR("ZEUS0001", "请求宙斯创建监控异常"),
    ZEUS_MONITOR_UPDATE_ERROR("ZEUS0002", "请求宙斯更新监控异常"),
    ZEUS_MONITOR_DELETE_ERROR("ZEUS0003", "请求宙斯删除监控异常"),
    ZEUS_MONITOR_SELECT_ERROR("ZEUS0004", "请求宙斯查询监控异常"),

    TASK_RUN_FAILED("TASK0001", "任务执行结果为空")
    ;

    private String code;
    private String message;

    CodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
