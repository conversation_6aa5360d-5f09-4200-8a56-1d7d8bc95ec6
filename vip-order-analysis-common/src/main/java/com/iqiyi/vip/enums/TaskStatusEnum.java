package com.iqiyi.vip.enums;

/**
 * <AUTHOR>
 * @className TaskStatusEnum
 * @description
 * @date 2022/6/6
 **/
public enum TaskStatusEnum {
    READY(0, "待执行"),
    RUNNING(1, "执行中"),
    FINISHED(2, "执行完成"),
    FAILED(3, "执行失败"),;

    private Integer status;

    private String desc;

    TaskStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
