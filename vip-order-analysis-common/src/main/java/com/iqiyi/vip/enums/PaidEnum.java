package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 免付费标识 0:免费 1:付费
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum PaidEnum {

    /**
     * 免付费标识 0:免费 1:付费
     */
    PAY(1, "付费(非0元)"),
    ZERO_PAY(2, "付费(0元)"),
    FREE(0, "免费"),
    ;
    private Integer code;
    private String desc;

    PaidEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(PaidEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }


    public static PaidEnum findEnumByPaidType(Integer paidType) {
        for (PaidEnum paidEnum : PaidEnum.values()) {
            if (paidEnum.getCode().equals(paidType)) {
                return paidEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByPaidTypes(List<Integer> paidTypes) {
        return paidTypes == null ? new ArrayList<>() : paidTypes.stream().map(PaidEnum::findEnumByPaidType)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getCode(), e.getDesc()))
            .collect(Collectors.toList());
    }
}
