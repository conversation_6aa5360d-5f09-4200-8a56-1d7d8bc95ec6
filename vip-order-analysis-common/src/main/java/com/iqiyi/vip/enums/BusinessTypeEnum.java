package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BusinessTypeEnum {

    BASIC_ANALYSIS(1, "基础分析"),
    TRANSFER_ANALYSIS(2, "转移分析"),
    DIAGNOSTIC_ANALYSIS(3, "诊断分析");

    private Integer code;
    private String msg;

    public static BusinessTypeEnum findEnumByThemeType(Integer themeType) {
        for (BusinessTypeEnum themeTypeEnum : BusinessTypeEnum.values()) {
            if (themeTypeEnum.code.equals(themeType)) {
                return themeTypeEnum;
            }
        }
        return null;
    }
}
