package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @className RemainConditionTypeEnum
 * @description
 * @date 2024/6/30
 **/
public enum RemainConditionTypeEnum {

    VIP_GROUP("vip_group", "会员类型"),
    VIP_BIZ_TYPE("vip_biz_type", "套餐类型"),
    PRESENT_TYPE("zero_order_type", "买赠类型"),
    CARD_TYPE("vip_card_type", "卡种"),
    RENEW_FLAG("renew_flag", "生命周期"),
    UID_LAYER("uid_layer", "基石潮汐"),
    SEX_AND_AGE("sexandage", "圈层");

    @Getter
    private final String code;

    @Getter
    private final String name;

    RemainConditionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RemainConditionTypeEnum getByCode(String code) {
        for (RemainConditionTypeEnum remainConditionTypeEnum : RemainConditionTypeEnum.values()) {
            if (remainConditionTypeEnum.getCode().equals(code)) {
                return remainConditionTypeEnum;
            }
        }
        return null;
    }

}
