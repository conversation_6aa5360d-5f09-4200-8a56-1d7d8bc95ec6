package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum VipProductTypeEnum {

    DANDIAN(0, "单点"),
    VIP(1, "会员"),
    TAOCAN(2, "套餐"),
    OTHER(3, "其他"),
    ;
    private Integer code;
    private String desc;

    VipProductTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(VipProductTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }


    public static VipProductTypeEnum findEnumByProductType(Integer productType) {
        for (VipProductTypeEnum vipProductTypeEnum : VipProductTypeEnum.values()) {
            if (vipProductTypeEnum.getCode().equals(productType)) {
                return vipProductTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByProductTypes(List<Integer> productTypes) {
        return productTypes == null ? new ArrayList<>() : productTypes.stream().map(VipProductTypeEnum::findEnumByProductType)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getCode(), e.getDesc()))
            .collect(Collectors.toList());
    }
}
