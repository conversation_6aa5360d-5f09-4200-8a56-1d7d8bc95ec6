package com.iqiyi.vip.enums;

/**
 * @author: guojing
 * @date: 2025/3/11 16:47
 */
public enum MetricTypeEnum {

    Counter("Counter", "计数器"),
    Gauge("Gauge", "仪表盘"),
    ;

    private String value;
    private String desc;

    MetricTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static MetricTypeEnum findByValue(String value) {
        for (MetricTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

}
