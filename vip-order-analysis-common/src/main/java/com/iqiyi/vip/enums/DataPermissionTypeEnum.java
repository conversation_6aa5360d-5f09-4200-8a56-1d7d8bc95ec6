package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/19 20:41
 */
@AllArgsConstructor
@Getter
public enum DataPermissionTypeEnum {

    FV_DATA_PERMISSION(1, "渠道产品申请"),
    VIP_TYPE_DATA_PERMISSION(2, "会员类型申请"),
    COMPETITOR_DATA_PERMISSION(3, "竞品监控申请"),
    ;
    private Integer code;
    private String msg;

    public static DataPermissionTypeEnum findEnumByDataPermissionType(Integer dataPermissionType) {
        for (DataPermissionTypeEnum dataPermissionTypeEnum : DataPermissionTypeEnum.values()) {
            if (dataPermissionTypeEnum.getCode().equals(dataPermissionType)) {
                return dataPermissionTypeEnum;
            }
        }
        return null;
    }
}
