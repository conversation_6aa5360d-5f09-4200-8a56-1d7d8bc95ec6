package com.iqiyi.vip.enums;

/**
 * <AUTHOR>
 * @date 5/13/22
 * @apiNote
 */
public enum StatusEnum {

    EFFECTIV(1, "有效"),
    INVALID(0, "无效");

    private Integer code;
    private String desc;

    StatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }}
