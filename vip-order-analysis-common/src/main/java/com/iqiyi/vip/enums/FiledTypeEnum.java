package com.iqiyi.vip.enums;


/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2023/09/13
 */
public enum FiledTypeEnum {
    STRING(1, "String类型"),
    INTEGER(2, "Integer类型"),
    ;

    private final Integer type;
    private final String desc;

    FiledTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isString(Integer filedType) {
        return STRING.getType().equals(filedType);
    }

    public static boolean isInteger(Integer filedType) {
        return INTEGER.getType().equals(filedType);
    }

}
