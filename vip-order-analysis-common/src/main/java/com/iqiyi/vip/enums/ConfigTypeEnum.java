package com.iqiyi.vip.enums;

/**
 * <AUTHOR>
 * @className ConfigTypeEnum
 * @description
 * @date 2022/11/24
 **/
public enum ConfigTypeEnum {
    SQL_TEMPLATE(1, "指标模板"),
    TARGET(2, "指标"),
    DIMENSION(3, "维度"),
    ;
    private final Integer type;
    private final String desc;

    ConfigTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ConfigTypeEnum of(Integer configType) {
        if (configType == null) {
            return null;
        }
        for (ConfigTypeEnum typeEnum : ConfigTypeEnum.values()) {
            if (typeEnum.type.equals(configType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
