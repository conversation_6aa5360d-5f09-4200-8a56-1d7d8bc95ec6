package com.iqiyi.vip.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import com.iqiyi.vip.dto.condition.CodeDescPair;


/**
 * <AUTHOR>
 * @className RemainDataTypeEnum
 * @description
 * @date 2024/6/25
 **/
public enum RemainDataTypeEnum {

    REMAIN(1, "留存账号数"),
    INFLOW(2, "流入账号数"),
    OUTFLOW(3, "流出账号数"),
    INCREASE(4, "净增"),
    ;

    RemainDataTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;

    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取code-desc列表
     */
    public static List<CodeDescPair> getCodeDescPairList() {
        return Arrays.stream(RemainDataTypeEnum.values()).map(e -> new CodeDescPair(e.getCode(), e.getDesc())).collect(Collectors.toList());
    }

    public static RemainDataTypeEnum getByCode(Integer code) {
        return Arrays.stream(RemainDataTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }
}
