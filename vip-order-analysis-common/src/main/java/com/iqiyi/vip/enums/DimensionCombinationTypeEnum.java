package com.iqiyi.vip.enums;

/**
 * <AUTHOR>
 * @className DimensionCombinationTypeEnum
 * @description
 * @date 2024/6/29
 **/
public enum DimensionCombinationTypeEnum {

    SINGLE("single", "单维度"),
    MULTI("multi", "多维度组合");

    private final String type;
    private final String message;

    DimensionCombinationTypeEnum(String type, String message) {
        this.type = type;
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }
}
