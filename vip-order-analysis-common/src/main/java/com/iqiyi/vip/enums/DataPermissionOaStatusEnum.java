package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OA审批状态
 * <AUTHOR>
 * @date 2022/8/18 17:28
 */
@Getter
@AllArgsConstructor
public enum DataPermissionOaStatusEnum {

    // 1 已提交 2 审批通过 4 已驳回 8 已撤回
    APPLYED(1, "已提交"),
    APPROVED(APPLYED.code << 1, "审批通过"),
    REJECT(APPROVED.code << 1, "已驳回"),
    WITHDRAW(REJECT.code << 1, "已撤回"),
    ;

    private int code;
    private String desc;


    public static DataPermissionOaStatusEnum transfor(Integer oaStatus) {
        if (oaStatus == null) {
            return REJECT;
        } else if (oaStatus == 0) {
            return APPROVED;
        } else if (oaStatus == 1) {
            return WITHDRAW;
        } else {
            return REJECT;
        }
    }

}
