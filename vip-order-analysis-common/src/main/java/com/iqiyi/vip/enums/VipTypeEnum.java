package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.apache.bcel.classfile.Code;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum VipTypeEnum {

    FUN(7, "爱奇艺fun会员"),
    QYG_EXCLUSIVE(57, "奇异果电视专享会员"),
    GAME_VIP(10, "游戏会员"),
    QYG_CHILD(11, "奇异果儿童会员"),
    GOLD(13, "爱奇艺黄金会员"),
//    SILVER(14, "爱奇艺白银会员"),
STAR(15, "爱奇艺星钻会员"),
    STUDENT(16, "学生VIP会员"),
    VR(50, "爱奇艺VR会员"),
    BUSINESS_VIP(51, "爱奇艺商用会员"),
    TV_BUSINESS(52, "TV商用会员"),
    QYG_STAR(54, "奇异果星钻会员"),
    LITE(56, "爱奇艺基础会员"),
    QYG_PLATINUM(8, "奇异果白金会员"),
    PLATINUM(58, "爱奇艺白金会员"),
    QYG_BASIC(60, "奇异果基础会员"),
    ;
    private Integer vipType;
    private String desc;

    VipTypeEnum(Integer vipType, String desc) {
        this.vipType = vipType;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(VipTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getVipType(), s.getDesc()));
        });
        return list;
    }

    public static VipTypeEnum findEnumByVipType(Integer vipType) {
        for (VipTypeEnum vipTypeEnum : VipTypeEnum.values()) {
            if (vipTypeEnum.getVipType().equals(vipType)) {
                return vipTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByVipTypes(List<Integer> vipTypes) {
        return vipTypes == null ? new ArrayList<>() : vipTypes.stream().map(VipTypeEnum::findEnumByVipType)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getVipType(), e.getDesc()))
            .collect(Collectors.toList());
    }
}
