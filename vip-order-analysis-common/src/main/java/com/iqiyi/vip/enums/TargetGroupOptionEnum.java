package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * 指标类型
 */
@Getter
public enum TargetGroupOptionEnum {

    ORDINARY_INDICATORS(-1, "普通指标"),
    ADVANCED_INDICATORS(1, "高级指标");

    private final Integer code;
    private final String desc;

    TargetGroupOptionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(TargetGroupOptionEnum.values()).forEach(s -> list.add(new CodeDescPair(s.getCode(), s.getDesc())));
        return list;
    }
}
