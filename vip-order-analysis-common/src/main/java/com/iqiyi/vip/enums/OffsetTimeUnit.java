package com.iqiyi.vip.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

/**
 * 查询表达式时间单位
 * @author: guojing
 * @date: 2023/12/18 18:05
 */
public enum OffsetTimeUnit {

    s("秒"),
    m( "分"),
    h("小时"),
    d("天"),
    w("周"),
    ;

    private String desc;

    OffsetTimeUnit(String desc) {
        this.desc = desc;
    }

    private static final HashMap<String, OffsetTimeUnit> map = new HashMap<>();
    static {
        for (OffsetTimeUnit enumType : OffsetTimeUnit.values()) {
            map.put(enumType.name(), enumType);
        }
    }

    public static OffsetTimeUnit parseValue(String value) {
        return map.getOrDefault(value, null);
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 将秒单位转换为时间单位
     * @param durationInSec
     */
    public static String toOffsetTimeUnitName(int durationInSec) {
        if (durationInSec < 60) {
            return durationInSec + s.name().toLowerCase();
        } else if (durationInSec < 3600) {
            return durationInSec / 60 + m.name().toLowerCase();
        } else if (durationInSec < 86400) {
            return durationInSec / 3600 + h.name().toLowerCase();
        } else if (durationInSec < 604800) {
            return durationInSec / 86400 + d.name().toLowerCase();
        } else {
            return durationInSec / 604800 + w.name().toLowerCase();
        }
    }

    /**
     * 获取时间单位对应的秒数
     * @param duration
     */
    public static int toSeconds(String duration) {
        if (StringUtils.isBlank(duration)) {
            return 0;
        }
        int seconds = Integer.parseInt(duration.substring(0, duration.length() - 1));
        if (duration.endsWith(s.name())) {
            return seconds;
        } else if (duration.endsWith(m.name())) {
            return seconds * 60;
        } else if (duration.endsWith(h.name())) {
            return seconds * 3600;
        } else if (duration.endsWith(d.name())) {
            return seconds * 86400;
        } else if (duration.endsWith(w.name())) {
            return seconds * 604800;
        } else {
            throw new IllegalArgumentException("Invalid offset time unit name, duration:" + duration);
        }
    }

}
