package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import static com.iqiyi.vip.enums.VipTypeEnum.GOLD;
import static com.iqiyi.vip.enums.VipTypeEnum.LITE;
import static com.iqiyi.vip.enums.VipTypeEnum.PLATINUM;
import static com.iqiyi.vip.enums.VipTypeEnum.QYG_BASIC;
import static com.iqiyi.vip.enums.VipTypeEnum.QYG_CHILD;
import static com.iqiyi.vip.enums.VipTypeEnum.QYG_EXCLUSIVE;
import static com.iqiyi.vip.enums.VipTypeEnum.QYG_PLATINUM;
import static com.iqiyi.vip.enums.VipTypeEnum.QYG_STAR;
import static com.iqiyi.vip.enums.VipTypeEnum.STAR;
import static com.iqiyi.vip.enums.VipTypeEnum.STUDENT;
import static com.iqiyi.vip.enums.VipTypeEnum.TV_BUSINESS;

/**
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum VipGroupEnum {

    IQY(1, "爱奇艺会员", Lists.newArrayList(GOLD, PLATINUM, STAR, STUDENT, LITE)),
    QYG(2, "奇异果会员", Lists.newArrayList(QYG_EXCLUSIVE, QYG_PLATINUM, QYG_STAR, QYG_BASIC)),
    FUN(4, "爱奇艺Fun会员", Lists.newArrayList(VipTypeEnum.FUN)),
    VR(5, "爱奇艺VR会员", Lists.newArrayList(VipTypeEnum.VR)),
    CHILDREN(6, "奇异果儿童会员", Lists.newArrayList(QYG_CHILD)),
    COMMERCIAL_AFFAIRS(7, "商务会员", Lists.newArrayList(TV_BUSINESS)),
    ;
    private Integer code;
    private String desc;
    private List<VipTypeEnum> nestedVipTypes;

    VipGroupEnum(Integer code, String desc, List<VipTypeEnum> nestedVipTypes) {
        this.code = code;
        this.desc = desc;
        this.nestedVipTypes = nestedVipTypes;
    }

    public static List<Integer> getSystemVipTypes(List<Integer> vipSystems) {
        if (CollectionUtils.isEmpty(vipSystems)) {
            return Collections.emptyList();
        }
        List<Integer> systemVipTypes = Lists.newArrayList();
        for (VipGroupEnum systemEnum : VipGroupEnum.values()) {
            if (vipSystems.contains(systemEnum.getCode())) {
                systemVipTypes.addAll(systemEnum.getNestedVipTypes().stream().map(VipTypeEnum::getVipType).collect(Collectors.toList()));
            }
        }
        return systemVipTypes;
    }

    public static List<CodeDescPair> getGroupPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(VipGroupEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getCode(), s.getDesc()));
        });
        return list;
    }

    public static List<CodeDescPair> getVipTypesByGroup(List<Integer> groupIds) {
        ArrayList<CodeDescPair> arrayList = new ArrayList<>();
        for (Integer groupId : groupIds) {
            VipGroupEnum groupEnum = Arrays.stream(VipGroupEnum.values())
                .filter(vipGroupEnum -> vipGroupEnum.getCode().equals(groupId))
                .findFirst()
                .orElse(null);
            if (groupEnum == null) {
                continue;
            }
            arrayList.addAll(fromVipTypeEnums(groupEnum.getNestedVipTypes()));
        }
        return arrayList;
    }


    public static List<CodeDescPair> fromVipTypeEnums(List<VipTypeEnum> nestedVipTypes) {
        return nestedVipTypes.stream()
            .map(vipTypeEnum -> new CodeDescPair(vipTypeEnum.getVipType(), vipTypeEnum.getDesc()))
            .collect(Collectors.toList());
    }


    public static VipGroupEnum findEnumByVipGroup(Integer vipGroup) {
        for (VipGroupEnum vipGroupEnum : VipGroupEnum.values()) {
            if (vipGroupEnum.getCode().equals(vipGroup)) {
                return vipGroupEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByVipGroupIds(List<Integer> vipGroupIds) {
        return vipGroupIds == null ? new ArrayList<>() : vipGroupIds.stream().map(VipGroupEnum::findEnumByVipGroup)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.code, e.getDesc()))
            .collect(Collectors.toList());
    }
}
