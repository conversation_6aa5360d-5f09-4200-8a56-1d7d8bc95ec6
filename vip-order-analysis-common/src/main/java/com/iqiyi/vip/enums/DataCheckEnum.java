package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DataCheckEnum
 * @description
 * @date 2022/10/27
 **/
@AllArgsConstructor
@Getter
public enum DataCheckEnum {

    ORDER_COUNT(1, "订单总数（自建宽表对比BI底表）"),
    ORDER_REALFEE_SUM(2,  "订单总金额（自建宽表对比BI底表）"),
    NEW_OLD_FV_MAPPING(3, "新老fv映射"),
    FV_MAPPING(4, "渠道信息映射"),
    TEAM_MAPPING(5, "团队信息映射"),
    BIZ_MAPPING(6, "产品信息映射"),
    PLATFORM_END_KEY_MAPPING(7, "端平台信息映射"),
    PARTNER_ORDER_MAPPING(8, "联名订单映射"),
    ORDER_COUNT_VIPTYPE_TABLES_DIFF(9, "订购数_会员类型（自建宽表对比BI底表）"),
    ORDER_UID_COUNT_VIPTYPE_TABLES_DIFF(10, "订购会员数_会员类型（自建宽表对比BI底表）"),
    ORDER_DHSR_VIPTYPE_TABLES_DIFF(11, "兑换收入_会员类型（自建宽表对比BI底表）"),
    ORDER_COUNT_VIPTYPE_FV_DIFF(12, "订购数_会员类型_渠道（自建宽表对比）"),
    ORDER_UID_COUNT_VIPTYPE_FV_DIFF(13, "订购会员数_会员类型_渠道（自建宽表对比）"),
    ORDER_DHSR_VIPTYPE_FV_DIFF(14, "兑换收入_会员类型_渠道（自建宽表对比）"),
    ORDER_LTV_VIPTYPE_FV_DIFF(15, "LTV_会员类型_渠道（自建宽表对比）"),
    ORDER_COUNT_CILCK_HOUSE(16, "订单总数（自建宽表对比clickhouse）"),
    SUM_Fee_CILCK_HOUSE(17, "订单总数（自建宽表对比clickhouse）"),
    HIVE_TABLES_SUM_VS_CLICKHOUSE_ORDER_COUNT(18, "订单总数（会员订单、凭证订单表之和对比Clickhouse）"),
    HIVE_TABLES_SUM_VS_CLICKHOUSE_SUM_FEE(19, "订单总金额（会员订单、凭证订单表之和对比Clickhouse）"),
    ;
    private int id;
    private String desc;

    public static Map<Integer, String> getAllToMap() {
        Map<Integer, String> map = new HashMap<>();
        for (DataCheckEnum dataCheckEnum : values()) {
            map.put(dataCheckEnum.getId(), dataCheckEnum.getDesc());
        }
        return map;
    }

}
