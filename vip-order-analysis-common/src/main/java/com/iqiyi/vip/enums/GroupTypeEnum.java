package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.dto.condition.CodeDescPair;

/**
 * 人群包类型
 */
@Getter
public enum GroupTypeEnum {

    USER_PACKAGE(1, "人群包"),
    ORDER_PACKAGE(2, "订单包");

    private final Integer code;
    private final String desc;

    GroupTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(GroupTypeEnum.values()).forEach(s -> list.add(new CodeDescPair(s.getCode(), s.getDesc())));
        return list;
    }
}
