package com.iqiyi.vip.enums;

/**
 * 任务来源
 * <AUTHOR>
 * @className TaskSourceEnum
 * @description
 * @date 2022/6/6
 **/
public enum AnalysisTaskSourceEnum {
    UI_MANUAL(1, "天眼页面"),
    SCHEDULED(2, "定时任务"),
    AGENT(3, "代理任务"),
    ;

    private Integer value;

    private String desc;

    AnalysisTaskSourceEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AnalysisTaskSourceEnum parseValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (AnalysisTaskSourceEnum taskSourceEnum : AnalysisTaskSourceEnum.values()) {
            if (taskSourceEnum.getValue().equals(value)) {
                return taskSourceEnum;
            }
        }
        return null;
    }

}
