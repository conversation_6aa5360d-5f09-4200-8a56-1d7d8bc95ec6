package com.iqiyi.vip.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.iqiyi.vip.constant.Constants;

/**
 * <AUTHOR>
 * @date 2022/8/17 14:47
 */
@Getter
@AllArgsConstructor
public enum ConditionEnum {

    BUSINESS_LEVEL("fv", Constants.DATA_PERMISSION_LEVEL_ABBREVIATION),

    PRO("pro", Constants.DATA_PERMISSION_PRO_ABBREVIATION),

    VIP_TYPE(" vipType", Constants.DATA_PERMISSION_VT_ABBREVIATION),

    VIP_CARD_TYPE(" vipCardType", Constants.DATA_PERMISSION_CARD_ABBREVIATION),

    RENEW_TYPE(" renewType", Constants.DATA_PERMISSION_RENEW_ABBREVIATION),

    PAY_CHANNEL("payChannel", Constants.DATA_PERMISSION_PAY_CHANNEL_ABBREVIATION),

    REN<PERSON>W_FLAG("renewFlag", Constants.DATA_PERMISSION_RENEW_FLAG_ABBREVIATION),

    COMPETITOR_MONITOR("competitorMonitor", Constants.DATA_PERMISSION_COMPETITOR_MONITOR_ABBREVIATION),
    ;

    public String getDesc() {
        return desc;
    }

    private String desc;
    private String fieldPrefix; // 映射 LayeredDataPermissionDTO 属性前缀

}
