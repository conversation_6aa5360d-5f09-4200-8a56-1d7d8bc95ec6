package com.iqiyi.vip.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.dto.dimension.RemainV2CodeDescPair;

/**
 * <AUTHOR>
 * @date 2025/6/13 13:33
 */
public enum RemainV2DimensionEnum {
    VIP_GROUP("vip_group", "会员类型"),
    VIP_BIZ_TYPE("vip_biz_type", "套餐类型"),
    ZERO_ORDER_TYPE("zero_order_type", "买赠类型"),
    VIP_CARD_TYPE("vip_card_type", "卡种"),
    RENEW_FLAG("renew_flag", "生命周期"),
    UID_LAYER("uid_layer", "基石潮汐"),
    SEX_AND_AGE("sexandage", "圈层"),
    AUTO_RENEW_STATUS("auto_renew", "自动续费状态"),
    KR_ALBUM("kr_channel_name", "KR专辑频道")
    ;

    RemainV2DimensionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取code-desc列表
     */
    public static List<RemainV2CodeDescPair> getCodeDescPairList() {
        return Arrays.stream(RemainV2DimensionEnum.values()).map(e -> new RemainV2CodeDescPair(e.getCode(), e.getDesc())).collect(Collectors.toList());
    }
}
