package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import com.iqiyi.vip.dto.condition.CodeDescPair;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 付费类型 0：普通购买（非连包），1签约购买（连包购买），2系统代扣（连包代扣）
 * <AUTHOR>
 * @date 6/10/22
 * @apiNote
 */
@Getter
public enum RenewTypeEnum {

    RENEW_TYPE_0(0, "普通购买（非连包）"),
    RENEW_TYPE_1(1, "签约购买（连包购买）"),
    RENEW_TYPE_2(2, "系统代扣（连包代扣）"),
    ;
    private Integer renewType;
    private String desc;

    RenewTypeEnum(Integer renewType, String desc) {
        this.renewType = renewType;
        this.desc = desc;
    }

    public static List<CodeDescPair> getPairs() {
        List<CodeDescPair> list = Lists.newArrayList();
        Arrays.stream(RenewTypeEnum.values()).forEach(s -> {
            list.add(new CodeDescPair(s.getRenewType(), s.getDesc()));
        });
        return list;
    }


    public static RenewTypeEnum findEnumByRenewType(Integer renewType) {
        for (RenewTypeEnum renewTypeEnum : RenewTypeEnum.values()) {
            if (renewTypeEnum.getRenewType().equals(renewType)) {
                return renewTypeEnum;
            }
        }
        return null;
    }

    public static List<CodeDescPair> getPairsByRenewTypes(List<Integer> renewTypes) {
        return renewTypes == null ? new ArrayList<>() : renewTypes.stream().map(RenewTypeEnum::findEnumByRenewType)
            .filter(Objects::nonNull)
            .map(e -> new CodeDescPair(e.getRenewType(), e.getDesc()))
            .collect(Collectors.toList());
    }

}
