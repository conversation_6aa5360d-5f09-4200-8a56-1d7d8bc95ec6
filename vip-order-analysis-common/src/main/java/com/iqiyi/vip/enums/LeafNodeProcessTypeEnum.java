package com.iqiyi.vip.enums;

/**
 * <AUTHOR>
 * @className FvChannelLeafNodeProcessType
 * @description
 * @date 2022/10/10
 **/
public enum LeafNodeProcessTypeEnum {

    ALL_FV_LEVEl(1, "考虑每层渠道的叶子节点作为查询条件"),
    ONLY_FINAL_FV(2, "仅考虑显示指定的最后一级渠道作为查询条件"),;

    private Integer type;
    private String desc;

    LeafNodeProcessTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LeafNodeProcessTypeEnum of(Integer processType) {
        if (processType == null) {
            return ONLY_FINAL_FV;
        }
        for (LeafNodeProcessTypeEnum processTypeEnum : values()) {
            if (processTypeEnum.getType().equals(processType)) {
                return processTypeEnum;
            }
        }
        return ONLY_FINAL_FV;
    }
}
