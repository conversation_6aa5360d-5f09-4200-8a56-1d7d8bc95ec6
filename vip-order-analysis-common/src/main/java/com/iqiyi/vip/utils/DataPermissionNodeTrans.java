package com.iqiyi.vip.utils;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.data.DataPermissionNode;
import com.iqiyi.vip.dto.permission.DataPermissionDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2022/9/13 10:15
 */
public final class DataPermissionNodeTrans {

    /**
     * 复制数据权限节点，得到可返回的对象实体
     * @param dataPermissionNode
     * @return
     */
    public static DataPermissionDTO copyDataPermissionNode(DataPermissionNode dataPermissionNode) {
        return DataPermissionDTO.builder()
            .id(dataPermissionNode.getId())
            .level(dataPermissionNode.getCurLabelLevel())
            .name(dataPermissionNode.getName())
            .parentId(dataPermissionNode.getParent() == null ? null :
                dataPermissionNode.getLabel() == LabelEnum.L3 ?
                    dataPermissionNode.getParent().getParent() == null ? null : dataPermissionNode.getParent().getParent().getId()
                    : dataPermissionNode.getParent().getId())
            .parentName(dataPermissionNode.getParent() == null ? null : dataPermissionNode.getParent().getName())
            .teamId(dataPermissionNode.getTeamId())
            .teamName(dataPermissionNode.getTeamName())
            .isMainStation(dataPermissionNode.getLabel() == LabelEnum.L2 && !Constants.unMainStation(dataPermissionNode.getId()))
            .parent(dataPermissionNode.getParent() == null ? null :
                copyDataPermissionNode((DataPermissionNode) dataPermissionNode.getParent()))
            .build();
    }

    /**
     * 复制数据权限节点，得到基础数据对象
     * @param dataPermissionDTO
     * @return
     */
    public static DataPermissionNode copyDataPermissionDTO(DataPermissionDTO dataPermissionDTO) {
        return DataPermissionNode.builder()
            .id(dataPermissionDTO.getId())
            .name(dataPermissionDTO.getName())
            .label(LabelEnum.levelOf(dataPermissionDTO.getLevel()))
            .teamId(dataPermissionDTO.getTeamId())
            .teamName(dataPermissionDTO.getTeamName())
            .parent(dataPermissionDTO == null || dataPermissionDTO.getParent() == null ? null : copyDataPermissionDTO(dataPermissionDTO.getParent()))
            .build();
    }

    private DataPermissionNodeTrans() {
        throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
    }
}
