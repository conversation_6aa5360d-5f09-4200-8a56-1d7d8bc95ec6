package com.iqiyi.vip.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2022/9/14 17:18
 */
public final class DateUtils {

    public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String NORM2_DATETIME_PATTERN = "yyyy-MM-dd_HH:mm:ss";
    public static final String yyyyMMddHHmmss_PATTERN = "yyyyMMddHHmmss";

    public static final ZoneOffset PLUS8_ZONE = ZoneOffset.ofHours(8);
    public static final DateTimeFormatter NORM_DATETIME_FORMAT = DateTimeFormatter.ofPattern(NORM_DATETIME_PATTERN);
    public static final DateTimeFormatter NORM2_DATETIME_FORMAT = DateTimeFormatter.ofPattern(NORM2_DATETIME_PATTERN);
    public static final DateTimeFormatter yyyyMMddHHmmss_FORMAT = DateTimeFormatter.ofPattern(yyyyMMddHHmmss_PATTERN);
    private static final DateTimeFormatter DATE_DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YM_DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");

    public static LocalDateTime ofMilliseconds(long milliseconds) {
        final long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds);
        final long nanos = TimeUnit.MILLISECONDS.toNanos(milliseconds - TimeUnit.SECONDS.toMillis(seconds));
        return LocalDateTime.ofEpochSecond(seconds, (int) nanos, PLUS8_ZONE);
    }

    public static long toMilliseconds(LocalDateTime localDateTime) {
        return localDateTime.toInstant(PLUS8_ZONE).toEpochMilli();
    }

    /**
     * 将时间设置为月初
     * @param localDateTime
     * @return
     */
    public static LocalDateTime retBeginningOfMonth(LocalDateTime localDateTime) {
        return localDateTime.with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDateTime retBeginningOfMonth(long milliseconds) {
        return retBeginningOfMonth(DateUtils.ofMilliseconds(milliseconds));
    }

    /**
     * 将时间设置为月末
     * @param localDateTime
     * @return
     */
    public static LocalDateTime retEndOfTheMonth(LocalDateTime localDateTime) {
        return localDateTime.with(TemporalAdjusters.lastDayOfMonth());
    }

    public static LocalDateTime retEndOfTheMonth(long milliseconds) {
        return retEndOfTheMonth(DateUtils.ofMilliseconds(milliseconds));
    }

    public static long dateDistance(LocalDateTime l1, LocalDateTime l2) {
        return Math.abs(l1.until(l2, ChronoUnit.MONTHS));
    }

    public static String normDatetimeFormat(long milliseconds) {
        return NORM_DATETIME_FORMAT.format(ofMilliseconds(milliseconds));
    }

    public static String getYyyyMMddHHmmssStr() {
        return yyyyMMddHHmmss_FORMAT.format(LocalDateTime.now());
    }

    public static String getYyyyMMddHHmmssStr(Date date) {
        return yyyyMMddHHmmss_FORMAT.format(ofMilliseconds(date.getTime()));
    }

    public static String currentDateTimeStr(DateTimeFormatter formatter) {
        return formatter.format(LocalDateTime.now());
    }

    public static String dateDatetimeFormat(long milliseconds) {
        return DATE_DATETIME_FORMAT.format(ofMilliseconds(milliseconds));
    }


    public static String ymDataTimeFormat(LocalDateTime localDateTime) {
        return YM_DATETIME_FORMAT.format(localDateTime);
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.toInstant(PLUS8_ZONE));
    }

    private DateUtils() {
        throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
    }

    /**
     * 返回两个时间之间的时间格式字符串
     */
    public static List<String> getDateStrBetweenDate(String startTime, String endTime, String pattern, int fieldType) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 声明保存日期集合
        List<String> list = new ArrayList<String>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把日期增加一个单位
                calendar.add(fieldType, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }

    public static long calculateDaysBetween(long timestamp1, long timestamp2) {
        // 计算两个时间戳之间的时间差（以毫秒为单位）
        long diffInMillis = Math.abs(timestamp2 - timestamp1);
        // 将时间差转换为天数
        long daysBetween = TimeUnit.MILLISECONDS.toDays(diffInMillis);
        return daysBetween;
    }

    public static Date string2Date(String dateTimeStr) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, NORM_DATETIME_FORMAT);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 根据日期字符串和偏移量计算偏移后的日期字符串
     *
     * @param dateStr 日期字符串，格式为 "yyyy-MM-dd"
     * @param offset  偏移量（正数为未来，负数为过去）
     * @return 偏移后的日期字符串，格式为 "yyyy-MM-dd"
     */
    public static String calculateDate(String dateStr, int offset) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将日期字符串解析为 LocalDate 对象
        LocalDate date = LocalDate.parse(dateStr, formatter);

        // 计算偏移后的日期
        LocalDate offsetDate = date.plusDays(offset);

        // 将结果格式化为字符串并返回
        return offsetDate.format(formatter);
    }

    /**
     * 格式化两个时间戳为日期字符串
     * 如果两个时间戳是同一天，返回格式为 "yyyy-MM-dd"
     * 如果不是同一天，返回格式为 "yyyy-MM-dd ~ yyyy-MM-dd"
     * @param timestamp1 第一个时间戳（毫秒）
     * @param timestamp2 第二个时间戳（毫秒）
     * @return 格式化后的日期字符串
     */
    public static String format(long timestamp1, long timestamp2) {
        return format(timestamp1, timestamp2, ZoneId.systemDefault());
    }

    /**
     * 格式化两个时间戳为日期字符串（带时区）
     * 如果两个时间戳是同一天，返回格式为 "yyyy-MM-dd"
     * 如果不是同一天，返回格式为 "yyyy-MM-dd ~ yyyy-MM-dd"
     * @param timestamp1 第一个时间戳（毫秒）
     * @param timestamp2 第二个时间戳（毫秒）
     * @param zoneId 时区
     * @return 格式化后的日期字符串
     */
    public static String format(long timestamp1, long timestamp2, ZoneId zoneId) {
        LocalDate date1 = Instant.ofEpochMilli(timestamp1).atZone(zoneId).toLocalDate();
        LocalDate date2 = Instant.ofEpochMilli(timestamp2).atZone(zoneId).toLocalDate();

        if (date1.isEqual(date2)) {
            return date1.format(DATE_DATETIME_FORMAT);
        } else {
            return date1.format(DATE_DATETIME_FORMAT) + " ~ " + date2.format(DATE_DATETIME_FORMAT);
        }
    }

    // 1. 将格式化器定义为静态常量，避免每次调用方法时都重新创建
    // 这样效率更高，也更安全。
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 将指定的毫秒时间戳转换为 "yyyyMMddHHmmss" 格式的字符串。
     * 使用系统默认时区进行转换。
     *
     * @param millis 从Java纪元（1970-01-01T00:00:00Z）开始的毫秒数
     * @return 格式化后的日期时间字符串
     */
    public static String formatMillisToString(long millis) {
        // 调用下面的重载方法，并传入系统默认时区
        return formatMillisToString(millis, ZoneId.systemDefault());
    }

    /**
     * 将指定的毫秒时间戳转换为 "yyyyMMddHHmmss" 格式的字符串。
     * 使用给定的时区进行转换。
     *
     * @param millis 从Java纪元（1970-01-01T00:00:00Z）开始的毫秒数
     * @param zoneId 指定的时区
     * @return 格式化后的日期时间字符串
     */
    public static String formatMillisToString(long millis, ZoneId zoneId) {
        Instant instant = Instant.ofEpochMilli(millis);
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        return zonedDateTime.format(FORMATTER);
    }

}
