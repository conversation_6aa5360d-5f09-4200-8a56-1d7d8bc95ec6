package com.iqiyi.vip.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.data.DimensionLayerNode;
import com.iqiyi.vip.dto.theme.ThemeTypeWhiteList;

/**
 * <AUTHOR>
 * @className CloudConfigUtils
 * @description
 * @date 2023/5/26
 **/
@Component
@Lazy(false)
public class CloudConfigUtils implements ApplicationContextAware, InitializingBean {

    private static ApplicationContext context;

    /**
     * 普通的云配置 config
     */
    private static CloudConfig cloudConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        cloudConfig = context.getBean("cloudConfig", CloudConfig.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public static boolean noNeedToConvertNumbers(String target) {
        String[] needConvert = cloudConfig.getArrayProperty("no.need.to.convert.numbers", ",", new String[]{"用户id,按年"});
        for (String s : needConvert) {
            if (s.equals(target)){
                return true;
            }
        }
        return false;
    }

    /**
     * 同步北斗上限5000万：当超过上限，“同步北斗”按钮置灰,因此不需要提前准备数据上传到oss
     * @return 最大处理上限
     */
    public static boolean userGroupLimit(Long count) {
        Long canUpload = cloudConfig.getLongProperty("user.group.count", 50000000L);
        return canUpload.compareTo(count) > 0;
    }

    /**
     * 转移分析的指标识别
     * @param targetCode 指标code
     * @return 是否需要处理
     */
    public static boolean transUserGroup(String targetCode) {
        String property = cloudConfig.getProperty("trans.target.user.group", null);
        return StringUtils.isNotBlank(property) && Splitter.on(",").trimResults().splitToList(property).contains(String.valueOf(targetCode));
    }

    /**
     * 用户分群的 用户包需要上传到oss，支持同步到北斗
     * @param targetCode 指标code
     * @return 是否需要返回
     */
    public static boolean isUserGroup(String targetCode) {
        String property = cloudConfig.getProperty("target.user.group", null);
        return StringUtils.isNotBlank(property) && Splitter.on(",").trimResults().splitToList(property).contains(String.valueOf(targetCode));
    }

    public static boolean hasTargetAccess(String targetCode, String operator) {
        String property = cloudConfig.getProperty("important.target.whiteList", null);
        Map<String, Object> targetAccessWhiteList = JacksonUtils.parseMap(property);
        if (MapUtils.isEmpty(targetAccessWhiteList) || !targetAccessWhiteList.containsKey(targetCode.trim())) {
            return true;
        }
        String operators = MapUtils.getString(targetAccessWhiteList, targetCode, (String) null);
        return Splitter.on(",")
            .splitToList(operators)
            .stream()
            .anyMatch(operator::equals);
    }

    public static boolean hasDimensionAccess(String dimensionCode, String operator) {
        String property = cloudConfig.getProperty("important.dimension.whiteList", null);
        Map<String, Object> dimensionAccessWhiteList = JacksonUtils.parseMap(property);
        if (MapUtils.isEmpty(dimensionAccessWhiteList) || !dimensionAccessWhiteList.containsKey(dimensionCode.trim())) {
            return true;
        }
        String operators = MapUtils.getString(dimensionAccessWhiteList, dimensionCode, (String) null);
        return Splitter.on(",")
            .splitToList(operators)
            .stream()
            .anyMatch(operator::equals);
    }

    public static boolean ownRemainBoardAccess(String operator) {
        String property = cloudConfig.getProperty("remain.board.whiteList", null);
        return StringUtils.contains(property, operator);
    }

    public static boolean hasConditionAccess(String conditionCode, String operator) {
        String property = cloudConfig.getProperty("important.condition.whiteList", null);
        Map<String, Object> dimensionAccessWhiteList = JacksonUtils.parseMap(property);
        if (MapUtils.isEmpty(dimensionAccessWhiteList) || !dimensionAccessWhiteList.containsKey(conditionCode.trim())) {
            return true;
        }
        String operators = MapUtils.getString(dimensionAccessWhiteList, conditionCode, (String) null);
        return Splitter.on(",")
            .splitToList(operators)
            .stream()
            .anyMatch(operator::equals);
    }

    public static DimensionLayerNode getDimensionLayerPair(String targetCode) {
        String prefix = "diagnosis.dimension.layers.";
        String key = StringUtils.isBlank(targetCode) ? prefix + "default" : prefix + targetCode;
        // 在这里，我们返回一个空的 List<B>。你可能需要根据实际需求进行更改
        String property = cloudConfig.getProperty(key, null);
        if (StringUtils.isBlank(property)) {
            return null;
        }
        return JacksonUtils.parseObject(property, new TypeReference<DimensionLayerNode>() {
        });
    }

    public static List<ThemeTypeWhiteList> getThemeTypeWhiteList() {
        String property = cloudConfig.getProperty("themeType.WhiteList", "{\"4\":\"chenguilong,guoyichen\"}");
        Map<String, Object> parseMap = JacksonUtils.parseMap(property);
        if (MapUtils.isEmpty(parseMap)) {
            return Collections.emptyList();
        }
        ArrayList<ThemeTypeWhiteList> result = new ArrayList<>();
        parseMap.entrySet().stream().forEach(entry -> {
            String key = entry.getKey();
            Integer themeType = Integer.parseInt(key);
            String value = MapUtils.getString(parseMap, key, "");
            List<String> operators = Arrays.asList(value.split(","));
            ThemeTypeWhiteList themeTypeWhiteList = new ThemeTypeWhiteList(themeType, operators);
            result.add(themeTypeWhiteList);
        });
        return result;
    }

    public static boolean enableAnalysisRemainByMonth() {
        return cloudConfig.getBooleanProperty("enable.analysis.remain.by.month", false);
    }
}