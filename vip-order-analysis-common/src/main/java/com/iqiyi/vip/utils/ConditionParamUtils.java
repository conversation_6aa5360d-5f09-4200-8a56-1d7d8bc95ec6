package com.iqiyi.vip.utils;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * @author: linpeihui
 * @createTime: 2023/09/13
 */
@Slf4j
public class ConditionParamUtils {

    public static final ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T getSingleValue(Map<String, List<Object>> paramMap, String conditionCode, Class<T> clazz) {
        List<Object> objectList = paramMap.get(conditionCode);
        if (CollectionUtils.isEmpty(objectList)) {
            return null;
        }
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        // 指定泛型类型参数为clazz
        JavaType javaType = typeFactory.constructParametricType(List.class, clazz);
        List<T> list = Lists.newArrayList();
        try {
            list = objectMapper.convertValue(objectList, javaType);
        } catch (Exception e) {
            log.error("getSingleValue error, conditionCode: {}, clazz:{}", conditionCode, clazz.getName());
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public static <T> List<T> getListValue(Map<String, List<Object>> paramMap, String conditionCode, Class<T> clazz) {
        List<Object> objectList = paramMap.get(conditionCode);
        if (CollectionUtils.isEmpty(objectList)) {
            return new ArrayList<>();
        }
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        // 指定泛型类型参数为clazz
        JavaType javaType = typeFactory.constructParametricType(List.class, clazz);
        try {
            return objectMapper.convertValue(objectList, javaType);
        } catch (Exception e) {
            log.error("getListValue error, conditionCode: {}, clazz:{}", conditionCode, clazz.getName(), e);
        }
        return Lists.newArrayList();
    }

    public static Long getPayStartTime(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "payStartTime", Long.class);
    }

    public static Long getPayEndTime(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "payEndTime", Long.class);
    }

    public static Long getCompareStartTime(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "compareStartTime", Long.class);
    }

    public static Long getCompareEndTime(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "compareEndTime", Long.class);
    }

    public static Integer getLtPeriod(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "ltPeriod", Integer.class);
    }

    public static Integer getMinusLtPeriod(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "minusLtPeriod", Integer.class);
    }

    public static List<Integer> getVipTypes(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "vipTypes", Integer.class);
    }

    public static List<Integer> getGroupIds(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "groupIds", Integer.class);
    }

    public static List<String> getLevel2BusinessIds(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "level2BusinessIds", String.class);
    }

    public static List<String> getLevel3BusinessIds(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "level3BusinessIds", String.class);
    }

    public static Integer getAnalysisType(Map<String, List<Object>> paramMap) {
        return getSingleValue(paramMap, "analysisType", Integer.class);
    }

    public static List<String> getBizNames(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "bizName", String.class);
    }

    public static List<String> getEndKeys(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "endKeys", String.class);
    }

    public static List<String> getTeamIds(Map<String, List<Object>> paramMap) {
        return getListValue(paramMap, "teamIds", String.class);
    }

}
