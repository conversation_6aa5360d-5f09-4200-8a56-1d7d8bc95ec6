package com.iqiyi.vip.utils;

import com.google.common.base.Joiner;

import java.util.Map;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2022/8/24 17:46
 */
public final class UrlAssembleUtils {

    public static String make(String baseUrl, Map<String, Object> params) {
        return new StringBuilder(baseUrl).append("?").append(Joiner.on("&").withKeyValueSeparator("=").join(params)).toString();
    }

    private UrlAssembleUtils() {
        throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
    }

}
