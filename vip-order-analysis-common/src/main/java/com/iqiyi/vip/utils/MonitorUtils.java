package com.iqiyi.vip.utils;

import java.util.Date;

/**
 * @author: guojing
 * @date: 2025/3/11 15:22
 */
public class MonitorUtils {

    public static String genRuleName(String themeTypeName, String targetCode, String dimensionPart, Date createTime) {
        String newDimensionPart = dimensionPart != null ? dimensionPart : "noDimensionPart";
        return String.format("tianyan-%s-%s-%s-%s", themeTypeName.toLowerCase(), targetCode, newDimensionPart, DateUtils.getYyyyMMddHHmmssStr(createTime));
    }

    public static String genMetricName(String themeTypeName, String targetCode, String dimensionPart) {
        String newDimensionPart = dimensionPart != null ? dimensionPart : "noDimensionPart";
        return String.format("tianyan:%s:%s:%s", themeTypeName.toLowerCase(), targetCode.replace("-", "_"), newDimensionPart.replace("-", "_"));
    }

}
