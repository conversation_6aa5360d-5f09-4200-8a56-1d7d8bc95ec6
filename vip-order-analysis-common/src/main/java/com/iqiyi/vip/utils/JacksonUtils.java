package com.iqiyi.vip.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created at: 2020-12-21
 *
 * <AUTHOR>
 */
@Slf4j
public class JacksonUtils {

    public static ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
        try {
            return objectMapper.readValue(jsonString, javaType);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    /**
     * json str解析为map
     * @param jsonString
     * @return
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>(){});
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> String toJsonString(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.error("writeValueAsString occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> byte[] toJsonBytes(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsBytes(object);
        } catch (IOException e) {
            log.error("writeValueAsBytes occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> T parseObjectFromBytes(byte[] bytes, Class<T> clazz) {
        if (bytes == null) {
            return null;
        }
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(bytes, clazz);
        } catch (IOException e) {
            log.error("readValue occurred exception, bytes length: {}", bytes.length, e);
            throw new RuntimeException("解析json字节数组出现异常", e);
        }
    }

    public static <T> Map<String, Object> beanToMap(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, Object>>(){});
    }

    public static <T> Map<String, String> beanToStringMap(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, String>>(){});
    }

    public static String obj2jsonWithOutException(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException var2) {
            var2.printStackTrace();
            return "";
        }
    }


    public static Map<String, List<Object>> getMapObject(String jsonString) throws JsonProcessingException {
        if (StringUtils.isEmpty(jsonString)) {
            return Maps.newHashMap();
        }
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> originalMap = mapper.readValue(jsonString, Map.class);
        Map<String, List<Object>> finalMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
            if (entry.getValue() instanceof List) {
                finalMap.put(entry.getKey(), (List) entry.getValue());
            } else {
                List<Object> singleValueList = new ArrayList<>();
                singleValueList.add(entry.getValue());
                finalMap.put(entry.getKey(), singleValueList);
            }
        }
        return finalMap;
    }

    public static <T> Map<String, T> getMapObjectByType(String jsonString, TypeReference<Map<String, T>> typeRef) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(jsonString, typeRef);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

}
