package com.iqiyi.vip.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @className DimensionLayerNode
 * @description
 * @date 2023/10/26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class DimensionLayerNode {

    private String desc;

    private String dimensionCode;

    private String conditionKey;

    private Integer level;

    private String groupCode;

    private List<DimensionLayerNode> children;

    public DimensionLayerNode findNodeWithChildDimensionCode(String targetDimensionCode) {
        if (this.children != null && !this.children.isEmpty()) {
            for (DimensionLayerNode child : this.children) {
                if (targetDimensionCode.equals(child.getDimensionCode())) {
                    return this;    //返回的是当前节点
                }
                DimensionLayerNode result = child.findNodeWithChildDimensionCode(targetDimensionCode);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

}
