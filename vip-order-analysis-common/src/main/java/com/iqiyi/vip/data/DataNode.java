package com.iqiyi.vip.data;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据存储
 * <AUTHOR>
 * @date 2022/8/17 14:14
 */
@Getter
@ToString(exclude = {"subDataNodeList", "parent"})
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class DataNode implements java.io.Serializable {

    /**
     * ID
     */
    protected String id;
    /**
     * 名称
     */
    protected String name;
    /**
     * 父节点
     */
    protected DataNode parent;
    /**
     * 子集合
     */
    protected List<DataNode> subDataNodeList;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        DataNode node = (DataNode) o;
        return Objects.equals(id, node.id) && Objects.equals(name, node.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }

    /**
     * 添加DataNode
     * @param node
     */
    public synchronized DataNode addDataNode(DataNode node) {
        if (subDataNodeList == null) {
            subDataNodeList = new ArrayList<>();
        }
        subDataNodeList.add(node);
        return node;
    }

    public synchronized boolean remove(DataNode node) {
        if (subDataNodeList == null) return false;
        final List<DataNode> collect = subDataNodeList.stream().filter(dataNode -> dataNode.getId() != node.getId()).collect(Collectors.toList());
        boolean success = subDataNodeList.size() > collect.size();
        subDataNodeList = collect;
        return success;
    }

    public boolean contains(DataNode node) {
        return subDataNodeList == null ? false : subDataNodeList.contains(node);
    }

}
