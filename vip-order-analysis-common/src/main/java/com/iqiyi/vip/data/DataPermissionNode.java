package com.iqiyi.vip.data;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Objects;

import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.enums.LabelEnum;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/8/17 14:32
 */
@Getter
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class DataPermissionNode extends DataNode {

    /**
     * 渠道标签，1级渠道、2级渠道。。。。。。
     */
    private LabelEnum label;

    /**
     * 次级渠道id集合
     */
    private StringBuilder subIds;

    /**
     * 团队ID
     */
    private String teamId;
    /**
     * 团队名称
     */
    private String teamName;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DataPermissionNode)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        DataPermissionNode that = (DataPermissionNode) o;
        return label == that.label && Objects.equals(teamId, that.teamId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), label, subIds);
    }

    @Override
    public synchronized DataNode addDataNode(DataNode node) {
        if (subIds == null) {
            subIds = new StringBuilder("");
        }
        subIds.append(node.getId()).append(Constants.LAYERED_DATA_PERMISSION_DATA_SEPARATOR);
        return super.addDataNode(node);
    }

    @Override
    public synchronized boolean remove(DataNode node) {
        final boolean remove = super.remove(node);
        if (remove) {
            subIds = new StringBuilder(subIds.toString().replace(node.getId() + Constants.LAYERED_DATA_PERMISSION_DATA_SEPARATOR, ""));
        }
        return remove;
    }

    /**
     * 获取渠道层级
     * @return
     */
    public int getCurLabelLevel() {
        return this.label.getLevel();
    }

    public int getNextLevel() {
        if (this.getLabel() == LabelEnum.L2) {
            return LabelEnum.T.getLevel();
        }
        return this.getLabel() != LabelEnum.T ? this.getCurLabelLevel() + 1 : ((DataPermissionNode) this.getParent()).getCurLabelLevel() + 1;
    }

    public int getPreLevel() {
        if (this.getLabel() == LabelEnum.T) {
            return LabelEnum.L2.getLevel();
        }
        if (this.getLabel() == LabelEnum.L3) {
            return LabelEnum.T.getLevel();
        }
        return this.getCurLabelLevel() - 1;
    }

    /**
     * 获取当前节点的Level
     * @return
     */
    public int getInitDataPermissionCurNodeLevel() {
        return this.getLabel() == LabelEnum.T ? ((DataPermissionNode) this.getParent()).getCurLabelLevel() : this.getCurLabelLevel();
    }
    public String getInitDataPermissionCurNodeId() {
        return this.getLabel() == LabelEnum.T ? this.getParent().getId() : this.getId();
    }
    public int getInitDataPermissionNextNodeLevel() {
        return this.getNextLevel();
    }

    public DataPermissionNode copyDataPermissionNodeSimple() {
        return DataPermissionNode.builder()
                .name(this.getName())
                .id(this.getId())
                .label(this.label)
                .teamId(this.teamId)
                .teamName(this.teamName)
                .parent(parent)
                .build();
    }
}
