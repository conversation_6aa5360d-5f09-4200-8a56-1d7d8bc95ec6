package com.iqiyi.vip.exception;

import com.iqiyi.vip.enums.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021-05-25
 */
public class BizRuntimeException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final CodeEnum codeEnum;

    public CodeEnum getCodeEnum() {
        return codeEnum;
    }

    public BizRuntimeException(CodeEnum codeEnum) {
        super(String.format("%s[%s]", codeEnum.getCode(), codeEnum.getMessage()));
        this.codeEnum = codeEnum;
    }

    public BizRuntimeException(String code, String message) {
        super(String.format("%s[%s]", code, message));
        this.codeEnum = CodeEnum.ERROR_PARAM;
    }

    public static BizRuntimeException newParamException(String message) {
        return new BizRuntimeException(CodeEnum.ERROR_PARAM.getCode(), message);
    }

}
