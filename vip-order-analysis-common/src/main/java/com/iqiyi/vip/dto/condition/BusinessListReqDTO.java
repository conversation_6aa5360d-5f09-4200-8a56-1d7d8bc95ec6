package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @className BusinessListReqDTO
 * @description
 * @date 2022/8/25
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "获取产品列表请求参数")
public class BusinessListReqDTO extends BaseQry {

    @NotBlank(message = "operator为空")
    @ApiModelProperty(value = "用户名称，邮箱前缀")
    private String operator;
}
