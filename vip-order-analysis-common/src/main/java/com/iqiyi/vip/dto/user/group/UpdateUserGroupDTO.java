package com.iqiyi.vip.dto.user.group;

import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * <AUTHOR>
 * 用户分群
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateUserGroupDTO extends BaseQry implements Serializable {

    @ApiParam(name = "id", value = "人群包id")
    @NotNull(message = "id为空")
    private Integer id;
    /**
     * 分群名称
     */
    @NotNull(message = "分群名称不能为空")
    private String name;

    @ApiParam(name = "operator", value = "操作人")
    @NotBlank(message = "operator为空")
    private String operator;
}