package com.iqiyi.vip.dto.remain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.iqiyi.vip.dto.base.PageQry;

/**
 * <AUTHOR>
 * @className AlbumMetaDataPageQueryDTO
 * @description
 * @date 2024/9/27
 **/

@Data
@ApiModel(value = "条件分页查询请求")
public class AlbumMetaDataPageQueryDTO extends PageQry {

    @ApiModelProperty(value = "根据奇谱id、内容名称、频道名称查询")
    private String query;

    @ApiModelProperty(value = "播后定级")
    private String resourceRating;

    @ApiModelProperty(value = "关联留存展示")
    private Integer remainRelated;

    @ApiModelProperty(value = "上线时间开始日期")
    private String firstOnlineTimeStart;

    @ApiModelProperty(value = "上线时间结束日期")
    private String firstOnlineTimeEnd;

    @ApiModelProperty(value = "会员完结开始日期")
    private String vipEndTimeStart;

    @ApiModelProperty(value = "会员完结结束日期")
    private String vipEndTimeEnd;

    @ApiModelProperty(value = "非会员完结开始时间")
    private String commonEndTimeStart;

    @ApiModelProperty(value = "非会员完结结束时间")
    private String commonEndTimeEnd;

    @ApiModelProperty(value = "排序字段", hidden = true)
    private String orderBy;

    @ApiModelProperty(value = "数据起始值", hidden = true)
    private Integer start;

}
