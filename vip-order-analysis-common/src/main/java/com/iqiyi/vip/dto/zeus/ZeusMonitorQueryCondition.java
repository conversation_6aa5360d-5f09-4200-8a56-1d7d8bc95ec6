package com.iqiyi.vip.dto.zeus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/1/18 21:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZeusMonitorQueryCondition {

    /**
     * 条件Key
     */
    private String key;
    /**
     * 条件操作符
     */
    private String operator;
    /**
     * 条件值
     */
    private List<String> values;
    /**
     * 条件值类型，默认为字符串，取值范围：number、string、datetime
     */
    private String valueType;

    public ZeusMonitorQueryCondition(String key, String operator, List<String> values) {
        this.key = key;
        this.operator = operator;
        this.values = values;
    }

}
