package com.iqiyi.vip.dto.diagnosis;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DiagnosisConditionPairDTO
 * @description
 * @date 2023/11/6
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DiagnosisConditionPairDTO {

    private List<DiagnosisConditionDTO> diagnosisConditions;

    private Map<String, List<Object>> diagnosisPathConditionParamMap;
}
