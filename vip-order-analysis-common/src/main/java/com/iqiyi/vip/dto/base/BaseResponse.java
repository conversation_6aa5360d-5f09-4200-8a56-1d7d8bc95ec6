package com.iqiyi.vip.dto.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@NoArgsConstructor
@Setter
@Getter
@ApiModel(value = "基本返回信息")
public class BaseResponse<T> {

    private static ObjectMapper mapper = new ObjectMapper();

    @ApiModelProperty(value = "返回码")
    private String code;
    @ApiModelProperty(value = "返回描述")
    private String msg;

    @ApiModelProperty(value = "返回描述")
    private String message;

    @ApiModelProperty(value = "返回数据")
    private T data;
    private T dataList;

    public BaseResponse setData(T data) {
        this.data = data;
        return this;
    }

    public T getData() {
        if (data != null) {
            return data;
        }
        return dataList;
    }

    public BaseResponse(CodeEnum responseCode) {
        this.code = responseCode.getCode();
        this.msg = responseCode.getMsg();
        this.message = responseCode.getMsg();
    }

    public BaseResponse(CodeEnum responseCode, String msg) {
        this(responseCode);
        this.msg = msg;
        this.message = msg;
    }

    public BaseResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.message = msg;
    }

    public static <T> BaseResponse<T> create(CodeEnum responseCode) {
        return new BaseResponse<>(responseCode);
    }

    public static <T> BaseResponse<T> create(String code, String msg) {
        return new BaseResponse<>(code, msg);
    }

    public static <T> BaseResponse<T> createSuccess(T data) {
        BaseResponse<T> response = new BaseResponse<>(CodeEnum.SUCCESS);
        response.setData(data);
        return response;
    }

    public static BaseResponse success() {
        return new BaseResponse(CodeEnum.SUCCESS);
    }

    public boolean isFailure() {
        return !CodeEnum.isSuccess(code);
    }

    public boolean isSuccess() {
        return CodeEnum.isSuccess(code);
    }

    public static BaseResponse notLoggedIn() {
        return new BaseResponse(CodeEnum.ERROR_LOGIN);
    }

    public static BaseResponse notAutoRenewUser() {
        return new BaseResponse(CodeEnum.ERROR_USER_NOT_AUTORENEW);
    }

    public static BaseResponse paramError() {
        return new BaseResponse(CodeEnum.ERROR_PARAM);
    }

    public static BaseResponse systemError() {
        return new BaseResponse(CodeEnum.ERROR_SYSTEM);
    }

    public enum CodeEnum {

        SUCCESS("A00000", "处理成功"),
        ERROR_SYSTEM("Q00332", "系统错误"),
        ERROR_PARAM("Q00301", "参数错误"),
        INVALID_SIGN("Q00302", "签名错误"),
        ERROR_LOGIN("Q00304", "用户未登陆"),
        ERROR_USER_NOT_AUTORENEW("Q00305","用户未开通自动续费"),
        ACCESS_DENY("Q00347", "无权访问"),
        ERROR_NO_BIND("Q00362", "未绑定,不能操作自动续费"),
        ERROR_RESTRICT_CANCELLATION("Q00364", "限制取消自动续费"),
        FAILED_QUERY_USER_DUT_DISCOUNTS("Q00365", "查询用户立减优惠信息失败"),
        OUTER_SERVICE_ERROR("Q00366", "请求外部服务异常"),


//        AGREEMENT_OPEN_SET_LOG_NOT_EXIST("Q00410", "未查到协议开通日志"),
//        AGREEMENT_ALREADY_SIGNED("Q00411", "用户已经签约此协议"),
//        AGREEMENT_SIGN_OCCURRED_EXCEPTION("Q00412", "签约协议时出现异常"),
//        AGREEMENT_SETTLE_IN_PROGRESS_EXCEPTION("Q00413", "协议正在结算中"),
//        AGREEMENT_SETTLE_EXCEPTION("Q00414", "协议结算时出现异常"),
//        AGREEMENT_CANCEL_OCCURRED_EXCEPTION("Q00415", "取消协议出现异常"),
        ACCOUNT_UNBIND_EXCEPTION("Q00416", "账户中心解绑超时"),
        DUT_BIND_EXCEPTION("Q00417", "账户中心签约出现异常"),
//        AGREEMENT_CANCEL_PENDING_EXCEPTION("Q00418", "协议置为待解约时出现异常"),
        REFUND_EXCEPTION("Q00419", "退款时出现异常"),
        CLOSE_ORDER_EXCEPTION("Q00420", "调用支付中心取消订单时出现异常"),
//        AGREEMENT_RENEW_LOG_NOT_EXIST("Q00421", "未查到协议代扣日志"),
//        AGREEMENT_DUT_RENEW_LOG_NOT_EXIST("Q00422", "未查到协议代扣日志")
        WECHAT_PAY_SCORE_ORDER_UN_COMPLETE("Q00423", "您有未支付订单，请支付完成后使用该服务"),
        ;

        private String code;

        private String msg;

        CodeEnum(String code, String desc) {
            this.code = code;
            this.msg = desc;
        }

        public String getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }


        public static boolean isSuccess(String code) {
            return SUCCESS.getCode().equals(code);
        }
    }
}
