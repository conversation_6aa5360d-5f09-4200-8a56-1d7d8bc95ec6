package com.iqiyi.vip.dto.task;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className AnalysisTaskDeleteDTO
 * @description
 * @date 2022/6/2
 **/
@Data
public class AnalysisTaskDeleteDTO {

    @ApiParam(name = "taskId", value = "待删除任务id")
    @NotNull(message = "taskId为空")
    private Long taskId;

    @ApiParam(name = "operator", value = "操作人")
    @NotBlank(message = "operator为空")
    private String operator;
}
