package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @className FvChannelPair
 * @description
 * @date 2022/6/13
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "渠道层级信息")
public class FvChannelPair {

    @ApiModelProperty(value = "当前层级，二级渠道为2，业务团队为3，以此类推")
    private Integer level;

    @ApiModelProperty(value = "对应的id, 当level=3时，表示业务团队id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "团队id")
    private String teamId;

    @ApiModelProperty(value = "团队名称")
    private String teamName;
}
