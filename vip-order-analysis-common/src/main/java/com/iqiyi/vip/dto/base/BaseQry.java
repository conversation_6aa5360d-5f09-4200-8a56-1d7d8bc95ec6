package com.iqiyi.vip.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/2/23 14:10
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "基础VO")
public class BaseQry implements java.io.Serializable {

    @ApiModelProperty(name = "businessTypeId", value = "天眼业务类型 - 区分不同业务对应不同指标、维度集合的类型。1 基础分析 2 转移分析 联系：<EMAIL>; <EMAIL>")
    private Integer businessTypeId;

    @ApiModelProperty(name = "dataPermissionType", value = "天眼权限类型 - 1 渠道、产品端平台权限、2 会员类型权限；3 竞品监控权限。联系：<EMAIL>; <EMAIL>")
    private Integer dataPermissionType;

    @ApiModelProperty(name = "themeType", value = "天眼主题类型 - 1 订单分析主题、2 自动续费分析主题、3 收银台。联系：<EMAIL>; <EMAIL>")
    private Integer themeType;

    @ApiModelProperty(name = "subThemeType", value = "天眼主题子类型, 与themeType搭配使用. themeType=4&subThemeType=1: 流失分析, themeType=4&subThemeType=2: 流失转化分析")
    private Integer subThemeType;

    @ApiModelProperty(value = "操作者", required = true)
    private String operator;

}
