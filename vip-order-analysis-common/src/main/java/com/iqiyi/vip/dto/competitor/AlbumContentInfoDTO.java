package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 专辑内容信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "专辑内容信息")
public class AlbumContentInfoDTO {
    
    @ApiModelProperty(value = "内容ID")
    private Long contentId;
    
    @ApiModelProperty(value = "内容名称")
    private String contentName;
    
    @ApiModelProperty(value = "等级， S或A+")
    private String level;
    
    @ApiModelProperty(value = "频道名称")
    private String channelName;

    @ApiModelProperty(value = "频道名称")
    private String tags;
    
    @ApiModelProperty(value = "上线时间")
    private String onlineTime;
    
    @ApiModelProperty(value = "完结时间")
    private String finishTime;
    
    @ApiModelProperty(value = "状态，完或更新到多少集")
    private String status;
    
    @ApiModelProperty(value = "播放平台，多个用逗号隔开")
    private String platforms;
    
    @ApiModelProperty(value = "是否独播, 联或独")
    private String exclusiveType;
} 