package com.iqiyi.vip.dto.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/9/14 19:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConditionPayEndTimeDTO implements java.io.Serializable {

    private long payEndTime;

    public static ConditionPayEndTimeDTO of(long payStartTime) {
        return ConditionPayEndTimeDTO.builder()
            .payEndTime(payStartTime)
            .build();
    }

}
