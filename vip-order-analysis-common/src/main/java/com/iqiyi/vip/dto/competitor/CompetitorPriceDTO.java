package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2024/10/29 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品价格数据")
public class CompetitorPriceDTO {

    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "用户状态(生命周期)")
    private String userStatus;
    @ApiModelProperty(value = "会员类型")
    private String vipType;
    @ApiModelProperty(value = "商品")
    private String product;
    @ApiModelProperty(value = "价格")
    private String price;

}
