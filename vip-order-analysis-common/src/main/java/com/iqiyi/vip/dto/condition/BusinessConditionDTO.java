package com.iqiyi.vip.dto.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分析类型下的条件管理实体dto
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @createTime: 2023/08/11
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessConditionDTO {

    private Long id;

    private Long themeId;

    private Long businessId;

    private Long conditionId;

    private Integer sort;

    private String operator;

    private String conditionCode;

    private String conditionName;

    private Integer conditionType;

    private Boolean conditionRequired;

    private String conditionDefaultPrompt;

    private String conditionDataLoadAddr;

    private Boolean conditionSelectAll;

    private Boolean conditionHasTipsIcon;

    private String conditionTips;

    private Integer conditionMultiChoice;

    private Date createTime;

    private Date updateTime;

    private Integer fieldType;

    private String defaultValue;
}
