package com.iqiyi.vip.dto.dimension;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 维度管理新增修改
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
public class DimensionUpdateDTO extends BaseQry {

    @ApiParam(name = "id", value = "维度id 修改时必填，新增时为空")
    private Long id;

    @ApiParam(name="code", value = "维度code", required = true)
    @NotBlank
    private String code;

    @ApiParam(name="name", value = "维度名称", required = true)
    @NotBlank
    private String name;

    @ApiParam(name="value", value = "维度值", required = true)
    @NotBlank
    private String value;

    @ApiParam(name = "group", value = "维度分组", required = true)
    @NotNull
    private Integer group;

    @ApiParam(name = "order", value = "维度排序", required = true)
    private Integer order;

    @ApiParam(name = "operator", value = "操作人")
    private String operator;

    @ApiModelProperty(value = "更改记录")
    private String commitNote;

    @ApiParam(value = "说明文档", required = true)
    private String documentation;

    @ApiParam(value = "业务类型ID")
    private Integer businessTypeId;


}
