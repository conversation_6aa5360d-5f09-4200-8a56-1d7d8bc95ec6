package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 带品牌信息的产品DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "带品牌信息的产品DTO")
public class ProductWithBrandDTO {
    
    @ApiModelProperty(value = "会员类型")
    private String vipType;
    
    @ApiModelProperty(value = "商品")
    private String product;
    
    @ApiModelProperty(value = "价格")
    private String price;
    
    @ApiModelProperty(value = "营销信息")
    private String priceText;
    
    @ApiModelProperty(value = "人群")
    private String userGroup;
    
    @ApiModelProperty(value = "品牌")
    private String brand;
} 