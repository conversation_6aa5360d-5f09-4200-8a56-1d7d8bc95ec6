package com.iqiyi.vip.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @className DiagnosisConditionDTO
 * @description
 * @date 2023/10/27
 **/
@Data
@ApiModel(value = "诊断分析维度信息")
@AllArgsConstructor
@NoArgsConstructor
public class DiagnosisConditionDTO {

    @ApiModelProperty(value = "条件名称，如下钻二级渠道，此时条件key为level2_business_name")
    private String conditionKey;

    @ApiModelProperty(value = "条件名称，如下钻二级渠道，此时条件key为level2_business_name对应的取值")
    private Object conditionValue;

    @ApiModelProperty(value = "下钻维度，如下钻二级渠道")
    private String dimensionCode;

    @ApiModelProperty(value = "下钻维度分组，如下钻二级渠道，此时维度分组为fv")
    private String groupCode;
}
