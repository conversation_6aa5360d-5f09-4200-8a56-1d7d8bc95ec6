package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className VersionDiffReqDTO
 * @description
 * @date 2022/11/25
 **/
@Data
@ApiModel(value = "版本对比")
public class VersionDiffReqDTO {

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3", required = true)
    @NotNull
    private Integer configType;

    @ApiModelProperty(value = "模板id", required = true)
    @NotNull
    private Long configId;

    @ApiModelProperty(value = "当前版本", required = true)
    @NotNull
    private Integer currentVersion;

    @ApiModelProperty(value = "对比的版本，若为null默认为上一版本")
    private Integer diffVersion;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank
    private String operator;
}
