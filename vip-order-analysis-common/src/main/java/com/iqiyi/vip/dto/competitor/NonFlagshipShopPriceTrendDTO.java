package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 非旗舰店价格趋势DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "非旗舰店价格趋势DTO")
public class NonFlagshipShopPriceTrendDTO {

    @ApiModelProperty(value = "日期")
    private String date;
    
    @ApiModelProperty(value = "卡种价格信息列表")
    private List<CardTypePriceInfo> cardTypeList;
    
    /**
     * 卡种价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "卡种价格信息")
    public static class CardTypePriceInfo {
        
        @ApiModelProperty(value = "卡种全名（会员类型+卡种）")
        private String fullCardType;
        
        @ApiModelProperty(value = "最低价")
        private String minPrice;
        
        @ApiModelProperty(value = "平均价")
        private String avgPrice;
        
        @ApiModelProperty(value = "品牌")
        private String brand;
    }
} 