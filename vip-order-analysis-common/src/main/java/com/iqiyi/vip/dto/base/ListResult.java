package com.iqiyi.vip.dto.base;

import com.iqiyi.vip.enums.CodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@ApiModel(value = "返回集合结果")
@Data
public class ListResult<T> extends CommonResult {

    @ApiModelProperty(value = "返回集合")
    private List<T> dataList;

    public ListResult() {
    }

    public ListResult(List<T> dataList) {
        this.dataList = dataList;
    }

    public ListResult(String code, String msg) {
        super(code, msg);
    }

    public List<T> getDataList() {
        return this.dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    public static <T> ListResult<T> createSuccess(List<T> list) {
        ListResult result = new ListResult();
        result.setSuccess(true);
        result.setCode(CommonResult.SUCCESS_CODE);
        result.setMsg("成功");
        result.setMessage("成功");
        result.setDataList(list);
        return result;
    }

    public static <T> ListResult<T> createFailed(CodeEnum codeEnum) {
        ListResult result = new ListResult();
        result.setSuccess(false);
        result.setCode(codeEnum.getCode());
        result.setMsg(codeEnum.getMessage());
        result.setMessage(codeEnum.getMessage());
        result.setDataList(null);
        return result;
    }
}
