package com.iqiyi.vip.dto.realmonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import java.util.List;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2025/3/1 18:38
 */
@Data
@ApiModel(value = "业务指标监控配置-创建、更新参数类")
public class BizTargetMonitorConfigSaveDTO extends BaseQry {

    /**
     * 主键di
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 条件
     */
    @NotEmpty(message = "条件不能为空")
    @ApiModelProperty(value = "条件")
    private List<BizTargetMonitorConfigCondition> conditions;
    /**
     * 业务指标
     */
    @NotBlank(message = "业务指标不能为空")
    @ApiModelProperty(value = "业务指标")
    private String target;
    /**
     * 维度
     */
    @NotEmpty(message = "维度不能为空")
    @ApiModelProperty(value = "维度")
    private List<String> dimensions;
    /**
     * 执行频率
     */
    @NotBlank(message = "执行频率不能为空")
    @ApiModelProperty(value = "执行频率，格式：\"(number)(unit)\", 比如：\"1m\". 合法的单位: m, h, d")
    private String execFrequency;
    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    @ApiModelProperty(value = "创建人")
    private String createOpr;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "更新人")
    private String updateOpr;

}
