package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @className EndKeyListReqDTO
 * @description
 * @date 2022/8/25
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EndKeyListReqDTO extends BaseQry {

    @NotNull(message = "产品不能为空")
    private List<String> businessName;

    @NotBlank(message = "operator为空")
    @ApiModelProperty(value = "用户名称，邮箱前缀")
    private String operator;
}
