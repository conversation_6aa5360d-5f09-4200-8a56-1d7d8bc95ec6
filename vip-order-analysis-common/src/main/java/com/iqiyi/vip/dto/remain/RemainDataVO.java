package com.iqiyi.vip.dto.remain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR>
 * @className RemainDataVO
 * @description
 * @date 2024/6/29
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RemainDataVO {

    @Schema(description = "指标名称")
    private String name;

    @Schema(description = "指标数据")
    private List<RemainData> data;
}
