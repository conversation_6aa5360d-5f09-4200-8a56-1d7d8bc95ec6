package com.iqiyi.vip.dto.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.BeanUtils;

import java.util.List;

import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.condition.ConditionParamContext;
import com.iqiyi.vip.dto.diagnosis.DiagnosisConditionDTO;
import com.iqiyi.vip.dto.target.BaseQueryDTO;
import com.iqiyi.vip.dto.target.DiagnosisQueryDTO;
import com.iqiyi.vip.enums.AnalysisTaskSourceEnum;

/**
 * <AUTHOR>
 * @className AnalysisTaskExecuteDTO
 * @description
 * @date 2022/6/6
 **/
@Data
@ToString(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisTaskExecuteDTO extends BaseQry {

    private ConditionParamContext conditionParamContext;

    private List<String> dimensionCodes;

    private List<DiagnosisConditionDTO> diagnosisConditions;

    private List<String> targetCodes;

    private String resultEmailAddress;

    private String taskName;

    private String operator;

    private String taskMD5;

    private Long taskId;

    private boolean useCK;

    private AnalysisTaskSourceEnum taskSource;

    public static AnalysisTaskExecuteDTO buildFrom(DiagnosisQueryDTO queryDTO) {
        AnalysisTaskExecuteDTO executeDTO = new AnalysisTaskExecuteDTO();
        BeanUtils.copyProperties(queryDTO, executeDTO);
        executeDTO.setTaskMD5(queryDTO.getUniqueId());
        return executeDTO;
    }

    public static AnalysisTaskExecuteDTO initializeTaskExecuteDO(BaseQueryDTO queryDTO, String taskMD5, Long taskId, AnalysisTaskSourceEnum taskSourceEnum) {
        return AnalysisTaskExecuteDTO.builder()
            .taskId(taskId)
            .taskName(queryDTO.getTaskName())
            .conditionParamContext(queryDTO.getConditionParamContext())
            .dimensionCodes(queryDTO.getDimensionCodes())
            .targetCodes(queryDTO.getTargetCodes())
            .operator(queryDTO.getOperator())
            .taskMD5(taskMD5)
            .businessTypeId(queryDTO.getBusinessTypeId())
            .dataPermissionType(queryDTO.getDataPermissionType())
            .themeType(queryDTO.getThemeType())
            .taskSource(taskSourceEnum)
            .build();
    }

}
