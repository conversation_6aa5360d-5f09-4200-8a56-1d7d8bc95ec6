package com.iqiyi.vip.dto.remainV2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LossAndBackAnalysisResData {
    @Schema(description = "日期")
    private String dt;

    @Schema(description = "1:流失, 2:回归")
    private Integer isLoss;

    @Schema(description = "数量和维度子项的描述信息")
    private List<DescAndNumPair> descAndNumPairs;
}
