package com.iqiyi.vip.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @className DiagnosisResultDTO
 * @description
 * @date 2023/10/27
 **/
@Data
@ApiModel(value = "诊断分析结果")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DiagnosisResultDTO {

    @ApiModelProperty(value = "唯一标识")
    private String uniqueId;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "列名及其对应属性map，key是列名，value是引用DiagnosisResult对应属性名")
    private LinkedHashMap<String, String> columnNames;

    @ApiModelProperty(value = "分析结果")
    private LinkedHashMap<String, List<DiagnosisResult>> diagnosisResults;

    @ApiModelProperty(value = "excel文件名")
    private String result;
}
