package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * @author: guojing
 * @date: 2024/11/20 15:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品价格监控商品和价格信息")
public class CompetitorPriceProduct {

    @ApiModelProperty(value = "商品", required = true)
    @NotBlank(message = "商品不能为空")
    private String product;
    @ApiModelProperty(value = "价格", required = true)
    @NotBlank(message = "价格不能为空")
    private String price;

}
