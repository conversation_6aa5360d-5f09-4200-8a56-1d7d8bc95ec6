package com.iqiyi.vip.dto.zeus;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2023/12/2 17:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZeusMonitorQuery {

    /**
     * 查询条件id
     */
    private Integer id;
    /**
     * 指标模版id
     */
    private Integer metricTmpId;
    /**
     * 查询来源
     * type为prometheus时，取值为metric
     * type为mysql时，取值为表名
     */
    private String source;
    /**
     * 查询条件
     */
    private List<ZeusMonitorQueryCondition> conditions;
    /**
     * 时间范围字段, 数据源为MySQL时，不能为空
     */
    private String timeFilter;
    /**
     * 展示维度
     */
    private String groupBy;
    /**
     * 展示说明, 数据源为Prometheus时，不能为空
     */
    private String displayName;
    /**
     * 是否需要时间范围字段
     */
    @JsonIgnore
    private boolean needTimeFilter;
    /**
     * 是否支持分组
     */
    @JsonIgnore
    private boolean needGroupBy;
    /**
     * 是否需要展示说明
     */
    @JsonIgnore
    private boolean needDisplayName;

}
