package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/12/25 10:00
 */
@Data
@ApiModel(value = "竞品店铺监控数据保存参数类")
public class CompetitorShopSaveDTO {

    @ApiModelProperty(value = "平台", required = true, example = "拼多多")
    @NotBlank(message = "平台不能为空")
    private String platform;

    @ApiModelProperty(value = "品牌", required = true, example = "腾讯")
    @NotBlank(message = "品牌不能为空")
    private String brand;

    @ApiModelProperty(value = "日期，格式: yyyy-MM-dd", required = true, example = "2025-05-08")
    @NotBlank(message = "日期不能为空")
    private String date;

    @ApiModelProperty(value = "店铺类型，1-旗舰店，0-非旗舰店", required = true, example = "1")
    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;

    @ApiModelProperty(value = "截图URL列表", required = true)
    @NotEmpty(message = "截图URL列表不能为空")
    private List<String> screenshotUrls;

    @ApiModelProperty(value = "交互录屏URL")
    private String interactionVideoUrl;
} 