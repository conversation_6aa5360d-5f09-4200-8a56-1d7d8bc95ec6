package com.iqiyi.vip.dto.sqltemplate;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "sql模板")
public class SqlTemplateUpdateDTO extends BaseQry {

    @ApiModelProperty(value = "模板id 修改时必填，新增时为空")
    private Long id;

    @ApiModelProperty(value = "模板名称", required = true)
    @NotBlank
    private String name;

    @ApiModelProperty(value = "模板sql 需要前端特殊字符转码", required = true)
    @NotBlank
    private String value;

    @ApiModelProperty(value = "描述")
    private String description;


    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "更改记录")
    private String commitNote;
}
