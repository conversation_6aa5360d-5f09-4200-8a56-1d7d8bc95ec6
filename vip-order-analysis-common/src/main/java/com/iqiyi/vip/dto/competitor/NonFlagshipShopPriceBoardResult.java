package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "非旗舰店看板结果")
public class NonFlagshipShopPriceBoardResult {

    @ApiModelProperty(value = "非旗舰店价格趋势")
    private List<NonFlagshipShopPriceTrendDTO> nonFlagshipShopPriceTrend;

    @ApiModelProperty(value = "专辑内容信息列表")
    private List<AlbumContentInfoDTO> albumContents;
}
