package com.iqiyi.vip.dto.base;

import com.iqiyi.vip.enums.CodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@ApiModel(value = "返回数据结果")
public class DataResult<T> extends CommonResult {

    @ApiModelProperty(value = "返回数据体")
    private T data;

    public DataResult() {
    }

    public DataResult(T data) {
        this.data = data;
    }

    public DataResult(String code, String msg) {
        super(code, msg);
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> DataResult<T> success(T data) {
        DataResult result = new DataResult();
        result.setSuccess(true);
        result.setCode(CommonResult.SUCCESS_CODE);
        result.setMsg("成功");
        result.setMessage("成功");
        result.setData(data);
        return result;
    }

    public static <T> DataResult<T> create(CodeEnum codeEnum, T data) {
        DataResult result = new DataResult(codeEnum.getCode(), codeEnum.getMessage());
        result.setData(data);
        return result;
    }
}
