package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2024/10/29 15:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品页面监控数据查询参数类")
public class CompetitorPriceSearchParam extends BaseQry {

    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;

    @ApiModelProperty(value = "日期", required = true)
    @NotBlank(message = "日期不能为空")
    private String date;

    @ApiModelProperty(value = "用户状态(生命周期)", required = true)
    @NotBlank(message = "生命周期不能为空")
    private String userStatus;
    
    @ApiModelProperty(value = "开始时间戳")
    private Long startTime;
    
    @ApiModelProperty(value = "结束时间戳")
    private Long endTime;

    @ApiModelProperty(value = "是否需要普卡")
    private boolean needNormalCard;
    
}
