package com.iqiyi.vip.dto.permission;

import com.google.common.collect.Lists;
import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.enums.LabelEnum;
import com.iqiyi.vip.utils.DataPermissionNodeTrans;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @className BaseQueryDTO
 * @description
 * <AUTHOR>
 * @date 2022/5/12
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "权限申请对象", description = "")
public class DataPermissionDTO extends BaseQry implements java.io.Serializable {

    @ApiModelProperty(value = "层级")
    private Integer level;
    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "数据权限名称")
    private String permissionName;
    @ApiModelProperty(value = "父节点ID")
    private String parentId;
    @ApiModelProperty(value = "父节点名称")
    private String parentName;
    @ApiModelProperty(value = "站内渠道, 只适用于二级渠道")
    private Boolean isMainStation;
    @ApiModelProperty(value = "已拥有权限")
    private Boolean alreadyAuthorized;
    @ApiModelProperty(value = "选中状态，1或者null 未选中，2 部分选中，4 全选中")
    private Integer checked;
    @ApiModelProperty(value = "次级集合")
    private List<DataPermissionDTO> dataPermissionDTOList;
    // 临时存储使用
    private String teamId;
    private String teamName;
    private DataPermissionDTO parent;

    public synchronized void addDataNode(DataPermissionDTO node) {
        if (dataPermissionDTOList == null) {
            dataPermissionDTOList = new ArrayList<>();
        }
        dataPermissionDTOList.add(node);
    }

    /**
     * Node节点，唯一标识
     * @return
     */
    public String identifier() {
        return this.getId()
            .concat(this.getParentName() == null ? "" : this.getParentName()).concat("_")
            .concat(this.getTeamId() == null ? "" : this.getTeamId()).concat("_")
            .concat(String.valueOf(this.level));
    }

    /**
     * Node节点，唯一标识
     * @return
     */
    public String identifier4parent() {
        return ""
            .concat(this.getParentId() == null ? "" : this.getParentId()).concat("_")
            .concat(this.getTeamId() == null ? "" : this.getTeamId()).concat("_")
            .concat(String.valueOf(DataPermissionNodeTrans.copyDataPermissionDTO(this).getPreLevel()));
    }

    public List<DataPermissionDTO> getDataPermissionDTOList() {
        return dataPermissionDTOList == null ? Lists.newArrayList() : dataPermissionDTOList.stream().map(v -> { v.setParentId(LabelEnum.levelOf(this.level) == LabelEnum.T ? this.parentId : this.id); return v; }).collect(Collectors.toList());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DataPermissionDTO)) {
            return false;
        }
        DataPermissionDTO that = (DataPermissionDTO) o;
        return Objects.equals(level, that.level) && Objects.equals(id, that.id) && Objects.equals(name, that.name)
            && Objects.equals(parentId, that.parentId) && Objects.equals(parentName, that.parentName)
            && Objects.equals(teamId, that.teamId) && Objects.equals(teamName, that.teamName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(level, id, name, parentId, parentName, teamId, teamName);
    }
}
