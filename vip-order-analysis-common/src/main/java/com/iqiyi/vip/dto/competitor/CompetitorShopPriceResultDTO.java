package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 竞品店铺价格查询结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品店铺价格查询结果")
public class CompetitorShopPriceResultDTO {

    @ApiModelProperty(value = "旗舰店商品信息列表")
    private List<FlagshipStoreItem> flagshipItems;
    
    @ApiModelProperty(value = "非旗舰店商品信息列表")
    private List<NonFlagshipStoreItem> nonFlagshipItems;

    /**
     * 旗舰店商品信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @ApiModel(value = "旗舰店商品信息")
    public static class FlagshipStoreItem {

        @ApiModelProperty(value = "品牌")
        private String brand;
        
        @ApiModelProperty(value = "会员类型")
        private String vipType;
        
        @ApiModelProperty(value = "商品类型")
        private String product;
        
        @ApiModelProperty(value = "价格（元）")
        private String price;
        
        @ApiModelProperty(value = "促销信息")
        private String priceText;
    }
    
    /**
     * 非旗舰店商品信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @ApiModel(value = "非旗舰店商品信息")
    public static class NonFlagshipStoreItem {

        @ApiModelProperty(value = "品牌")
        private String brand;
        
        @ApiModelProperty(value = "会员类型")
        private String vipType;
        
        @ApiModelProperty(value = "商品类型")
        private String product;
        
        @ApiModelProperty(value = "平均价格（元）")
        private String avgPrice;
        
        @ApiModelProperty(value = "最低价格（元）")
        private String minPrice;
    }
} 