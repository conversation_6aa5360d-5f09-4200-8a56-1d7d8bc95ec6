package com.iqiyi.vip.dto.dimension;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/6/13 13:53
 */
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "枚举类信息")
@Builder
public class RemainV2CodeDescPair {
    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "描述或名称")
    private String desc; //

    public RemainV2CodeDescPair(String desc) {
        this.desc = desc;
    }

    public RemainV2CodeDescPair(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
