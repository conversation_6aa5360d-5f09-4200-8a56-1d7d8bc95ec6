package com.iqiyi.vip.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @className NextDiagnosisDimensionInfo
 * @description
 * @date 2023/11/1
 **/
@Data
@ApiModel(value = "下转对应维度信息")
@AllArgsConstructor
public class NextDiagnosisDimensionInfo {

    @ApiModelProperty(value = "当前维度下一层维度code， 没有下一级时为null")
    private String nextDimensionLayerCode;

    @ApiModelProperty(value = "当前维度下一层维度组code， 没有下一级时为null")
    private String nextGroupCode;

}
