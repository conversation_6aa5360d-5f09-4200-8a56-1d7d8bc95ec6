package com.iqiyi.vip.dto.realmonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2025/3/1 19:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "业务指标监控条件")
public class BizTargetMonitorConfigCondition {

    @ApiModelProperty(value = "条件key")
    private String key;
    @ApiModelProperty(value = "条件值")
    private List<Object> value;

}
