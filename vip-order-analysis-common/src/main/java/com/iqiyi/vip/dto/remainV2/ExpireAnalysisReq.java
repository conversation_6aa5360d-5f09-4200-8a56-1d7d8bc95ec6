package com.iqiyi.vip.dto.remainV2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/11 21:37
 */
@Data
@ApiModel(value = "会员到期分析查询参数")
public class ExpireAnalysisReq {
    @Schema(description = "操作者", required = true)
    @NotBlank(message = "不能为空")
    private String operator;

    @Schema(description = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    @Schema(description = "展示维度")
    private String dimensionCode;

    @ApiModelProperty(value = "订单圈选条件")
    private Map<String, List<Object>> conditionParamMap;

}
