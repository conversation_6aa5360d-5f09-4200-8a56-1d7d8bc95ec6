package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * 条件实体
 *
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2023/08/07
 */
@Data
@ApiModel(value = "条件信息")
public class ConditionUpdateDTO  extends BaseQry {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "字段code", required = true)
    private String code;

    @ApiModelProperty(value = "字段名称", required = true)
    private String name;

    @ApiModelProperty(value = "字段类型", required = true)
    private Integer type;

    @ApiModelProperty(value = "是否必填。 0：非必填  1：必填", required = true)
    private Boolean required;

    @ApiModelProperty(value = "默认提示词")
    private String defaultPrompt;

    @ApiModelProperty(value = "加载数据的地址（接口等）")
    private String dataLoadAddr;

    @ApiModelProperty(value = "是否可全选", required = true)
    private Boolean selectAll;

    @ApiModelProperty(value = "是否有提示icon", required = true)
    private Boolean hasTipsIcon;

    @ApiModelProperty(value = "提示文案")
    private String tips;

    @ApiModelProperty(value = "字段类型 1: String  2: Integer")
    private Integer fieldType;

    @ApiModelProperty(value = "状态 0：无效 1：有效")
    private Integer status;

    @ApiModelProperty(value = "单选or多选 0：单选 1：多选")
    private Integer multiChoice;

    @ApiModelProperty(value = "修改人")
    private String operator;

    @ApiModelProperty(value = "别名列表")
    private List<ConditionAlias> aliases;

}
