package com.iqiyi.vip.dto.remainV2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11 22:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LossAndBackAnalysisRes {
    @Schema(description = "是否根据维度分组, 0: 不根据维度分组, 返回dataList, 1: 根据维度分组, 返回excel文件名")
    private Integer byDimension;

    @Schema(description = "excel文件名")
    private String excelName;

    @Schema(description = "不按维度分组查询时, 得到的结果列表")
    List<LossAndBackAnalysisResData> dataList;
}
