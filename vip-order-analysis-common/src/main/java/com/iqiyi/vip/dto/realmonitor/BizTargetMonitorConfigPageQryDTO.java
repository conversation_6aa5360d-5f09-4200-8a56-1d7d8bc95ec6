package com.iqiyi.vip.dto.realmonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.iqiyi.vip.dto.base.PageQry;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "业务指标监控配置分页查询请求参数")
public class BizTargetMonitorConfigPageQryDTO extends PageQry {

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "指标code")
    private String target;
    @ApiModelProperty(value = "创建人")
    private String createOpr;

}
