package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @className BusinessObjectDTO
 * @description
 * @date 2022/9/6
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "产品及端平台对象")
public class BusinessObjectDTO {

    @ApiModelProperty(value = "产品名称字符串")
    private String bizName;

    @ApiModelProperty(value = "端平台名称字符串")
    private List<String> endKeys;
}
