package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.iqiyi.vip.dto.base.BaseQry;

/**
 * 旗舰店价格趋势查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "旗舰店价格趋势查询参数")
public class FlagshipStorePriceTrendQueryParam extends BaseQry {

    @ApiModelProperty(value = "开始时间戳(毫秒)", required = true)
    @NotNull(message = "开始时间不能为空")
    private Long startTime;
    
    @ApiModelProperty(value = "结束时间戳(毫秒)", required = true)
    @NotNull(message = "结束时间不能为空")
    private Long endTime;
    
    @ApiModelProperty(value = "平台名称", required = true)
    private String platform;
    
    @ApiModelProperty(value = "品牌名称", required = true)
    private String brand;
} 