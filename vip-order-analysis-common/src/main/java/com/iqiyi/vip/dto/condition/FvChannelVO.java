package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @className FvChannelRespDTO
 * @description
 * @date 2023/2/9
 **/
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "渠道信息返回对象")
@Builder
@AllArgsConstructor
public class FvChannelVO {

    @ApiModelProperty(value = "当前层级，二级渠道为2，业务团队为3，以此类推")
    private Integer level;

    @ApiModelProperty(value = "对应的id")
    private String id;

    @ApiModelProperty(value = "团队id")
    private Integer teamId;

    @ApiModelProperty(value = "前端渠道显示名称")
    private String name;

}
