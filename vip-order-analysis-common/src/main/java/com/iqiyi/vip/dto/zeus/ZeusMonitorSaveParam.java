package com.iqiyi.vip.dto.zeus;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/12/20 11:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZeusMonitorSaveParam {

    /**
     * 宙斯监控id
     */
    private Integer id;
    /**
     * 主题code
     */
    private String themeTypeName;
    /**
     * 指标名称
     */
    private String targetName;
    /**
     * 监控名称
     */
    private String name;
    /**
     * 监控查询配置信息
     */
    private List<ZeusMonitorQuery> query;
    /**
     * 扩展信息
     */
    private Map<String, Object> extraData;
    /**
     * 智能告警配置参数
     */
    private ZeusSmartAlertSaveParam smartAlertParam;

}
