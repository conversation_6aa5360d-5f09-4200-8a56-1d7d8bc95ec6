package com.iqiyi.vip.dto.platform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "平台分组")
public class PlatformGroup {

    @ApiModelProperty(value = "平台类型")
    private Integer plateFormType;
    @ApiModelProperty(value = "平台类型名称")
    private String plateFormTypeName;
    @ApiModelProperty(value = "包含的平台列表")
    private List<QiyuePlate> dataList;
}
