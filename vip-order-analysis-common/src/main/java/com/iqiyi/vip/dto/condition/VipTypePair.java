package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @className VipTypePair
 * @description
 * @date 2022/6/13
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "会员类型层级信息")
public class VipTypePair {

    @ApiModelProperty(value = "当前层级，二级渠道为2，业务团队为3，以此类推")
    private Integer level;

    @ApiModelProperty(value = "对应的id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;
}
