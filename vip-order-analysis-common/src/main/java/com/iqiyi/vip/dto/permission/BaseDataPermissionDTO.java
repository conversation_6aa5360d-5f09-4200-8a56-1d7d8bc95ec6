package com.iqiyi.vip.dto.permission;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/8/17 17:30
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "权限申请对象基础", description = "")
public class BaseDataPermissionDTO extends BaseQry implements java.io.Serializable {

    @NotBlank(message = "operator 参数不允许为空")
    @ApiModelProperty(value = "操作人, 订单分析平台账号，用户唯一标识", required = true)
    private String operator;
}
