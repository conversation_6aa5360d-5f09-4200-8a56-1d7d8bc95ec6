package com.iqiyi.vip.dto.base;

import com.iqiyi.vip.enums.CodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@ToString
@ApiModel(value = "通用返回结果")
public class CommonResult {
    public static final String SUCCESS_CODE = "A00000";

    @ApiModelProperty(value = "是否成功")
    private Boolean success;
    @ApiModelProperty(value = "返回码")
    private String code;
    @ApiModelProperty(value = "返回描述")
    private String message;
    @ApiModelProperty(value = "返回描述")
    private String msg;

    public CommonResult() {
        this.success = true;
        this.code = "A00000";
        this.msg = "成功";
        this.message = "成功";
    }

    public CommonResult(String code, String message) {
        this.success = false;
        this.code = code;
        this.message = message;
        this.msg = message;
    }

    public Boolean getSuccess() {
        return this.success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return StringUtils.isBlank(this.message) ? this.msg : this.message;
    }

    public void setMessage(String message) {
        this.message = message;
        this.msg = message;
    }

    public String getMsg() {
        return StringUtils.isBlank(this.msg) ? this.message : this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
        this.message = msg;
    }

    public static CommonResult success() {
        return new CommonResult();
    }

    public static CommonResult create(CodeEnum codeEnum) {
        return new CommonResult(codeEnum.getCode(), codeEnum.getMessage());
    }
}
