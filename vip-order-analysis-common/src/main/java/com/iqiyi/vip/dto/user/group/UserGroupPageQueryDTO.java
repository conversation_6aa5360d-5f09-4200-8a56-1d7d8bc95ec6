package com.iqiyi.vip.dto.user.group;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Date;

import com.iqiyi.vip.dto.base.PageQry;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 */
@Data
public class UserGroupPageQueryDTO extends PageQry {

    private Integer start;

    private Integer limit;

    @ApiParam(name = "id", value = "人群包id")
    private Integer id;

    /**
     * 分群名称
     */
    @ApiParam(name = "name", value = "分群名称")
    private String name;

    /**
     * 人群包类型,1:人群包 2:订单包
     */
    @ApiParam(name = "groupType", value = "人群包类型,1:人群包 2:订单包")
    private Integer groupType;

    /**
     * 创建方式
     */
    @ApiParam(name = "createType", value = "创建方式,1:文件上传")
    private Integer createType;
    /**
     * 上传进度,0:上传中 1:上传成功 2:上传失败
     */
    @ApiParam(name = "uploadProgress", value = "上传进度,0:上传中 1:上传成功 2:上传失败")
    private Integer uploadProgress;

    /**
     * 是否已同步北斗
     */
    @ApiParam(name = "isBeidouSynced", value = "是否已同步北斗, 0未同步 1已同步")
    private Integer isBeidouSynced;

    /*
     * 创建人
     */
    @ApiParam(name = "createUser", value = "创建人")
    private String createUser;


    @ApiParam(name = "startTime", value = "开始时间")
    private Long startTime;

    @ApiParam(name = "endTime", value = "结束时间")
    private Long endTime;


    public Date getStartTime() {
        try {
            return startTime != null ? DateUtils.toDate(DateUtils.ofMilliseconds(startTime)) : null;
        } catch (Exception e) {
            return null;
        }
    }

    public Date getEndTime() {
        try {
            return endTime != null ? DateUtils.toDate(DateUtils.ofMilliseconds(endTime)) : null;
        } catch (Exception e) {
            return null;
        }
    }
}