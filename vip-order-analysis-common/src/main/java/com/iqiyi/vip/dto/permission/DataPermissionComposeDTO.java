package com.iqiyi.vip.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/8/17 17:47
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "权限申请对象组合", description = "")
public class DataPermissionComposeDTO implements java.io.Serializable {

    /**
     * fv数据权限
     */
    @ApiModelProperty(value = "fv数据权限")
    private DataPermissionDTO fvData;
    /**
     * 产品数据权限
     */
    @ApiModelProperty(value = "产品数据权限")
    private DataPermissionDTO proData;

    @ApiModelProperty(value = "会员类型权限")
    private DataPermissionDTO vipTypeData;

    @ApiModelProperty(value = "竞品监控权限")
    private DataPermissionDTO competitorMonitorData;

}
