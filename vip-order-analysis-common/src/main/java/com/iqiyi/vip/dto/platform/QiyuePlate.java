package com.iqiyi.vip.dto.platform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 6/28/22
 * @apiNote
 */
@Data
@ToString
@ApiModel(value = "平台信息")
public class QiyuePlate {

    @ApiModelProperty(value = "平台名称")
    private String name;
    @ApiModelProperty(value = "平台code")
    private String code;
    @ApiModelProperty(value = "是否禁用 0：可用 1：禁用")
    private Integer disable = 0;
    @ApiModelProperty(value = "描述")
    private String description;

    public QiyuePlate() {
    }

    public QiyuePlate(String name, String code, String description) {
        this.name = name;
        this.code = code;
        this.description = description;
    }
}
