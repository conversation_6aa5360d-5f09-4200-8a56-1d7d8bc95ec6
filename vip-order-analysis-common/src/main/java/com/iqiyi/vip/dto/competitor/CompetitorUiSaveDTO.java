package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/10/25 17:39
 */
@Data
@ApiModel(value = "竞品页面数据保存参数类")
public class CompetitorUiSaveDTO {

    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;
    @ApiModelProperty(value = "版本号", required = true)
    @NotBlank(message = "版本号不能为空")
    private String clientVersion;
    @ApiModelProperty(value = "日期，格式: yyyy-MM-dd HH:mm:ss", required = true)
    @NotBlank(message = "日期不能为空")
    private String createTime;
    @ApiModelProperty(value = "场景", required = true)
    @NotBlank(message = "场景不能为空")
    private String page;
    @ApiModelProperty(value = "用户状态(生命周期)", required = true)
    @NotBlank(message = "生命周期不能为空")
    private String userStatus;
    @ApiModelProperty(value = "客户端类型，手机端或tv端", required = true)
    @NotBlank(message = "客户端类型不能为空")
    private String clientType;
    @ApiModelProperty(value = "UI截图URL", required = true)
    @NotEmpty(message = "截图链接不能为空")
    private List<String> screenshotUrls;
    @ApiModelProperty(value = "交互录屏URL")
    private String screenRecordingUrl;

}
