package com.iqiyi.vip.dto.remain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.iqiyi.vip.dto.base.BaseQry;

/**
 * <AUTHOR>
 * @className UpdateAlbumMetaDataDTO
 * @description
 * @date 2024/9/28
 **/
@Data
@ApiModel(value = "更新内容信息")
public class UpdateAlbumMetaDataDTO extends BaseQry {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "albumId")
    @NotBlank(message = "qipuAlbumId不能为空")
    private String qipuAlbumId;

    /**
     * 会员完结时间
     */
    @ApiModelProperty(value = "会员完结时间")
    private String vipEndTime;

    /**
     * 非会员完结时间
     */
    @ApiModelProperty(value = "非会员完结时间")
    private String commonEndTime;

    /**
     * 播后定级
     */
    @ApiModelProperty(value = "播后定级")
    private String resourceRating;

    /**
     * 关联留存展示
     */
    @ApiModelProperty(value = "关联留存展示，0：否，1：是")
    private Integer remainRelated;
}
