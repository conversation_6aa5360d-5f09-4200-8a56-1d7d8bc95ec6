package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/12/25 11:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品店铺价格信息")
public class CompetitorShopPriceDTO {

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "店铺类型，1-旗舰店，0-非旗舰店")
    private Integer storeType;

    @ApiModelProperty(value = "店铺类型描述")
    private String storeTypeName;
    
    @ApiModelProperty(value = "会员类型")
    private String vipType;
    
    @ApiModelProperty(value = "商品")
    private String product;
    
    @ApiModelProperty(value = "价格")
    private String price;
    
    @ApiModelProperty(value = "营销信息")
    private String priceText;
    
    @ApiModelProperty(value = "人群")
    private String userGroup;

    public String buildBrandPriceKey() {
        return (vipType != null ? vipType.trim() : "").replace("全屏会员", "SVIP").replace("白金", "SVIP").replace("黄金", "VIP").replace("会员", "VIP") + (product != null ? product.trim() : "");
    }

    public String getVipTypeNormalName() {
        if (StringUtils.isBlank(this.vipType)) {
            return "";
        }
        return this.vipType.trim().replace("全屏会员", "SVIP").replace("白金", "SVIP").replace("黄金", "VIP").replace("会员", "VIP").trim();
    }

    public boolean matchUserGroup(String userGroup, List<String> userGroupStrings) {
        return userGroupStrings.stream().anyMatch(this.userGroup::equals) || userGroupStrings.stream().anyMatch(this.userGroup::equals);
    }
} 