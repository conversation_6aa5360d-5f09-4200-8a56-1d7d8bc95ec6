package com.iqiyi.vip.dto.remain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AlbumMetaDataVO
 * @description
 * @date 2024/9/27
 **/
@Data
@ApiModel(value = "内容信息")
@Builder
public class AlbumMetaDataVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 专辑id
     */
    @ApiModelProperty(value = "专辑id")
    private String qipuAlbumId;

    /**
     * 内容名称
     */
    @ApiModelProperty(value = "内容名称")
    private String tvName;

    /**
     * 频道id
     */
    @ApiModelProperty(value = "频道id")
    private Integer channelId;


    /**
     * 频道名称
     */
    @ApiModelProperty(value = "频道名称")
    private String channelName;

    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    private String firstEpisodeOnlineTime;

    /**
     * 会员完结时间
     */
    @ApiModelProperty(value = "会员完结时间")
    private String vipEndTime;

    /**
     * 非会员完结时间
     */
    @ApiModelProperty(value = "非会员完结时间")
    private String commonEndTime;

    /**
     * 播后定级
     */
    @ApiModelProperty(value = "播后定级")
    private String resourceRating;

    /**
     * 关联留存展示
     */
    @ApiModelProperty(value = "关联留存展示，0：否，1：是")
    private Integer remainRelated;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;

    @ApiModelProperty(value = "是否完结，1：已完结，其他：未完结")
    private Integer finished;

}
