package com.iqiyi.vip.dto.remain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className RemainData
 * @description
 * @date 2024/6/29
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "留存数据")
public class RemainData {

    @Schema(description = "日期")
    private String dt;

    @Schema(description = "数量")
    private Long num;

}
