package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 旗舰店价格分布DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "旗舰店价格分布DTO")
public class FlagshipStorePriceDistributionDTO {

    @ApiModelProperty(value = "平台列表")
    private List<PlatformPriceInfo> platforms;

    /**
     * 平台价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "平台价格信息")
    public static class PlatformPriceInfo {
        
        @ApiModelProperty(value = "平台名称")
        private String platform;
        
        @ApiModelProperty(value = "卡种列表")
        private List<CardTypeInfo> cardTypes;
    }
    
    /**
     * 卡种信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "卡种信息")
    public static class CardTypeInfo {
        
        @ApiModelProperty(value = "卡种名称")
        private String cardType;
        
        @ApiModelProperty(value = "人群价格列表")
        private List<UserGroupPriceInfo> userGroups;
    }
    
    /**
     * 人群价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "人群价格信息")
    public static class UserGroupPriceInfo {
        
        @ApiModelProperty(value = "人群名称")
        private String userGroup;
        
        @ApiModelProperty(value = "品牌价格列表")
        private List<BrandPriceInfo> brandPrices;

    }
    
    /**
     * 品牌价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "品牌价格信息")
    public static class BrandPriceInfo {
        
        @ApiModelProperty(value = "品牌名称")
        private String brand;
        
        @ApiModelProperty(value = "价格")
        private String price;
        
        @ApiModelProperty(value = "促销信息")
        private String priceText;

        @ApiModelProperty(value = "是否发生变化")
        private Boolean changed;

        @ApiModelProperty(value = "最终展示文案")
        private String finalText;
    }
} 