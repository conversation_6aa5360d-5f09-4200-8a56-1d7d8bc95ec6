package com.iqiyi.vip.dto.competitor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserStatusType {

    private Map<String, ProductInfo> userStatusInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductInfo {
        private Map<String, SaleInfo> products; // 存储产品数据

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class SaleInfo {
            private String product; // 产品名称
            private String price;   // 产品价格
        }
    }
}
