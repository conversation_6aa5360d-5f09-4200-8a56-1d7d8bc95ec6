package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/12/25 11:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品店铺价格信息保存参数")
public class CompetitorShopPriceSaveDTO {

    @ApiModelProperty(value = "平台", required = true, example = "拼多多")
    @NotBlank(message = "平台不能为空")
    private String platform;

    @ApiModelProperty(value = "品牌", required = true, example = "腾讯")
    @NotBlank(message = "品牌不能为空")
    private String brand;

    @ApiModelProperty(value = "日期，格式: yyyy-MM-dd", required = true, example = "2025-05-25")
    @NotBlank(message = "日期不能为空")
    private String date;

    @ApiModelProperty(value = "店铺类型，1-旗舰店，0-非旗舰店", required = true, example = "1")
    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;


    @ApiModelProperty(value = "商品价格信息列表", required = true)
    @NotEmpty(message = "商品价格信息列表不能为空")
    private List<ProductInfo> products;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @ApiModel(value = "商品价格信息")
    public static class ProductInfo {
        
        @ApiModelProperty(value = "会员类型", required = true, example = "黄金会员")
        @NotBlank(message = "会员类型不能为空")
        private String vipType;
        
        @ApiModelProperty(value = "商品", required = true, example = "年卡")
        @NotBlank(message = "商品不能为空")
        private String product;
        
        @ApiModelProperty(value = "价格", required = true, example = "98.00")
        @NotBlank(message = "价格不能为空")
        private String price;
        
        @ApiModelProperty(value = "价格文案", example = "限时7折优惠")
        private String priceText;

        @ApiModelProperty(value = "人群", required = true, example = "店铺会员")
        private String userGroup;
    }
} 