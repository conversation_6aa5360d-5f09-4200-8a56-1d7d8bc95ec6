package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @className QiyuePlatformPair
 * @description
 * @date 2022/6/13
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "产品端平台层级信息")
public class QiyuePlatformPair {

    @ApiModelProperty(value = "当前层级，二级渠道为2，业务团队为3，以此类推")
    private Integer level;

    @ApiModelProperty(value = "对应的id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;
}
