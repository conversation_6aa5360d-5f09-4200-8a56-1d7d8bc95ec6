package com.iqiyi.vip.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/12/22
 * @apiNote
 */
@ApiModel(value = "返回分页结果")
public class PageListResult<T> extends ListResult<T> {

    @ApiModelProperty(value = "返回分页信息")
    private PageInfo pageInfo;

    public PageListResult() {
    }

    public PageListResult(List<T> dataList, PageInfo pageInfo) {
        super(dataList);
        this.pageInfo = pageInfo;
    }

    public PageListResult(String code, String msg) {
        super(code, msg);
    }

    public PageInfo getPageInfo() {
        return this.pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public static <T> PageListResult<T> createSuccess(List<T> dataList, Integer currentPage, Integer pageSize, Integer count) {
        PageListResult result = new PageListResult();
        result.setSuccess(true);
        result.setCode(CommonResult.SUCCESS_CODE);
        result.setMsg("成功");
        result.setMessage("成功");
        result.setDataList(dataList);

        PageInfo pageInfo = new PageInfo();
        pageInfo.setCurrentPage(currentPage);
        pageInfo.setCurrentCount(CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());
        pageInfo.setTotalCount(count);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotalPage(count % pageSize == 0 ? count / pageSize : count / pageSize + 1);
        result.setPageInfo(pageInfo);
        return result;
    }
}
