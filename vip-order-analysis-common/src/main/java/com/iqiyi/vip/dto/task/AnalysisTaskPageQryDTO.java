package com.iqiyi.vip.dto.task;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

import com.iqiyi.vip.dto.base.PageQry;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @className AnalysisTaskPageQryDTO
 * @description
 * @date 2022/6/1
 **/
@Data
public class AnalysisTaskPageQryDTO extends PageQry {

    @ApiParam(name = "targetName", value = "指标名称")
    private String targetName;

    @ApiParam(name = "taskId", value = "任务id")
    private Long taskId;

    @ApiParam(name = "taskName", value = "任务名称")
    private String taskName;

    @ApiParam(name = "startTime", value = "开始时间")
    private Long startTime;

    @ApiParam(name = "endTime", value = "结束时间")
    private Long endTime;

    @ApiParam(name = "operator", value = "操作人")
    @NotBlank(message = "operator为空")
    private String operator;

    public Date getStartTime() {
        try {
            return startTime != null ? DateUtils.toDate(DateUtils.ofMilliseconds(startTime)) : null;
        } catch (Exception e) {
            return null;
        }
    }

    public Date getEndTime() {
        try {
            return endTime != null ? DateUtils.toDate(DateUtils.ofMilliseconds(endTime)) : null;
        } catch (Exception e) {
            return null;
        }
    }
}
