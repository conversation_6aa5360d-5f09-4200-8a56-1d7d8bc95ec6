package com.iqiyi.vip.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 5/11/22
 * @apiNote
 */
@Data
@ApiModel(value = "分页查询请求")
public class PageQry extends BaseQry {

    @ApiModelProperty(value = "当前页", required = true)
    @NotNull(message = "pageNo不能为空")
    private Integer pageNo;

    @ApiModelProperty(value = "每页大小", required = true)
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;
}
