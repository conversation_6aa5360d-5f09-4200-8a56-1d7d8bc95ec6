package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @className FvChannelReqDTO
 * @description
 * @date 2022/9/8
 **/
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "渠道信息请求对象")
@Builder
@AllArgsConstructor
public class FvChannelReqDTO {

    @ApiModelProperty(value = "当前层级，二级渠道为2，业务团队为3，以此类推")
    private Integer level;

    @ApiModelProperty(value = "对应的id")
    private String id;

    @ApiModelProperty(value = "团队id")
    private Integer teamId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FvChannelReqDTO)) {
            return false;
        }
        FvChannelReqDTO that = (FvChannelReqDTO) o;
        return Objects.equals(id, that.id) && Objects.equals(teamId, that.teamId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, teamId);
    }
}
