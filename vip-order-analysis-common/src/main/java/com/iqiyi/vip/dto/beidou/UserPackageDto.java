package com.iqiyi.vip.dto.beidou;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/5 0:24
 */
@Data
public class UserPackageDto {
    private String name;
    private String username;
    private String url;
    private int pid;
    private int ptype;
    private String external;
    private String token;
    private Integer id;

    @Override
    public String toString() {
        return "UserPackageDto{" +
                "name='" + name + '\'' +
                ", username='" + username + '\'' +
                ", url='" + url + '\'' +
                ", pid=" + pid +
                ", ptype=" + ptype +
                ", external='" + external + '\'' +
                ", token='" + token + '\'' +
                ", id=" + id +
                '}';
    }
}
