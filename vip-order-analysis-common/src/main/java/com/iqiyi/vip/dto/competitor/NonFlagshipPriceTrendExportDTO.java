package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 非旗舰店价格趋势导出DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "非旗舰店价格趋势导出DTO")
public class NonFlagshipPriceTrendExportDTO {

    @ApiModelProperty(value = "日期")
    private String date;
    
    @ApiModelProperty(value = "平台")
    private String platform;
    
    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "卡种")
    private String cardType;
    
    @ApiModelProperty(value = "最低价")
    private String minPrice;
    
    @ApiModelProperty(value = "平均价")
    private String avgPrice;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;
} 