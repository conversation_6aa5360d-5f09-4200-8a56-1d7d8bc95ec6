package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/12/25 10:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品店铺监控数据")
public class CompetitorShopDTO {

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "店铺类型，1-旗舰店，0-非旗舰店")
    private Integer storeType;

    @ApiModelProperty(value = "店铺类型描述")
    private String storeTypeName;

    @ApiModelProperty(value = "截图URL列表")
    private List<String> screenshotUrls;

    @ApiModelProperty(value = "交互录屏URL")
    private String interactionVideoUrl;
} 