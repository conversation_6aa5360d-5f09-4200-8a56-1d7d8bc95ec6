package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className VersionRollBackReqDTO
 * @description
 * @date 2022/11/24
 **/
@Data
@ApiModel(value = "版本切换")
public class VersionSwitchReqDTO {

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3", required = true)
    @NotNull
    private Integer configType;

    @ApiModelProperty(value = "模板id", required = true)
    @NotNull
    private Long configId;

    @ApiModelProperty(value = "当前版本", required = true)
    @NotNull
    private Integer currentVersion;

    @ApiModelProperty(value = "版本，若为null默认回滚上一版本")
    private Integer version;

    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank
    private String operator;
}
