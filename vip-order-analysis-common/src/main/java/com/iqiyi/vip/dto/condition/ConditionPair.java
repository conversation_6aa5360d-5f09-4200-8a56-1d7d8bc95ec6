package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: linpeih<PERSON>
 * @createTime: 2023/09/22
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "条件通用的枚举类信息")
@Builder
public class ConditionPair {
    @ApiModelProperty(value = "编码")
    private Object code;

    @ApiModelProperty(value = "描述或名称")
    private String desc;

    private List<ConditionPair> subData;

    /**
     * 在该主题下不展示该枚举值
     */
    private Integer excludedThemeType;

    public ConditionPair(Object code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
