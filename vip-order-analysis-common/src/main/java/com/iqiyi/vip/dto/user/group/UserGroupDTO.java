package com.iqiyi.vip.dto.user.group;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * <AUTHOR>
 * 用户分群
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserGroupDTO extends BaseQry implements Serializable {

    /**
     * 分群名称
     */
    @NotNull(message = "分群名称不能为空")
    private String name;

    /**
     * 人群包类型,1:人群包 2:订单包
     */
    @NotNull(message = "人群包类型不能为空")
    private Integer groupType;

    /**
     * 人群包的地址
     */
    @NotNull(message = "网盘地址不能为空")
    private String dataUrl;

}