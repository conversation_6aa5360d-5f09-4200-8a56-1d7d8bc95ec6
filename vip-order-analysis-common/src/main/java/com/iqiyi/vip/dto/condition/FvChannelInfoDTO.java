package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @className FvChannelInfoDTO
 * @description
 * @date 2022/9/8
 **/
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "条件筛选框渠道信息")
@Builder
@AllArgsConstructor
public class FvChannelInfoDTO {
    @ApiModelProperty(value = "编码")
    private Integer code;

    @ApiModelProperty(value = "描述")
    private String desc; //

    @ApiModelProperty(value = "真实名称")
    private String name; // B

    @ApiModelProperty(value = "上一节点名称")
    private String parentName; // A

    @ApiModelProperty(value = "对应团队id")
    private Integer teamId;

    public FvChannelInfoDTO(Integer code, String desc, String name, String parentName) {
        this.code = code;
        this.desc = desc;
        this.name = name;
        this.parentName = parentName;
    }

    public FvChannelInfoDTO(String desc) {
        this.desc = desc;
    }

    public FvChannelInfoDTO(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
