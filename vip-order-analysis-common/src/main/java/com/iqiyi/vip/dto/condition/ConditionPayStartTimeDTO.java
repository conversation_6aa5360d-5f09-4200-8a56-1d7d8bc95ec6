package com.iqiyi.vip.dto.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/9/14 19:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConditionPayStartTimeDTO implements java.io.Serializable {

    private long payStartTime;

    public static ConditionPayStartTimeDTO of(long payStartTime) {
        return ConditionPayStartTimeDTO.builder()
            .payStartTime(payStartTime)
            .build();
    }

}
