package com.iqiyi.vip.dto.remain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @className ConditionCombinationVO
 * @description
 * @date 2024/6/29
 **/
@ApiModel(value = "remain条件组合")
@Data
@Builder
public class ConditionCombinationVO {

    @ApiModelProperty(value = "组合类型，单组：single， 多组：multi")
    private String type;

    @ApiModelProperty(value = "分组值")
    private List<String> combination;

}
