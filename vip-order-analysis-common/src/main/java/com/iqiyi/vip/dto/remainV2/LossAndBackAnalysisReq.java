package com.iqiyi.vip.dto.remainV2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/11 21:37
 */
@Data
@ApiModel(value = "会员流失和回归分析查询参数")
public class LossAndBackAnalysisReq {
    @Schema(description = "操作者", required = true)
    @NotBlank(message = "不能为空")
    private String operator;

    @Schema(description = "流失开始时间")
    @NotNull(message = "流失开始时间不能为空")
    private Long lossStartTime;

    @Schema(description = "流失结束时间", required = true)
    @NotNull(message = "流失结束时间不能为空")
    private Long lossEndTime;

    @Schema(description = "回归开始时间", required = true)
    @NotNull(message = "回归开始时间不能为空")
    private Long backStartTime;

    @Schema(description = "回归结束时间", required = true)
    @NotNull(message = "回归结束时间不能为空")
    private Long backEndTime;

    @Schema(description = "展示维度")
    private String dimensionCode;

    @ApiModelProperty(value = "订单圈选条件")
    private Map<String, List<Object>> conditionParamMap;
}
