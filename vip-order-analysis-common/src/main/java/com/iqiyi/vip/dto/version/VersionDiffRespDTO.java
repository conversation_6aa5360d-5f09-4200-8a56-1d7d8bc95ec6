package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @className VersionDiffRespDTO
 * @description
 * @date 2022/11/25
 **/
@Data
@ApiModel(value = "版本对比结果")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VersionDiffRespDTO {

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3", required = true)
    private Integer configType;

    @ApiModelProperty(value = "模板id", required = true)
    private Long configId;

    @ApiModelProperty(value = "当前版本")
    private Integer version;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "内容")
    private String value;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "操作时间")
    private Long operateTime;

    @ApiModelProperty(value = "配置code")
    private String configCode;

    @ApiModelProperty(value = "配置描述")
    private String configDesc;

    @ApiModelProperty(value = "配置分组")
    private Integer configGroupId;

    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @ApiModelProperty(value = "改动记录")
    private String commitNote;

}
