package com.iqiyi.vip.dto.remain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @className RemainConditionGroupVO
 * @description
 * @date 2024/6/25
 **/
@ApiModel(value = "remain维度信息")
@Data
@Builder
public class RemainConditionGroupVO {

    @ApiModelProperty(value = "分组名称")
    private String name;

    @ApiModelProperty(value = "分组code")
    private String code;

    @ApiModelProperty(value = "分组值")
    private List<String> groupValue;
}
