package com.iqiyi.vip.dto.sku;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * <AUTHOR>
 * @className SkuInfoQueryDTO
 * @description
 * @date 2024/5/23
 **/
@Data
@ApiModel(value = "Sku信息查询")
public class SkuInfoQueryDTO extends BaseQry {

    @ApiModelProperty(value = "skuId", required = true)
    @NotBlank(message = "skuId不能为空")
    private String skuId;

}
