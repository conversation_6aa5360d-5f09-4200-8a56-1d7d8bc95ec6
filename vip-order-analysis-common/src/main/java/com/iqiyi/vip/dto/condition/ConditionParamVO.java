package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @className ConditionParamVO
 * @description
 * @date 2023/2/9
 **/
@Data
@ApiModel(value = "接收圈选订单条件对象Vo", description = "圈选订单条件参数比如支付时间等")
public class ConditionParamVO {


    @ApiModelProperty(value = "订单支付开始时间")
    private Long payStartTime;

    @ApiModelProperty(value = "订单支付结束时间")
    private Long payEndTime;

    @ApiModelProperty(value = "订单对应会员类型")
    private List<CodeDescPair> vipTypes;

    @ApiModelProperty(value = "会员体系 1:爱奇艺会员 2:奇异果会员")
    private List<CodeDescPair> groupIds;

    @ApiModelProperty(value = "产品及端平台关系列表")
    private List<BusinessObjectDTO> businessObjects;

    @ApiModelProperty(value = "2级渠道")
    private List<FvChannelVO> level2BusinessIds;

    @ApiModelProperty(value = "团队id集合")
    private List<TeamVO> teamIds;

    @ApiModelProperty(value = "3级渠道")
    private List<FvChannelVO> level3BusinessIds;

    @ApiModelProperty(value = "4级渠道")
    private List<FvChannelVO> level4BusinessIds;

    @ApiModelProperty(value = "5级渠道")
    private List<FvChannelVO> level5BusinessIds;

    @ApiModelProperty(value = "6级渠道")
    private List<FvChannelVO> level6BusinessIds;

    @ApiModelProperty(value = "7级渠道")
    private List<FvChannelVO> level7BusinessIds;

    @ApiModelProperty(value = "8级渠道")
    private List<FvChannelVO> level8BusinessIds;

    @ApiModelProperty(value = "LT & LTV时长（单位：月）")
    private Integer ltPeriod;

    @ApiModelProperty(value = "-LT & -LTV时长（单位：月）")
    private Integer minusLtPeriod;

    @ApiModelProperty(value = "会员卡类型 1:天卡、2:月卡、3:季卡、4:半年卡、5:年卡")
    private List<CodeDescPair> vipCardTypes;

    @ApiModelProperty(value = "购买类型 0：普通购买（非连包），1签约购买（连包购买），2系统代扣（连包代扣）")
    private List<CodeDescPair> renewTypes;

    @ApiModelProperty(value = "支付渠道(支付方式列表)")
    private List<CodeDescPair> payTypes;

    @ApiModelProperty(value = "生命周期 0:新增1:到期前续费2:到期后[0,7]天内续费3:到期后[8,30]天内续费4:到期后[31,60]天内续费5:到期后[61,90]天内续费6:到期后[91,180]天内续费7:到期后[181,~]天内续费")
    private List<CodeDescPair> renewFlags;

    @ApiModelProperty(value = "付费类型标识：免费：0，免费：1，0元付费：2")
    private List<CodeDescPair> paidTypes;

    @ApiModelProperty(value = "收入类型，1：会员，2：套餐(比如超前点播套餐），3：其他增值套餐")
    private List<CodeDescPair> productTypes;

    @ApiModelProperty(value = "针对fv渠道是否考虑中间叶子节点，1：考虑每层渠道的叶子节点作为查询条件， 2：仅考虑显示指定的最后一级渠道作为查询条件")
    private Integer analysisType;

    @ApiModelProperty(value = "对比开始时间")
    private Long compareStartTime;

    @ApiModelProperty(value = "对比结束时间")
    private Long compareEndTime;

    @ApiModelProperty(value = "签约后时长（单位：月）")
    private Integer signPeriod;

    @ApiModelProperty(value = "fc码")
    private List<String> fc;

    @ApiModelProperty(value = "签约类型")
    private List<CodeDescPair> signTypes;

    @ApiModelProperty(value = "保价等级")
    private List<CodeDescPair> priceInsurances;

    @ApiModelProperty(value = "协议类型")
    private List<CodeDescPair> agreementTypes;

    private Integer refundPeriod;

}
