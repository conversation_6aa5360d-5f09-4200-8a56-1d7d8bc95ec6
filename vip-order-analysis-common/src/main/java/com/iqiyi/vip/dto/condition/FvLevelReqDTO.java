package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections4.MapUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className FvLevelReqDTO
 * @description
 * @date 2022/8/16
 **/
@ApiModel(value = "fv渠道信息请求DTO")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FvLevelReqDTO extends BaseQry {

    @ApiModelProperty(value = "当前请求层级，二级渠道为2，以此类推，业务团队为-1", required = true)
    @NotNull(message = "currentLevel不能为空")
    private Integer currentLevel;

    @ApiModelProperty(value = "选中的团队信息，支持多选")
    private List<Integer> teamIds;

    @NotBlank(message = "operator为空")
    @ApiModelProperty(value = "用户名称，邮箱前缀")
    private String operator;

    private Map<String, Object> dynamicFields = new HashMap<>();

    @JsonAnySetter
    public void setDynamicField(String field, Object value) {
        this.dynamicFields.put(field, value);
    }

    public List<Integer> getPreLevelIds() {
        if (MapUtils.isEmpty(dynamicFields)) {
            return Lists.newArrayList();
        }
        for (String key : dynamicFields.keySet()) {
            Object object = MapUtils.getObject(dynamicFields, key);
            if (object instanceof List) {
                try {
                    List<Integer> preLevelIds = (List<Integer>) object;
                    return preLevelIds;
                } catch (Exception ignored) {
                }
            }
        }
        return Lists.newArrayList();
    }
}
