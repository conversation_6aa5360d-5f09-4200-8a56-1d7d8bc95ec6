package com.iqiyi.vip.dto.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.dto.base.BaseQry;
import com.iqiyi.vip.dto.dimension.AnalysisDimensionVO;
import com.iqiyi.vip.dto.target.AnalysisTargetVO;

/**
 * <AUTHOR>
 * @className AnalysisTaskConfigRespDTO
 * @description
 * @date 2023/2/1
 **/
@Data
@SuperBuilder
@ApiModel(value = "分析任务参数配置", description = "任务相关参数配置")
public class AnalysisTaskConfigRespDTO extends BaseQry {

    @ApiModelProperty(name = "taskId", value = "任务id")
    private Long taskId;

    @ApiModelProperty(name = "taskName", value = "任务名称")
    private String taskName;

    @ApiModelProperty(name = "conditionParam", value = "条件参数")
    private Map<String, List<Object>> conditionParamVO;

    @ApiModelProperty(name = "dimensionList", value = "维度列表")
    private List<AnalysisDimensionVO> dimensionList;

    @ApiModelProperty(name = "targetList", value = "指标列表")
    private List<AnalysisTargetVO> targetList;

}
