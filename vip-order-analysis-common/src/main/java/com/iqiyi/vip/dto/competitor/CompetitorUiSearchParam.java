package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2024/10/29 15:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品页面监控数据查询参数类")
public class CompetitorUiSearchParam extends BaseQry {

    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;
    @ApiModelProperty(value = "版本号")
    private String clientVersion;
    @ApiModelProperty(value = "日期", required = true)
    @NotBlank(message = "日期不能为空")
    private String date;
    @ApiModelProperty(value = "场景")
    private String page;
    @ApiModelProperty(value = "用户生命周期")
    private String userStatus;

}
