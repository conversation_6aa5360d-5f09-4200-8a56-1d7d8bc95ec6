package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分析类型下关联的条件信息
 *
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2023/08/11
 */
@Data
@ApiModel(value = "分析类型下关联的条件信息")
public class BusinessConditionUpdateDTO extends BaseQry {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "分析主题id", required = true)
    private Long themeId;

    @ApiModelProperty(value = "分析类型id", required = true)
    private Long businessId;

    @ApiModelProperty(value = "修改人")
    private String operator;

    @ApiModelProperty(value = "条件列表")
    private List<ConditionSort> conditionList;

    @Data
    public static class ConditionSort {
        private Long id;
        private Integer sort;
    }
}
