package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2023/09/25
 */
@ApiModel(value = "获取条件枚举值的请求实体")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ConditionEnumReq extends BaseQry {
    private String code;
}
