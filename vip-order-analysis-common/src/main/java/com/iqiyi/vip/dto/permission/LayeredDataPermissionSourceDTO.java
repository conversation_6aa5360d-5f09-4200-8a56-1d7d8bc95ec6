package com.iqiyi.vip.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分层数据权限源
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/19 15:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LayeredDataPermissionSourceDTO {

    private String fieldPrefix;
    private DataPermissionDTO dataPermissionDTO;

}
