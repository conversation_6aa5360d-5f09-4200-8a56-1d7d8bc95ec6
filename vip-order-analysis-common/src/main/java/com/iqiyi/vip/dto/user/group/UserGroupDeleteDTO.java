package com.iqiyi.vip.dto.user.group;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UserGroupDeleteDTO {

    @ApiParam(name = "userGroupId", value = "待删除人群包id")
    @NotNull(message = "userGroupId为空")
    private Long userGroupId;

    @ApiParam(name = "operator", value = "操作人")
    @NotBlank(message = "operator为空")
    private String operator;
}
