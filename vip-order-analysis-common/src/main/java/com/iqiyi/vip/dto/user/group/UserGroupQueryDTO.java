package com.iqiyi.vip.dto.user.group;

import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * <AUTHOR>
 * 用户分群
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserGroupQueryDTO extends BaseQry implements Serializable {
    /**
     * 人群包类型,1:人群包 2:订单包
     */
    @ApiParam(name = "groupType", value = "人群包类型,1:人群包 2:订单包")
    private Integer groupType;
}