package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2024/12/25 11:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品店铺促销信息查询参数")
public class CompetitorShopPriceSearchParam extends BaseQry {

    @ApiModelProperty(value = "平台", required = true)
    private String platform;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "日期时间戳(毫秒)", required = true)
    private Long date;

    @ApiModelProperty(value = "店铺类型，1-旗舰店，0-非旗舰店")
    private Integer storeType;

    @ApiModelProperty(value = "人群")
    private String userGroup;
} 