package com.iqiyi.vip.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @className BaseQueryDTO
 * @description
 * <AUTHOR>
 * @date 2022/5/12
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "权限申请入参", description = "")
public class DataPermissionApplyDTO extends BaseDataPermissionDTO {

    @NotNull(message = "dataPermissionComposeDTO 数据权限不允许为空")
    @ApiModelProperty(value = "申请权限")
    private DataPermissionComposeDTO dataPermissionComposeDTO;

    @NotBlank(message = "applyReason 申请理由不允许为空")
    @Size(min = 15, message = "applyReason 申请理由需不少于15个字符")
    @ApiModelProperty(value = "数据权限申请理由", required = true)
    private String applyReason;
}
