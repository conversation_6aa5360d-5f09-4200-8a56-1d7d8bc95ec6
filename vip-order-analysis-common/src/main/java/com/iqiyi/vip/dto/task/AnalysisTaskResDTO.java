package com.iqiyi.vip.dto.task;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @className AnalysisTaskPageResDTO
 * @description
 * @date 2022/6/1
 **/
@Data
@SuperBuilder
@ApiModel(value = "分析任务分页查询结果", description = "分析任务分页查询结果")
public class AnalysisTaskResDTO extends BaseQry {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 指标code
     */
    @ApiModelProperty(value = "指标名称，、号分割")
    private String targetNames;

    /**
     * 筛选条件
     */
    @ApiModelProperty(value = "条件（json字符串）")
    private String condition;

    /**
     * 维度code
     */
    @ApiModelProperty(value = "维度名称，、号分割")
    private String dimensionNames;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态，0：待执行，1：执行中， 2：已完成， 3：执行失败")
    private Integer status;

    /**
     * 指标结果链接
     */
    @ApiModelProperty(value = "结果文件名称")
    private String result;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "最近一次任务执行时间")
    private Integer executeTime;

    /**
     * 任务执行周期
     */
    @ApiModelProperty(value = "任务执行周期")
    private String executeCycle;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "任务更新时间")
    private Date updateTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 任务参数的唯一标识
     */
    @ApiModelProperty(value = "任务唯一标识")
    private String uniqueIdentification;

    /**
     * 天眼业务类型
     */
    @ApiParam(name = "businessTypeId", value = "天眼业务类型 - 区分不同业务对应不同指标、维度集合的类型。联系：<EMAIL>; <EMAIL>")
    private Integer businessTypeId;

    /**
     * 导出Excel的查询sql
     */
    @ApiModelProperty(value = "天眼指标查询Sql")
    private String querySql;


    /**
     * 跳转北斗按钮状态，0：置灰，1：可点击
     */
    @ApiModelProperty(value = "上传北斗按钮状态")
    private int uploadToBeiDouStatus;
}
