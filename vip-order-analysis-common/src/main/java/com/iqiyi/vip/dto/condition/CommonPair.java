package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @className CommonPair
 * @description
 * @date 2023/10/24
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonPair {

    @ApiModelProperty(value = "当前层级")
    private Integer level;

    @ApiModelProperty(value = "对应的id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

}
