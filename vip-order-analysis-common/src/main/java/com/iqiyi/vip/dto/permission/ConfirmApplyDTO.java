package com.iqiyi.vip.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 1:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "权限申请时,确认提交界面返回", description = "")
public class ConfirmApplyDTO {
    @ApiModelProperty(value = "本次申请权限")
    List<String> permissionApply;

    @ApiModelProperty(value = "已有权限")
    List<String> permissionAlreadyHas;
}
