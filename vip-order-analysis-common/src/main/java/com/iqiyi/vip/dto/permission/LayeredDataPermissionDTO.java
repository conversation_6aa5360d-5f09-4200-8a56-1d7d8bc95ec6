package com.iqiyi.vip.dto.permission;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.iqiyi.vip.constant.Constants;
import com.iqiyi.vip.enums.LabelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 当前类的作用是实现一个，用户已申请权限的层级平铺实体，方便后续逻辑使用
 * <AUTHOR>
 * @date 2022/8/19 18:06
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class LayeredDataPermissionDTO implements java.io.Serializable {

    // <父节点标识, 父节点下的子节点集合>
    private Map<String, List<DataPermissionDTO>> l1;
    private Map<String, List<DataPermissionDTO>> l2;
    private Map<String, List<DataPermissionDTO>> l3;
    private Map<String, List<DataPermissionDTO>> l4;
    private Map<String, List<DataPermissionDTO>> l5;
    private Map<String, List<DataPermissionDTO>> l6;
    private Map<String, List<DataPermissionDTO>> l7;
    private Map<String, List<DataPermissionDTO>> l8;
    private Map<String, List<DataPermissionDTO>> t;

    private Map<String, List<DataPermissionDTO>> p20;
    private Map<String, List<DataPermissionDTO>> p21;
    private Map<String, List<DataPermissionDTO>> p22;

    private Map<String, List<DataPermissionDTO>> vt30;
    private Map<String, List<DataPermissionDTO>> vt31;
    private Map<String, List<DataPermissionDTO>> vt32;

    private Map<String, List<DataPermissionDTO>> competitor50;

    // < 属性名, < 父节点标识, < 父节点下的子节点集合 > > >
    private Map<String, Map<String, List<DataPermissionDTO>>> layeredDataPermissionMap;

    /**
     * Json 转实体对象
     * @param layeredPermissionJson
     * @return
     */
    public static LayeredDataPermissionDTO getJson2LayeredDataPermissionDTO(String layeredPermissionJson) {
        return JSON.parseObject(layeredPermissionJson, LayeredDataPermissionDTO.class);
    }

    /**
     * 获得分层数据权限
     * @return < 属性名, < 父节点标识, < 父节点下的子节点集合 > > >
     */
    public Map<String, Map<String, List<DataPermissionDTO>>> buildLayeredDataPermissionMap() {
        if (layeredDataPermissionMap == null) {
            synchronized (this) {
                if (layeredDataPermissionMap == null) {
                    layeredDataPermissionMap = new HashMap<>();
                    for (Field field : LayeredDataPermissionDTO.class.getDeclaredFields()) {
                        if (!field.getName().contains("layeredDataPermissionMap")) {
                            ReflectionUtils.makeAccessible(field);
                            Map<String, List<DataPermissionDTO>> datas = (Map<String, List<DataPermissionDTO>>) ReflectionUtils.getField(field, this);
                            if (datas != null) {
                                layeredDataPermissionMap.put(field.getName(), datas);
                            }
                        }
                    }
                }
            }
        }
        return layeredDataPermissionMap;
    }

    /**
     * 获取当前分成下面的所有权限节点
     * @param fieldPrefix
     * @param labelEnum
     * @return
     */
    public List<DataPermissionDTO> getDataPermissions4LayeredOwned(String fieldPrefix, LabelEnum labelEnum) {
        final Map<String, List<DataPermissionDTO>> maps = this.buildLayeredDataPermissionMap().get(this.getDataPermissions4LayeredFieldName(labelEnum, fieldPrefix)); // 获取 LabelEnum 层数据
        return maps != null ? maps.values().stream().flatMap(v -> v.stream()).collect(Collectors.toList()) : Lists.newArrayList();
    }

    /**
     * 获取含有权限的子节点集合
     * @param fieldPrefix
     * @param labelEnum
     * @param parent
     * @return
     */
    public List<DataPermissionDTO> getDataPermissions4LayeredOwned(String fieldPrefix, LabelEnum labelEnum, DataPermissionDTO parent) {
        final Map<String, List<DataPermissionDTO>> maps = buildLayeredDataPermissionMap().get(this.getDataPermissions4LayeredFieldName(labelEnum, fieldPrefix));
        return maps != null ? maps.values().stream()
                .flatMap(Collection::stream)
                .filter(v -> Objects.equals(parent.getId(), v.getParentId()) && (parent.getTeamId() == null || Objects.equals(parent.getTeamId(), v.getTeamId())))
                .collect(Collectors.toList()) : Lists.newArrayList();
    }

    /**
     * 拥有节点权限
     * @param fieldPrefix
     * @param labelEnum
     * @param dataPermissionDTO
     * @return
     */
    public boolean containsDataPermissions4LayeredOwned(
            String fieldPrefix, LabelEnum labelEnum, DataPermissionDTO dataPermissionDTO) {
        // <父节点标识, 父节点下的子节点集合>
        final Map<String, List<DataPermissionDTO>> allDataPermissions = buildLayeredDataPermissionMap().get(this.getDataPermissions4LayeredFieldName(labelEnum, fieldPrefix));
        return allDataPermissions != null && allDataPermissions.getOrDefault(dataPermissionDTO.identifier4parent(), Lists.newArrayList())
                .stream()
                .anyMatch(v ->
                        Objects.equals(dataPermissionDTO.getTeamId(), v.getTeamId())
                                && Objects.equals(dataPermissionDTO.getParentId(), v.getParentId())
                                && Objects.equals(dataPermissionDTO.getId(), v.getId()));
    }

    public void createLayeredDataPermissions(LayeredDataPermissionSourceDTO... layeredDataPermissionSourceDTOs) {
        if (layeredDataPermissionSourceDTOs != null) {
            for (LayeredDataPermissionSourceDTO layeredDataPermissionSourceDTO : layeredDataPermissionSourceDTOs) {
                if (layeredDataPermissionSourceDTO != null && layeredDataPermissionSourceDTO.getDataPermissionDTO() != null) {
                    this.createLayeredDataPermission(layeredDataPermissionSourceDTO.getDataPermissionDTO(), layeredDataPermissionSourceDTO.getFieldPrefix());
                }
            }
        }
    }

    /**
     * 创建用户已拥有分层权限数据
     * @param data
     * @param fieldPrefix
     */
    private void createLayeredDataPermission(final DataPermissionDTO data, String fieldPrefix) {
        if (data == null || data.getLevel() == null || ObjectUtils.isEmpty(data.getId())) {
            return ;
        }
        Queue<DataPermissionDTO> queue = new LinkedList<>();
        queue.offer(data);
        DataPermissionDTO cur = null;
        while ((cur = queue.poll()) != null) {
            final Field field = ReflectionUtils.findField(LayeredDataPermissionDTO.class,
                    this.getDataPermissions4LayeredFieldName(LabelEnum.levelOf(cur.getLevel()), fieldPrefix));
            // 对象反射赋值
            ReflectionUtils.makeAccessible(field);
            Map<String, List<DataPermissionDTO>> val = (Map<String, List<DataPermissionDTO>>) ReflectionUtils.getField(field, this);
            if (val == null) {
                ReflectionUtils.setField(field, this, new HashMap<>());
                val =  (Map<String, List<DataPermissionDTO>>) ReflectionUtils.getField(field, this);
            }
            if (val != null) {
                val.computeIfAbsent(cur.identifier4parent(), key -> Lists.newArrayList()).add(cur);
            }
            // 将子节点加入队列Queue
            if (cur.getDataPermissionDTOList() != null) {
                cur.getDataPermissionDTOList().stream().forEach(queue::offer);
            }
        }
    }

    /**
     * 获取对应 LayeredDataPermissionDTO 中的属性名称
     * @param labelEnum
     * @param fieldPrefix
     * @return
     */
    private String getDataPermissions4LayeredFieldName(LabelEnum labelEnum, String fieldPrefix) {
        return LabelEnum.T == labelEnum ?
                Constants.DATA_PERMISSION_TEAM_ABBREVIATION : fieldPrefix + labelEnum.getLevel();
    }

}
