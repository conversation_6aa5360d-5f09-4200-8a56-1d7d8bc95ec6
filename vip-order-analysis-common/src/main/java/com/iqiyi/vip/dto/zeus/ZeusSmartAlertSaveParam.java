package com.iqiyi.vip.dto.zeus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2025/3/7 00:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZeusSmartAlertSaveParam {

    /**
     * 智能告警规则id
     */
    private Integer id;
    /**
     * 监控id
     */
    private Integer monitorId;
    /**
     * 只能告警规则名称
     */
    private String ruleName;
    /**
     * 持续时间, 默认2m
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     */
    private String duration;
    /**
     * 检测频率, 默认30s
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     */
    private String checkFrequency;
    /**
     * 告警接收人，多个用逗号分隔
     */
    private String receivers;

}
