package com.iqiyi.vip.dto.dimension;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AnalysisDimensionVO
 * @description
 * @date 2023/2/1
 **/
@Data
@ApiModel(value = "分析任务维度参数配置", description = "任务维度参数配置")
public class AnalysisDimensionVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "维度名称")
    private String name;

    @ApiModelProperty(value = "维度code")
    private String code;

    @ApiModelProperty(value = "业务类型ID")
    private Integer businessTypeId;

}
