package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import javax.validation.constraints.NotNull;
import com.iqiyi.vip.dto.base.BaseQry;

/**
 * 非旗舰店价格查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "非旗舰店价格查询参数")
public class NonFlagshipStorePriceQueryParam extends BaseQry {

    @ApiModelProperty(value = "日期时间戳(毫秒)", required = true)
    private Long date;

    @ApiModelProperty(value = "平台名称，如果有值则仅查询该平台")
    private String platform;
} 