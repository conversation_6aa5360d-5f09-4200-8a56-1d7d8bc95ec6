package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR>
 * @className FlagshipShopPriceBoardResult
 * @description
 * @date 2025/6/27
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "旗舰店看板结果")
public class FlagshipShopPriceBoardResult {

    @ApiModelProperty(value = "旗舰店价格趋势")
    private List<FlagshipShopPriceTrendDTO> flagshipShopPriceTrend;

    @ApiModelProperty(value = "专辑内容信息列表")
    private List<AlbumContentInfoDTO> albumContents;
}
