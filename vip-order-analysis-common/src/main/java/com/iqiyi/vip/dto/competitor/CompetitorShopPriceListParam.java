package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2024/12/25 11:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品店铺促销信息列表查询参数")
public class CompetitorShopPriceListParam extends BaseQry {

    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String platform;

    @ApiModelProperty(value = "日期时间戳(毫秒)")
    private Long date;
} 