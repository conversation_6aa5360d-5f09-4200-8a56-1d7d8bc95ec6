package com.iqiyi.vip.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/7/10 16:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "权限申请状态及已申请的权限主题和权限集合", description = "")
public class DataPermissionStatusDTO {
    @ApiModelProperty(value = "导航主题与对应的权限集合")
    private Map<Integer, List<Integer>> themeTypes;
}
