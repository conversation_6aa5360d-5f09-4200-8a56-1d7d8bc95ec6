package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className ChangeListReqDTO
 * @description
 * @date 2022/11/25
 **/
@ApiModel("修改记录列表请求")
@Data
public class ChangeListReqDTO {

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3", required = true)
    @NotNull
    private Integer configType;

    @ApiModelProperty(value = "模板id", required = true)
    @NotNull
    private Long configId;

    @ApiModelProperty(value = "操作人")
    private String operator;

}
