package com.iqiyi.vip.dto.realmonitor;

import java.util.Arrays;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.iqiyi.vip.enums.OffsetTimeUnit;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TimeIntervalPair {

    private static final String OFFSET_TIMEUNIT_NAMES = Arrays.stream(OffsetTimeUnit.values()).map(Enum::name).collect(Collectors.joining(""));

    private final static Pattern EXEC_FREQUENCY_STR_PATTERN = Pattern.compile("^[1-9]\\d*[" + OFFSET_TIMEUNIT_NAMES + "]$");

    private int timeValue;
    private OffsetTimeUnit timeUnit;

    public TimeIntervalPair(Integer timeValue, OffsetTimeUnit timeUnit) {
        this.timeValue = timeValue;
        this.timeUnit = timeUnit;
    }

    /**
     * @param execFrequencyStr 格式：1s、1m、1h、1d、1w
     */
    public static TimeIntervalPair of(String execFrequencyStr) {
        if (StringUtils.isBlank(execFrequencyStr)) {
            return null;
        }
        if (!EXEC_FREQUENCY_STR_PATTERN.matcher(execFrequencyStr).matches()) {
            return null;
        }
        int lastCharIndex = execFrequencyStr.length() - 1;
        int numberPart = Integer.parseInt(execFrequencyStr.substring(0, lastCharIndex));
        if (numberPart <= 0) {
            return null;
        }
        OffsetTimeUnit timeUnit = OffsetTimeUnit.valueOf(execFrequencyStr.substring(lastCharIndex));
        return new TimeIntervalPair(numberPart, timeUnit);
    }

    public int toSeconds() {
        int seconds = 0;
        switch (timeUnit) {
            case s:
                seconds = timeValue;
                break;
            case m:
                seconds =  timeValue * 60;
                break;
            case h:
                seconds =  timeValue * 3600;
                break;
            case d:
                seconds =  timeValue * 86400;
                break;
            case w:
                seconds =  timeValue * 604800;
                break;
            default:
                break;
        }
        return seconds;
    }

    public boolean minuteUnit() {
        return this.timeUnit == OffsetTimeUnit.m;
    }

    public boolean hourUnit() {
        return this.timeUnit == OffsetTimeUnit.h;
    }

}
