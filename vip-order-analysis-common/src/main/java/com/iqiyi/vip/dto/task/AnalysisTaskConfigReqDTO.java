package com.iqiyi.vip.dto.task;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @className AnalysisTaskConfigReqDTO
 * @description
 * @date 2023/2/1
 **/
@Data
public class AnalysisTaskConfigReqDTO {

    @ApiParam(name = "taskId", value = "任务id")
    @NotNull(message = "taskId为空")
    private Long taskId;

    @ApiParam(name = "operator", value = "操作者")
    @NotBlank(message = "operator为空")
    private String operator;

    @ApiParam(value = "业务类型ID")
    private Integer businessTypeId;


}
