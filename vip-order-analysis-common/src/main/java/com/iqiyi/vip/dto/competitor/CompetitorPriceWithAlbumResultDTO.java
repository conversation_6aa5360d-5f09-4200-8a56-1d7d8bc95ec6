package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 竞品价格与专辑内容信息结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "竞品价格与专辑内容信息结果")
public class CompetitorPriceWithAlbumResultDTO {
    
    @ApiModelProperty(value = "竞品价格列表")
    private List<CompetitorPriceDTO> competitorPrices;
    
    @ApiModelProperty(value = "专辑内容信息列表")
    private List<AlbumContentInfoDTO> albumContents;
} 