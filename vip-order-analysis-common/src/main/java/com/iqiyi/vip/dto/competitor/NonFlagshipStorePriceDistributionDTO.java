package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 非旗舰店价格分布DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "非旗舰店价格分布DTO")
public class NonFlagshipStorePriceDistributionDTO {

    @ApiModelProperty(value = "平台列表")
    private List<PlatformPriceInfo> platforms;

    /**
     * 平台价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "平台价格信息")
    public static class PlatformPriceInfo {
        
        @ApiModelProperty(value = "平台名称")
        private String platform;
        
        @ApiModelProperty(value = "卡种列表")
        private List<CardTypeInfo> cardTypes;
    }
    
    /**
     * 卡种信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "卡种信息")
    public static class CardTypeInfo {
        
        @ApiModelProperty(value = "卡种名称")
        private String cardType;
        
        @ApiModelProperty(value = "品牌价格信息")
        private BrandPriceInfo brandPrices;
    }
    
    /**
     * 品牌价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "品牌价格信息")
    public static class BrandPriceInfo {
        
        @ApiModelProperty(value = "卡种名称")
        private String cardType;
        
        @ApiModelProperty(value = "爱奇艺最低价")
        private String iqiyiMinPrice;
        
        @ApiModelProperty(value = "爱奇艺平均价")
        private String iqiyiAvgPrice;
        
        @ApiModelProperty(value = "腾讯最低价")
        private String tencentMinPrice;
        
        @ApiModelProperty(value = "腾讯平均价")
        private String tencentAvgPrice;
        
        @ApiModelProperty(value = "优酷最低价")
        private String youkuMinPrice;
        
        @ApiModelProperty(value = "优酷平均价")
        private String youkuAvgPrice;
        
        @ApiModelProperty(value = "芒果最低价")
        private String manguoMinPrice;
        
        @ApiModelProperty(value = "芒果平均价")
        private String manguoAvgPrice;
    }
} 