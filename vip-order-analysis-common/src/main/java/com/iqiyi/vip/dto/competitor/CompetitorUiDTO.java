package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/10/29 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品页面数据")
public class CompetitorUiDTO {

    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "版本号")
    private String clientVersion;
    @ApiModelProperty(value = "客户端类型")
    private String clientType;
    @ApiModelProperty(value = "场景")
    private String page;
    @ApiModelProperty(value = "用户状态(生命周期)")
    private String userStatus;
    @ApiModelProperty(value = "UI截图URL")
    private List<String> screenshotUrls;
    @ApiModelProperty(value = "交互录屏URL")
    private String screenRecordingUrl;

}
