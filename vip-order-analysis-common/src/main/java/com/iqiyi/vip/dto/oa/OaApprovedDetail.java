package com.iqiyi.vip.dto.oa;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/23 17:52
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OaApprovedDetail implements java.io.Serializable {

    private String reason;
    private String dataPermission;
    private String dataPermissionAlreadyHas;
    private String themeType;
    private String permissionType;
    private String desc;
    private String messageId;

    public String oaDetails() {
        List<Map<String, String>> list = new ArrayList<>();
        list.add(makeOaDetail("申请理由", this.reason));
        list.add(makeOaDetail("分析主题", this.themeType));
        list.add(makeOaDetail("申请类型", this.permissionType));
        list.add(makeOaDetail("本次申请的数据权限", this.dataPermission));
        list.add(makeOaDetail("用户已有的数据权限", this.dataPermissionAlreadyHas));
        list.add(makeOaDetail("备注", this.desc));
        return JSON.toJSONString(list);
    }

    private Map<String, String> makeOaDetail(String name, String val) {
        Map<String, String> map = new HashMap<>();
        map.put("propName", name);
        map.put("value", val);
        return map;
    }

}
