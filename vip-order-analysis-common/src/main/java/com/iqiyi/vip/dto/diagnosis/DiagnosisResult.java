package com.iqiyi.vip.dto.diagnosis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @className DiagnosisResult
 * @description
 * @date 2023/10/30
 **/
@Data
@ApiModel(value = "诊断请求参数对象", description = "包含指标code条件等")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiagnosisResult {

    @ApiModelProperty(value = "维度组code, 用于区分维度组，如fc")
    private String dimensionGroupCode;

    @ApiModelProperty(value = "下钻时对应下一层级相关信息")
    private List<NextDiagnosisDimensionInfo> nextDiagnosisDimensionInfos;

    @ApiModelProperty(value = "是否可继续下钻")
    private Boolean couldDrillDown;

    @ApiModelProperty(value = "维度描述，如会员类型、二级渠道")
    private String dimensionLayerDesc;

    @ApiModelProperty(value = "维度层级code, 如level2_business_name")
    private String dimensionLayerCode;

    @ApiModelProperty(value = "下钻时条件传参code")
    private String conditionCode;

    @ApiModelProperty(value = "下钻时传参value")
    private String conditionValue;

    @ApiModelProperty(value = "维度显示名称，如爱奇艺VIP会员、站内渠道")
    private String dimensionName;

    @ApiModelProperty(value = "指标结果-本期")
    private String targetValueCurrentPeriod;

    @ApiModelProperty(value = "指标结果-基期")
    private String targetValueDiffPeriod;

    @ApiModelProperty(value = "增量")
    private String increment;

    @ApiModelProperty(value = "增量贡献占比")
    private String incrementalContributionRatio;

    @ApiModelProperty(value = "增长率")
    private String incrementalRatio;

    @ApiModelProperty(value = "超均贡献度")
    private String exceedingAverageContribution;
}
