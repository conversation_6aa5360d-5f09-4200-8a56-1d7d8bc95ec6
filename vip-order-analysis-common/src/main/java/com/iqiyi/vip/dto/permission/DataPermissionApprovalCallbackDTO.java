package com.iqiyi.vip.dto.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/17 13:58
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "审批回调对象", description = "")
public class DataPermissionApprovalCallbackDTO implements java.io.Serializable {

    @NotBlank(message = "acId Oa审批标识不允许为空")
    @ApiModelProperty(value = "Oa审批标识")
    private String acId;

    @NotNull(message = "oaStatus Oa工单状态不允许为空")
    @ApiModelProperty(value = "Oa工单状态  0-通过, 1-不通过, 2-中断(审批中的单据)")
    private Integer oaStatus;

    @ApiModelProperty(value = "Oa审批意见")
    private String desc;



}
