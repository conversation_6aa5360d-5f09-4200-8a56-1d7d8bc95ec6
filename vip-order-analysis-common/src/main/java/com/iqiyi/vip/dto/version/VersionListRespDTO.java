package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @className VersionListRespDTO
 * @description
 * @date 2022/11/25
 **/
@Data
@ApiModel("版本列表")
@AllArgsConstructor
public class VersionListRespDTO {

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3", required = true)
    private Integer configType;

    @ApiModelProperty(value = "模板id", required = true)
    private Long configId;

    @ApiModelProperty(value = "当前版本")
    private Integer version;
}
