package com.iqiyi.vip.dto.version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * @className ChangeListRespDTO
 * @description
 * @date 2022/11/25
 **/
@Data
@ApiModel("修改记录列表")
@Builder
public class ChangeListRespDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "配置类型，指标模板：1，指标：2，维度：3")
    private Integer configType;

    @ApiModelProperty(value = "模板id")
    private Long configId;

    @ApiModelProperty(value = "模板id")
    private Long configCode;

    @ApiModelProperty(value = "配置内容")
    private String configValue;

    @ApiModelProperty(value = "名称")
    private String configName;

    @ApiModelProperty(value = "配置说明")
    private String configDesc;

    @ApiModelProperty(value = "配置分组")
    private Integer configGroupId;

    @ApiModelProperty(value = "模板id")
    private Integer configTemplateId;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private Long operateTime;

    @ApiModelProperty(value = "修改备注")
    private String commitNote;

    @ApiModelProperty(value = "版本")
    private Integer version;
}
