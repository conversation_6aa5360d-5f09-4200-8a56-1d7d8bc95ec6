package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @className TeamVO
 * @description
 * @date 2023/2/9
 **/
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "渠道信息返回对象")
@Builder
@AllArgsConstructor
public class TeamVO {

    @ApiModelProperty(value = "团队id")
    private Integer teamId;

    @ApiModelProperty(value = "团队名称")
    private String teamName;


}
