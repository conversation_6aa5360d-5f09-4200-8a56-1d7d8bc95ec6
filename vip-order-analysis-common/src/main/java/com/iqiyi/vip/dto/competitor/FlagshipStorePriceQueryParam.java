package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import javax.validation.constraints.NotNull;
import com.iqiyi.vip.dto.base.BaseQry;

/**
 * 旗舰店价格查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "旗舰店价格查询参数")
public class FlagshipStorePriceQueryParam extends BaseQry {

//    @ApiModelProperty(value = "开始日期时间戳(毫秒)", required = true)
//    @NotNull(message = "开始日期不能为空")
//    private Long startDate;
//
//    @ApiModelProperty(value = "结束日期时间戳(毫秒)", required = true)
//    @NotNull(message = "结束日期不能为空")
//    private Long endDate;
    
    // 保留原有字段，用于向后兼容
    @ApiModelProperty(value = "日期时间戳(毫秒)")
    @NotNull(message = "日期不能为空")
    private Long date;

    @ApiModelProperty(value = "平台名称，如果有值则仅查询该平台")
    private String platform;
} 