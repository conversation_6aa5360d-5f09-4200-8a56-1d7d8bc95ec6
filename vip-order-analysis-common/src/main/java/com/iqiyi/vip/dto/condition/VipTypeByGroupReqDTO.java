package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @className VipTypeByGroupReqDTO
 * @description
 * @date 2022/8/25
 **/
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "根据会员体系查询会员类型请求参数")
public class VipTypeByGroupReqDTO extends BaseQry {
    @ApiModelProperty(value = "会员体系分组，1：爱奇艺会员, 2：奇异果会员，4：爱奇艺Fun会员，5：爱奇艺VR会员，6：奇异果儿童会员，7：商务会员")
//    @NotEmpty(message = "groupIds为空")
    private List<Integer> groupIds;

    private List<Integer> vipOrderGroupIds;
}
