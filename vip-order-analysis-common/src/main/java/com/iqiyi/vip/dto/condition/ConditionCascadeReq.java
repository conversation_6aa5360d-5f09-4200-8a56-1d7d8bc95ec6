package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.BaseQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 条件级联管理
 *
 * @author: linpeih<PERSON>
 * @createTime: 2023/08/07
 */
@Data
@ApiModel(value = "条件级联管理")
public class ConditionCascadeReq extends BaseQry {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "分析主题id", required = true)
    private Integer themeId;

    @ApiModelProperty(value = "分析类型id", required = true)
    private Integer businessId;

    @ApiModelProperty(value = "条件列表")
    private List<ConditionCascade> conditionList;

    @Data
    public static class ConditionCascade {
        private Long cascadeId;
        private Long currentId;
        private Long nextId;
        private Integer sort;
    }
}
