package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.dto.base.BaseQry;

/**
 * @author: guojing
 * @date: 2024/11/12 14:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel(value = "竞品监控页面场景列表参数类")
public class CompetitorPageListParam extends BaseQry {

    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;
    @ApiModelProperty(value = "日期")
    private String date;

}
