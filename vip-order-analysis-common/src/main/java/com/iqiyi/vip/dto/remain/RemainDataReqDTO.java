package com.iqiyi.vip.dto.remain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className RemainDataReqDTO
 * @description
 * @date 2024/6/25
 **/
@ApiModel(value = "留存数据查询参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RemainDataReqDTO {

    @Schema(description = "操作者", required = true)
    @NotBlank(message = "不能为空")
    private String operator;

    @Schema(description = "支付开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private Long payStartTime;

    @Schema(description = "支付结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private Long payEndTime;

    @Schema(description = "整体会员", required = false)
    private String remainAll;

    @Schema(description = "会员类型，传到订单圈选条件里", required = false)
    private String vipGroup;

    @Schema(description = "套餐类型，传到订单圈选条件里", required = false)
    private String vipType;

    @Schema(description = "买赠类型，传到订单圈选条件里", required = false)
    private String presentType;

    @Schema(description = "卡种，传到订单圈选条件里", required = false)
    private String cardType;

    @Schema(description = "生命周期，传到订单圈选条件里", required = false)
    private String renewFlag;

    @Schema(description = "基石潮汐，传到订单圈选条件里", required = false)
    private String uidLayer;

    @Schema(description = "圈层, 传到订单圈选条件里", required = false)
    private String sexAndAge;

    @Schema(description = "留存数据类型， 1：留存，2：流入，3：流出，4：净增", example = "1")
    private Integer remainDataType;

    @Schema(description = "展示维度， remainConditionGroup返回数据的一级name", example = "vip_group")
    private String dimensionCode;

    @ApiModelProperty(value = "订单圈选条件")
    private Map<String, List<Object>> conditionParamMap;
}
