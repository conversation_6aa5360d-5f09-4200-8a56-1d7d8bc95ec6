package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2024/11/20 14:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品监控日期范围")
public class CompetitorDateRangeDTO {

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;

}
