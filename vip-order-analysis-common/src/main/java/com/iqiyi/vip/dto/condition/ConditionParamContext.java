package com.iqiyi.vip.dto.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.utils.ConditionParamUtils;

/**
 * @author: linpeih<PERSON>
 * @createTime: 2023/09/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConditionParamContext {

    /**
     * 默认分享类型，按显示最低的层级分析
     */
    public static final int DEFAULT_ANALYSIS_TYPE = 2;
    /**
     * 入参的条件map
     */
    private Map<String, List<Object>> paramMap;

    public Long getPayStartTime() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "payStartTime", Long.class);
    }

    public Long getPayEndTime() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "payEndTime", Long.class);
    }

    public Integer getUserGroup() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "userGroup", Integer.class);
    }

    public Long getCompareStartTime() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "compareStartTime", Long.class);
    }

    public Long getCompareEndTime() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "compareEndTime", Long.class);
    }

    public Integer getLtPeriod() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "ltPeriod", Integer.class);
    }

    public Integer getRefundPeriod() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "refundPeriod", Integer.class);
    }

    public Integer getMinusLtPeriod() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "minusLtPeriod", Integer.class);
    }

    public Integer getAutoRenewLtPeriodMonth() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "autoRenewLtPeriodMonth", Integer.class);
    }

    public String getUgType() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "ugType", String.class);
    }

    public List<String> getVipAbTest() {
        return ConditionParamUtils.getListValue(this.paramMap, "vipAbTest", String.class);
    }

    public List<Integer> getVipTypes() {
        return ConditionParamUtils.getListValue(this.paramMap, "vipTypes", Integer.class);
    }

    public List<Integer> getGroupIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "groupIds", Integer.class);
    }

    public List<Integer> getLevel2BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level2BusinessIds", Integer.class);
    }

    public List<Integer> getLevel3BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level3BusinessIds", Integer.class);
    }

    public List<Integer> getLevel4BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level4BusinessIds", Integer.class);
    }

    public List<Integer> getLevel5BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level5BusinessIds", Integer.class);
    }

    public List<Integer> getLevel6BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level6BusinessIds", Integer.class);
    }

    public List<Integer> getLevel7BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level7BusinessIds", Integer.class);
    }

    public List<Integer> getLevel8BusinessIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "level8BusinessIds", Integer.class);
    }

    public Integer getSortType() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "sortType", Integer.class);
    }


    public Integer getAnalysisType() {
        Integer analysisType = ConditionParamUtils.getSingleValue(this.paramMap, "analysisType", Integer.class);
        return analysisType == null ? DEFAULT_ANALYSIS_TYPE : analysisType;
    }

    public List<String> getBizNames() {
        return ConditionParamUtils.getListValue(this.paramMap, "businessName", String.class);
    }

    public List<String> getBizEndKeys() {
        return ConditionParamUtils.getListValue(this.paramMap, "endKeys", String.class);
    }

    public List<String> getTeamIds() {
        return ConditionParamUtils.getListValue(this.paramMap, "teamIds", String.class);
    }

    public List<String> getAbExperiments() {
        return ConditionParamUtils.getListValue(this.paramMap, "abExperiment", String.class);
    }

    public String getCustomIncomeType() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "customIncomeType", String.class);
    }

    public List<String> getTargetCodes() {
        return ConditionParamUtils.getListValue(this.paramMap, "targetCodes", String.class);
    }

    public List<String> getDimensionCodes() {
        return ConditionParamUtils.getListValue(this.paramMap, "dimensionCodes", String.class);
    }

    public List<String> getSimpleQueryOperator() {
        return ConditionParamUtils.getListValue(this.paramMap, "uid", String.class);
    }

    public String getUniqueId() {
        return ConditionParamUtils.getSingleValue(this.paramMap, "taskUniqueId", String.class);
    }
}
