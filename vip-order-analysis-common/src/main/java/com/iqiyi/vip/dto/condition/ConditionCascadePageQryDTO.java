package com.iqiyi.vip.dto.condition;

import com.iqiyi.vip.dto.base.PageQry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: linpeihui
 * @createTime: 2023/08/07
 */
@Data
@ApiModel(value = "条件级联关系分页查询请求")
public class ConditionCascadePageQryDTO extends PageQry {

    @ApiModelProperty(value = "themeId")
    private Long themeId;
    @ApiModelProperty(value = "businessId")
    private Long businessId;
    @ApiModelProperty(value = "conditionName")
    private String conditionName;
    @ApiModelProperty(value = "起始的创建时间")
    private Long startCreateTime;
    @ApiModelProperty(value = "截止的创建时间")
    private Long endCreateTime;
    @ApiModelProperty(value = "起始的更新时间")
    private Long startUpdateTime;
    @ApiModelProperty(value = "截止的更新时间")
    private Long endUpdateTime;

}
