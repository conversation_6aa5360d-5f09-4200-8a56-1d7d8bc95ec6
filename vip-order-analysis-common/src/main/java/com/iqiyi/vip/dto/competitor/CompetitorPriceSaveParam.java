package com.iqiyi.vip.dto.competitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/10/29 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "竞品价格数据保存参数")
public class CompetitorPriceSaveParam {

    @ApiModelProperty(value = "日期，格式：yyyy-MM-dd HH:mm:ss", required = true)
    @NotBlank(message = "日期不能为空")
    private String date;
    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;
    @ApiModelProperty(value = "用户生命周期", required = true)
    @NotBlank(message = "用户生命周期不能为空")
    private String userStatus;
    @ApiModelProperty(value = "会员类型", required = true)
    @NotBlank(message = "会员类型不能为空")
    private String vipType;
    @ApiModelProperty(value = "商品价格信息", required = true)
    @NotEmpty(message = "商品价格信息不能为空")
    private List<CompetitorPriceProduct> productPrices;

}
