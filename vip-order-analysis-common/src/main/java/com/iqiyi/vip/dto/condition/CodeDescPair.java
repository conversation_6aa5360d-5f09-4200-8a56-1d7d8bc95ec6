package com.iqiyi.vip.dto.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
/**
 * <AUTHOR>
 * @date 6/13/22
 * @apiNote
 */
@Data
@ToString
@NoArgsConstructor
@ApiModel(value = "枚举类信息")
@Builder
public class CodeDescPair {

    @ApiModelProperty(value = "编码")
    private Integer code;

    @ApiModelProperty(value = "描述或名称")
    private String desc; //

    public CodeDescPair(String desc) {
        this.desc = desc;
    }

    public CodeDescPair(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
