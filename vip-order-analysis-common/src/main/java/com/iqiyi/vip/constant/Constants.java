package com.iqiyi.vip.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.iqiyi.vip.enums.VipGroupEnum;
import com.iqiyi.vip.enums.VipTypeEnum;

/**
 * <AUTHOR>
 * @date 2022/3/28 下午 08:35
 */
public class Constants {

    public static final Logger BUSINESS_LOG = LoggerFactory.getLogger("business_data_log");

    public static final String DEFAULT_OPERATOR = "backend";


    // 数据权限 渠道权限缩写，与LayeredDataPermissionDTO反写权限相关，方便快速校验用户已拥有权限
    public static final String DATA_PERMISSION_LEVEL_ABBREVIATION = "l";
    public static final String DATA_PERMISSION_PRO_ABBREVIATION = "p";
    public static final String DATA_PERMISSION_TEAM_ABBREVIATION = "t";
    public static final String DATA_PERMISSION_VT_ABBREVIATION = "vt";
    public static final String DATA_PERMISSION_CARD_ABBREVIATION = "card";
    public static final String DATA_PERMISSION_RENEW_ABBREVIATION = "renew";
    public static final String DATA_PERMISSION_PAY_CHANNEL_ABBREVIATION = "pay";
    public static final String DATA_PERMISSION_RENEW_FLAG_ABBREVIATION = "pay";
    public static final String DATA_PERMISSION_COMPETITOR_MONITOR_ABBREVIATION = "competitor";


    public static final String DATA_PERMISSION_TEAM_ABBREVIATION_COLUMN_FORMAT = "level%d_business_id";

    // 二级渠 - 道站外渠道
    public static final String UN_MAIN_STATION = "251000,251033,251134,";

    public static final boolean unMainStation(String dataPermissionId) { // 二级渠道非站内渠道
        return UN_MAIN_STATION.contains(dataPermissionId + Constants.DATA_PERMISSION_COMMON_SEPARATOR);
    }

    public static final int unMainStationIndexOf(String dataPermissionId) {
        return UN_MAIN_STATION.indexOf(dataPermissionId + Constants.DATA_PERMISSION_COMMON_SEPARATOR);
    }

    // 分层数据权限数据分隔符
    public static final String DATA_PERMISSION_COMMON_SINGLE_QUOTES = "'";
    public static final String DATA_PERMISSION_COMMON_SEPARATOR = ",";
    public static final String DATA_PERMISSION_COMMON_LINE = "%s_%s";
    public static final String DATA_PERMISSION_COMMON_BRACKETS = "(%s)";

    public static final String getBizEndKey(String bizName, String endKey) {
        return String.format(DATA_PERMISSION_COMMON_LINE, bizName == null ? "" : bizName, endKey);
    }

    public static final Pattern DATA_PERMISSION_COMMON_PATTERN = Pattern.compile(DATA_PERMISSION_COMMON_SEPARATOR);
    public static final String LAYERED_DATA_PERMISSION_DATA_SEPARATOR = DATA_PERMISSION_COMMON_SEPARATOR;
    public static final Pattern LAYERED_DATA_PERMISSION_DATA_PATTERN = Pattern.compile(LAYERED_DATA_PERMISSION_DATA_SEPARATOR);

    // 拼接字符串使用
    public static final String DATA_PERMISSION_SQL_PART_AND = "and ";
    public static final String DATA_PERMISSION_SQL_PART_OR = "or ";

    public static final boolean isAndStrEnd(String str) {
        return str.endsWith(DATA_PERMISSION_SQL_PART_AND);
    }
    public static final boolean isOrStrEnd(String str) {
        return str.endsWith(DATA_PERMISSION_SQL_PART_OR);
    }


    // Oa 审批工单备注
    public static final String OA_APPROVED_DESC = "如对该申请存在疑问，可联系TCG-会员业务事业部-赵而峰确认，谢谢！";


    // 指标分析，ARPU标识
    // 特殊逻辑，ARPU 指标分析需要支持批量处理。例如分析时间区间为 2022-05-01 - 2022-10-31，则需要迭代成05、06、07、08、09、10的时间区间，6个sql分析查询模板。
    public static final String TARGET_ANALYSIS_ARPU_CHARACTERISTIC = "ARPU";

    public static final String TARGET_ANALYSIS_REAMIN_CHARACTERISTIC = "remain";

    public static final String PRO_ORDER = "爱奇艺视频,爱奇艺极速版,爱奇艺随刻,爱奇艺漫画,爱奇艺小说,爱奇艺奇巴布,爱奇艺叭嗒,爱奇艺VR,爱奇艺AR,好多视频,云票,其他产品,对外合作接口,未知来源";
    public static final int proOrderIndexOf(String pro) {
        final int idx = Constants.PRO_ORDER.indexOf(pro);
        return idx > -1 ? idx : 9999;
    }

    public static Map<Integer, List<Integer>> getPresentVipMap() {
        Map<Integer, List<Integer>> presentVipMap = Maps.newHashMap();
        presentVipMap.put(VipGroupEnum.IQY.getCode(), Lists.newArrayList(VipTypeEnum.GOLD.getVipType(), VipTypeEnum.STUDENT.getVipType(), VipTypeEnum.LITE.getVipType()));
        presentVipMap.put(VipGroupEnum.QYG.getCode(), Lists.newArrayList(VipTypeEnum.QYG_EXCLUSIVE.getVipType(), VipTypeEnum.QYG_PLATINUM.getVipType()));
        presentVipMap.put(VipGroupEnum.FUN.getCode(), Lists.newArrayList(VipTypeEnum.FUN.getVipType()));
        presentVipMap.put(VipGroupEnum.VR.getCode(), Lists.newArrayList(VipTypeEnum.VR.getVipType()));
        presentVipMap.put(VipGroupEnum.CHILDREN.getCode(), Lists.newArrayList(VipTypeEnum.QYG_CHILD.getVipType()));
        presentVipMap.put(VipGroupEnum.COMMERCIAL_AFFAIRS.getCode(), Lists.newArrayList(VipTypeEnum.BUSINESS_VIP.getVipType()));
        return presentVipMap;
    }

    public static final String DATA_CHECK_OPERATOR = "chenguilong";


    public static final Integer DEFAULT_VERSION = 1;

    public static final Integer DEFAULT_COUNT = 1;

    public static final String HIVE_WIDE_TABLE_NAME = "boss.hive_trade_qiyue_order_widetable_order_analysis";

    public static final String CLICKHOUSE_WIDE_TABLE_NAME = "dist_hive_trade_qiyue_order_widetable_order_analysis final";

    public static final String STARROCKS_REAL_ORDER_TABLE_NAME = "real_vip_order_widetable_order_analysis";

    public static final int DEFAULT_QUARTER = 3;

    public static final String FAST_THREAD_POOL_NAME = "fast-thread-pool-%d&";

    public static final String SLOW_THREAD_POOL_NAME = "slow-thread-pool-%d&";

    public static final String CHECk_THREAD_POOL_NAME = "check-thread-pool-%d&";
    public static final String DIAGNOSIS_THREAD_POOL_NAME = "diagnosis-thread-pool-%d&";

    public static final String TRANSFER_ANALYSIS_SQL_SEPARATE = "-separation-";
    public static final Pattern TRANSFER_ANALYSIS_SQL_SEPARATE_PATTERN = Pattern.compile(TRANSFER_ANALYSIS_SQL_SEPARATE);

    public static final String CONTACT_INFO = "分析结果如附件，如有问题，请联系********************";

    public static final String IQIYI_VIDEO_NAME = "爱奇艺视频";

    public static final String INTERNAL_CHANNEL_NAME = "站内渠道";

    public static final String INTERNAL_CHANNEL_ID = "251040";
    public static final int INTERNAL_CHANNEL_ID_INT = 251040;

    public static final String TV_PLATFORM_END_KEY = "TVA-APK";

    public static final String DIMENSION_ITEM = "维度项";

    public static final String DIMENSIONS = "维度";

    public static final String CURRENT_PERIOD = "本期";
    public static final String DIFF_PERIOD = "基期";
    public static final String INCREMENT = "增量";
    public static final String INCREMENTAL_CONTRIBUTION_RATIO = "增量贡献";

    public static final String INCREMENTAL_RATIO = "增长率";
    public static final String EXCEEDING_AVERAGE_CONTRIBUTION = "大盘贡献度";
    public static final String SUMMATION = "合计";

    public static final String CLAUSE_OP_IN = "IN";
    public static final String CLAUSE_OP_LIKE = "LIKE";
    public static final String CLAUSE_OP_RULE = "RULE";


    public static Map<String, String> CodeNameMap() {
        HashMap<String, String> codeNameMap = Maps.newHashMap();
        codeNameMap.put("iqiyi", "爱奇艺");
        codeNameMap.put("qq", "腾讯");
        codeNameMap.put("youku", "优酷");
        codeNameMap.put("mangguo", "芒果");
        codeNameMap.put("bilibili", "哔哩哔哩");
        codeNameMap.put("sohu", "搜狐");
        codeNameMap.put("pptv", "PPTV");
        codeNameMap.put("letv", "乐视");
        codeNameMap.put("xigua", "西瓜");
        return codeNameMap;
    }

    public static Map<String, String> NameCodeMap() {
        HashMap<String, String> nameCodeMap = Maps.newHashMap();
        nameCodeMap.put("爱奇艺", "iqiyi");
        nameCodeMap.put("腾讯", "qq");
        nameCodeMap.put("优酷", "youku");
        nameCodeMap.put("芒果", "mangguo");
        nameCodeMap.put("哔哩哔哩", "bilibili");
        nameCodeMap.put("搜狐", "sohu");
        nameCodeMap.put("PPTV", "pptv");
        nameCodeMap.put("乐视", "letv");
        nameCodeMap.put("西瓜", "xigua");
        return nameCodeMap;
    }

}
