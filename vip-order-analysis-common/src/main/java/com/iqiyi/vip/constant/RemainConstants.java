package com.iqiyi.vip.constant;

import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import com.iqiyi.vip.dto.remain.RemainConditionGroupVO;
import com.iqiyi.vip.enums.RemainConditionTypeEnum;
/**
 * <AUTHOR>
 * @className RemainConstants
 * @description
 * @date 2024/6/25
 **/
public class RemainConstants {

    public static final String REMAIN_ALL = "整体会员";
    public static final String VIP_GROUP = "会员类型";
    public static final String VIP_TYPE = "套餐类型";
    public static final String PRESENT_TYPE = "买赠类型";
    public static final String CARD_TYPE = "卡种";
    public static final String RENEW_FLAG = "生命周期";
    public static final String UID_LAYER = "基石潮汐";
    public static final String SEX_AND_AGE = "圈层";

    public static final String REMAIN_ALL_CODE = "ALL";
    public static final String REMAIN_DATA_TYPE_COLUMN = "data_type";
    public static final List<String> VIP_GROUP_DIMENSION_VALUE = Lists.newArrayList("爱奇艺VIP会员", "奇异果VIP会员", "爱奇艺Fun会员", "爱奇艺VR会员", "奇异果儿童会员");
    public static final List<String> VIP_TYPE_DIMENSION_VALUE = Lists.newArrayList("爱奇艺黄金会员", "爱奇艺白金会员", "爱奇艺星钻会员", "爱奇艺学生会员", "爱奇艺基础会员", "奇异果白金会员", "奇异果星钻会员", "奇异果电视专享会员", "奇异果基础会员", "爱奇艺Fun会员", "爱奇艺VR会员", "奇异果儿童会员");
    public static final List<String> PRESENT_TYPE_DIMENSION_VALUE = Lists.newArrayList("同体系买赠0元单", "跨体系买赠0元单", "非买赠0元免费单", "非买赠0元付费单", "非0元付费单");
    public static final List<String> CARD_TYPE_DIMENSION_VALUE = Lists.newArrayList("月卡-普通购买", "月卡-连包购买", "月卡-系统代扣", "年卡-普通购买", "年卡-连包购买", "年卡-系统代扣", "季卡-普通购买", "季卡-连包购买", "季卡-系统代扣", "半年卡", "半天卡", "天卡");
    public static final List<String> RENEW_FLAG_DIMENSION_VALUE = Lists.newArrayList("新增", "到期前续费", "到期后[0,7]天内续费", "到期后[8,30]天内续费", "到期后[31,60]天内续费", "到期后[61,90]天内续费", "到期后[91,180]天内续费", "到期后[181,365]天内续费", "召回", "其他");
    public static final List<String> UID_LAYER_TYPE_DIMENSION_VALUE = Lists.newArrayList("基石会员-最基石", "基石会员-次基石", "潮汐会员-多次购买", "潮汐会员-单次购买", "其他");
    public static final List<String> SEX_AND_AGE_DIMENSION_VALUE = Lists.newArrayList("少男", "少女", "男青", "女青", "中高龄", "其他");

    public static List<RemainConditionGroupVO> initializeRemainConditionGroupList() {
        ArrayList<RemainConditionGroupVO> list = new ArrayList<>();
        list.add(RemainConditionGroupVO.builder().name(REMAIN_ALL).code(REMAIN_ALL_CODE).build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.VIP_GROUP.getName())
            .code(RemainConditionTypeEnum.VIP_GROUP.getCode())
            .groupValue(VIP_GROUP_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.VIP_BIZ_TYPE.getName())
            .code(RemainConditionTypeEnum.VIP_BIZ_TYPE.getCode())
            .groupValue(VIP_TYPE_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.PRESENT_TYPE.getName())
            .code(RemainConditionTypeEnum.PRESENT_TYPE.getCode())
            .groupValue(PRESENT_TYPE_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.CARD_TYPE.getName())
            .code(RemainConditionTypeEnum.CARD_TYPE.getCode())
            .groupValue(CARD_TYPE_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.RENEW_FLAG.getName())
            .code(RemainConditionTypeEnum.RENEW_FLAG.getCode())
            .groupValue(RENEW_FLAG_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.UID_LAYER.getName())
            .code(RemainConditionTypeEnum.UID_LAYER.getCode())
            .groupValue(UID_LAYER_TYPE_DIMENSION_VALUE)
            .build());
        list.add(RemainConditionGroupVO.builder()
            .name(RemainConditionTypeEnum.SEX_AND_AGE.getName())
            .code(RemainConditionTypeEnum.SEX_AND_AGE.getCode())
            .groupValue(SEX_AND_AGE_DIMENSION_VALUE)
            .build());
        return list;
    }
}
