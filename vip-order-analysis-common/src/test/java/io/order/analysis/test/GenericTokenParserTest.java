package io.order.analysis.test;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.parsing.GenericTokenParser;
import org.apache.ibatis.parsing.TokenHandler;

import java.util.List;
import java.util.Properties;

import com.iqiyi.vip.enums.DataBaseTypeEnum;

/**
 * <AUTHOR>
 * @date 2022/10/19 20:53
 */
public class GenericTokenParserTest {

    public static void main(String[] args) {

        Properties properties = new Properties();
        properties.put("dataPermissionLimit", "1 == 1");

        VariableTokenNewHandler handler = new VariableTokenNewHandler(properties);
        GenericTokenParser parser = new GenericTokenParser("${", "}", handler);
        String sql = parser.parse("a = a and ${ and @dataPermissionLimit@} and ${@HIVE@  hive }  and ${@CLICKHOUSE@  clickhouse } and ${a}"); // 解析指标对应模板，解析@标识符替换和${}标识符已存在的变量
        System.out.println(sql);

    }

    public static class VariableTokenNewHandler implements TokenHandler {
        private final Properties variables;
        private final boolean enableDefaultValue;
        private final String defaultValueSeparator;
        private final String valueBracketSign;

        public VariableTokenNewHandler(Properties variables) {
            this.variables = variables;
            this.enableDefaultValue = Boolean.parseBoolean(this.getPropertyValue("org.apache.ibatis.parsing.PropertyParser.enable-default-value", "true"));
            this.defaultValueSeparator = this.getPropertyValue("org.apache.ibatis.parsing.PropertyParser.default-value-separator", ":");
            this.valueBracketSign = "@";
        }

        private String getPropertyValue(String key, String defaultValue) {
            return this.variables == null ? defaultValue : this.variables.getProperty(key, defaultValue);
        }

        @Override
        public String handleToken(String content) {
            if (this.variables == null) {
                return "${" + content + "}";
            }

            //需要处理动态条件 来判断是否增加此条件和填充最终的值
            if (content.contains(valueBracketSign)) {
                //and dt = '@dt@' TO  and dt='1'
                int firstIndex = StringUtils.indexOf(content, valueBracketSign);
                int lastIndex = StringUtils.lastIndexOf(content, valueBracketSign);

                if (firstIndex < lastIndex) {
                    String prefix = content.substring(0, firstIndex);
                    String valueKey = content.substring(firstIndex + 1, lastIndex);
                    String suffix = content.substring(lastIndex + 1);
                    List<String> allDataBaseCodes = DataBaseTypeEnum.getAllCodes();
                    String value = this.variables.getProperty(valueKey);
                    if (allDataBaseCodes.contains(valueKey.toLowerCase())) {
                        String dataBase = this.variables.getProperty("dataBase");
                        if (valueKey.equalsIgnoreCase(dataBase)) {
                            return prefix + suffix;
                        } else {
                            return "";
                        }
                    }

                    if (StringUtils.isBlank(value)) {
                        return "";
                    } else {
                        return prefix + value + suffix;
                    }
                }
            }

            //需要处理动态条件，来填充最终的值
            String key = content;
            if (this.enableDefaultValue) {
                int separatorIndex = content.indexOf(this.defaultValueSeparator);
                String defaultValue = null;
                if (separatorIndex >= 0) {
                    key = content.substring(0, separatorIndex);
                    defaultValue = content.substring(separatorIndex + this.defaultValueSeparator.length());
                }

                if (defaultValue != null) {
                    return this.variables.getProperty(key, defaultValue);
                }
            }

            if (this.variables.containsKey(key)) {
                return this.variables.getProperty(key);
            }

            return "${" + content + "}";
        }
    }


}
