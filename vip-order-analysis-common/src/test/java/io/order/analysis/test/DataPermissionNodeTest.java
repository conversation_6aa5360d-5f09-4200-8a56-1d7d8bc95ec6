package io.order.analysis.test;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.data.DataNode;
import com.iqiyi.vip.dto.permission.LayeredDataPermissionDTO;
import org.junit.jupiter.api.Test;

import java.util.Collections;

public class DataPermissionNodeTest {

    public static void main(String[] args) {

        DataNode dataNode = DataNode.builder()
                .id("1")
                .name("one")
                .subDataNodeList(Collections.EMPTY_LIST)
                .build();

        DataNode dataNode2 = dataNode.toBuilder().name("two").build();

        System.out.println(dataNode);
        System.out.println(dataNode2);

    }

    @Test
    public void test() {

        String ss = "{\"l1\":{\"__0\":[{\"alreadyAuthorized\":false,\"checked\":2,\"dataPermissionDTOList\":[{\"alreadyAuthorized\":false,\"checked\":2,\"dataPermissionDTOList\":[{\"alreadyAuthorized\":false,\"checked\":4,\"dataPermissionDTOList\":[],\"id\":\"1\",\"level\":-1,\"mainStation\":false,\"name\":\"i联盟\",\"parentId\":\"251000\",\"parentName\":\"站外自营渠道\",\"teamId\":\"1\",\"teamName\":\"i联盟\"}],\"id\":\"251000\",\"level\":2,\"mainStation\":false,\"name\":\"站外自营渠道\",\"parentId\":\"2084\",\"parentName\":\"中国大陆\"},{\"alreadyAuthorized\":false,\"checked\":2,\"dataPermissionDTOList\":[{\"alreadyAuthorized\":false,\"checked\":2,\"dataPermissionDTOList\":[{\"alreadyAuthorized\":false,\"checked\":4,\"dataPermissionDTOList\":[],\"id\":\"251975\",\"level\":3,\"mainStation\":false,\"name\":\"电商\",\"parentId\":\"251134\",\"parentName\":\"渠道组\",\"teamId\":\"8\",\"teamName\":\"渠道组\"}],\"id\":\"8\",\"level\":-1,\"mainStation\":false,\"name\":\"渠道组\",\"parentId\":\"251134\",\"parentName\":\"站外销售渠道\",\"teamId\":\"8\",\"teamName\":\"渠道组\"}],\"id\":\"251134\",\"level\":2,\"mainStation\":false,\"name\":\"站外销售渠道\",\"parentId\":\"2084\",\"parentName\":\"中国大陆\"}],\"id\":\"2084\",\"level\":1,\"mainStation\":false,\"name\":\"中国大陆\"}]}," +
                "\"l2\":{\"2084__1\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[0]\"},{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[1]\"}]}," +
                "\"l3\":{" +
                "\"251000_2_-1\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0]\"}]," + // .dataPermissionDTOList[0]
                "\"251000_3_-1\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[0]\"}]," + // .dataPermissionDTOList[0]
                "\"251000_1_-1\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[0].dataPermissionDTOList[0]\"}]," + // .dataPermissionDTOList[0]
                "\"251134_8_-1\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[1].dataPermissionDTOList[0]\"}]" + // .dataPermissionDTOList[0]
                "}," +
                "\"t\":{\"251000_1_2\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[0].dataPermissionDTOList[0]\"}],\"251134_8_2\":[{\"$ref\":\"$.l1.\\\\_\\\\_0[0].dataPermissionDTOList[1].dataPermissionDTOList[0]\"}]}}";
        LayeredDataPermissionDTO layeredDataPermissionDTO = JSON.parseObject(ss, LayeredDataPermissionDTO.class);
        System.out.println(layeredDataPermissionDTO);
        System.out.println(JSON.toJSONString(layeredDataPermissionDTO));

    }

}
