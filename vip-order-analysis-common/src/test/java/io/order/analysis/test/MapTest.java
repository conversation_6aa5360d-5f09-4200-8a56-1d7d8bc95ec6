package io.order.analysis.test;

import com.google.common.collect.Maps;

import java.util.EnumMap;
import java.util.Map;

import com.iqiyi.vip.enums.LabelEnum;

/**
 * <AUTHOR>
 * @date 2022/10/11 14:01
 */
public class MapTest {

    public static void main(String[] args) {

        final EnumMap<LabelEnum, Object> labelEnumObjectEnumMap = Maps.newEnumMap(LabelEnum.class);
        labelEnumObjectEnumMap.put(LabelEnum.L3, 1);
        labelEnumObjectEnumMap.put(LabelEnum.L2, 1);
        labelEnumObjectEnumMap.put(LabelEnum.L1, 1);
        labelEnumObjectEnumMap.put(LabelEnum.L5, 1);
        labelEnumObjectEnumMap.put(LabelEnum.L6, 1);
        labelEnumObjectEnumMap.put(LabelEnum.L4, 1);

        for (Map.Entry<LabelEnum, Object> entry : labelEnumObjectEnumMap.entrySet()) {
            System.out.println(entry);
        }

    }

}
