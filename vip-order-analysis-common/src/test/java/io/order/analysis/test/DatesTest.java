package io.order.analysis.test;

import java.time.LocalDateTime;
import java.util.Date;

import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2022/9/15 10:31
 */
public class DatesTest {

    public static void main(String[] args) {

        final LocalDateTime now = LocalDateTime.now();
        System.out.println(DateUtils.retBeginningOfMonth(now));
        System.out.println(DateUtils.retEndOfTheMonth(now));
        System.out.println(now);
        final LocalDateTime now1 = LocalDateTime.now();
        System.out.println(DateUtils.dateDistance(now1, now1.minusYears(1)));

        System.out.println("----------------------------------");
        final Date date = new Date();
        System.out.println(date);
        System.out.println(date.getTime());
        final LocalDateTime localDateTime = DateUtils.ofMilliseconds(date.getTime());
        System.out.println(DateUtils.toMilliseconds(localDateTime));
        System.out.println(Date.from(localDateTime.toInstant(DateUtils.PLUS8_ZONE)));

    }

}
